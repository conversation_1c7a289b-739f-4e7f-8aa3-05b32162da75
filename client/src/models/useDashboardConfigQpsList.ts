/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-06-20 12:05:14
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-06-20 14:56:38
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getConfigQps } from '@/services/api';

export default function useCreativeList() {
  const { data, run, loading, error } = useCustomRequest(getConfigQps, {
    cacheKey: 'dashabord-config-qps-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    data,
    reload: run,
    loading
  };
}
