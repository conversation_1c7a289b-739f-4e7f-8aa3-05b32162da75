/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-05-25 10:28:38
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-11-29 15:06:32
 * @Description: 平台权限验证
 */
import { Context, Next } from 'koa';
import { getCtxResult } from '../utils/response';
import { getConfig, md5, getUserIp } from '@/utils';
import { getRedisByKey, setRedisByKey } from '@/db/redis';
import { sessionConfig } from '@/config/session';
import { InterfaceType } from '@/constants/permission';
import { commonModel, roleModel } from '@/models';
import { getLogger } from '@/config/log4js';
import { sendLarkAlertCard } from '@/utils/alert';
import { generateSignature } from '@/utils/signature';

const { redisConfig, domainConfig } = getConfig();

// 登出的正则
const IgnoreCookiePage = [/^\/api\/user\/logOut$/i];

// 验证当前api是否有权限
function validApiPath(url: string, apis: string[]) {
  const originalUrl = url;
  const regApis = apis
    .filter((v: string) => v.includes(':'))
    .map(v => {
      const str = v.replace(/:[^\s/]+/g, '(?:[^/]+?)');
      const reg = new RegExp(str);
      return reg;
    });
  // 新增判断
  if (
    apis.includes(originalUrl) ||
    regApis.some((v: RegExp) => v.test(originalUrl))
  ) {
    return true;
  }
  return false;
}

// 获取开放api 在运营平台刷新
export async function getGlobalApi(ctx: Context, next: Next) {
  const key = `${redisConfig.platform_key}_GLOBAL_API`;
  let apis: any = await getRedisByKey(key);
  if (Array.isArray(apis)) {
    apis = apis.filter(v => v.path && v.path.trim());
  } else {
    apis = await commonModel.getGlobalInterface();
    apis = apis.filter((v: any) => v.path && v.path.trim());
    // 保存到库中
    setRedisByKey(key, apis);
  }
  const login_global_api = apis.filter(
    (v: any) => v.type === InterfaceType['Login-Global']
  );
  const global_api = apis.filter(
    (v: any) => v.type === InterfaceType['NO-Login']
  );
  ctx.state.login_global_api = login_global_api.map((v: any) => v.path.trim());
  ctx.state.global_api = global_api.map((v: any) => v.path.trim());
  const arr = ctx.originalUrl.split('?').filter(e => e.trim());
  // 请求的路径
  ctx.state.api_url = arr.length > 0 ? arr[0] : ctx.originalUrl;
  return next();
}

// 验证登录状态
export async function validLogin(ctx: Context, next: Next) {
  const { global_api, api_url } = ctx.state;
  const flag = validApiPath(api_url, global_api || []);
  ctx.state.isNoLoginApi = flag;
  if (flag) {
    return next();
  }
  // session拦截
  if (!ctx.session || !ctx.session!.session_id) {
    ctx.session = null;
    const result = getCtxResult('FAIL_USER_NO_LOGIN');
    ctx.body = result;
  } else {
    const { tnt_id, user_id, session_id } = ctx.session!;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    const data: any = await getRedisByKey(key);
    if (!data) {
      // 删除对应session
      ctx.session = null;
      const result = getCtxResult('FAIL_USER_NO_LOGIN');
      ctx.body = result;
    } else {
      // 重复登录
      if (session_id !== data.session_id) {
        ctx.session = null;
        const result = getCtxResult('FAIL_USER_LOGIN_REPEAT');
        ctx.body = result;
        return;
      }
      // sessionConfig 同步session过期时间
      await setRedisByKey(key, data, sessionConfig.maxAge / 1000);
      // 保存当前用户的信息
      ctx.state.curUser = data;
      return next();
    }
  }
}

// 添加上默认角色的权限
export async function resolveCtxRole(ctx: Context, next: Next) {
  if (ctx.state.isNoLoginApi) {
    return next();
  }
  const {
    user_id = 0,
    tnt_id = 0,
    role_id: role,
    role_list = []
  } = ctx.state.curUser || {};
  let role_id = role || 0;
  // 兼容以登录用户
  if (!role_id && role_list?.length) {
    role_id = role_list[0]?.role_id || 0;
  }
  ctx.state.curUser.api_list = [];
  // 如果是登录用户 需要加上当前登录用户的角色权限
  if (user_id && role_id && tnt_id) {
    // 不加tnt_id 因为存在系统提供的角色
    const key = `${redisConfig.platform_key}_ROLE_PMS_${role_id}`;
    let data: any = await getRedisByKey(key);
    // 需要获取当前角色的权限
    if (!data) {
      data = await roleModel.getRolePmsById(role_id);
      // 更新redis
      setRedisByKey(key, data);
    }
    // 添加角色权限
    ctx.state.curUser = { ...ctx.state.curUser, ...data };
  }
  return next();
}

// 验证登录接口权限
export function validApiPermission(ctx: Context, next: Next) {
  const { isNoLoginApi, login_global_api = [], api_url, curUser } = ctx.state;
  if (isNoLoginApi) {
    return next();
  }
  const { api_list = [] } = curUser;
  const apis = [...api_list, ...login_global_api];
  const flag = validApiPath(api_url, apis);
  if (flag) {
    return next();
  }
  const { cur_log_id } = ctx.state;
  const { user_id = 0, account_name = '', tnt_id = 0 } = curUser || {};
  const ip = getUserIp(ctx);
  const result = getCtxResult('FAIL_USER_NO_AUTH', '');
  sendLarkAlertCard({
    title: 'Saas Std Platform Permission Limit',
    data: {
      'Log ID': cur_log_id,
      IP: ip,
      'API URL': ctx.request.originalUrl,
      'Page URL': ctx.request.header.referer,
      Operator: `${account_name}(${user_id})`,
      'Tenant ID': tnt_id || 0,
      'Session ID': ctx.session!.session_id || 0,
      'User Agent': ctx.request.headers['user-agent'],
      'x-time-zone': ctx.request.headers['x-time-zone'],
      Params: ctx.request.body || {},
      Error: result
    }
  });
  // 没权限访问
  ctx.body = result;
}

// 刷新过期时间
export function refreshSessionExpired(ctx: Context, next: Next) {
  // 不是退出接口刷新登出时间
  if (!IgnoreCookiePage.some(v => v.test(ctx.originalUrl))) {
    const cookie = ctx.cookies.get(sessionConfig.key);
    // 更新cookie时间
    ctx.cookies.set(sessionConfig.key, cookie, {
      maxAge: sessionConfig.maxAge,
      signed: sessionConfig.signed
    });
  }
  return next();
}

export async function resolveCsDomain(ctx: Context, next: Next) {
  const Domain = domainConfig;
  const env = process.env.NODE_ENV || '';

  if (!['prod', 'test', 'hotfix'].includes(env)) {
    ctx.state.cs_domain = `allowed.${Domain.console}`;
  } else {
    ctx.state.cs_domain = ctx.request.host || '';
  }
  getLogger('app').info(`resolveCsDomain env=${env} cs_domain=${ctx.state.cs_domain}`);
  return await next();
}

// 特殊接口不走签名验证
const IgnoreSignatureApi = [
  '/api/user/login',
  '/api/common/file/logo',
  '/api/common/file/favicon.ico',
  '/api/billing/downloadAdvertiserBillingList',
  '/api/billing/downloadPublisherBillingList',
  '/api/dashboard/downloadDashboardReport',
  '/api/dashboard/downloadAdvReport',
  '/api/dashboard/downloadPubReport',
  '/api/dashboard/downloadPixalateReport',
  '/api/dashboard/downloadHumanReport',
  '/api/exported-report/download',
  '/api/common/getDict'
];

export function verifySignature(ctx: Context, next: Next) {
  // 特殊接口不校验签名，包含
  const flag = IgnoreSignatureApi.some(v => ctx.originalUrl?.includes(v));
  if (flag) {
    return next();
  }

  const { 'x-timestamp': timestamp, 'x-platform': signature } =
    ctx.request.headers;

  // 校验必要参数
  if (!timestamp || !signature) {
    ctx.body = getCtxResult('SIGNATURE_MISSING', '');
    return;
  }

  // 验证时间戳，防止重放攻击（时间戳与当前时间差不超过60s）
  // const currentTime = Date.now();
  // if (Math.abs(currentTime - +timestamp) > 60 * 1000) {
  //   ctx.body = getCtxResult('FAIL_USER_NO_AUTH', '');
  //   return;
  // }

  // 验证签名
  const sign = generateSignature({
    ...ctx.request.body,
    timestamp: +timestamp
  });

  if (sign !== signature) {
    ctx.body = getCtxResult('SIGNATURE_ERROR', '');
    return;
  }

  return next();
}
