/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-07 15:10:55
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 10:37:40
 * @Description:
 */
import React, { useState, useEffect, useMemo } from 'react';
import SupplyCard from '../components/SupplyCard';
import { getSupplyAuth } from '@/services/api';
import { useLocation, useModel } from '@umijs/max';
import { parse } from 'query-string';
import AppList from '../components/AppList';
import AuthorizationTable from '../components/AuthorizationTable';
import { fetchData } from '@/utils';
import { DetailBreadOptions } from '@/constants/supply';
import PageContainer from '@/components/RightPageContainer';

const SupplyDetail: React.FC = () => {
  const location: any = useLocation();
  const [supplyDemandList, setSupplyDemandList] = useState<SupplyAPI.SellerDemandAuth[]>([]);
  const [demandLoading, setDemandLoading] = useState(false);
  const { dataSource, reload, loading: supplyLoading } = useModel('useSupplyList');
  const { dataSource: demandList, reload: reloadDemand, loading: demandListLoading } = useModel('useDemandList');
  const { state } = location;
  const { id } = parse(location.search);
  const [supplyData, setSupplyData] = useState<SupplyAPI.SupplyListItem | undefined>(undefined);
  const [isMax, setIsMax] = useState(false);
  const { initialState } = useModel('@@initialState');

  useEffect(() => {
    getSupplyDemand();
    if (state?.row) {
      setSupplyData(state.row as SupplyAPI.SupplyListItem);
    } else {
      if (!dataSource || !dataSource.length) {
        reload();
      }
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
  }, []);

  useEffect(() => {
    if (supplyData && ['max-jstag', 'mw-jstag'].includes(supplyData.integration_type_desc.toLowerCase())) {
      setIsMax(true);
    } else {
      setIsMax(false);
    }
  }, [supplyData]);

  useEffect(() => {
    if (dataSource && dataSource.length) {
      const item = dataSource.find((val: SupplyAPI.SupplyListItem) => id && +val.seller_id === +id);
      if (item) {
        setSupplyData(item);
      }
    }
  }, [dataSource]);
  // 获取授权
  const getSupplyDemand = () => {
    const onSuccess = (data: any) => {
      if (data) {
        setSupplyDemandList(data);
      }
    };
    fetchData({ setLoading: setDemandLoading, request: getSupplyAuth, params: { seller_id: id }, onSuccess });
  };

  return (
    <PageContainer options={DetailBreadOptions} isBack handleGoBack={() => history.go(-1)}>
      {useMemo(() => {
        return <SupplyCard supplyData={supplyData} supplyLoading={supplyLoading} />;
      }, [supplyData, supplyLoading])}
      {useMemo(() => {
        return (
          <AuthorizationTable
            loading={supplyLoading || demandLoading || demandListLoading}
            supplyDemandList={supplyDemandList}
            supplyData={supplyData}
            fetchData={getSupplyDemand}
            demandLoading={demandLoading}
            demandList={demandList}
          ></AuthorizationTable>
        );
      }, [supplyLoading, supplyDemandList, supplyData, demandLoading, demandList])}
      {useMemo(() => {
        return (
          <AppList
            demandList={demandList}
            loading={demandListLoading}
            isMax={isMax}
            token={supplyData?.token || ''}
            domain={initialState?.currentUser?.host_prefix || ''}
            pvDomain={initialState?.currentUser?.pv_domain || ''}
            publisherType={supplyData?.integration_type || 0}
          />
        );
      }, [demandList, demandListLoading, isMax, supplyData])}
    </PageContainer>
  );
};

export default SupplyDetail;
