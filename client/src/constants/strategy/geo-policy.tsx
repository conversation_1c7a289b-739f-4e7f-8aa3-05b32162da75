import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/NewStatusTag';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { TabOption } from '@/hooks/useCurrentTabState';
import { ExclamationCircleOutlined, TagOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import type { ColumnProps } from 'antd/lib/table';
import moment from 'moment';
import { StatusOptions } from '../common';

export const GeoDefaultKeyOptions = [
  {
    label: 'Yes',
    value: 1
  },
  {
    label: 'No',
    value: 0
  }
];

export const GeoDefaultKeyMap = {
  Yes: 1,
  No: 0
};

export const GeoPolicyKeyColumns: ColumnProps<StrategyAPI.GeoPolicyKeyListItem>[] = [
  {
    title: 'Policy Key',
    dataIndex: 'policy_key',
    width: 150,
    ellipsis: { showTitle: false },
    fixed: 'left',
    render: (key: string, record) => {
      // is_default 为 1 的时候 添加一个 <TagOutlined />
      const isDefault = record.is_default === GeoDefaultKeyMap.Yes;
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          {isDefault && (
            <Tooltip title={'The default policy key'}>
              <TagOutlined style={{ color: '#0cafc7', cursor: 'pointer', fontSize: 16 }} />
            </Tooltip>
          )}
          <HoverToolTip title={key}>
            <span>{key}</span>
          </HoverToolTip>
        </div>
      );
    }
  },
  {
    title: 'Policy Name',
    dataIndex: 'remark',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt: string) => {
      return txt || '-';
    }
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100,
    render: _ => <StatusTag value={_} />
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['op_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160,
    render: (txt: number) => {
      return txt ? moment(txt * 1000).format('YYYY-MM-DD HH:mm') : '-';
    }
  }
];

export const GeoPolicyKeyRelationColumns: ColumnProps<StrategyAPI.GeoPolicyKeyRelationListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt: string, record) => {
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HoverToolTip title={txt}>
            <span>
              {txt}({record.seller_id})
            </span>
          </HoverToolTip>
        </div>
      );
    }
  },
  {
    title: 'Policy Key',
    dataIndex: 'policy_key',
    width: 150,
    ellipsis: { showTitle: false },
    fixed: 'left',
    render: (key: string, record) => {
      // is_default 为 1 的时候 添加一个 <TagOutlined />
      const isDefault = record.is_default === GeoDefaultKeyMap.Yes;
      return (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          {isDefault && (
            <Tooltip title={'The default policy key'}>
              <TagOutlined style={{ color: '#0cafc7', cursor: 'pointer', fontSize: 16 }} />
            </Tooltip>
          )}
          <HoverToolTip title={key}>
            <StatusTag value={record.policy_key_status} statusDescMap={{ 1: key, 2: key }} />
          </HoverToolTip>
        </div>
      );
    }
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100,
    render: (_, row) => <StatusTag value={_} />
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['op_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160,
    render: (txt: number) => {
      return txt ? moment(txt * 1000).format('YYYY-MM-DD HH:mm') : '-';
    }
  }
];

export const GeoPolicyKeySearchOption: TopBarSearchItem[] = [
  {
    name: 'Policy Key',
    type: 'bundle',
    key: 'policy_key',
    value: [],
    options: [],
    placeholder: 'Please Select Policy Key'
  },
  {
    name: 'Policy Name',
    type: 'bundle',
    key: 'remark',
    value: [],
    options: [],
    placeholder: 'Please Select Policy Name'
  },
  {
    name: 'Status',
    type: 'select',
    mode: 'multiple',
    key: 'status',
    value: '',
    options: StatusOptions
  }
];

export const GeoPolicyKeyRelationSearchOption: TopBarSearchItem[] = [
  {
    name: 'Policy Key',
    type: 'select',
    mode: 'multiple',
    key: 'policy_key_id',
    value: [],
    options: [],
    placeholder: 'Please Select Policy Key'
  },
  {
    name: 'Publisher',
    type: 'select',
    mode: 'multiple',
    key: 'seller_id',
    value: [],
    options: [],
    placeholder: 'Please Select Publisher'
  },
  {
    name: 'Status',
    type: 'select',
    mode: 'multiple',
    key: 'status',
    value: '',
    options: StatusOptions
  }
];

export const GeoPolicyBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'GeoEdge Policy'
  }
];

export const GeoPolicyTab = {
  key: 'key',
  auth: 'auth'
} as const;

export type GeoPolicyTabType = typeof GeoPolicyTab[keyof typeof GeoPolicyTab];

export const GeoPolicyTabOptions: TabOption<GeoPolicyTabType>[] = [
  { label: 'Policy Authorization', value: 'auth' },
  { label: 'Policy Key', value: 'key' }
];
