.floor-container {
  :global {
    .ant-table-tbody {
      tr.expanded-row {
        td {
          position: sticky;
          top: 0;
          background: #fff;
          z-index: 2;
        }
        .ant-table-cell.ant-table-cell-fix-left.ant-table-cell-fix-left-last.ant-table-cell-ellipsis.ant-table-cell-with-append,
        .ant-table-cell.ant-table-cell-fix-right.ant-table-cell-fix-right-first {
          position: sticky !important;
          top: 0 !important;
          left: 0 !important;
          z-index: 20;
        }
        .ant-table-cell.ant-table-cell-fix-right.ant-table-cell-fix-right-first {
          right: 0;
        }
      }
    }
  }
}

.content-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .content-item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }
  .icon-container {
    display: flex;
    flex-direction: column;
    .copy-icon {
      margin: 0;
      :global {
        .ant-typography-copy {
          margin: 0;
        }
      }
    }
  }
}
