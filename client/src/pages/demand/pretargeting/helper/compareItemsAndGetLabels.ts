import { LevelConfigMap } from './level-mapping';

/**
 * 比较两个items数组的差异，返回差异项的label信息
 * @param oldItems - 原始items数组
 * @param newItems - 新的items数组
 * @returns 包含差异项label的数组
 */
export const compareItemsAndGetLabels = (
  oldItems: { level: number; content: string }[],
  newItems: { level: number; content: string }[]
): string[] => {
  // 创建level到content的映射
  const oldMap = new Map(oldItems.map(item => [item.level, item.content]));
  const newMap = new Map(newItems.map(item => [item.level, item.content]));

  const differentLabels = new Set<string>();

  // 检查新items中的差异
  for (const [level, content] of newMap) {
    if (oldMap.get(level) !== content && LevelConfigMap[level - 1]) {
      differentLabels.add(LevelConfigMap[level - 1].label);
    }
  }

  // 检查被删除的items（只存在于oldMap中）
  for (const [level] of oldMap) {
    if (!newMap.has(level) && LevelConfigMap[level - 1]) {
      differentLabels.add(LevelConfigMap[level - 1].label);
    }
  }

  return Array.from(differentLabels).sort();
};
