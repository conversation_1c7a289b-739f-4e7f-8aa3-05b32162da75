import { openServiceCtrl } from '@/controllers';
import { validateCsDomain } from '@/middleware/backend-api';
import { checkOpenServiceWhiteIp } from '@/middleware/open-service';
import Router from 'koa-router';

const router = new Router({ prefix: '/rix' });

const routers = router
  .post(
    '/user/login',
    checkOpenServiceWhiteIp,
    validateCsDomain,
    openServiceCtrl.login
  )
  .post(
    '/supply/get',
    checkOpenServiceWhiteIp,
    validateCsDomain,
    openServiceCtrl.getSupply
  );

export default routers;
