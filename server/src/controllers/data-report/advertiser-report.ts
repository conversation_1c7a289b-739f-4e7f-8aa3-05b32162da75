/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-03 21:33:34
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-21 18:44:37
 * @Description:
 */

import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { dashboardModel } from '@/models';
import {
  AdvDashbaordScheme,
  DownloadPubAndAdvDashboardScheme
} from '@/schema/report/dashboard';
import { dashboardService } from '@/services';
import { DashboardAPI } from '@/types/dashboard';
import { getUserIp } from '@/utils';
import { sendLarkAlertCard } from '@/utils/alert';
import { transformLimitStr, updateBQConfigAdapter } from '@/utils/report/slim';
import { getCtxBackResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import {
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  includeToday,
  transformOneRowSQL
} from '@rixfe/rix-tools';
import eventStream from 'event-stream';
import { Context } from 'koa';
// @ts-ignore
import JSONStream from 'JSONStream';
import { PassThrough } from 'stream';
import { getUserRole } from '@/utils/report/getBQLabel';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class AdvReportCtrl implements DashboardAPI.DashboardCtrlInterface {
  @validateBody(AdvDashbaordScheme)
  async getDashboardList(ctx: Context) {
    let result = { ...RESULT };
    const { curUser = {} } = ctx.state;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const buyer_id = curUser.buyer_id || [];
    formData.buyer_id = Array.isArray(buyer_id) ? buyer_id : [buyer_id];
    if (!formData.buyer_id.length) {
      result = getCtxBackResult('SUCCESS', { total: 0, data: [] });
      ctx.body = result;
      return;
    }

    const userType = ctx.state.curUser?.type;
    const list = await dashboardService.getNewDashboardList(
      formData,
      { tag: api_url, tenantId: formData.tnt_id, userRole: getUserRole(userType) },
      { reportType: 'advertiser' }
    );
    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(DownloadPubAndAdvDashboardScheme)
  async downloadAllReport(ctx: Context) {
    let result = { ...RESULT };
    const {
      curUser = {},
      api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0]
    } = ctx.state;

    const buyer_id = curUser.buyer_id || [];

    const formData = {
      ...ctx.request.body,
      tnt_id: ctx.session!.tnt_id,
      buyer_id: buyer_id.length ? buyer_id : [buyer_id]
    };
    // formData.tnt_id = 1052;
    // formData.buyer_id = [32156];

    if (!formData.buyer_id.length) {
      ctx.body = [];
      return;
    }

    const {
      metrics,
      split_time,
      start_date,
      end_date,
      cur_time_zone,
      tnt_id,
      hour
    } = formData;

    const labels = {
      tag: api_url,
      tenantId: tnt_id
    };

    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments, info } = await adaptGenerateBigQuerySQL(
      formData,
      // @ts-ignore
      labels
    );

    const sql = concatSQLFragments({
      ...fragments,
      // 最多查询10000条
      limit: transformLimitStr(tnt_id, '', true)
    });

    // 判断表类型
    const isDailyTable = info.isDaily;
    const isDemandTable = info.isDemand;

    const [data, today_hours] = await Promise.all([
      // 查询列表数据
      dashboardModel.newDownloadAllReport(sql, labels),
      // 对于非daily表单独查询今日小时数
      !isDailyTable
        ? dashboardModel.getNewHoursToday(
            tnt_id,
            fragments.from,
            cur_time_zone,
            labels
          )
        : 0
    ]);

    if (data) {
      ctx.body = data
        .on('error', async (err: any) => {
          getLogger('error').error(`downloadPubDashboardList error: [${err?.message}]`);
          data.end();
          ctx.body = result;
          const { cur_log_id, curUser } = ctx.state;
          const { account_name = '', tnt_id = 0, user_id = 0 } = curUser || {};
          const ip = getUserIp(ctx);
          sendLarkAlertCard({
            title: 'Saas Std Platform Download Report Error',
            data: {
              'Log ID': cur_log_id,
              IP: ip,
              'API URL': ctx.request.originalUrl,
              'Page URL': ctx.request.header.referer,
              Operator: `${account_name}(${user_id})`,
              'Tenant ID': tnt_id || 0,
              'Session ID': ctx.session!.session_id || 0,
              'User Agent': ctx.request.headers['user-agent'],
              'x-time-zone': ctx.request.headers['x-time-zone'],
              Params: ctx.request.body || {},
              Error: err,
              Result: result
            }
          });
        })
        .on('end', () => {
          data.end();
        })
        .pipe(
          eventStream.mapSync((data: any, index: number) => {
            const item = transformOneRowSQL(data, {
              metrics: metrics || [],
              context: {
                start_date,
                end_date,
                today_hours,
                isDemand: isDemandTable,
                hour,
                isToday: includeToday(start_date, end_date),
                split_time,
                timezone: cur_time_zone,
                isDemandReport: true
              }
            });
            return item;
          })
        )
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}

export default new AdvReportCtrl();
