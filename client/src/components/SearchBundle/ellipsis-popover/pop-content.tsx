/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-12 18:32:30
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:25:34
 * @Description:
 */

import React, { useState, useEffect, useRef } from 'react';
import styles from './pop.less';
import { Button, Input, Tooltip } from 'antd';
import LongContentMore from '@/components/LongContentMore';
import { PlusCircleOutlined } from '@ant-design/icons';
import PopItem from './pop-item';
import RixEngineFont from '@/components/RixEngineFont';

type PopContentProps = {
  dataSource: string[];
  contentWidth?:number; // popover宽度
  contentHeight?: number; // content的最大高度
  open: boolean;
  defaultShowNum?: number; // 默认显示多少条，数据量大的时候
  setOpen: (flag: boolean) => void;
  onValueChange?: (val: string[]) => void;
  limit?: number;
  splitBy?: RegExp;
  placeholder?: string;
}

const PopContent: React.FC<PopContentProps> =
({ dataSource: data, contentWidth = 400, contentHeight = 270, open,
  defaultShowNum = 100, setOpen, onValueChange, limit,
  splitBy = /,|，|\s+/g, placeholder = 'Input to filter/add,separated by comma/space'
}) => {
  const divRef = useRef<HTMLDivElement | null>(null);
  const [dataSource, setDataSource] = useState(data);
  const [filterList, setFilterList] = useState<string[]>([]);
  // 渲染的数据 默认渲染300条
  const [renderData, setRenderData] = useState<string[]>([]);
  // 过滤的值 只支持单选
  const [filterValue, setFilterValue] = useState('');
  // 当前编辑的数据下标
  const [editValue, setEditValue] = useState('');

  useEffect(() => {
    setFilterValue('');
    if (open) {
      window.addEventListener('wheel', handleScroll);
    } else {
      handleSave();
      window.removeEventListener('wheel', handleScroll);
    }
    return () => {
      window.removeEventListener('wheel', handleScroll);
    };
  }, [open]);

  useEffect(() => {
    setDataSource(data || []);
  }, [data]);

  useEffect(() => {
    const val = filterValue.toUpperCase();
    let list = [...dataSource].reverse(); // 目前是需要翻转数据的
    if (val) {
      list = list.filter((el) => {
        return `${el}`.toUpperCase().includes(val);
      });
    }
    setFilterList(list);
  }, [dataSource, filterValue]);

  const handleScroll = (e: MouseEvent) => {
    const { x, y } = e;
    // 判断鼠标位置是否在可视区域内
    if (divRef.current && open) {
      const rect = divRef.current.getBoundingClientRect();
      const { width, height, x: pointX, y: pointY } = rect;
      const flag = x >= pointX && x <= pointX + width && y >= pointY && y <= pointY + height;
      if (!flag) {
        setOpen(false);
      }
    } else {
      setOpen(false);
    }
  };

  const handleShowDataChange = (val: string[] | number[]) => {
    setRenderData(val as string[]);
  };

  const handleAdd = () => {
    const tmp = filterValue.split(splitBy);
    const arr = [...new Set([...dataSource, ...tmp])].filter(v => v && v.trim());
    setFilterValue('');
    if (typeof limit === 'undefined' || arr.length <= limit) {
      setDataSource(arr);
    } else {
      const res = arr.slice(0, limit);
      setDataSource(res);
    }
  };

  const handleSave = () => {
    onValueChange && onValueChange(dataSource);
  };

  const onDeleteItem = (val: string) => {
    const arr = dataSource.filter(v => v !== val);
    setDataSource(arr);
  };

  const handleItemChange = (oldVal: string, newVal: string) => {
    const arr = dataSource.map(v => v === oldVal ? newVal : v);
    setDataSource(arr);
  };

  return (
    <div className={styles.list} style={{ width: contentWidth }} ref={divRef} onClick={(e) => e.stopPropagation()}>
      <div className={styles.header}>
        <div className={styles['search-top']}>
          <Input
            placeholder={placeholder}
            onChange={(e) => setFilterValue(e.target.value)}
            value={filterValue}
          />
          <Tooltip title="Add">
            <Button
              type="primary"
              icon={<PlusCircleOutlined />}
              style={{ width: 36, marginLeft: 8 }}
              disabled={!filterValue}
              onClick={() => handleAdd()}
            />
          </Tooltip>
        </div>
        <div className={styles['header-total']}>
          <span>Total: {filterList.length}</span>
        </div>
      </div>
      {/* 定高度 */}
      <LongContentMore
        contentMaxHeight={contentHeight}
        defaultShowNum={defaultShowNum}
        open={open}
        onShowDataChange={handleShowDataChange}
        dataSource={filterList}
        border={false}
        className={styles['pop-long-container']}
      >
        {
          renderData.length ? (
            renderData.map((el, idx) => {
              return (
                <React.Fragment key={`pop-${el}-${idx}`}>
                  <PopItem
                    value={el}
                    contentWidth={contentWidth}
                    onDelete={onDeleteItem}
                    onChange={handleItemChange}
                    onEditChange={(val) => setEditValue(val)}
                    editValue={editValue}
                    open={open}
                    splitBy={splitBy}
                  />
                </React.Fragment>
              );
            })
          ) : (
            <div className={styles['content-no-data']}>
              <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
              <span>No Data</span>
            </div>
          )
        }
      </LongContentMore>
    </div>
  );
};

export default PopContent;
