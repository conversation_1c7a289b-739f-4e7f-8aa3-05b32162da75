import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { StatusOptions } from '@/constants/common';
import { addGeoPolicyKeyRelation, updateGeoPolicyKeyRelation } from '@/services/api';
import { fetchData } from '@/utils';
import { Form, type FormInstance, message } from 'antd';
import { useImperativeHandle, useMemo, useState } from 'react';
import StatusSelect from './helper/StatusSelect';
import { PublisherOption, updatePublisherOptions } from './helper/tools';
import { type PolicyKeyOption } from './helper/useSearchOptions';

export type KeyRelationDrawerToolRef = {
  openDrawer: (params?: StrategyAPI.GeoPolicyKeyRelationListItem) => void;
  closeDrawer: () => void;
  form: FormInstance;
};

type AddGeoPolicyKeyRelationDrawerProps = {
  drawerToolRef?: React.RefObject<KeyRelationDrawerToolRef>;
  // 接口成功的回调
  onSuccess?: () => void;
  supplyOptions?: PublisherOption[];
  policyKeyOptions?: PolicyKeyOption[];
  relationDataSource: StrategyAPI.GeoPolicyKeyRelationListItem[];
};

const DefaultFormData = {
  policy_key: ''
};

const AddGeoPolicyKeyRelationDrawer = ({
  drawerToolRef,
  onSuccess,
  supplyOptions,
  policyKeyOptions,
  relationDataSource
}: AddGeoPolicyKeyRelationDrawerProps) => {
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [publisherOptions, setPublisherOptions] = useState<PublisherOption[]>([]);

  useImperativeHandle(
    drawerToolRef,
    () => ({
      openDrawer: (row?: StrategyAPI.GeoPolicyKeyRelationListItem) => {
        setOpen(true);
        form.resetFields();
        if (row) {
          form.setFieldsValue({ ...row });
        } else {
          form.setFieldsValue(DefaultFormData);
        }
        setPublisherOptions(updatePublisherOptions(supplyOptions || [], relationDataSource, row));
        setIsEdit(!!row);
      },
      closeDrawer: () => {
        setOpen(false);
      },
      form
    }),
    [drawerToolRef, form, relationDataSource, supplyOptions]
  );

  const handleConfirm = async () => {
    const values = await form.validateFields();

    fetchData({
      setLoading,
      request: isEdit ? updateGeoPolicyKeyRelation : addGeoPolicyKeyRelation,
      params: values,
      onSuccess: () => {
        setOpen(false);
        message.success('Success');
        onSuccess?.();
      }
    });
  };

  // 根据 status 排序 policyKeyOptions
  const sortedPolicyKeyOptions = useMemo(() => {
    return (policyKeyOptions || []).slice().sort((a, b) => {
      // is_default: 1（是默认）排前面，0（不是默认）排后面
      if (a.is_default !== b.is_default) {
        return b.is_default - a.is_default;
      }
      // is_default 相同，再按 status 升序
      return a.status - b.status;
    });
  }, [policyKeyOptions]);

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Policy Authorization` : `Create Policy Authorization`}
      onConfirm={handleConfirm}
      open={open}
      onClose={() => {
        setOpen(false);
        setIsEdit(false);
      }}
      loading={loading}
      maskClosable={true}
    >
      <Form form={form} layout="vertical">
        <Form.Item name="id" hidden>
          <NormalInput />
        </Form.Item>
        <Form.Item label="Publisher" name="seller_id" rules={[{ required: true, message: 'Please select publisher' }]}>
          <NormalSelect options={publisherOptions} placeholder="Please select publisher" showSearch disabled={isEdit} />
        </Form.Item>
        <Form.Item
          label="Policy Key"
          name="policy_key_id"
          rules={[{ required: true, message: 'Please input policy key' }]}
        >
          <StatusSelect options={sortedPolicyKeyOptions} placeholder="Please select policy key" showSearch />
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status">
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddGeoPolicyKeyRelationDrawer;
