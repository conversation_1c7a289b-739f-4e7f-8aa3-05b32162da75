/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:59:11
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Button, Form, Input, Radio, message } from 'antd';
import { StatusOptions } from '@/constants';
import { addPmpDeal, updatePmpDeal } from '@/services/api';
import { fetchData } from '@/utils';
import InputNumber from '@/components/Input/InputNumber';
import Select from '@/components/Select/NormalSelect';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import md5 from 'md5';

type AddDealProps = {
  item?: StrategyAPI.PmpDealListItem;
  visible: boolean;
  isEdit: boolean;
  onClose: () => void;
  reload: () => void;
  demandList?: DemandAPI.DemandListItem[];
  interList?: StrategyAPI.PmpInternalListItem[]
};

const DefaultFormData = {
  pmp_id: undefined,
  buyer_id: undefined,
  auction_type: 0,
  bidfloor: 0,
  status: 1,
  deal_id: ''
};

const AddDealModel: React.FC<AddDealProps> = ({
  item,
  onClose,
  visible,
  isEdit,
  reload,
  demandList,
  interList
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (item && isEdit && visible) {
      const params = {
        ...item
      };
      form.setFieldsValue(params);
    } else {
      form.resetFields();
    }
  }, [item, isEdit, visible]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      handleEditDeal({ ...values, buyer_id: item?.buyer_id, id: item?.id, ori_data: item });
    } else {
      handleAddDeal(values);
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    onClose();
    reload();
  };

  const onCancel = () => {
    form.resetFields();
    onClose();
  };

  const handleAddDeal = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addPmpDeal, params, onSuccess });
  };

  const handleEditDeal = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updatePmpDeal, params });
  };

  const handleGenerateDealID = () => {
    const editForm = form.getFieldsValue();
    if (!editForm.buyer_id || !editForm.pmp_id) {
      message.error({
        content: 'Please select Inventory and Advertiser before generate deal id',
        duration: 3
      });
      return false;
    }
    const now = Date.now();
    const key = `${now}_${editForm.buyer_id}_${editForm.pmp_id}_${editForm.bidfloor || 0}_${editForm.auction_type || 0}}`;
    const uniqueID = md5(key);
    const deal_id = `Deal-${uniqueID}`;
    form.setFieldValue('deal_id', deal_id);
  };

  const handleGenerateName = () => {
    const editForm = form.getFieldsValue();
    if (editForm.pmp_id && editForm.buyer_id) {
      const invItem = interList?.find(v => v.id === editForm.pmp_id);
      const buyerItem = demandList?.find(v => v.buyer_id === editForm.buyer_id);
      const name = `${invItem?.name}(${buyerItem?.buyer_name}-${editForm.bidfloor ? editForm.bidfloor : 0})`;
      form.setFieldValue('name', name);
    } else {
      message.error({
        content: 'Please select Inventory and Advertiser before generate name',
        duration: 3
      });
    }
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit PMP Deal` : `Create PMP Deal`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="pmp_id"
          label="PMP Inventory"
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: 'Please Select PMP Inventory' }]}
        >
          <Select
            placeholder="Please Select PMP Inventory"
            options={interList?.map(v => ({ value: v.id, label: `${v.name}(${v.id})` }))}
            allowClear
            showSearch
            disabled={isEdit}
          />
        </Form.Item>
        <Form.Item
          name='buyer_id'
          label='Advertiser'
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: 'Please Select Demand' }]}
        >
          <Select
            placeholder="Please Select Advertiser"
            showSearch
            disabled={isEdit}
            allowClear
            options={demandList?.map(v => ({ value: v.buyer_id, label: `${v.buyer_name}(${v.buyer_id})` }))}
          />
        </Form.Item>
        <Form.Item
          name="auction_type"
          label="Auction Type"
          rules={[
            { required: true, message: 'Please Select Auction Type' }
          ]}
        >
          <Radio.Group>
            <Radio value={0}>Default</Radio>
            <Radio value={1}>First Price</Radio>
            <Radio value={2}>Second Price</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="bidfloor"
          label="Bidfloor"
          rules={[
            { required: true, message: 'Please Input Bidfloor' }
          ]}
        >
          <InputNumber min={0} style={{ width: 160 }} placeholder="Please Input Bidfloor"/>
        </Form.Item>
        <Form.Item
          name="deal_id"
          label="Deal ID"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            { required: true, message: 'Please Input Deal ID' }
          ]}
        >
          <Input
            placeholder='Please Input Deal ID'
            addonBefore={
              <Button
                onClick={handleGenerateDealID}
                type="text"
                style={{ padding: 0, height: 30 }}
              >
              Generate
              </Button>}
          />
        </Form.Item>
        <Form.Item
          label="Name"
          name="name"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            { required: true, message: 'Please Input Name' }
          ]}
        >
          <Input
            placeholder='Please Input Name'
            addonBefore={
              <Button
                onClick={handleGenerateName}
                type="text"
                style={{ padding: 0, height: 30 }}
              >
              Generate
              </Button>}
          />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddDealModel;
