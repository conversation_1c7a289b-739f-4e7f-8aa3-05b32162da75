/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-09 19:43:14
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 19:39:08
 * @Description:
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2023-08-08 17:11:07
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-31 16:19:27
 * @Description:
 */

import { getLogger } from '@/config/log4js';
import { RESULT, StatusMap } from '@/constants';
import {
  AddIvtConfigSchema,
  UpdateIvtConfigSchema
} from '@/schema/strategy/ivt';
import { ivtService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class IvtController {
  @validateBody(AddIvtConfigSchema)
  async addIvt(ctx: Context) {
    let result = { ...RESULT };
    const { user_id, tnt_id } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = user_id;
    options.tnt_id = tnt_id;
    const isExist = await ivtService.isIvtExist(options);
    if (Array.isArray(isExist) && isExist.length > 0) {
      const acticveIndex = isExist.findIndex(
        item => item.status === StatusMap.Active
      );
      if (acticveIndex !== -1) {
        result = getCtxResult(
          'PIXALATE_EXISTS',
          [],
          `The IVT rule you created already exists.Please edit it.`
        );
      } else {
        const res = await ivtService.updateIvt(
          { ...options, status: StatusMap.Active },
          true
        );
        if (res) {
          getLogger('app').info(
            `addIvtConfig(on duplicate) success formData=[${JSON.stringify(
              options
            )}]`
          );
          result = getCtxResult('UPDATE_PIXALATE');
        }
      }
    } else {
      const data = await ivtService.addIvt(options);
      if (data) {
        getLogger('app').info(
          `addIvtConfig success formData=[${JSON.stringify(options)}]`
        );
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  @validateBody(UpdateIvtConfigSchema)
  async updateIvt(ctx: Context) {
    let result = { ...RESULT };
    const { user_id, tnt_id } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = user_id;
    options.tnt_id = tnt_id;

    const isExist = await ivtService.isIvtExist(options);
    if (
      Array.isArray(isExist) &&
      isExist.length > 0 &&
      options.status === StatusMap.Active
    ) {
      result = getCtxResult(
        'PIXALATE_EXISTS',
        [],
        `The IVT rule already exists.Please edit it`
      );
    } else {
      const data = await ivtService.updateIvt(options);
      if (data) {
        getLogger('app').info(
          `updateIvt success formData=[${JSON.stringify(options)}]`
        );
        result = getCtxResult('SUCCESS');
      }
    }
    ctx.body = result;
  }

  async getIvtList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id = 0 } = ctx.session!;
    const data = await ivtService.getIvtList(tnt_id);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const ivtCtrl = new IvtController();
