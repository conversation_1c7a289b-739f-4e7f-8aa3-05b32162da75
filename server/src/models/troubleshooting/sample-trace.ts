/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-22 11:49:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 18:31:20
 * @Description:
 */
import { getTzTimeSql } from '@/constants/time-zone';
import { queryStackOverflow } from '@/db/bigquery';
import dbUtil from '@/db/mysql';
import { TroubleShootingAPI } from '@/types/troubleshooting';
import moment from 'moment-timezone';

class SampleTraceModel implements TroubleShootingAPI.SampleTraceModel {
  async getSampleTraceTaskList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<TroubleShootingAPI.TaskListItem[]> {
    // 支持查询一个月的
    const startTime = moment()
      .startOf('day')
      .subtract(31, 'day')
      .format('YYYY-MM-DD');
    const sql = `select 
      t.status,
      t.id,
      t.type,
      t.server_region,
      t.seller_id,
      t.bundle,
      t.ad_format,
      t.plm_id,
      t.adomain,
      t.cid,
      t.crid,
      t.buyer_id,
      t.tag_id,
      t.expected_num,
      t.op_id,
      t.country,
      u.status as account_status,
      u.account_name,
      u.type as account_type,
      ${getTzTimeSql('t.create_time', cur_time_zone)} as create_time,
      s.seller_name,
      b.buyer_name
      from trace_task t
      left join seller s on t.seller_id = s.seller_id
      left join buyer b on t.buyer_id = b.buyer_id
      left join user u on t.op_id = u.user_id
      where t.tnt_id = ? and DATE_FORMAT(t.create_time, '%Y-%m-%d') >= ?
      order by create_time desc`;
    return await dbUtil.query(sql, [tnt_id, startTime]);
  }

  async addSampleTraceTask(task: TroubleShootingAPI.TaskListItem) {
    const {
      type,
      server_region = 1,
      seller_id = 0,
      bundle = '',
      // block_status = '',
      ad_format = 0,
      plm_id = 0,
      adomain = '',
      cid = '',
      crid = '',
      // bid_status = '',
      buyer_id = 0,
      tag_id,
      expected_num = 10,
      tnt_id = 0,
      op_id = 0,
      country = ''
    } = task;
    console.log(task);
    const sql = `insert into trace_task (type,server_region,seller_id,bundle,ad_format,plm_id,adomain,cid,crid,buyer_id,tag_id,expected_num,tnt_id,op_id, country) values ?`;

    return !!(await dbUtil.query(sql, [
      [
        [
          type,
          server_region,
          seller_id,
          bundle,
          ad_format,
          plm_id,
          adomain,
          cid,
          crid,
          buyer_id,
          tag_id,
          expected_num,
          tnt_id,
          op_id,
          country
        ]
      ]
    ]));
  }

  async getSampleTraceList(
    params: TroubleShootingAPI.getSampleTraceTaskListParams,
    api_url: string
  ) {
    const start_date = moment()
      .startOf('day')
      .subtract(31, 'day')
      .format('YYYYMMDD');
    const end_date = moment().format('YYYYMMDD');
    const { tnt_id, tag_id } = params;
    // tnt_id = process.env.NODE_ENV === 'development' ? 1000 : tnt_id;
    const sql = `
    select 
      type,
      seller_id,
      region,
      ad_format,
      tag_id,
      bundle,
      placement_id,
      cid,
      crid,
      adomain,
      buyer_id,
      data,
      bid_status,
      block_reason,
    from saas-373106.saas_others.trace_report
      where tnt_id = ${tnt_id} and day >= "${start_date}" and day <= "${end_date}" and tag_id = "${tag_id}"
      `;
    return await queryStackOverflow(sql, { tag: api_url });
  }

  async getUserInfoBySampleTraceTaskId(id: number, tnt_id: number) {
    const sql = `select user_id,type from user where user_id = (select op_id from trace_task where id = ? and tnt_id = ?)`;
    return await dbUtil.query(sql, [id, tnt_id]);
  }

  async updateSampleTraceTask(task: TroubleShootingAPI.TaskListItem) {
    const { id, status, tnt_id } = task;
    const sql = `update trace_task set status = ? where id = ? and tnt_id = ?`;
    return !!(await dbUtil.query(sql, [status, id, tnt_id]));
  }
}

export const sampleTraceModel = new SampleTraceModel();
