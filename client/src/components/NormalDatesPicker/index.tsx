/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-04 17:11:18
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-23 19:12:55
 * @Description:
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-02-21 16:28:14
 * @Description:
 */
import { DatePicker, Form } from 'antd';
import type { DatePickerProps, SelectProps } from 'antd';
import type { Moment } from 'moment-timezone';
import moment from 'moment-timezone';
import { Tag } from 'antd';
import styles from './index.less';
import React, { useState, useEffect } from 'react';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import NormalSelect from '@/components/Select/NormalSelect';

// 自定义tag
const TagRender: React.FC<CustomTagProps> = (props: CustomTagProps) => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <Tag
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={() => onClose(value)}
      className={styles['tag-container']}
    >
      {label}
    </Tag>
  );
};
type NormalDatesPickerProps = SelectProps & {
  onChange?: (value: any) => void;
  pickerOptions?: DatePickerProps;
  timeLimit?: number; // 大于0时，限制选择过去时间范围，小于0时，限制选择未来时间范围
  maxDates?: number;
  name?: string;
  utc?: boolean; // 选择器是否使用utc时间
};

function NormalRangePicker(props: NormalDatesPickerProps): JSX.Element {
  const [open, setOpen] = useState(false);
  const [selectedDates, setSelectedDates] = useState<Moment[]>([]);

  useEffect(() => {
    let values = selectedDates.sort((a, b) => a.valueOf() - b.valueOf()).map(date => date.format('YYYYMMDD'));
    values = [...new Set(values)];
    props.onChange?.(values);
  }, [selectedDates]);
  useEffect(() => {
    const dates = props.defaultValue || [];
    if (!props.value || !props.value.length) {
      setSelectedDates(
        dates?.map((date: string) => {
          return moment(date, 'YYYYMMDD');
        }) || []
      );
    }
  }, [props.defaultValue]);

  const handleDateClick = (e: any, date: any) => {
    const tmp = [...selectedDates];
    const index = tmp.findIndex(selectedDate => selectedDate.isSame(date, 'day'));
    if (index > -1) {
      tmp.splice(index, 1);
    } else {
      if (tmp.length >= (props.maxDates || 2)) {
        tmp.shift();
      }
      tmp.unshift(date);
    }
    setSelectedDates(tmp);
  };

  const dateRender = (current: any) => {
    const isSelected = selectedDates.some(selectedDate => selectedDate.isSame(current, 'day'));
    return (
      <div
        className={`ant-picker-cell-inner ${isSelected ? 'selected-date' : 'no-selected-date'}`}
        onClick={e => handleDateClick(e, current)}
      >
        {props.utc ? current.tz('Etc/UTC').date() : current.date()}
      </div>
    );
  };

  const handleOpenDatesPicker = (e: any) => {
    e.stopPropagation();
    e.preventDefault();
    setOpen(!open);
  };
  const handleBlur = () => {
    setOpen(false);
  };
  const handleClear = () => {
    setSelectedDates([]);
  };

  const handleCloseItem = (val: any) => {
    setSelectedDates(selectedDates.filter(date => date.format('YYYYMMDD') !== val));
  };

  const disabledDate = (currentDate: Moment) => {
    if (props.timeLimit! <= 0) {
      return currentDate < moment().startOf('day') || currentDate > moment().add(-props.timeLimit! - 1 || 0, 'days');
    }
    return currentDate > moment().endOf('day') || currentDate < moment().subtract(props.timeLimit || 0, 'days');
  };

  return (
    <div className={styles['dates-container']}>
      <NormalSelect
        onClick={handleOpenDatesPicker}
        onBlur={handleBlur}
        onClear={handleClear}
        open={false}
        mode={props.mode}
        showArrow={false}
        placeholder="Please Select Dates"
        value={props.value}
        defaultValue={props.defaultValue}
        tagRender={props => <TagRender {...props} onClose={handleCloseItem} />}
        popupClassName={styles['dates-popup']}
        className={styles['select-container']}
      ></NormalSelect>

      <Form.Item className={`${styles['dates-picker']} ${styles['hide-picker']}`}>
        <DatePicker
          popupClassName={styles['dates-picker-container']}
          open={open}
          dateRender={dateRender}
          disabledDate={disabledDate}
          inputReadOnly
          {...props.pickerOptions}
        />
      </Form.Item>
    </div>
  );
}
export default NormalRangePicker;
