/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-08 17:41:11
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-13 15:37:52
 * @Description:
 */
import moment from 'moment-timezone';
export const formatPercent = (num: number) => {
  if (typeof num !== 'number') return '';
  if (num > 100) return 100;
  const formatNum = num.toFixed(2);
  return Number(
    formatNum.endsWith('.00') ? formatNum.slice(0, -3) : formatNum.endsWith('0') ? formatNum.slice(0, -1) : formatNum
  );
};

export const formatDailyCap = (cur_cap: number, cap: number, sys_update_time: string, unit: string) => {
  const lastModified_time = moment(sys_update_time);
  const current_time = moment();
  const isInvalidData = cap === 0 || current_time.diff(lastModified_time, 'minutes') > 30;
  if (unit === 'K') {
    cur_cap = cur_cap / 1000;
    cap = cap / 1000;
  }
  const percent: string | number = isInvalidData
    ? `${cap === 0 ? 'unlimit' : 'No Data'}`
    : formatPercent((cur_cap / cap) * 100);
  const percentStr: string = isInvalidData ? `- / ${cap}` : `${cur_cap} / ${cap}`;
  return { percent, percentStr };
};
