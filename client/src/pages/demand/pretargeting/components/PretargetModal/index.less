.pretarget-edit-container {
  display: flex;
  flex-direction: row;
  .left {
    flex: 1;
    padding-right: 16px;
  }
  .verticle-bar {
    border-left: 1px solid #d9d9d9;
    margin-bottom: 10px;
  }
  .center {
    width: 470px;
    padding: 0 16px;
  }
  .right {
    flex: 1;
    padding-left: 16px;
  }
  .pretarget-form {
    > div {
      margin-bottom: 16px;
    }
    .form-campaign {
      div {
        > div:first-child {
          padding-bottom: 6px;
          label {
            color: #5E6466;
          }
        }
        > div:last-child {
          padding-bottom: 0px;
        }
      }
    }
  }

  :global {
    .ant-form.ant-form-vertical {
      width: 100%;
    }
  }
}

.pretarget-multi-rows {
  // 使用 columns 实现多列布局
  // 每列宽度为 320px，最大 3 列，列间距为 16px，列边框为 1px 的实线，颜色为 #d9d9d9
  columns: 320px 3;
  column-rule: 1px solid #d9d9d9;
  column-gap: 16px;

  :global {
    .ant-form-item {
      margin-bottom: 8px;
      break-inside: avoid;
    }
  }
}

.pretarget-modal {
  > div:nth-child(2) {
    > div:nth-child(3) {
      padding-bottom: 12px;
      max-height: calc(100vh - 170px);
      overflow-y: auto;
    }
    > div:nth-child(4) {
      padding: 16px 20px;
      padding-bottom: 12px;
    }
  }
}

.btn-svg {
  display: inline-block;
  cursor: pointer;
}
