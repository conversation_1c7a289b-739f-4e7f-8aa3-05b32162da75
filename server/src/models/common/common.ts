/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:30:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-18 11:23:28
 * @Description:
 */
import { Readable } from 'stream';
import { StatusMap, UserType } from '@/constants';
import { InterfaceType, StatusType } from '@/constants/permission';
import { getTzTimeSql } from '@/constants/time-zone';
import { downloadFileStream } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { CommonAPI } from '@/types/common';

class CommonModel implements CommonAPI.Common {
  async getBuyerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    const sql = 'select itg_name, id from buyer_integration_type order by id';
    return await dbUtils.query(sql);
  }

  async getSellerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    const sql = 'select itg_name, id from seller_integration_type order by id';
    return await dbUtils.query(sql);
  }

  async getBuyerOrSellerByID(params: {
    user_id: number;
    type: number;
    tnt_id: number;
  }) {
    const { user_id, type, tnt_id } = params;
    let sql = 'select buyer_id from buyer where user_id=? and tnt_id=?';
    // 查下游的
    if (type === UserType.Supply) {
      sql =
        'select seller_id,cus_status from seller where user_id=? and tnt_id=?';
    }
    return await dbUtils.query(sql, [user_id, tnt_id]);
  }

  async getAllTenantHostPrefix() {
    const sql = 'select host_prefix,cs_domain from tenant';
    return await dbUtils.permissionQuery(sql);
  }

  async vaildTesting(tnt_id: number, seller_id?: number, buyer_id?: number) {
    const sqls = [];
    if (seller_id) {
      sqls.push({
        sql: 'select 1 from seller where seller_id in (?) and tnt_id=? and status=? limit 1',
        values: [seller_id || 0, tnt_id, StatusMap.Testing]
      });
    }
    if (buyer_id) {
      sqls.push({
        sql: 'select 1 from buyer where buyer_id in (?) and tnt_id=? and status=? limit 1',
        values: [buyer_id || 0, tnt_id, StatusMap.Testing]
      });
    }

    let res = await Promise.all(sqls.map(s => dbUtils.query(s.sql, s.values)));
    res = res.filter(item => item && item.length);
    console.log('res', res);
    if (res && res.length) {
      return true;
    }
    return false;
  }

  async getNotificationList(
    tnt_id: number,
    user_id: number,
    cur_time_zone: string
  ): Promise<[CommonAPI.NotificationListItem[], CommonAPI.ReadLogListItem[]]> {
    const msgSql = `select id, title, content, rule_id, ${getTzTimeSql(
      'create_time',
      cur_time_zone
    )} as create_time from alert_msg_log where tnt_id=? and DATE(create_time) >= DATE(NOW()) - INTERVAL 15 DAY order by create_time desc`;

    const readSql =
      'select msg_id from alert_read_log where user_id=? and tnt_id=?';

    return await Promise.all([
      dbUtils.query(msgSql, [tnt_id]),
      dbUtils.query(readSql, [user_id, tnt_id])
    ]);
  }

  async updateNotificationStatus(
    params: CommonAPI.UpdateNotificationParams
  ): Promise<any> {
    const { tnt_id, user_id, msg_id } = params;
    const msg_ids = Array.isArray(msg_id) ? msg_id : [msg_id];

    const values = msg_ids.map<[number, number, number]>(id => [
      id,
      user_id,
      tnt_id
    ]);

    const sql = `insert into alert_read_log (msg_id, user_id, tnt_id) values ? on duplicate key update msg_id=msg_id`;

    return !!(await dbUtils.query(sql, [values]));
  }

  async getTenantByExceptId(tnt_id: number) {
    return tnt_id
      ? await dbUtils.query(
          'select tnt_id from tenant where tnt_id != ? and status = ?',
          [tnt_id, StatusType.Active]
        )
      : [];
  }

  async getDemandByIds(ids: number[], tnt_id: number) {
    if (ids.length) {
      const sql =
        'select buyer_id, buyer_name from buyer where buyer_id in (?) and tnt_id=?';
      return await dbUtils.permissionQuery(sql, [ids, tnt_id]);
    }
    return [];
  }

  async getBrandInfo(params: CommonAPI.GetFaviconParams) {
    const { cs_domain } = params;
    return await dbUtils.permissionQuery(
      'select p.brand_name, p.brand_logo_path, p.brand_favicon_path from privatization p join tenant t on t.tnt_id = p.tnt_id where t.cs_domain=?',
      [cs_domain]
    );
  }

  async getFavicon(params: CommonAPI.DownloadParams): Promise<Readable> {
    return await downloadFileStream(params);
  }

  async getLogo(params: CommonAPI.DownloadParams): Promise<Readable> {
    return await downloadFileStream(params);
  }

  // 获取开放接口, 包含登录开放的
  async getGlobalInterface() {
    return await dbUtils.permissionQuery(
      'select path, type from interface where type != ? and status=?',
      [InterfaceType.Normal, StatusType.Active]
    );
  }

  async getInterfaceByMenu(ids: number[]) {
    return await dbUtils.permissionQuery(
      'select i.path as path from interface_rl as il left join interface as i on i.id=il.itf_id where il.rsc_id in (?)',
      [ids]
    );
  }

  async getMenuByRole(role_id: number) {
    return await dbUtils.permissionQuery(
      'select m.id as menu_id, m.access as access, m.type as type from role_permission_rl as rpl left join menu as m on m.id=rpl.rsc_id where rpl.role_id=? and m.status=?',
      [role_id, StatusType.Active]
    );
  }

  // 通过partner账户获取所属的上下游id
  async getSellerParentByUsrID(params: { user_id: number; tnt_id: number }) {
    return await dbUtils.query(
      'select sl.seller_id, sl.cus_status from seller as sl left join seller_parent as sp on sp.sp_id=sl.sp_id where sp.user_id=? and sl.tnt_id=?',
      [params.user_id, params.tnt_id]
    );
  }

  // 通过partner账户获取所属的上下游id
  async getBuyerParentByUsrID(params: { user_id: number; tnt_id: number }) {
    return await dbUtils.query(
      'select byr.buyer_id from buyer as byr left join buyer_parent as bp on bp.dp_id=byr.dp_id where bp.user_id=? and byr.tnt_id=?',
      [params.user_id, params.tnt_id]
    );
  }

  // 根据 hostName 获取 tnt_id
  async getTntIdByHostName(hostName: string): Promise<number> {
    const res = await dbUtils.query(
      'select tnt_id from tenant where cs_domain=?',
      [hostName]
    );
    return res[0]?.tnt_id || 0;
  }
}

export default new CommonModel();
