/**
 * @deprecated 待后续删除
 */

// 注: platform跟mobile os有顺序需求，不能更改
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { demandCampaign } from './demand-campaign';
import { DeviceBrandOptions } from '../global-mapping/device-brand';
import { CountryOptions } from '../global-mapping/country';
import { history } from '@umijs/max';

export const Platform = [
  {
    value: '1',
    label: 'Mobile/Tablet'
  },
  {
    value: '4',
    label: 'Phone'
  },
  {
    value: '7',
    label: 'Set Top Box'
  },
  {
    value: '3',
    label: 'Connected TV'
  },
  {
    value: '5',
    label: 'Tablet'
  },
  {
    value: '6',
    label: 'Connected Device'
  },
  {
    value: '2',
    label: 'Personal Computer'
  }
];

export const MobileOs = [
  {
    label: 'Undefined',
    value: '0'
  },
  {
    label: 'iOS',
    value: '1'
  },
  {
    label: 'Android',
    value: '2'
  },
  {
    label: 'Other',
    value: '3'
  },
  {
    label: 'Linux',
    value: '4'
  },
  {
    label: 'MacOS',
    value: '5'
  },
  {
    label: 'Windows',
    value: '6'
  },
  {
    label: 'tvOS',
    value: '11'
  },
  {
    label: 'Roku',
    value: '12'
  },
  {
    label: 'Amazon',
    value: '13'
  },
  {
    label: 'Microsoft',
    value: '14'
  },
  {
    label: 'Samsung Smart TV',
    value: '15'
  },
  {
    label: 'LG Smart TV',
    value: '16'
  },
  {
    label: 'Vizio',
    value: '18'
  },
  {
    label: 'Sony Playstation',
    value: '17'
  },
  {
    label: 'Tizen',
    value: '50'
  },
  {
    label: 'KaiOS',
    value: '51'
  },
  {
    label: 'Philips Smart TV',
    value: '19'
  }
];

const rightRadioOption = [
  {
    label: 'Allow',
    value: 18
  },
  {
    label: 'Block',
    value: 19
  }
];

export const LeftOptions: DemandAPI.PretargetEditItemProps[] = [
  {
    name: 'Publisher',
    rightRadioOption: rightRadioOption,
    bottomType: 'select',
    key: 'seller',
    bottomOption: [],
    defaultValue: {
      level: 18,
      value: []
    }
  },
  {
    name: 'Country',
    key: 'country',
    rightRadioOption: rightRadioOption.map((item, index) => {
      return {
        label: item.label,
        value: index + 1
      };
    }),
    bottomType: 'select',
    bottomOption: CountryOptions,
    defaultValue: {
      level: 1,
      value: []
    },
    maxTagCount: 1
  },
  {
    name: 'Category',
    key: 'category',
    rightRadioOption: rightRadioOption.map((item, index) => {
      return {
        label: item.label,
        value: index + 6
      };
    }),
    bottomType: 'select',
    bottomOption: Object.keys(demandCampaign.Category).map(item => {
      return {
        value: item,
        label: `${(demandCampaign as any).Category[item]}`
      };
    }),
    defaultValue: {
      level: 6,
      value: []
    }
  },
  {
    name: 'Network',
    key: 'network',
    rightRadioOption: rightRadioOption.map((item, index) => {
      return {
        label: item.label,
        value: index + 16
      };
    }),
    bottomType: 'select',
    bottomOption: Object.keys(demandCampaign.Network).map(item => {
      return {
        value: item,
        label: `${(demandCampaign as any).Network[item]}`
      };
    }),
    defaultValue: {
      level: 16,
      value: []
    }
  },

  {
    name: 'Delivery Time Slot',
    key: 'time_slot',
    rightText: 'Allow',
    bottomType: 'selectAll',
    tooltip: '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported',
    bottomOption: Object.keys(demandCampaign.TimeSlot).map(item => {
      return {
        value: item,
        label: `${(demandCampaign as any).TimeSlot[item]}`
      };
    }),
    defaultValue: {
      level: 20,
      value: []
    }
  }
];
export const CenterOptions: DemandAPI.PretargetEditItemProps[] = [
  {
    name: 'OS',
    rightText: 'Allow',
    bottomType: 'checkbox',
    key: 'mobile_os',
    bottomOption: MobileOs,
    defaultValue: {
      level: 4,
      value: []
    }
  },
  {
    name: 'Device',
    key: 'platform',
    rightText: 'Allow',
    bottomType: 'checkbox',
    bottomOption: Platform,
    defaultValue: {
      level: 3,
      value: []
    }
  },
  {
    name: 'Inventory',
    key: 'mobile_inventory',
    rightText: 'Allow',
    bottomType: 'checkbox',
    bottomOption: Object.keys(demandCampaign.MoblieInventory).map(item => {
      return {
        value: item,
        label: `${(demandCampaign as any).MoblieInventory[item]}`
      };
    }),
    defaultValue: {
      level: 5,
      value: []
    }
  },
  {
    name: 'Ad Format',
    key: 'ad_platform',
    rightText: 'Allow',
    bottomType: 'select',
    bottomOption: Object.keys(demandCampaign.AdPlatform).map(item => {
      return {
        value: item,
        label: `${(demandCampaign as any).AdPlatform[item]}`
      };
    }),
    defaultValue: {
      level: 8,
      value: []
    }
  },
  {
    name: 'Ad Size',
    key: 'ad_size',
    rightRadioOption: rightRadioOption.map((item, index) => {
      return {
        label: item.label,
        value: index + 12
      };
    }),
    bottomType: 'select',
    bottomOption: [],
    defaultValue: {
      level: 12,
      value: []
    }
  }
];
// 弹窗顺序
export const RightOptions: DemandAPI.PretargetEditItemProps[] = [
  // {
  //   name: 'Site',
  //   rightText: 'Block',
  //   key: 'site',
  //   defaultValue: {
  //     level: 9,
  //     value: []
  //   },
  //   bottomType: 'edit'
  // },
  {
    name: 'Device Brand',
    key: 'brand',
    rightRadioOption: rightRadioOption.map((item, index) => { 
      return {
        label: item.label,
        value: index + 21
      };
    }),
    bottomType: 'select',
    bottomOption: DeviceBrandOptions,
    defaultValue: {
      level: 21,
      value: []
    }
  },
  {
    name: 'Bundle(Domain)',
    tooltip: 'Bundle:com.tencent.xin\rDomain:rixengine.com',
    key: 'mobile_app',
    rightRadioOption: rightRadioOption.map((item, index) => {
      return {
        label: item.label,
        value: index + 10
      };
    }),
    bottomType: 'edit',
    bottomOption: [],
    defaultValue: {
      level: 10,
      value: []
    }
  },
  {
    name: 'Maximum Floor Price',
    key: 'max_price',
    bottomType: 'number',
    defaultValue: {
      level: 14,
      value: ''
    }
  },
  // TODO 需求要点梳理
  // 1. 新增Minimum Floor Price 输入框 ✅
  // 2. 数据校验 最大值必须大于等于最小值，否者提示修改 ✅
  // 3. 验证新增的 min_price 后端入库流程 ✅
  // 4. pretargeting item 响应式优化
  {
    name: 'Minimum Floor Price',
    key: 'min_price',
    bottomType: 'number',
    defaultValue: {
      level: 23,
      value: ''
    }
  },
  {
    name: 'Server Region',
    key: 'server_region',
    bottomType: 'radio',
    bottomOption: [
      { label: 'DEFAULT', value: 'default' },
      { label: 'APAC', value: 'apac' },
      { label: 'USE', value: 'use' },
      { label: 'EUW', value: 'euw' }
    ],
    defaultValue: {
      level: 15,
      value: 'default'
    }
  }
];

export const LeftValue: DemandAPI.LeftType[] = [
  {
    level: 18,
    value: [],
    label: 'Publisher',
    key: 'seller'
  },
  {
    level: 1,
    value: [],
    label: 'Country',
    key: 'country'
  },
  {
    level: 6,
    value: [],
    label: 'Category',
    key: 'category'
  },
  {
    level: 16,
    value: [],
    label: 'Network',
    key: 'network'
  },
  {
    level: 20,
    value: [],
    label: 'Delivery Time Slot',
    key: 'time_slot'
  }
];
export const CenterValue: DemandAPI.LeftType[] = [
  {
    level: 4,
    value: [],
    label: 'OS',
    key: 'mobile_os'
  },
  {
    level: 3,
    value: [],
    label: 'Device',
    key: 'platform'
  },
  {
    level: 5,
    value: [],
    label: 'Inventory',
    key: 'mobile_inventory'
  },
  {
    level: 8,
    value: [],
    label: 'Ad Format',
    key: 'ad_platform'
  },
  {
    level: 12,
    value: [],
    label: 'Ad Size',
    key: 'ad_size'
  }
];
export const RightValue: DemandAPI.LeftType[] = [
  // {
  //   level: 9,
  //   value: [],
  //   label: 'Site',
  //   key: 'site'
  // },
  {
    level: 21,
    value: [],
    label: 'Device Brand',
    key: 'brand'
  },
  {
    level: 14,
    value: '',
    label: 'Maximum Floor Price',
    key: 'max_price'
  },
  {
    level: 23,
    value: '',
    label: 'Minimum Floor Price',
    key: 'min_price'
  },
  {
    level: 15,
    value: '',
    label: 'Server Region',
    key: 'server_region'
  },
  {
    level: 10,
    value: [],
    label: 'Bundle(Domain)',
    key: 'mobile_app'
  }
];

export const PretargetBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Demand',
    url: '/demand/advertiser',
    icon: 'rix-demand'
  },
  {
    name: 'Advertiser List',
    onClick: () => {
      history.push('/demand/advertiser');
    }
  },
  {
    name: 'Pretargeting'
  }
];
