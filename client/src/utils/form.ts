import { FormInstance, FormProps } from 'antd';

/**
 * 处理 form 表单的错误信息
 * @param form - 表单实例
 * @param options - 配置项
 * @returns 返回一个函数，用于处理表单的错误信息
 */
export function handleFormError(
  form: FormInstance,
  options?: { scrollToField?: boolean; callback?: (errorInfo: any) => void }
): FormProps['onFinishFailed'] {
  const { scrollToField = true, callback } = options || {};

  return (errorInfo) => {
    console.error('form', errorInfo);
    if (!errorInfo.errorFields.length) {
      return;
    }

    // 表单滚动到错误信息处
    if (scrollToField) {
      form.scrollToField(errorInfo.errorFields[0].name, {
        behavior: 'smooth',
        block: 'center'
      });
    }

    callback?.(errorInfo);
  };
}
