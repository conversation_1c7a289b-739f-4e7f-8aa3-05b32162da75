import { ABTestAPI } from '@/types/ab-test';

/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-21 15:38:49
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-22 17:44:51
 * @Description:`
 */
import { abTestModel } from '@/models';
class ABTestService implements ABTestAPI.ABTestService {
  async getABTestList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<ABTestAPI.ABTestListItem[]> {
    return await abTestModel.getABTestList(tnt_id, cur_time_zone);
  }
  async addABTest(params: ABTestAPI.AddABTestParams) {
    return await abTestModel.addABTest(params);
  }
  async updateABTest(params: ABTestAPI.UpdateABTestParams) {
    return await abTestModel.updateABTest(params);
  }
}
export const abTestService = new ABTestService();
