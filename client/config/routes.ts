/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-31 11:46:12
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-28 18:59:50
 * @Description:
 */
export default [
  {
    path: '/',
    redirect: '/welcome'
  },
  {
    path: '/welcome',
    name: 'welcome',
    component: '@/pages/welcome',
    hideInMenu: true
  },
  {
    path: '/user',
    name: 'User',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: '@/pages/login'
      }
    ]
  },
  {
    path: '/help',
    name: 'Help Center',
    icon: 'rix-help',
    hideInMenu: true,
    layout: false,
    routes: [
      {
        path: '/help/get-started',
        name: 'Get Started',
        component: '@/pages/help/get-started',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/androidsdk',
        name: 'SDK Integration',
        component: '@/pages/help/androidsdk',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/iossdk',
        name: 'SDK Integration',
        component: '@/pages/help/iossdk',
        hideInMenu: true,
        menuRender: false
      },
      // {
      //   path: '/help/maxjs-tag',
      //   name: 'MaxJsTag Integration',
      //   component: '@/pages/help/maxjs-tag',
      //   hideInMenu: true,
      //   menuRender: false
      // },
      {
        path: '/help/mw-jstag',
        name: 'MWJsTag Integration',
        component: '@/pages/help/mw-jstag',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/c2s',
        name: 'C2S Integration',
        component: '@/pages/help/c2s',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/sspapi',
        name: 'Publisher Report API',
        component: '@/pages/help/sspapi',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/sspapi-v2',
        name: 'Publisher Report API V2',
        component: '@/pages/help/sspapi-v2',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/demandapi',
        name: 'Advertiser Report API',
        component: '@/pages/help/demandapi',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/demandapi-v2',
        name: 'Advertiser Report API V2',
        component: '@/pages/help/demandapi-v2',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/supply-partner-api',
        name: 'Supply Partner Report API',
        component: '@/pages/help/partner-api/supply',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/demand-partner-api',
        name: 'Demand Partner Report API',
        component: '@/pages/help/partner-api/demand',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/openrtb',
        name: 'Advertiser Report API',
        component: '@/pages/help/openrtb',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/vasttag',
        name: 'Advertiser Report API',
        component: '@/pages/help/vasttag',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/faq',
        name: 'FAQ',
        component: '@/pages/help/faq',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/operational-guide',
        name: 'Operational Guide',
        component: '@/pages/help/operational-guide',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/ai-guide',
        name: 'Operational Guide',
        component: '@/pages/help/ai-guide',
        hideInMenu: true,
        menuRender: false
      },
      {
        path: '/help/daily-csv-reporting-api',
        name: 'Daily CSV Reporting API',
        component: '@/pages/help/daily-csv-reporting-api',
        hideInMenu: true,
        menuRender: false
      }
    ]
  },
  {
    path: '/iion/ab7e1dec65824a2c5c517deb9da70f81',
    name: 'Full Report API',
    component: '@/pages/help/iion-fullreport-api',
    hideInMenu: true,
    menuRender: false,
    layout: false
  },
  {
    path: '/403',
    component: '@/pages/403.tsx'
  },
  {
    path: '/500',
    layout: false,
    component: '@/pages/500.tsx'
  },
  {
    path: '*',
    component: '@/pages/404.tsx'
  }
];
