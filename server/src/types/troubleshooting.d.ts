/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-22 11:32:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-13 18:31:36
 * @Description:
 */
import { Context } from 'koa';

declare namespace TroubleShootingAPI {
  type TaskListItem = {
    id: number;
    status: number;
    type: number;
    server_region: string;
    seller_id: number;
    bundle: string;
    block_status: string;
    ad_format: number;
    plm_id: number;
    adomain: string;
    country: string;
    crid: string;
    cid: string;
    bid_status: string;
    buyer_id: number;
    tag_id: string;
    expected_num: number;
    tnt_id: number;
    created_time: string;
    account_status: number;
    account_name: string;
    account_type: number;
    op_id: number;
    data: string;
  };

  type getSampleTraceTaskListParams = {
    tag_id: string;
    tnt_id: number;
    cur_time_zone: string;
  };
  interface SampleTraceCtrlInterface {
    getSampleTraceTaskList(ctx: Context): void;
    addSampleTraceTask(ctx: Context): void;
    getSampleTraceList(ctx: Context): void;
    updateSampleTraceTask(ctx: Context): void;
  }

  interface SampleTraceService {
    getSampleTraceTaskList(
      tnt_id: number,
      cur_time_zone: string,
      curUser: any,
    ): Promise<TaskListItem[]>;
    addSampleTraceTask(task: TaskListItem): Promise<boolean>;
    getSampleTraceList(
      params: getSampleTraceTaskListParams,
      api_url: string
    ): Promise<TaskListItem[]>;
    updateSampleTraceTask(task: TaskListItem, curUser: any): Promise<boolean>;
  }

  interface SampleTraceModel {
    getSampleTraceTaskList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<TaskListItem[]>;
    addSampleTraceTask(task: TaskListItem): Promise<boolean>;
    getSampleTraceList(
      params: getSampleTraceTaskListParams,
      api_url: string
    ): Promise<TaskListItem[]>;
    updateSampleTraceTask(task: TaskListItem): Promise<boolean>;
  }
}
