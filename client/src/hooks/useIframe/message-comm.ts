import { IframeMessage, Message, MessageHandler, MessageHandlerMap, SendMessageOptions, TypedMessageMap } from './type';

/**
 * 消息通信基类
 * 统一处理父页面和iframe的消息发送和接收
 */
export class MessageCommunicator {
  protected debug: boolean;
  protected handlers: MessageHandlerMap = {};
  protected cleanup: (() => void) | null = null;
  protected isInIframe: boolean;
  public sendWhiteList: string[];
  public listenWhiteList: string[];

  /**
   * 创建消息通信器
   * @param sendWhiteList 发送消息的域名白名单，支持完整URL
   * @param listenWhiteList 监听消息的域名白名单，支持完整URL和正则表达式（以 'regex:' 开头）
   * @param debug 是否开启调试模式
   */
  constructor(sendWhiteList: string[], listenWhiteList: string[], debug: boolean = false) {
    this.debug = debug;
    this.isInIframe = this.checkFrame();
    this.sendWhiteList = sendWhiteList;
    this.listenWhiteList = listenWhiteList;
  }

  /**
   * 检查源是否在监听白名单中
   * 支持字符串全匹配和正则表达式匹配 (以 'regex:' 开头)
   * @param origin 源
   * @returns 是否允许
   */
  protected isOriginAllowedForListening(origin: string): boolean {
    return this.listenWhiteList.some(pattern => {
      if (pattern.startsWith('regex:')) {
        const regexPattern = pattern.substring(6);
        try {
          const regex = new RegExp(regexPattern);
          return regex.test(origin);
        } catch (error) {
          if (this.debug) {
            console.error(`[MessageCommunicator] Invalid regex pattern in listenWhiteList: ${regexPattern}`, error);
          }
          return false;
        }
      } else {
        return pattern === origin;
      }
    });
  }

  /**
   * 检查当前页面是否在iframe中
   * @returns 是否在iframe中
   */
  protected checkFrame(): boolean {
    return window !== window.parent;
  }

  /**
   * 注册消息处理函数
   * @param type 消息类型
   * @param handler 处理函数
   * @returns 取消注册的函数
   */
  on<K extends keyof TypedMessageMap>(
    type: K,
    handler: (data: TypedMessageMap[K]) => void | Promise<void>
  ): () => void {
    this.handlers[type] = handler as MessageHandler;

    if (this.debug) {
      console.log(`[${this.isInIframe ? 'iframe' : 'parent'}] Registered message handler: ${type}`);
    }

    return () => {
      delete this.handlers[type];
      if (this.debug) {
        console.log(`[${this.isInIframe ? 'iframe' : 'parent'}] Removed message handler: ${type}`);
      }
    };
  }

  /**
   * 停止监听消息
   */
  stopListening(): void {
    if (this.cleanup) {
      this.cleanup();
      this.cleanup = null;
    }
  }
}

/**
 * iframe消息通信器
 * 用于iframe与父页面通信
 */
export class IframeMessageCommunicator extends MessageCommunicator {
  /**
   * 创建iframe消息通信器
   * @param sendWhiteList 发送消息的域名白名单，支持完整URL
   * @param listenWhiteList 监听消息的域名白名单，支持完整URL和正则表达式（以 'regex:' 开头）
   * @param debug 是否开启调试模式
   */
  constructor(sendWhiteList: string[], listenWhiteList: string[], debug: boolean = false) {
    super(sendWhiteList, listenWhiteList, debug);
  }

  /**
   * 发送消息到父页面
   * @param type 消息类型
   * @param data 消息数据
   * @param options 发送选项
   * @returns 发送是否成功
   */
  send<K extends keyof TypedMessageMap>(type: K, data: TypedMessageMap[K], options?: SendMessageOptions): boolean {
    if (!this.isInIframe) {
      if (this.debug) {
        console.log('[iframe] Not in iframe, message sending skipped');
      }
      return false;
    }

    try {
      const message: Message = { type, data };

      for (const origin of this.sendWhiteList) {
        window.parent.postMessage(message, origin);
      }

      if (this.debug) {
        console.log(`[iframe] Message sent: ${type}`, data);
      }
      return true;
    } catch (error) {
      if (this.debug) {
        console.error('[iframe] Failed to send message:', error);
      }
      return false;
    }
  }

  /**
   * 发送就绪信号通知父窗口iframe已准备好
   */
  sendReadySignal(): boolean {
    return this.send(IframeMessage.ONLOAD, {
      timestamp: new Date().getTime()
    });
  }

  /**
   * 开始监听消息
   */
  listen(): void {
    if (this.cleanup) {
      return;
    }

    const messageHandler = (event: MessageEvent) => {
      // 检查源是否在白名单中
      if (!this.isOriginAllowedForListening(event.origin)) {
        return;
      }

      const message = event.data as Message;
      if (!message || !message.type) {
        return;
      }

      const handler = this.handlers[message.type];
      if (handler) {
        if (this.debug) {
          console.log(`[iframe] Message received: ${message.type}`, message.data);
        }

        try {
          handler(message.data);
        } catch (error) {
          if (this.debug) {
            console.error(`[iframe] Failed to process message ${message.type}:`, error);
          }
        }
      }
    };

    window.addEventListener('message', messageHandler);
    this.cleanup = () => window.removeEventListener('message', messageHandler);
  }
}

/**
 * 父页面消息通信器
 * 用于父页面与iframe通信
 */
export class ParentMessageCommunicator extends MessageCommunicator {
  private iframe: HTMLIFrameElement | null;
  private targetOrigin: string;

  /**
   * 创建父页面消息通信器
   * @param iframe iframe元素
   * @param sendWhiteList 发送消息的域名白名单，支持完整URL
   * @param listenWhiteList 监听消息的域名白名单，支持完整URL和正则表达式（以 'regex:' 开头）
   * @param debug 是否开启调试模式
   */
  constructor(iframe: HTMLIFrameElement | null, sendWhiteList: string[], listenWhiteList: string[], debug: boolean = false) {
    super(sendWhiteList, listenWhiteList, debug);
    this.iframe = iframe;
    this.targetOrigin = this.sendWhiteList[0] || '*';
  }

  /**
   * 设置iframe元素
   * @param iframe iframe元素
   */
  setIframe(iframe: HTMLIFrameElement | null): void {
    this.iframe = iframe;
  }

  /**
   * 发送消息到iframe
   * @param type 消息类型
   * @param data 消息数据
   * @param options 发送选项
   * @returns 发送是否成功
   */
  send<K extends keyof TypedMessageMap>(type: K, data: TypedMessageMap[K], options?: SendMessageOptions): boolean {
    if (!this.iframe || !this.iframe.contentWindow) {
      if (this.debug) {
        console.error('Cannot send message: iframe does not exist or is not loaded');
      }
      return false;
    }

    try {
      const message: Message = { type, data };
      this.iframe.contentWindow.postMessage(message, this.targetOrigin);

      if (this.debug) {
        console.log(`[parent] Message sent: ${type}`, data);
      }
      return true;
    } catch (error) {
      if (this.debug) {
        console.error('Failed to send message:', error);
      }
      return false;
    }
  }

  /**
   * 开始监听消息
   */
  listen(): void {
    if (this.cleanup) {
      return;
    }

    const messageHandler = (event: MessageEvent) => {
      // 检查源是否在白名单中
      if (!this.isOriginAllowedForListening(event.origin)) {
        return;
      }

      const message = event.data as Message;
      if (!message || !message.type) {
        return;
      }

      const handler = this.handlers[message.type];
      if (handler) {
        if (this.debug) {
          console.log(`[parent] Message received: ${message.type}`, message.data);
        }

        try {
          handler(message.data);
        } catch (error) {
          if (this.debug) {
            console.error(`[parent] Failed to process message ${message.type}:`, error);
          }
        }
      }
    };

    window.addEventListener('message', messageHandler);
    this.cleanup = () => window.removeEventListener('message', messageHandler);
  }
}
