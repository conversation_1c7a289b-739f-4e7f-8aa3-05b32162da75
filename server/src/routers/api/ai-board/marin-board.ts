/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-13 18:47:58
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-20 16:46:07
 * @Description:
 */
import Router from 'koa-router';
import { boardController } from '@/controllers';
import { checkChartCache } from '@/middleware/ai-board';
import { parseDateParams } from '@/utils/report';
const router = new Router({ prefix: '/ai-board' });

// !注释都不删除，后续可能会用到
const routers = router
  .post(
    '/getOverview',
    checkChartCache,
    parseDateParams,
    boardController.getOverview
  )
  .post(
    '/getTopCountry',
    checkChartCache,
    parseDateParams,
    boardController.getTopCountry
  )
  .post(
    '/getSupplyDemand',
    checkChartCache,
    parseDateParams,
    boardController.getSupplyDemand
  )
  .post(
    '/getTopAdFormatEcpmAndEcpr',
    checkChartCache,
    parseDateParams,
    boardController.getTopAdFormatEcpmAndEcpr
  )
  .post(
    '/getBudgetAndTraffic',
    checkChartCache,
    boardController.getBudgetAndTraffic
  )
  .post('/trafficRequest', boardController.trafficRequest)
  .post('/getTffReqLog', boardController.getTffReqLog);

export default routers;
