.normal-modal-container {
  border-radius: 6px;
  :global {
    .ant-modal-content {
      border-radius: 6px;
    }
    .ant-modal-header {
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
      border-bottom: none;
      .ant-modal-title {
        color: var(--text-color);
        font-size: 16px;
        font-weight: 700;
      }
    }
    .ant-modal-body {
      padding: 20px;
      padding-top: 0px;
    }
    .ant-modal-confirm {
      .ant-modal-body {
        padding-top: 20px;
      }
    }
    .ant-form-item-label > label {
      color: #5E6466;
    }
    .ant-modal-footer {
      .ant-btn-default {
        span {
          color: #5E6466;
        }
      }
    }
  }
}
.normal-confirm-modal-container {
  :global {
    .ant-modal-content {
      border-radius: 6px;
    }
    .ant-modal-confirm-title {
      color: var(--text-color);
    }
    .ant-modal-confirm-content {
      color: #5E6466;
    }
    .ant-modal-confirm-btns {
      .ant-btn-default {
        color: #5E6466;
      }
    }
    .ant-modal-body {
      margin-bottom: 32px;
    }
  }
}
