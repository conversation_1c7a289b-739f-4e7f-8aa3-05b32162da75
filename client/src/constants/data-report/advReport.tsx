/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-03 20:59:03
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-12 19:46:36
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { formatMoney } from '@/utils';

export const DashboardSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 31
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    mode: 'multiple',
    value: '',
    type: 'select',
    options: AdFormatOptions
  },

  {
    name: 'Platform',
    key: 'platform',
    type: 'select',
    value: '',
    mode: 'multiple',
    options: Object.keys(demandCampaign.MoblieOS).map(item => {
      return {
        value: item,
        label: (demandCampaign.MoblieOS as any)[item]
      };
    })
  },

  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Date', value: 'day' },
      // { label: 'Hour', value: 'day_hour' },
      { label: 'Ad Format', value: 'ad_format' },
      { label: 'Platform', value: 'platform' }
      // { label: 'Country', value: 'country' },
      // { label: 'Bundle', value: 'app_bundle_id' }
    ]
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Net Revenue', value: 'buyer_net_revenue' },
      { label: 'Net eCpm', value: 'buyer_net_ecpm' },
      { label: 'Total Request', value: 'total_request' },
      { label: 'Response', value: 'response' },
      // { label: 'Gross Revenue', value: 'buyer_gross_revenue' },
      { label: 'Impression', value: 'impression' },
      { label: 'Win', value: 'win' },
      // { label: 'Bid Floor', value: 'total_seller_bid_floor' },
      { label: 'Fill Rate', value: 'fill_rate' },
      // { label: 'Render Rate', value: 'impression_rate' },
      { label: 'Win Rate', value: 'win_rate' },
      { label: 'Click Rate', value: 'click_rate' }
      // { label: 'Gross eCpm', value: 'buyer_gross_ecpm' }
    ]
  }
];

export const DashboardBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'Advertiser Reporting'
  }
];

export const DashBoardDefaultMetrics = [
  'total_request',
  'response',
  'impression',
  'buyer_net_revenue',
  'fill_rate',
  'buyer_net_ecpm'
];

export const DashboardDefaultDimension = ['day'];

export const DashboardAllColumns: ColumnProps<DashboardAPI.DashboardListItem>[] = Object.seal([
  {
    title: 'Date',
    key: 'day',
    dataIndex: 'date',
    fixed: 'left',
    width: 160,
    render: (txt, params) => <>{params.date}</>,
    sorter: true
  },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 150,
    key: 'ad_format',
    sorter: true
  },

  {
    title: 'Platform',
    dataIndex: 'platform',
    width: 120,
    key: 'platform'
  },

  {
    title: 'Net Revenue',
    key: 'buyer_net_revenue',
    dataIndex: 'buyer_net_revenue',
    width: 200,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Net eCPM',
    key: 'buyer_net_ecpm',
    dataIndex: 'buyer_net_ecpm',
    sorter: true,
    width: 200
  },

  {
    title: 'Total Request',
    key: 'total_request',
    dataIndex: 'total_request',
    sorter: true,
    width: 220,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  // {
  //   title: 'Total Request',
  //   key: 'total_request',
  //   dataIndex: 'total_request',
  //   width: 150,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>,
  // },
  {
    title: 'Response',
    key: 'response',
    dataIndex: 'response',
    width: 150,

    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Impression',
    key: 'impression',
    dataIndex: 'impression',

    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Win',
    key: 'win',
    dataIndex: 'win',

    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Fill Rate(%)',
    key: 'fill_rate',
    dataIndex: 'fill_rate',
    width: 150
  },
  {
    title: 'Win Rate(%)',
    key: 'win_rate',
    dataIndex: 'win_rate',
    sorter: true,
    width: 150
  },
  // {
  //   title: 'Render Rate(%)',
  //   key: 'impression_rate',
  //   dataIndex: 'impression_rate',
  //   width: 150
  // },
  {
    title: 'Click Rate(%)',
    key: 'click_rate',
    dataIndex: 'click_rate',
    width: 150
  }
]);

export const DashboardDefaultColumnKeys = [
  'day',
  'total_request',
  'response',
  'impression',
  'buyer_net_revenue',
  // 'buyer_gross_revenue',
  'fill_rate',
  // 'impression_rate',
  'buyer_net_ecpm'
  // 'buyer_gross_ecpm'
];

// custom
export const IplayableTNTSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],

    timeLimit: 3
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    mode: 'multiple',
    value: '',
    type: 'select',
    options: AdFormatOptions
  },

  {
    name: 'Platform',
    key: 'platform',
    type: 'select',
    value: '',
    mode: 'multiple',
    options: Object.keys(demandCampaign.MoblieOS).map(item => {
      return {
        value: item,
        label: (demandCampaign.MoblieOS as any)[item]
      };
    })
  },

  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Date', value: 'day' },
      // { label: 'Hour', value: 'day_hour' },
      { label: 'Ad Format', value: 'ad_format' },
      { label: 'Platform', value: 'platform' }
    ]
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Total Request', value: 'total_request' },
      { label: 'Response', value: 'response' },
      { label: 'Gross Revenue', value: 'buyer_gross_revenue' },
      { label: 'Impression', value: 'impression' },
      { label: 'Win', value: 'win' },
      { label: 'Bid Floor', value: 'total_seller_bid_floor' },
      { label: 'Fill Rate', value: 'fill_rate' },
      // { label: 'Render Rate', value: 'impression_rate' },
      { label: 'Win Rate', value: 'win_rate' },
      { label: 'Gross eCpm', value: 'buyer_gross_ecpm' }
    ]
  }
];
export const IplayableTNTAllColumns: ColumnProps<DashboardAPI.DashboardListItem>[] = Object.seal([
  {
    title: 'Date',
    dataIndex: 'day',
    fixed: 'left',
    width: 160,
    render: (txt, params) => <>{params.date}</>,
    sorter: true
  },
  // {
  //   title: 'Hour',
  //   dataIndex: 'day_hour',
  //   fixed: 'left',
  //   width: 160,
  //   render: (txt, params) => <>{params.date}</>,
  //   sorter: (a, b) => {
  //     const a1 = new Date(a.date).getTime();
  //     const b1 = new Date(b.date).getTime();
  //     return a1 - b1;
  //   }
  // },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 150,
    key: 'ad_format',
    sorter: true
  },

  {
    title: 'Platform',
    dataIndex: 'platform',
    width: 120,
    key: 'platform',
    sorter: true
  },

  {
    title: 'Net Revenue',
    key: 'buyer_net_revenue',
    dataIndex: 'buyer_net_revenue',
    width: 200,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Net eCPM',
    key: 'buyer_net_ecpm',
    dataIndex: 'buyer_net_ecpm',
    sorter: true,
    width: 200
  },
  {
    title: 'Gross Revenue',
    key: 'buyer_gross_revenue',
    dataIndex: 'buyer_gross_revenue',
    width: 220,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Total Request',
    key: 'total_request',
    dataIndex: 'total_request',
    sorter: true,
    width: 220,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },

  {
    title: 'Response',
    key: 'response',
    dataIndex: 'response',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Impression',
    key: 'impression',
    dataIndex: 'impression',
    sorter: true,
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Win',
    key: 'win',
    dataIndex: 'win',
    sorter: true,
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  // {
  //   title: 'Bid Floor',
  //   key: 'total_seller_bid_floor',
  //   dataIndex: 'total_seller_bid_floor',
  //   sorter: true,
  //   width: 150,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>
  // },
  // {
  //   title: 'Click',
  //   key: 'click',
  //   dataIndex: 'click',
  //   width: 150,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>,
  // },
  {
    title: 'Fill Rate(%)',
    key: 'fill_rate',
    dataIndex: 'fill_rate',
    sorter: true,
    width: 150
  },
  {
    title: 'Win Rate(%)',
    key: 'win_rate',
    dataIndex: 'win_rate',
    sorter: true,
    width: 150
  },
  {
    title: 'Publisher Gross Revenue',
    key: 'seller_gross_revenue',
    dataIndex: 'seller_gross_revenue',
    width: 220,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  // {
  //   title: 'Click Rate(%)',
  //   key: 'click_rate',
  //   dataIndex: 'ctr',
  //   width: 150,
  // },
  // {
  //   title: 'eCPR',
  //   key: 'ecpr',
  //   dataIndex: 'ecpr',
  //   width: 150,
  // },
  // {
  //   title: 'Profit Rate(%)',
  //   key: 'profit_rate',
  //   dataIndex: 'profit_rate',
  //   width: 150,
  // },
  // {
  //   title: 'Render Rate(%)',
  //   key: 'impression_rate',
  //   dataIndex: 'impression_rate',
  //   sorter: true,
  //   width: 150
  // },
  {
    title: 'Gross eCPM',
    key: 'buyer_gross_ecpm',
    dataIndex: 'buyer_gross_ecpm',
    sorter: true,
    width: 220
  }
]);
