:global {
  .normal-autocomplete {
    color: var(--text-color);

    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        border-radius: 6px;
      }
    }

    &:not(.ant-select-disabled):hover .ant-select-selector {
      border-color: var(--primary-color) !important;
    }

    &.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-color-outline) !important;
    }
  }
}
