/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:21:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-10 10:27:34
 * @Description:
 */

export declare namespace UserAPI {
  type DashBoardUser = {
    user_id: number;
    account_name: string;
    status: number;
    create_time: string;
  };
  type UserListItem = {
    user_id: number;
    account_name: string;
    password: string;
    tnt_id: number;
    tnt_type: number;
    status: number;
    create_time: string;
    update_time: string;
    auth_sign: string[];
    menu_path: string[];
    api_list: string[];
    pwd_expire_time: string;
    access: string[];
    path: string;
    email: string;
    type: number;
    role: number;
    special_user_id?: number;
  };

  type LoginParams = {
    account_name: string;
    password: string;
    cs_domain: string;
  };

  type addUserParams = {
    account_name: string;
    password: string;
    tnt_id: number;
    role_id: number;
    pms_id: number[];
    user_type: number;
    email: string;
  };

  type validAccountNameExistsParams = {
    account_name: string;
    tnt_id: number;
  }

  type EditUserParams = {
    user_id: number;
    status: number;
    api_status: number;
    tnt_id: number;
    new_password?: string;
    role_id: number;
    op_user_id: number;
    email: string;
    ov_type: number;
    roleChange: boolean;
    permission?: string[];
    pmsChange?: boolean;
    user_type: number;
    isSupply?: boolean;
    account_name?: string;
  };

  type DeleteUserParams = {
    user_id: number;
    tnt_id: number;
    account_name: string;
    role_id: number;
  };
  type ResetPasswordParams = {
    user_id: number;
    old_password: string;
    tnt_id: number;
    new_password: string;
  };

  type ConfirmPasswordParams = {
    user_id: number;
    old_password: string;
    tnt_id: number;
  };

  type EditUserAccountParams = {
    tnt_id: number;
    user_id: number;
    send_email?: string[]
    brand: string;
    host_prefix: string;
    pv_domain: string;
    to_self: number;
    user_email?: string;
    type: number;
    account_name: string;
  };

  type ResetUserPwdParams = {
    tnt_id: number;
    user_id: number;
    send_email?: string[]
    brand: string;
    host_prefix: string;
    pv_domain: string;
    to_self: number;
    user_email?: string;
    type: number;
    password: string;
    account_name: string;
  };
}
