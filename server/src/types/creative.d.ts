/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-20 14:20:17
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-13 15:43:01
 * @Description:
 */
import { Context } from 'koa';
declare namespace CreativeAPI {
  type CreativeListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    content: string;
    expire: string;
    remark: string;
    op_name: string;
    op_status: number;
    update_time: string;
    status: number;
  };
  type AddCreativeParams = {
    seller_id: number[];
    buyer_id: number;
    content: string[];
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
  };
  type updateCreativeParams = {
    id: number;
    seller_id: number[];
    buyer_id: number;
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
    content: string[];
    status: number;
  };

  interface Creative {
    getCreativeList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<CreativeListItem[]>;
    addCreative(params: AddCreativeParams): Promise<boolean>;
    updateCreative(params: updateCreativeParams): Promise<boolean>;
  }

  interface CreativeCtrlInterface {
    getCreativeList(ctx: Context): void;
    addCreative(ctx: Context): void;
    updateCreative(ctx: Context): void;
  }
}
