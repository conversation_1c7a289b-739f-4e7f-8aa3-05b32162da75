.endpoint-container {
  box-sizing: border-box;
  margin-right: 16px;
  height: 100%;
  background-color: #fff;
  border-radius: 6px;
  padding: 28px 32px;
  h3 {
    font-weight: 700;
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 0px;
  }
  .copy-container {
    background: #edeff0;
    border-radius: 6px;
    height: 32px;
    display: flex;
    align-items: center;
    // margin:0 16px 24px ;
    flex: 1;
    width: 0;
    > span.info {
      padding: 0 12px 0 16px;
      flex: 1;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: #8d9799;
    }

    :global {
      .ant-typography {
        margin-bottom: 0px;
        height: 32px;
        line-height: 32px;
        background: var(--primary-color);
        border-radius: 0px 6px 6px 0px;
        width: 51px;
        display: flex;
        .ant-typography-copy {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          text-align: center;
          // margin-left: 8px;
          margin: auto;
        }
      }
    }
  }

  .info-con {
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 518px;
    margin: 0 16px 24px;
    > span.title {
      width: 40px;
      color: #5e6466;
      text-align: right;
      margin-right: 20px;
    }
  }
  .global-con {
    display: flex;
    align-items: center;
    .info-con {
      margin: 0;
      margin-left: 16px;
    }
  }
}

.page-container-endpoint {
  :global {
    .ant-spin-nested-loading {
      flex: 1;
      height: 100%;
      .ant-spin-container {
        height: 100%;
      }
    }
  }
}
