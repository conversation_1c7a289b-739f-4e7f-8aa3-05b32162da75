/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 15:56:41
 * @Description:
 */
import { request } from '@umijs/max';

export async function getDemandList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DemandAPI.DemandListItem>>('/api/demand/getDemandList', {
    data: body || {},
    ...(options || {})
  });
}
export async function getDemandListWithTesting(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DemandAPI.DemandListItem>>('/api/demand/getDemandListWithTesting', {
    data: body || {},
    ...(options || {})
  });
}
export async function addDemand(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/addDemand', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateDemand(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/updateDemand', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function setDemandEndpoint(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/setDemandEndpoint', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getDemandEndpoint(body: { buyer_id: string | number }, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DemandAPI.DemandEndpointItem>>('/api/demand/getDemandEndpoint', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updatePretargetCampaign(body: DemandAPI.PretargetUpdateItem, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/updatePretargetCampaign', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getPretargetCampaign(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DemandAPI.PretargetCampaignItem>>('/api/demand/getPretargetCampaign', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updatePretargetStatus(
  body: DemandAPI.PretargetUpdateStatusItem,
  options?: { [key: string]: any }
) {
  return request<API.Result>('/api/demand/updatePretargetStatus', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addPretargetCampaign(body: DemandAPI.PretargetUpdateItem, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/addPretargetCampaign', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getDemandAuth(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/getDemandAuth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function setDemandAuth(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/demand/setDemandAuth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
