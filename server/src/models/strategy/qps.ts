/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:16:25
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:23:13
 * @Description:
 */
import { UserType } from '@/constants';
import { QpsLevel } from '@/constants/strategy';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { QpsAPI } from '@/types/qps';
import { sha256 } from '@/utils';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

const IgnoreQpsLevel = [
  QpsLevel['supply + ad_format'],
  QpsLevel['supply + bundle,'],
  QpsLevel['supply + country']
];

class QpsModel implements QpsAPI.Qps {
  async isQpsExists(params: QpsAPI.IsExistsQps): Promise<any> {
    // 注释代码暂时不删，后期可能会新增all region的逻辑
    const { level, buyer_id = 0, pub_id = 0, tnt_id, region, ots_id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['level = ?', level],
      ['buyer_id = ?', buyer_id],
      ['seller_id = ?', pub_id],
      ['server_region = ?', region],
      ['ots_id = ?', ots_id],
      ['tnt_id = ?', tnt_id]
    ]);

    const countSql = `select count(*) as count from (select id,status,server_region from qps ${clause}) as tmp`;

    const sql = `select id,status from qps ${clause} limit 1`;

    const [count, data] = await Promise.all([
      dbUtil.query(countSql, values),
      dbUtil.query(sql, values)
    ]);
    return {
      data,
      count: count[0].count
    };
  }

  async isBundleExists(params: QpsAPI.IsExistsQps): Promise<any> {
    const {
      level,
      buyer_id = 0,
      pub_id = 0,
      tnt_id,
      region,
      ots_id,
      id
    } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['level = ?', level],
      ['buyer_id = ?', buyer_id],
      ['seller_id = ?', pub_id],
      ['server_region = ?', region],
      ['ots_id = ?', ots_id],
      ['id != ?', id],
      ['tnt_id = ?', tnt_id]
    ]);

    const sql = `select count(ots_id) as count from qps ${clause}`;
    const data = await dbUtil.query(sql, values);
    return {
      count: data[0].count
    };
  }

  async getQpsList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<QpsAPI.QpsListItem[]> {
    const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];
    const Delete = 3;
    const sql = `
      select
        q.server_region as region,
        q.ots_id as ots_id,
        q.seller_id as pub_id,
        q.buyer_id as buyer_id,
        q.id as id,
        q.level as level,
        q.qps as qps,
        q.op_id as op_id,
        q.status as status,
        ${getTzTimeSql('q.update_time', cur_time_zone)} as update_time,
        b.buyer_name as buyer_name,
        s.seller_name as seller_name,
        s.seller_id as seller_id,
        a.status as account_status,
        (case 
          when a.status=? then "Unknown" 
          when a.type in (?) then "System" 
          else a.account_name end) as account_name
      from (select * from qps where tnt_id=? and level not in (?)) as q
      left join buyer as b on b.buyer_id=q.buyer_id
      left join seller as s on s.seller_id=q.seller_id
      left join user as a on a.user_id=q.op_id 
      order by q.update_time desc;
    `;
    return await dbUtil.query(sql, [
      Delete,
      SystemType,
      tnt_id,
      IgnoreQpsLevel
    ]);
  }

  async addQps(params: QpsAPI.AddQpsParams): Promise<boolean> {
    const {
      level,
      pub_id = 0,
      buyer_id = 0,
      qps = 0,
      status = 1,
      op_id,
      tnt_id,
      region = 0,
      ots_id
    } = params;

    const ukKey = sha256(
      `${tnt_id}${level}${buyer_id}${pub_id}${region}${ots_id}`
    );

    const sql = `insert into qps (buyer_id, level, seller_id, qps, status, op_id, tnt_id, ots_id, server_region, uk_key) values ?`;

    const oneData = [
      [
        buyer_id,
        level,
        pub_id,
        qps,
        status,
        op_id,
        tnt_id,
        ots_id,
        region,
        ukKey
      ]
    ];

    try {
      const result = await dbUtil.query(sql, [oneData]);
      return !!result.affectedRows;
    } catch (error: any) {
      // 处理唯一索引冲突
      if (error.errno === 1062) {
        throw { code: 'QPS_EXISTS', originalError: error };
      }
      throw error;
    }
  }

  async updateQps(params: any): Promise<boolean> {
    const {
      id,
      status,
      qps = 0,
      op_id,
      tnt_id,
      ots_id,
      level = 1,
      buyer_id = 0,
      pub_id = 0,
      region = 0
    } = params;

    const ukKey = sha256(
      `${tnt_id}${level}${buyer_id}${pub_id}${region}${ots_id}`
    );

    const qpsObj = buildSQLSetClause([
      ['qps', qps],
      ['status', status],
      ['op_id', op_id],
      ['ots_id', ots_id],
      ['uk_key', ukKey]
    ]);

    const sql = `update qps set ? where id=? and tnt_id=?`;

    try {
      const result = await dbUtil.query(sql, [qpsObj, id, tnt_id]);
      return !!result.affectedRows;
    } catch (error: any) {
      // 处理唯一索引冲突
      if (error.errno === 1062) {
        throw { code: 'QPS_EXISTS', originalError: error };
      }
      throw error;
    }
  }

  async updateDuplicatedQps(
    params: QpsAPI.AddQpsParams & QpsAPI.UpdateQpsParams
  ): Promise<boolean> {
    const {
      qps = 0,
      status = 1,
      tnt_id,
      ots_id,
      id,
      op_id,
      level = 1,
      buyer_id = 0,
      pub_id = 0,
      region = 0
    } = params;
    const ukKey = sha256(
      `${tnt_id}${level}${buyer_id}${pub_id}${region}${ots_id}`
    );

    const qpsObj = buildSQLSetClause([
      ['qps', qps],
      ['status', status],
      ['op_id', op_id],
      ['ots_id', ots_id],
      ['uk_key', ukKey]
    ]);

    const sql = `update qps set ? where id=? and tnt_id=?`;
    return !!(await dbUtil.query(sql, [qpsObj, id, tnt_id]));
  }

  /**
   * @description 这个接口没有用到
   */
  async updateQpsStatus(params: QpsAPI.UpdateQpsParams): Promise<boolean> {
    const { id, tnt_id } = params;

    const qpsObj = buildSQLSetClause([['status', 1]]);

    const sql = `update qps set ? where id=? and tnt_id=?`;
    return !!(await dbUtil.query(sql, [qpsObj, id, tnt_id]));
  }
}

export default new QpsModel();
