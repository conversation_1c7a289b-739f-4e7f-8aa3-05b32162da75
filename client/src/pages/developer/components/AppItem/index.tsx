/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-10-10 15:37:39
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-25 11:28:11
 * @Description:
 */
import React from 'react';
import styles from './index.less';
import { AppItemColor, Alphabets } from '@/constants';
import { PlatformTypeToLabel } from '@/constants/app-list';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import NormalTitle from '@/components/NormalTitle';
import RixEngineFont from '@/components/RixEngineFont';

type AppItemType = {
  item: AppAPI.AppListItem | undefined;
  ellipsis?: boolean;
  background?: string;
  handleClick?: (item: AppAPI.AppListItem | undefined, itemColor: string) => void;
  itemColor: string;
  isSite?: boolean;
};

const AppItem: React.FC<AppItemType> = ({ item, background = '#fff', handleClick, itemColor, isSite }) => {
  const current: any = item || {};
  const { app_name = '', bundle = '', app_id, platform = '' } = current;
  const str = app_name.replace(/[^a-zA-Z]/g, '');
  const alphabet = str.length ? str[0].toLowerCase() : 'a';
  const index = Alphabets.findIndex(val => val === alphabet.toLowerCase());
  const onClick = () => {
    if (handleClick) {
      handleClick(item, itemColor);
    }
  };
  return (
    <div className={styles['app-item-container']} onClick={onClick} style={{ background: background }}>
      <div className={styles['item-left']}>
        <div className={styles['profile-container']} style={{ background: itemColor }}>
          <span>
            <RixEngineFont type={`${alphabet}`} style={{ fontSize: 36 }} />
          </span>
          {platform ? (
            <div>
              <RixEngineFont
                type={`${platform < 4 ? PlatformTypeToLabel[platform].toLocaleLowerCase() : 'rix-others'}`}
                style={{ fontSize: 14 }}
              />
            </div>
          ) : isSite ? (
            <div>
              <RixEngineFont type={`site`} style={{ fontSize: 14, color: '#1a80f5' }} />
            </div>
          ) : null}
        </div>
      </div>
      <div className={styles['item-right']}>
        <HoverTooltip title={`${app_name}`} maxWidth={180}>
          <div className={styles['app-info-title']}>
            <div className={`${styles['ellipsis']} ${styles['app-name']}`}>{app_name}</div>
            <span className={styles['line']}>|</span>
            <div className={`${styles['ellipsis']} ${styles['app-id']}`}>{app_id}</div>
          </div>
        </HoverTooltip>
        <HoverTooltip title={`${bundle}`} maxWidth={180}>
          <div className={`${styles['ellipsis']} ${styles['gray']}`}>{bundle}</div>
        </HoverTooltip>
      </div>
    </div>
  );
};

export default AppItem;
