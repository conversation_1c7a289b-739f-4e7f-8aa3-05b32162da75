@import (reference) '~antd/es/style/themes/index';

.container {
  top: 72px !important;
  > * {
    background-color: @popover-bg;
    border-radius: 4px;
    box-shadow: @shadow-1-down;
  }
}

.title {
  cursor: pointer;
}
.dropdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  // margin-top: 12px;
  min-width: 160px;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

  :global {
    .ant-dropdown-menu {
      box-shadow: none;
      width: 100%;
      li {
        border-radius: 6px;
      }
    }
  }
}
@media screen and (max-width: @screen-xs) {
  .container {
    width: 100% !important;
  }
  .container > * {
    border-radius: 0 !important;
  }
}
