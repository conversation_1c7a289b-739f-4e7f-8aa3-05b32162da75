.app-list-tag {
  height: 26px;
  line-height: 26px;
  background: #F3F5F5;
  border-radius: 6px;
  color: var(--text-color);
  margin-bottom: 6px;
  cursor: pointer;
}

.app-list-tag-red {
  background: #F75941;
  color: #fff;
  cursor: pointer;
  height: auto;
  line-height: 23px;
}

.app-list-tag-span {
  padding: 5px 8px;
  border-radius: 3px;
}

.app-list-tag-container {
  display: flex;
  flex-wrap: wrap;
  max-width: 500px;
}

.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  .app-list-tag {
    margin-bottom: 0px;
  }
}
.tag-container {
  position: relative;
  width: 100%;
}

.normal-popover-container {
  width: 500px;
  :global {
    .ant-popover-title {
      color: #8D9799;
      padding: 10px 16px; 
      border-bottom: none;
    }
    .ant-popover-inner {
      border-radius: 6px;
      .ant-popover-inner-content {
        max-height: 300px;
        overflow-y: auto;
        padding-top: 0px;
      } 
    }
  }
}
