/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-06 19:01:16
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-07-06 19:01:16
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAppList } from '@/services/api';

export default function useAppList() {
  const { data, run, loading, error } = useCustomRequest(getAppList, {
    cacheKey: 'dashboard-app-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    data,
    reload: run,
    loading
  };
}
