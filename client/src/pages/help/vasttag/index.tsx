/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-14 21:41:29
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-23 15:48:36
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-14 18:42:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-09-14 18:46:39
 * @Description:
 */
import React from 'react';
import MdPage from '../components/MdPage';
// @ts-ignore
import GSContent from '@/assets/md/VastTag_API.md';
const VastTagDoc: React.FC = () => {
  return <MdPage enMdPath={GSContent}></MdPage>;
};

export default VastTagDoc;
