/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-10 16:45:40
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-04-27 12:37:58
 * @Description:
 */

import { UserType } from '@/constants';

export const isArrSame = (arr1: (number | string)[], arr2: (number | string)[]) => {
  return (
    arr1.length === arr2.length && arr1.every(a => arr2.some(b => a === b)) && arr2.every(a => arr1.some(b => a === b))
  );
};

// 深度对比两个对象数组是否相同
export const isObjArrSame = (arr1: any[], arr2: any[]) => {
  return (
    arr1.length === arr2.length &&
    arr1.every(a => arr2.some(b => JSON.stringify(a) === JSON.stringify(b))) &&
    arr2.every(a => arr1.some(b => JSON.stringify(a) === JSON.stringify(b)))
  );
};

/**
 *
 * @param access
 * @param accessCode
 * @returns 返回结果为true时，表示禁用，false时表示不禁用
 */
export const judgeAuth = (access: any, accessCode?: string) => {
  if (!accessCode) return false;
  if (typeof access[accessCode] === 'boolean') {
    return !access[accessCode];
  }
  return access.DisabledButton(accessCode);
};

/**
 * 判断是否是系统用户
 * @param type
 * @returns
 */
export const judgeIsRixSystemUser = (type: number) => [UserType.Rix_Admin, UserType.Rix_Data_Analyst].includes(type);

export const customProfitModel = {
  demand: [1071, 1052, 1075, 1093],
  supply: [1059, 1052, 1075, 1093]
};
/**
 *  demand 和 supply 的 profit model 开放租户
 * @param tnt_id number 租户id
 * @param type 'demand' | 'supply' 区分demand 和 supply
 * @returns boolean
 */
export const isCustomProfitModel = (tnt_id: number, type: 'demand' | 'supply') =>
  customProfitModel[type].includes(tnt_id);
