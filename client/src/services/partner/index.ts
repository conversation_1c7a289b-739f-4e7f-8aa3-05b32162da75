/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 16:13:51
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 16:19:20
 * @Description:
 */

import { request } from '@umijs/max';

// 合作伙伴
export async function getPartnerList(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/partner/getPartnerList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body || {},
    ...(options || {})
  });
}

export async function updatePartner(body: PartnerAPI.PartnerListItem, options?: { [key: string]: any }) {
  return request<API.Result>('/api/partner/updatePartner', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body || {},
    ...(options || {})
  });
}

export async function addPartner(body: PartnerAPI.PartnerListItem, options?: { [key: string]: any }) {
  return request<API.Result>('/api/partner/addPartner', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body || {},
    ...(options || {})
  });
}

export async function getPartnerAccount(body: { partner_id: number }, options?: { [key: string]: any }) {
  return request<API.Result>('/api/partner/getAccount', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body || {},
    ...(options || {})
  });
}

export async function createPartnerAccount(body: PartnerAPI.PartnerListItem, options?: { [key: string]: any }) {
  return request<API.Result>('/api/partner/createAccount', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body || {},
    ...(options || {})
  });
}

