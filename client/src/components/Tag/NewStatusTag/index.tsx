/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-26 15:40:40
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-26 15:21:14
 * @Description:
 */
import { Badge, Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { TagProps } from 'antd/lib/tag';

const StatusDescMap: { [key: number]: string } = {
  1: 'Active',
  2: 'Paused',
  3: 'Delete'
};

type StatusTagProps = TagProps & {
  value: number;
  textColor?: string;
  statusColorMap?: { [key: number]: string };
  statusDescMap?: { [key: number]: string };
  tips?: string; // 警告提示
};

export const ColorMap = {
  orange: '#FFB114',
  blue: '#476DF5',
  green: '#1CD880',
  red: '#FA5A42'
} as const;

const StatusColorMap: { [key: number]: string } = {
  1: ColorMap.green,
  2: ColorMap.red,
  3: ColorMap.blue
};
function StatusTag({
  value = 1,
  textColor = '#606266',
  statusColorMap = StatusColorMap,
  statusDescMap = StatusDescMap,
  tips,
  style,
  ...rest
}: StatusTagProps): JSX.Element {
  // 使用可选链和空值合并简化条件判断
  const color = statusColorMap?.[value] ?? ColorMap.orange;
  const text = statusDescMap?.[value] ?? 'Unknown';

  return (
    <span
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        whiteSpace: 'nowrap',
        width: '100%'
      }}
    >
      <Badge {...rest} color={color} text={text} style={{ color: textColor, ...style }} />
      {tips && (
        <Tooltip title={tips}>
          <ExclamationCircleOutlined
            style={{
              fontSize: 16,
              cursor: 'pointer',
              color,
              marginLeft: 6
            }}
          />
        </Tooltip>
      )}
    </span>
  );
}

export default StatusTag;
