.app-item-container {
  display: flex;
  align-items: center;
  padding-top: 16px;
  .item-left {
    position: relative;
    padding-left: 24px;
    .profile-container {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      background-color: #fa8c16;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      > span {
        font-size: 40px;
        font-family: 'Satoshi Variable';
        font-weight: 400;
        color: #fff;
        display: flex;
        align-items: center;
      }
      div {
        position: absolute;
        left: 0px;
        top: 0px;
        width: 14px;
        height: 14px;
        border-radius: 6px 0px;
        background-color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #a4c639;
        img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
  .item-right {
    padding-left: 12px;
    flex: 1;
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    .gray {
      color: #5e6466;
    }
  }
}

.container {
  background-color: #fff;
  flex: 1;
  border-radius: 6px;
  margin-left: 20px;
  margin-right: 16px;
  width: 100%;
  overflow: hidden;
  .app-center {
    padding: 0 20px;
    padding-top: 16px;
    h3 {
      font-weight: 700;
      color: var(--text-color);
      font-size: 16px;
    }
  }
  .app-bottom {
    padding: 0 20px;
    padding-top: 20px;
    h3 {
      color: var(--text-color);
      font-size: 16px;
      font-weight: 700;
    }
    > span {
      padding-bottom: 16px;
    }
    :global {
      .ant-input-search {
        max-width: 500px;
        .ant-btn {
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .ant-table-wrapper {
        padding-bottom: 16px;
      }
    }
  }
}

.btn-container {
  display: flex;
  align-items: center;
  span {
    color: #0db4bf;
  }
  button {
    padding: 4px 6px;
    &:first-child {
      margin-right: 8px;
    }
  }
}
