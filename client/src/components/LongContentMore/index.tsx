/*
 * @Author: ch<PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2023-03-02 18:57:22
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-03 13:05:09
 * @Description: 下拉加载更多组件 优化页面显示卡顿问题
 */

import styles from './index.less';
import { useEffect, useRef, useState } from 'react';
import { useLatest } from 'ahooks';

type LongContentMoreProps = {
  dataSource: string[] | number[]; // 显示的值
  contentMaxHeight?: number | string; // 内容最大高度
  defaultShowNum?:number;
  children: React.ReactNode; // 必须要有的子元素
  open?:boolean; // 组件是否正在显示 优化用
  onShowDataChange: (val:string[] | number[]) => void; // 当前显示数据的改变
  className?: string; // 类
  style?: React.CSSProperties; // 样式
  border?: boolean; // 是否显示边框
}

// 只管改变显示数据，不管其他 其他所有由子元素自己执行
export default function({
  dataSource,
  contentMaxHeight = 'calc(100vh - 500px)',
  defaultShowNum = 100,
  children,
  open = true,
  onShowDataChange,
  className,
  style,
  border = true
}: LongContentMoreProps): JSX.Element {
  const divRef = useRef<HTMLDivElement | null>(null);
  // 显示数据
  const [showList, setShowList] = useState<string[] | number[]>([]);
  // 数据截取到哪
  const [endNum, setEndNum] = useState(defaultShowNum);
  const [len, setLen] = useState(defaultShowNum);
  // 获取最新的值 解决闭包获取不到最新值问题
  const latestLen = useLatest(len);

  useEffect(() => {
    onShowDataChange(showList);
  }, [showList]);

  // 清空删除的时候
  useEffect(() => {
    // 默认值 变换需要判断当前的位置 不能重置位置
    if (dataSource.length <= defaultShowNum || latestLen.current > dataSource.length) {
      setShowList(dataSource.slice(0));
    } else {
      const num = latestLen.current && latestLen.current <= dataSource.length ? latestLen.current : defaultShowNum;
      const data = dataSource.slice(0, num);
      setShowList(data);
      setEndNum(num);
      setLen(num);
    }
  }, [dataSource]);

  useEffect(() => {
    if (open) {
      divRef.current?.addEventListener('scroll', handleScroll);
    } else {
      divRef.current?.removeEventListener('scroll', handleScroll);
    }
    return () => {
      divRef.current?.removeEventListener('scroll', handleScroll);
    };
  }, [divRef.current, open, dataSource]);

  useEffect(() => {
    const fLen = dataSource.length;
    if (endNum <= fLen) {
      const data = dataSource.slice(0, endNum);
      setShowList(data);
      setLen(data.length);
    }
  }, [endNum]);

  const handleScroll = (e: any) => {
    const scrollTop = e.target.scrollTop;
    const clientHeight = e.target.clientHeight;
    const scrollHeight = e.target.scrollHeight;
    const fLen = dataSource.length;
    const flag = (clientHeight + scrollTop + 20 >= scrollHeight) && fLen > defaultShowNum;
    // 到底了
    if (flag) {
      // 需要注意 dataSource变化的时候， 数据是否是变化的
      const tmp = latestLen.current;
      let end = tmp + defaultShowNum;
      end = fLen > end ? end : fLen;
      setEndNum(end);
    }
  };
  return (
    <div
      className={`${styles['long-content-more-container']} ${className || ''}`}
      ref={divRef}
      style={{ maxHeight: contentMaxHeight, border: border ? '1px solid #d9d9d9' : 'none', ...style }}
    >
      { children }
    </div>
  );
}
