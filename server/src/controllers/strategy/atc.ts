/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-07 11:30:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:40:08
 * @Description:
 */

import { Context } from 'koa';
import { atcService } from '@/services';
import { AtcAPI } from '@/types/atc';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import { AtcScheme } from '@/schema/strategy/atc';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class AtcCtrl implements AtcAPI.AtcCtrlInterface {
  async getAtcList(ctx: Context) {
    let result = { ...RESULT };
    const tnt_id = ctx.session!.tnt_id;

    const list = await atcService.getAtcList(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`getAtcList success tnt_id=[${tnt_id}]`);
    }

    ctx.body = result;
  }

  @validateBody(AtcScheme)
  async updateAtc(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    const list = await atcService.updateAtc(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updateAtc success formData=[${JSON.stringify(formData)}]`
      );
    }

    ctx.body = result;
  }
}

export const atcCtrl = new AtcCtrl();
