/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 17:15:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-25 11:21:58
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { ColumnProps } from 'antd/es/table';
export const AppBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Developer',
    icon: 'rix-developer'
  },
  {
    name: 'App List'
  }
];

export const columns: ColumnProps<AppAPI.PlacementListItem>[] = [
  {
    title: 'Unit',
    dataIndex: 'plm_name',
    fixed: 'left',
    width: 150,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Unit ID',
    ellipsis: { showTitle: false },
    width: 110,
    dataIndex: 'plm_id'
  },
  {
    title: 'Ad Format',
    width: 160,
    dataIndex: 'placement_type',
    render: (txt: number) => <>{PlacementTypeFlip[txt]}</>
  },
  {
    title: 'Ad Size',
    width: 110,
    dataIndex: 'ad_size',
    render: (txt, params) => {
      if (params.placement_type !== PlacementType.Native) {
        return <>{`${params.ad_width} * ${params.ad_height}`}</>;
      }
      return '-';
    }
  },
  // 暂时不需要
  // {
  //   title: 'Price($)',
  //   width: 90,
  //   dataIndex: 'price',
  //   render: (txt) => <> {txt}</>
  // },
  {
    title: 'Bid Floor',
    width: 100,
    dataIndex: 'bid_floor',
    render: txt => <> {txt}</>
  }
];

export const PlatformOptions = [
  {
    label: 'IOS',
    value: 1
  },
  {
    label: 'Android',
    value: 2
  },
  {
    label: 'Others',
    value: 3
  }
];

export const VastTagPlatfromOptions = [
  {
    label: 'IOS',
    value: 1
  },
  {
    label: 'Android',
    value: 2
  },
  {
    label: 'Others',
    value: 3
  },
  {
    label: 'Linux',
    value: 4
  },
  {
    label: 'MacOS',
    value: 5
  },
  {
    label: 'Windows',
    value: 6
  },
  {
    label: 'tvOS',
    value: 11
  },
  {
    label: 'Roku',
    value: 12
  },
  {
    label: 'Amazon',
    value: 13
  },
  {
    label: 'Microsoft',
    value: 14
  },
  {
    label: 'Samsung Smart TV',
    value: 15
  },
  {
    label: 'LG Smart TV',
    value: 16
  },
  {
    label: 'Sony Playstation',
    value: 17
  },
  {
    label: 'Vizio',
    value: 18
  },
  {
    label: 'Philips Smart TV',
    value: 19
  },
  {
    label: 'Tizen',
    value: 50
  },
  {
    label: 'KaiOS',
    value: 51
  }
];
export const PlatformTypeToLabel: API.StringToStringType = {
  0: 'Unknown',
  1: 'IOS',
  2: 'Android',
  3: 'Others',
  4: 'Linux',
  5: 'MacOS',
  6: 'Windows',
  11: 'tvOS',
  12: 'Roku',
  13: 'Amazon',
  14: 'Microsoft',
  15: 'Samsung Smart TV',
  16: 'LG Smart TV',
  17: 'Sony Playstation',
  18: 'Vizio',
  19: 'Philips Smart TV',
  50: 'Tizen',
  51: 'KaiOS'
};

export const ScreenOrientation = [
  {
    label: 'Portrait',
    value: 1
  },
  {
    label: 'Landscape',
    value: 2
  }
];

export const Category: API.StringType = {
  IAB1: 'Arts & Entertainment',
  IAB2: 'Automotive',
  IAB3: 'Business',
  IAB4: 'Careers',
  IAB5: 'Education',
  IAB6: 'Family & Parenting',
  IAB7: 'Health & Fitness',
  IAB8: 'Food & Drink',
  IAB9: 'Hobbies & Interests',
  IAB10: 'Home & Garden',
  IAB11: 'Law,Gov‘t & Politics',
  IAB12: 'News',
  IAB13: 'Personal Finance',
  IAB14: 'Society',
  IAB15: 'Science',
  IAB16: 'Pets',
  IAB17: 'Sports',
  IAB18: 'Style & Fashion',
  IAB19: 'Technology & Computing',
  IAB20: 'Travel',
  IAB21: 'Real Estate',
  IAB22: 'Shopping',
  IAB23: 'Religion & Spirituality',
  IAB24: 'Uncategorized',
  IAB25: 'Non-Standard Content',
  IAB26: 'Illegal Content'
};

export const ProtocolsOptions = [
  { label: 'VAST 2.0', value: 2 },
  { label: 'VAST 3.0', value: 3 },
  { label: 'VAST 4.0', value: 7 },
  { label: 'VAST 2.0 Wrapper', value: 5 },
  { label: 'VAST 3.0 Wrapper', value: 6 },
  { label: 'VAST 4.0 Wrapper', value: 8 }
];

export const AllAPIOptions = [
  { label: 'VPAID', value: 1 },
  { label: 'VPAID 2.0', value: 2 },
  { label: 'MRAID-1', value: 3 },
  { label: 'ORMMA', value: 4 },
  { label: 'MRAID-2', value: 5 },
  { label: 'MRAID-3', value: 6 }
];

export const AssetsOptions = [
  { label: 'Title', value: 1 },
  { label: 'Main Image', value: 2 },
  { label: 'Icon Image', value: 3 },
  { label: 'Description', value: 4 },
  { label: 'CTA Text', value: 5 }
];

export const AdPositionOptions = [
  { value: 1, label: 'Above the Fold' },
  { value: 3, label: 'Below the Fold' },
  { value: 4, label: 'Header' },
  { value: 5, label: 'Footer' }
];

export const PlacementTypeOptions = [
  { value: 1, label: 'Banner' },
  { value: 2, label: 'Medium Rectangle' },
  { value: 3, label: 'Interstitial' },
  { value: 4, label: 'Rewarded Video' },
  { value: 5, label: 'Native' }
];

export const PlacementType = {
  Banner: 1,
  'Medium Rectangle': 2,
  Interstitial: 3,
  'Rewarded Video': 4,
  Native: 5
};

export const PlacementTypeFlip: API.NumberType = {
  1: 'Banner',
  2: 'Medium Rectangle',
  3: 'Interstitial',
  4: 'Rewarded Video',
  5: 'Native'
};

export const AdSizes = {
  Banner: ['320|50', '728|90'],
  'Medium Rectangle': ['300|250'],
  Interstitial: ['320|480', '480|320', '720|1280', '1280|720', '768|1024', '1024|768'],
  'Rewarded Video': ['480|320', '1280|720', '1024|768', '320|480', '720|1280', '768|1024']
};

export const SupportMimeTypeOptions = [
  { label: 'Video', value: 'video', disabled: false },
  { label: 'HTML', value: 'html', disabled: false }
];

export const minDurationOptions = [
  { label: '5s', value: 5 },
  { label: '10s', value: 10 },
  { label: '15s', value: 15 }
];

export const maxDurationOptions = [
  { label: '30s', value: 30 },
  { label: '60s', value: 60 },
  { label: '120s', value: 120 }
];

export const PlDefaultFormData = {
  plm_name: undefined,
  placement_type: 1,
  ad_size: undefined,
  support_type: [],
  skip: true,
  banner_api: [],
  pos: '',
  mute: false,
  assets: [],
  protocols: [2, 3],
  minduration: 5,
  maxduration: 30,
  companionad: false,
  video_api: [],
  bid_floor: undefined
  // price: undefined,
};

export const IntegrationType = {
  RTB: 1,
  Perbid: 2,
  'Android SDK': 3,
  'Max-JSTag': 4,
  'iOS SDK': 5,
  'MW-JSTag': 6,
  VastTag: 7
};

export const PublisherType: { [key: number]: 'MW' | 'MAX' | 'DFP' } = {
  4: 'MAX',
  6: 'MW'
};
