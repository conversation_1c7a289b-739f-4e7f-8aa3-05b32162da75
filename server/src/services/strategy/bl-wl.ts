/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:05:48
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-20 16:44:38
 * @Description:
 */
import { UserType } from '@/constants';
import { blAndWlModel } from '@/models';
import { BlAndWlAPI } from '@/types/bl-wl';

class BlAndWlService implements BlAndWlAPI.BlAndWl {
  // eslint-disable-next-line max-len
  async isBlAndWlExists(params: BlAndWlAPI.IsExistParams): Promise<any[]> {
    return await blAndWlModel.isBlAndWlExists(params);
  }

  async getBlAndWlList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<BlAndWlAPI.BlAndWlListItem[]> {
    let list = await blAndWlModel.getBlAndWlList(tnt_id, cur_time_zone);
    list = list.map((item: any) => {
      [UserType.Rix_Admin, UserType.Rix_Data_Analyst].includes(
        item.account_type
      ) && (item.account_name = 'System');
      item.account_status === 3 && (item.account_name = 'UnKnow');
      delete item.account_type;
      return item;
    });
    return list;
  }

  async addBlAndWl(params: BlAndWlAPI.AddBlAndWlParams): Promise<boolean> {
    return await blAndWlModel.addBlAndWl(params);
  }

  async updateBlAndWl(
    params: BlAndWlAPI.updateBlAndWlParams
  ): Promise<boolean> {
    return await blAndWlModel.updateBlAndWl(params);
  }
}

export default new BlAndWlService();
