/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 11:39:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-03-01 17:50:50
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Form, message } from 'antd';
import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { addBlAndWl, updateBlAndWl } from '@/services/api';
import { fetchData, handleFilterSelect, isValidBundle } from '@/utils';
import Select from '@/components/Select/NormalSelect';
import { ListTypeOptions, ListType } from '@/constants/strategy/bl-wl';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import BundleTableEdit from '@/components/BundleTableEdit';
import { CountryOptions } from '@/constants/global-mapping/country';
import { AdSizeOptions } from '@/models/useAdSizeOptions';

type AddBlAndWlModelProps = {
  item?: StrategyAPI.BlAndWlListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadBlAndWl: () => void;
  demandList: DemandAPI.DemandListItem[];
  supplyList: SupplyAPI.SupplyListItem[];
  adSizeOptions: AdSizeOptions[];
};

const DefaultFormData = {
  buyer_id: undefined,
  seller_id: undefined,
  type: undefined,
  content: undefined,
  status: 1
};

const AddBlAndWlModel: React.FC<AddBlAndWlModelProps> = ({
  item,
  handleClose,
  visible,
  isEdit,
  reloadBlAndWl,
  demandList,
  supplyList,
  adSizeOptions
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (item && isEdit && visible) {
      const params = {
        ...item,
        content: item.content.split(',')
      };
      form.setFieldsValue(params);
      setType(item.type);
    } else {
      form.resetFields();
    }
  }, [item, visible, isEdit]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    const params = {
      ...values,
      content: Array.isArray(values.content) ? values.content.join(',') : '',
      ori_data: item || {}
    };
    if (isEdit) {
      handleEditBlAndWl({ ...params, id: item?.id });
    } else {
      handleAddBlAndWl(params);
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadBlAndWl();
  };

  const onCancel = () => {
    form.resetFields();
    setType(undefined);
    handleClose();
  };

  const handleAddBlAndWl = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addBlAndWl, params, onSuccess });
  };

  const handleEditBlAndWl = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateBlAndWl, params });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.type) {
      setType(changeValue.type);
      form.setFieldValue('content', []);
    }
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit BL & WL` : `Create BL & WL`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
      >
        <Form.Item
          name="seller_id"
          label="Publisher:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Publisher'
            }
          ]}
        >
          <Select
            placeholder="Please Select Publisher"
            disabled={isEdit}
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            <Select.Option value={0} key={-1}>
              All Publishers
            </Select.Option>
            {supplyList?.map((item: SupplyAPI.SupplyListItem, index: number) => {
              return (
                <Select.Option
                  value={item.seller_id}
                  key={index}
                  disabled={item.status === DemandAndSupplyStatusMap.Testing}
                >
                  {item.seller_name}({item.seller_id})
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item
          name="buyer_id"
          label="Advertiser:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Advertiser'
            }
          ]}
        >
          <Select
            placeholder="Please Select Advertiser"
            disabled={isEdit}
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            <Select.Option value={0} key={-1}>
              All Advertisers
            </Select.Option>
            {demandList?.map((item: DemandAPI.DemandListItem, index: number) => {
              return (
                <Select.Option value={item.buyer_id} key={index}>
                  {item.buyer_name}({item.buyer_id})
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item
          name="type"
          label="Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <Select options={ListTypeOptions} placeholder="Please Select Type" showSearch disabled={isEdit} />
        </Form.Item>
        {(type === ListType['Ad Format Black List'] || type === ListType['Ad Format White List']) && (
          <Form.Item
            label="Content"
            name="content"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Content'
              }
            ]}
          >
            <Select
              options={AdFormatOptions}
              placeholder="Please Select Ad Format"
              mode="multiple"
              filterOption={handleFilterSelect}
              allowClear
            />
          </Form.Item>
        )}

        {(!type ||
          type === ListType['Bundle(Domain) Black List'] ||
          type === ListType['Bundle(Domain) White List']) && (
          <Form.Item
            label="Content"
            name="content"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Content'
              }
            ]}
          >
            <BundleTableEdit
              editTitle="Edit Content"
              editTips="Enter the content(one per line)"
              deleteAllTitle="Delete All Content"
              deleteAllTips="Are you sure to delete all content?"
              open={visible}
              contentMaxHeight={isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'}
              editSingle={true}
              validator={(val: string) => isValidBundle(val)}
            />
          </Form.Item>
        )}
        {((type && type === ListType['Country Black List']) || type === ListType['Country White List']) && (
          <Form.Item
            label="Content"
            name="content"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Content'
              }
            ]}
          >
            <Select
              filterOption={handleFilterSelect}
              placeholder="Please Select Country"
              mode="multiple"
              allowClear
              options={CountryOptions}
            />
          </Form.Item>
        )}
        {((type && type === ListType['Ad Size White List']) || type === ListType['Ad Size Black List']) && (
          <Form.Item
            label="Content"
            name="content"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Content'
              }
            ]}
          >
            <Select
              placeholder="Please Select Ad Size"
              mode="multiple"
              filterOption={handleFilterSelect}
              allowClear
              options={adSizeOptions}
            />
          </Form.Item>
        )}
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddBlAndWlModel;
