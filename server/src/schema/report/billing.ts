/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-07 17:54:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-21 18:52:34
 * @Description:
 */
import Joi from 'joi';
import { AllBillingDimension } from '@/constants/report/billing-report';
import { validDateParams } from './utils';
import { bundleValidation } from './dashboard';
const NumReg = /^\d{8}$/;

const billingScheme = {
  columns: Joi.array().items(Joi.string().valid(...AllBillingDimension)),
  start_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  end_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  start: Joi.number().integer().min(0).required(),
  end: Joi.number()
    .integer()
    .min(1)
    .required()
    .when('start', {
      // 必须大于start
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .max(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: (relation) => relation + 500
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    })
    .when('start', {
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .min(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: (relation) => relation + 1
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    }),
  order: Joi.string().valid('desc', 'asc'),
  buyer_id: Joi.array().items(Joi.number()),
  partner_id: Joi.array().items(Joi.number().integer()),
  seller_id: Joi.array().items(Joi.number()),
  ad_format: Joi.array().items(Joi.string().pattern(/^\d+$/).required()),
  ad_size: Joi.array().items(Joi.number().integer().required()),
  app_bundle_id: bundleValidation.messages({
    'number.maximum': 'Maximum inquiries of {#limit} bundles'
  }),
  country: Joi.array().items(Joi.string().max(3).required())
};
const customBillingVaild = (value: any, helpers: any) => {
  const { start_date, end_date } = value;
  const { isVaild, message } = validDateParams(start_date, end_date, 183);
  return isVaild ? value : helpers.error(message);
};
export const BillingScheme = Joi.object({ ...billingScheme }).custom(
  customBillingVaild
);

export const DownloadBillingScheme = Joi.object({
  ...billingScheme,
  start: Joi.number(),
  end: Joi.number()
}).custom(customBillingVaild);
