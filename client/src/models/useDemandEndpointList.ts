/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-11 14:14:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-12-11 14:36:26
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-11 11:59:21
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-11 12:05:38
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDemandEndpoint } from '@/services/api';
type Props = {
  dataSource: DemandAPI.DemandEndpointItem[];
  reload: () => Promise<any>;
  loading: boolean;
};
export default function useDemandEndpoinList(): Props {
  const { data, run, loading, error } = useCustomRequest(getDemandEndpoint, {
    cacheKey: 'demand-endpoint-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
