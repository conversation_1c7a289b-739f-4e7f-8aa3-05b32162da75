/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:50:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 12:01:26
 * @Description:
 */
import Router from 'koa-router';
import { commonController, dictCtrl } from '@/controllers';
import { parseDateParams } from '@/utils/report';
const router = new Router({ prefix: '/common' });

const routers = router
  .post('/getBuyerIntegrationType', commonController.getBuyerIntegrationType)
  .post('/getSellerIntegrationType', commonController.getSellerIntegrationType)
  .post(
    '/getWelcomeDashboardList',
    parseDateParams,
    commonController.getWelcomeDashboardList
  )
  .post('/getNotificationList', commonController.getNotificationList)
  .post('/updateNotificationStatus', commonController.updateNotificationStatus)
  .get('/file/favicon.ico', commonController.getFavicon)
  .get('/file/logo', commonController.getLogo)
  .post('/brandInfo', commonController.getBrandInfo)
  .get('/getDict', dictCtrl.getDict);

export default routers;
