/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-22 18:42:55
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:06:26
 * @Description:
 */
import Joi from 'joi';

export const AddBlAndWlSchema = Joi.object({
  // '1 for Bundle WL, 2 for Bundle BL, 3 for Ad Format WL, 4 for Ad Format BL, 5 for Country WL, 6 for Country BL, 7 for AD Size WL, 8 for AD Size BL'
  type: Joi.number().required().valid(1, 2, 3, 4, 5, 6, 7, 8),
  buyer_id: Joi.number().integer().required(),
  seller_id: Joi.number().integer().required(),
  content: Joi.string().required()
});

export const UpdateBlAndWlSchema = Joi.object({
  type: Joi.number().required().valid(1, 2, 3, 4, 5, 6, 7, 8),
  buyer_id: Joi.number().integer().required(),
  seller_id: Joi.number().integer().required(),
  content: Joi.string().required(),
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
