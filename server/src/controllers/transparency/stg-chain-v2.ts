/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:52:53
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-31 14:21:45
 * @Description:
 */

import { Context } from 'koa';
import { stgChainServiceV2 } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { TransparencyAPI } from '@/types/transparency';
import { validateBody } from '@/utils/validate-params';
import { AddStgSchema, UpdateStgSchema } from '@/schema/transparency/stg-v2';
import { getEscapeString } from '@/utils/params';
import { getLogger } from '@/config/log4js';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class StgChainControllerV2 implements TransparencyAPI.StgChainController {
  async getStgChainList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const data = await stgChainServiceV2.getStgChainList(tnt_id);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(AddStgSchema)
  async addStgChain(ctx: Context) {
    let result = { ...RESULT };
    const { user_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = user_id;
    options.developer_website_domain = getEscapeString(
      options.developer_website_domain
    );
    const { count } = await stgChainServiceV2.isStgChainExist(options);
    if (count) {
      result = getCtxResult('STG_CHAIN_EXISTS', []);
    } else {
      const data = await stgChainServiceV2.addStgChain(options);
      if (data) {
        getLogger('app').info(
          `${account_name} addStg success formData=[${JSON.stringify(options)}]`
        );
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  @validateBody(UpdateStgSchema)
  async updateStgChain(ctx: Context) {
    let result = { ...RESULT };
    const { user_id, account_name } = ctx.session!;
    console.log(user_id);
    const options = ctx.request.body;
    options.op_id = user_id;
    options.isEdit = true;
    options.developer_website_domain = getEscapeString(
      options.developer_website_domain
    );
    const { count } = await stgChainServiceV2.isStgChainExist(options);
    if (count) {
      result = getCtxResult('STG_CHAIN_EXISTS', []);
      ctx.body = result;
      return;
    }
    const data = await stgChainServiceV2.updateStgChain(options);
    if (data) {
      getLogger('app').info(
        `${account_name} updateStg success formData=[${JSON.stringify(
          options
        )}]`
      );
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }
}

export const stgControllerV2 = new StgChainControllerV2();
