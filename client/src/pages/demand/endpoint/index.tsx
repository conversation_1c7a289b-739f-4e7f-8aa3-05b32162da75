import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { useModel, useLocation, history, useAccess } from 'umi';
import { Form, Space, message, But<PERSON>, Spin } from 'antd';
import PageContainer from '@/components/RightPageContainer';
import { ServerRegion, ADFormat } from '@/constants/demand';
import { parse } from 'query-string';
import { EndpointFormData, EndpointBreadOptions } from '@/constants/demand';
import { getDemandEndpoint, setDemandEndpoint } from '@/services/api';
import { fetchData } from '@/utils';
import Select from '@/components/Select/NormalSelect';
import InputNumber from '@/components/Input/InputNumber';
import Input from '@/components/Input/NormalInput';
import NormalTitle from '@/components/NormalTitle';
import { validUrl } from '@/utils';
import { DemandAndSupplyStatusMap } from '@/constants';
function getEndpointLabel(server_region: number, ad_format: number) {
  return `${ServerRegion[server_region]} | ${ADFormat[ad_format]}`;
}

const Page: React.FC = () => {
  const access = useAccess();
  const [form] = Form.useForm();
  const location: any = useLocation();
  const [loading, setLoading] = useState(false);
  const { previousUrl } = useModel('usePreviousUrl');
  const [formValue, setFormValue] = useState<DemandAPI.EndpointItem[]>(EndpointFormData.endpoint);
  const [beforeFromValue, setBeforeFromValue] = useState<DemandAPI.EndpointItem[]>([]);

  const [buyer_id, setBuyerId] = useState<number | string>('');
  const { dataSource, reload, loading: demandLoading } = useModel('useDemandListWithTesting');
  const [buyer, setBuyer] = useState<DemandAPI.DemandListItem | undefined>(undefined);

  useEffect(() => {
    const { id } = parse(location.search);
    if (id) {
      setBuyerId(id as string);
      fetchEndpointData(id as string);
      if (!dataSource || !dataSource.length) {
        reload();
      }
    }
  }, []);

  useEffect(() => {
    if (dataSource && buyer_id) {
      const data = dataSource.find((item: DemandAPI.DemandListItem) => item.buyer_id === +buyer_id);
      if (data) {
        setBuyer(data);
      }
    }
  }, [dataSource, buyer_id]);

  const fetchEndpointData = (id: number | string) => {
    const tmp: DemandAPI.EndpointItem[] = JSON.parse(JSON.stringify(formValue));
    console.log('tmp', tmp);
    // const ad_format_length = tmp.length / 3;
    // const offset = ad_format_length - 1;
    const useOffset = -1;
    const apacOffset = 2;
    const euwOffset = 5;
    const onSuccess = (data: any) => {
      data.forEach((item: DemandAPI.DemandEndpointItem) => {
        const obj = {
          url: item.url,
          connect_timeout: item.connect_timeout,
          socket_timeout: item.socket_timeout,
          gzip: item.gzip,
          server_region: item.server_region,
          ad_format: item.ad_format
        };
        if (+item.server_region === 1) {
          tmp[+item.ad_format + useOffset] = obj;
        } else if (+item.server_region === 2) {
          tmp[+item.ad_format + apacOffset] = obj;
        } else if (+item.server_region === 3) {
          tmp[+item.ad_format + euwOffset] = obj;
        }
      });
      form.setFieldsValue({ endpoint: tmp });
      setBeforeFromValue(tmp);
      setFormValue(tmp);
    };
    fetchData({ setLoading, request: getDemandEndpoint, params: { buyer_id: id }, onSuccess });
  };

  const setEndpointData = (params: any) => {
    const currentFromValue = JSON.parse(params?.endpoint);
    let isChange: boolean = false;
    currentFromValue.forEach((item: DemandAPI.EndpointItem, index: number) => {
      let key: keyof DemandAPI.EndpointItem;
      for (key in item) {
        if (item[key] !== beforeFromValue[index][key]) {
          isChange = true;
          break;
        }
      }
    });
    const onSuccess = async () => {
      isChange && message.success('success');
      if (previousUrl) {
        history.go(-1);
      } else {
        history.push('/demand/advertiser');
      }
    };
    fetchData({ setLoading, request: setDemandEndpoint, params, onSuccess });
  };

  const handleFinish = () => {
    const tmp = formValue
      .filter(item => item.url)
      .map(item => {
        const obj = {
          url: item.url.trim(),
          connect_timeout: item.connect_timeout || 0,
          socket_timeout: item.socket_timeout || 0,
          gzip: item.gzip,
          server_region: item.server_region,
          ad_format: item.ad_format
        };
        return obj;
      });
    const ori_endpoint = beforeFromValue.filter(item => item.url);
    const ori_data = {
      endpoint: JSON.stringify(ori_endpoint),
      buyer_id: buyer_id
    };
    const params = {
      endpoint: JSON.stringify(tmp),
      buyer_id: buyer_id,
      ori_data
    };
    setEndpointData(params);
  };

  const getEndpointTitle = (index: number) => {
    const val = formValue[index];
    return getEndpointLabel(val.server_region, val.ad_format);
  };

  const handleValueChange = (value: any, allValue: { endpoint: DemandAPI.EndpointItem[] }) => {
    const tmp: DemandAPI.EndpointItem[] = JSON.parse(JSON.stringify(formValue));
    allValue.endpoint.forEach((item, index) => {
      tmp[index] = {
        ...tmp[index],
        ...item
      };
    });
    setFormValue(tmp);
  };

  const handleCancel = () => {
    if (previousUrl) {
      history.go(-1);
    } else {
      history.push('/demand/advertiser');
    }
  };

  return (
    <PageContainer options={EndpointBreadOptions} isBack handleGoBack={() => history.go(-1)}>
      <Spin spinning={loading || demandLoading}>
        <div className={styles['endpoint-container']}>
          <div className={styles['top']}>
            <NormalTitle
              blackName="Edit Endpoint"
              grayName={`${buyer?.buyer_name}(${buyer?.buyer_id})`}
              isTitle={true}
              bottom={20}
            />
            <Form
              onFinishFailed={err => console.log(err)}
              onFinish={handleFinish}
              form={form}
              initialValues={EndpointFormData}
              layout="horizontal"
              onValuesChange={handleValueChange}
            >
              <Form.List name="endpoint">
                {fields => (
                  <>
                    {fields.map((field, index) => {
                      return (
                        <React.Fragment key={index}>
                          <div className={styles['title']}>{getEndpointTitle(index)}</div>
                          <Space key={field.key} align="baseline" size={20}>
                            <Form.Item
                              {...field}
                              label="Endpoint"
                              name={[field.name, 'url']}
                              key="url"
                              rules={[{ required: false }, { validator: validUrl }]}
                              validateTrigger={['onBlur']}
                            >
                              <Input style={{ width: 400 }} placeholder="Please Enter Endpoint URL" />
                            </Form.Item>
                            {/* <Form.Item
                            {...field}
                            label="Connect Timeout(ms)"
                            name={[field.name, 'connect_timeout']}
                            key="connect_timeout"
                          >
                            <InputNumber style={{ width: 100 }} min={0} />
                          </Form.Item> */}
                            <Form.Item
                              {...field}
                              label="Socket Timeout(ms)"
                              name={[field.name, 'socket_timeout']}
                              key="socket_timeout"
                            >
                              <InputNumber style={{ width: 120 }} min={0} />
                            </Form.Item>
                            <Form.Item {...field} label="Gzip" name={[field.name, 'gzip']} key="gzip">
                              <Select style={{ width: 170, paddingRight: 3 }}>
                                <Select.Option value={1}>Active</Select.Option>
                                <Select.Option value={2}>Paused</Select.Option>
                              </Select>
                            </Form.Item>
                          </Space>
                        </React.Fragment>
                      );
                    })}
                  </>
                )}
              </Form.List>
              <Form.Item>
                <div className={styles['btn-container']}>
                  <Button onClick={handleCancel} style={{ background: '#eee' }}>
                    Cancel
                  </Button>
                  <Button
                    type="primary"
                    style={{ marginLeft: '8px' }}
                    disabled={
                      buyer?.status === DemandAndSupplyStatusMap.Testing ||
                      access.DisabledButton('updateEndpointAuth') ||
                      !!loading
                    }
                    onClick={() => {
                      form?.submit();
                    }}
                  >
                    Submit
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </div>
        </div>
      </Spin>
    </PageContainer>
  );
};

export default Page;
