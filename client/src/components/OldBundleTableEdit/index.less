.content-container {
  .content-btn {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    span {
      &:first-child {
        margin-right: 6px;
      }
    }
  }
  .content-empty {
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 6px;
    border-radius: 6px;
    .empty-svg svg {
      padding-top: 12px;
    }
    span {
      padding-bottom: 12px;
      color: #8a8a8a;
    }
  }
  .content-body,
  .bid-content-body,
  .bid-content-body-header {
    border-bottom: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    color: var(--text-color);
    .left {
      text-align: center;
    }
    .center {
      border-right: 1px solid #d9d9d9;
      border-left: 1px solid #d9d9d9;
      overflow: hidden;
      height: 28px;
      line-height: 28px;
      display: flex;
      span {
        padding: 0px 12px;
        text-align: left;
        // margin: 0 auto;
        // min-width: 80px;
      }
    }
  }

  .bid-content-body,
  .bid-content-body-header {
    .center {
      span {
        padding: 0px 12px;
        text-align: left;
        margin: 0 auto;
        min-width: 80px;
      }
    }
  }
  .bid-content-body-header {
    position: sticky;
    top: 0;
    z-index: 2;
    background: #fff;
  }
  .svg-default {
    &:hover {
      color: var(--primary-1);
    }
  }
  .svg-trash {
    &:hover {
      color: #ff4d4f;
    }
  }
  .svg-disabled {
    color: #d7dadb;
    pointer-events: none;
    cursor: default;
    // &:hover {
    //   color: #d7dadb;
    // }
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }
}
