# RixEngine平台配置



## Step1: 创建Publisher

- 填写基础信息时，Integration Type选择MW-JSTag

  ![image-20240130171459744](https://static.rixengine.com/a/platform/help/mw-jstag/1.png)

## Step2: 【Developer】模块创建Site

- 注意：需先选择**Publisher**
- 创建Site，生成开发者对应的App ID

![image-20240130172915511](https://static.rixengine.com/a/platform/help/mw-jstag/2.png)

![image-20240130173015161](https://static.rixengine.com/a/platform/help/mw-jstag/3.png)

## Step3:创建Site对应的Ad Unit

- 命名规则

eg：xflowgames_320 * 50Banner_Above

**注意**：这里的尺寸，只是虚拟尺寸，实际需要创建的尺寸可在生成Js-Tag代码时配置

![image-20240130173123885](https://static.rixengine.com/a/platform/help/mw-jstag/4.png)

![image-20240130173204160](https://static.rixengine.com/a/platform/help/mw-jstag/5.png)

## Step4: 根据广告位尺寸信息，生成不同尺寸的Js-Tag代码（尺寸不支持宏替换）

**Js tag入口**：

![image-20240130173246978](https://static.rixengine.com/a/platform/help/mw-jstag/6.png)

**生成JsTag代码（注意：不同尺寸生成不同Js Tag），提供给对应的开发者**：

![](https://static.rixengine.com/a/platform/help/mw-jstag/7.png)

