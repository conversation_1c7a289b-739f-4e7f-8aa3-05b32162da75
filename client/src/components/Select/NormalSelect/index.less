.normal-select-container {
  color: var(--text-color);

  :global {
    .ant-select-selector {
      border-radius: 6px !important;
    }
  }
}
.normal-select-multiple-container {
  :global {
    .ant-select-selection-item {
      background: #ebeff0;
      border-radius: 6px;
      color: var(--text-color);
    }
  }
}

.normal-select-dropdown {
  :global {
    .ant-select-item-option-disabled {
      .ant-select-item-option-content {
        color: #8d9799;
      }
    }
  }
}
.tooltip-container {
  max-height: 300px;
  overflow-y: auto;
}
.placeholder-container {
  display: flex;
  flex-direction: column;
}

:global {
  .normal-select-container {
    &.ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input) {
      .ant-select-selector {
        border-color: var(--primary-color);
      }
    }
  }

  .normal-select-container:not(.ant-select-disabled):hover .ant-select-selector {
    border-color: var(--primary-color);
  }

  .normal-select-dropdown {
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state {
      color: var(--primary-color);
    }
  }
}
