/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2023-11-17 18:42:12
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-14 18:03:46
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import { exportLogService } from '@/services';
import { getCtxResult } from '@/utils/response';
import fs from 'fs';
import { Context } from 'koa';
import path from 'path';

class ExportLogController {
  // 查询
  async getExportLog(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const data = await exportLogService.getExportLog(
      ctx.session!.user_id,
      cur_time_zone
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async fileVisit(ctx: Context) {
    const result: any = 'Not Found';
    const { name } = ctx.params;
    console.log('name', name);
    const arr = name.split('.');
    const last = arr[arr.length - 1]; // 文件后缀
    // 验证文件名称是否正确

    const isCorrect = arr.length >= 2 && last.toLowerCase() === 'csv';

    getLogger('app').info(`fileVisit name=[${name}]`);
    if (isCorrect) {
      const filePath = await exportLogService.getExportLogByName(
        name,
        ctx.session!.user_id
      );
      console.log('filePath', filePath);
      if (
        filePath &&
        fs.existsSync(path.join(__dirname, `../../../${filePath}`))
      ) {
        ctx.set('Content-disposition', `attachment;filename=${name}`);
        ctx.set('content-type', 'text/csv; charset=utf-8');
        const stream = fs.createReadStream(
          path.join(__dirname, `../../../${filePath}`),
          'utf-8'
        );
        ctx.body = stream.on('error', err => {
          getLogger('error').error(`file visit stream error=[${err.message}]`);
        });
      } else {
        ctx.body = result;
      }
    } else {
      ctx.body = result;
    }
  }

  async getExportTaskStatus(ctx: Context) {
    let result = { ...RESULT };
    const { name } = ctx.request.body;
    const data = await exportLogService.getExportTaskStatus(
      name,
      ctx.session!.user_id
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}
export const exportLogCtrl = new ExportLogController();
