/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-24 14:54:58
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-24 14:57:41
 * @Description:
 */

import { FormInstance, message } from 'antd';
import moment from 'moment';

// metrics必选校验
export function checkMetrics(metrics: string[]): boolean {
  if (!metrics || metrics.length === 0) {
    message.error('"Metrics" is required');
    return false;
  }
  return true;
}

/**
 * 日期限制 adv report, pub report
 * @param key 表单字段名
 * @param date 日期数组
 * @param timeLimit 时间限制
 * @param form 表单实例
 * @param originCallback 原始回调函数
 */
export function dateLimit(
  key: string,
  date: any[],
  timeLimit: number,
  form: FormInstance<any>,
  originCallback: () => void
) {
  if (!date?.length) return;

  const [startDate, endDate] = date;
  const today = moment().endOf('day');
  const isIncludeToday = endDate.isSame(today, 'day');
  const diffDays = endDate.diff(startDate, 'days');
  const MAX_TODAY_RANGE = 2;
  const newEndDate = moment().subtract(1, 'days').endOf('day');

  // 超出总时间限制
  if (diffDays + 1 > timeLimit) {
    if (isIncludeToday) {
      form.setFieldValue(key, [startDate, newEndDate]);
    }
    originCallback();
    return;
  }

  // 包含今天且超过3天的特殊处理
  if (isIncludeToday && diffDays > MAX_TODAY_RANGE) {
    form.setFieldValue(key, [startDate, newEndDate]);
    message.warning(`The date range cannot exceed ${MAX_TODAY_RANGE + 1} days when including today`);
  }
}
