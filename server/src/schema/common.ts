/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2023-02-03 14:05:34
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 14:40:36
 * @Description:
 */

import <PERSON><PERSON> from 'joi';
import { UserType } from '@/constants';

const AllUseType = Object.values(UserType);

const password_reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;

export const LoginPasswordScheme = Joi.object({
  account_name: Joi.string().required(),
  password: Joi.string().required()
});

export const AutoLoginScheme = Joi.object({
  token: Joi.string().required()
});

export const ResetPasswordScheme = Joi.object({
  old_password: Joi.string().min(6).max(25).required(),
  new_password: Joi.string().min(6).max(25).pattern(password_reg)
    .required()
});

export const ConfirmPasswordScheme = Joi.object({
  old_password: Joi.string().min(6).max(25).required()
});

export const AddUserScheme = Joi.object({
  account_name: Joi.string().required(),
  password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const EditUserScheme = Joi.object({
  user_id: Joi.number().required(),
  status: Joi.number().required(),
  new_password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const DeleteUserScheme = Joi.object({
  user_id: Joi.number().required()
});

export const ForceLogOutScheme = Joi.object({
  user_id: Joi.number().required()
});

export const GetDashboardUserScheme = Joi.object({
  isSupply: Joi.boolean(),
  user_id: Joi.number().required()
});

export const IsAccountNameExistsScheme = Joi.object({
  account_name: Joi.string().required()
});

export const EditDashboardUserScheme = Joi.object({
  account_name: Joi.string(),
  user_id: Joi.number().required(),
  isSupply: Joi.boolean(),
  status: Joi.number(),
  // api_status: Joi.number().valid(1, 2),
  new_password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const VaildPasswordScheme = Joi.object({
  password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const EditUserAccountScheme = Joi.object({
  user_id: Joi.number().required(),
  type: Joi.number().valid(...AllUseType).required(),
  account_name: Joi.string().required(),
  send_email: Joi.array().items(Joi.string()).min(0),
  to_self: Joi.number().integer().valid(1, 2).required()
});

export const ResetUserPwdScheme = Joi.object({
  user_id: Joi.number().required(),
  type: Joi.number().valid(...AllUseType).required(),
  account_name: Joi.string().required(),
  password: Joi.string().min(6).max(25).pattern(password_reg)
    .required(),
  send_email: Joi.array().items(Joi.string()).min(0),
  to_self: Joi.number().integer().valid(1, 2).required()
});

export const SwitchAccountScheme = Joi.object({
  switch_user_id: Joi.number().required()
});

export const GetDictScheme = Joi.object({
  dict_type: Joi.string().required()
});
