/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-13 11:33:06
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-23 17:29:53
 * @Description:
 */

export const BeApiTimeZoneMap: { [key: string]: string } = {
  'UTC-12': 'Etc/GMT+12',
  'UTC-11': 'Etc/GMT+11',
  'UTC-10': 'Etc/GMT+10',
  'UTC-9': 'Etc/GMT+9',
  'UTC-8': 'Etc/GMT+8',
  'UTC-7': 'Etc/GMT+7',
  'UTC-6': 'Etc/GMT+6',
  'UTC-5': 'Etc/GMT+5',
  'UTC-4': 'Etc/GMT+4',
  'UTC-3': 'Etc/GMT+3',
  'UTC-2': 'Etc/GMT+2',
  'UTC-1': 'Etc/GMT+1',
  'UTC+0': 'Etc/UTC',
  'UTC+12': 'Etc/GMT-12',
  'UTC+11': 'Etc/GMT-11',
  'UTC+10': 'Etc/GMT-10',
  'UTC+9': 'Etc/GMT-9',
  'UTC+8': 'Etc/GMT-8',
  'UTC+7': 'Etc/GMT-7',
  'UTC+6': 'Etc/GMT-6',
  'UTC+5': 'Etc/GMT-5',
  'UTC+4': 'Etc/GMT-4',
  'UTC+3': 'Etc/GMT-3',
  'UTC+2': 'Etc/GMT-2',
  'UTC+1': 'Etc/GMT-1'
};

export const TimeZoneMap: { [key: string]: string } = {
  'Etc/GMT+12': '-12:00',
  'Etc/GMT+11': '-11:00',
  'Etc/GMT+10': '-10:00',
  'Etc/GMT+9': '-09:00',
  'Etc/GMT+8': '-08:00',
  'Etc/GMT+7': '-07:00',
  'Etc/GMT+6': '-06:00',
  'Etc/GMT+5': '-05:00',
  'Etc/GMT+4': '-04:00',
  'Etc/GMT+3': '-03:00',
  'Etc/GMT+2': '-02:00',
  'Etc/GMT+1': '-01:00',
  'Etc/UTC': '+00:00',
  'Etc/GMT-12': '+12:00',
  'Etc/GMT-11': '+11:00',
  'Etc/GMT-10': '+10:00',
  'Etc/GMT-9': '+09:00',
  'Etc/GMT-8': '+08:00',
  'Etc/GMT-7': '+07:00',
  'Etc/GMT-6': '+06:00',
  'Etc/GMT-5': '+05:00',
  'Etc/GMT-4': '+04:00',
  'Etc/GMT-3': '+03:00',
  'Etc/GMT-2': '+02:00',
  'Etc/GMT-1': '+01:00'
};

export const getTzTimeSql = (key: string, cur_time_zone: string) => `DATE_FORMAT(CONVERT_TZ(${key},'${TimeZoneMap['Etc/UTC']}','${
    TimeZoneMap[cur_time_zone] || TimeZoneMap['Etc/UTC']
  }'),'%Y-%m-%d %H:%i:%S')`;
