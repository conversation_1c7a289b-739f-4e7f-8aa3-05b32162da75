/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-06-28 15:06:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 10:28:56
 * @Description:
 */
import { bigqueryStream } from '@/db/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

// function leftJoinPartner(condition: string) {
//   const sql = `
//     select
//         buyer_id,
//         partner_id,
//         partner_name
//       from saas-373106.saas_others.buyer_partner
//       ${condition ? `where ${condition}` : ''}
//   `;
//   return sql;
// }

// /**
//  * @deprecated 废弃
//  */
// function joinSql(params: BillingAPI.QueryInnerParams) {
//   const {
//     dimensions,
//     group_dimension,
//     condition,
//     leftJoinCond,
//     date_time_str
//   } = params;
//   const sql = `
//     select 
//       ${date_time_str ? `${date_time_str},` : ''}
//       ${dimensions ? `${dimensions},` : ''} 
//       sum(buyer_request) as request, 
//       sum(impression) as impression,
//       round(cast (sum(buyer_net_revenue) as numeric),2) as buyer_net_revenue
//     from ${ReportSchema}.billing_overview_report as i
//     left join (${leftJoinPartner(leftJoinCond)}) as j on i.buyer_id = j.buyer_id
//     ${condition ? `where ${condition}` : ''}
//     ${group_dimension ? `group by ${group_dimension}` : ''}
//   `;
//   return sql;
// }
class AdvBillingModel {
  // /**
  //  * @deprecated 废弃 使用 queryBQDataWithCache 代替
  //  */
  // async getAdvertiserBillingList(
  //   options: BillingAPI.QueryInnerParams,
  //   api_url: string
  // ) {
  //   const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
  //   return await queryStackOverflow(sql, {
  //     cacheTime: 60 * 10,
  //     tag: api_url
  //   });
  // }

  // /**
  //  * @deprecated 目前下载逻辑沿用原来的下载逻辑，待优化
  //  */
  // async downloadAllReport(
  //   options: BillingAPI.QueryInnerParams,
  //   api_url: string
  // ) {
  //   const sql = `${joinSql(options)} ${options.order}`;
  //   return await bigqueryStream(sql, { tag: api_url });
  // }

  async downloadAllReport(sql: string, labels: LabelGenerationParams) {
    return await bigqueryStream(sql, labels);
  }

  // async newCount(sql: string, tag: LabelGenerationParams) {
  //   const countSql = `select count(*) as count from(${sql}) as t`;
  //   const res = await queryBQDataWithCache(countSql, tag);
  //   return res?.[0]?.count || 0;
  // }

  // /**
  //  * @deprecated 使用 newCountList 代替
  //  */
  // async countList(options: BillingAPI.QueryInnerParams, api_url: string) {
  //   const countSql = `select count(*) as count from(${joinSql(options)}) as t`;
  //   const res = await queryStackOverflow(countSql, {
  //     cacheTime: 60 * 10,
  //     tag: api_url
  //   });
  //   if (Array.isArray(res) && res.length) {
  //     return res[0];
  //   }
  //   return 0;
  // }
}

export const advBillingModel = new AdvBillingModel();
