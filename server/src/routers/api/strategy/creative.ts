/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-06-20 14:52:43
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-07 16:56:13
 * @Description:
 */
import Router from 'koa-router';
import { creativeController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/creative' });

const routers = router
  .post('/addCreative', validTesting, creativeController.addCreative)
  .post('/updateCreative', creativeController.updateCreative)
  .post('/getCreativeList', creativeController.getCreativeList);

export default routers;
