/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-22 17:53:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-09 12:20:41
 * @Description:
 */
import Joi from 'joi';

export const addTraceTaskSchema = Joi.object({
  type: Joi.number().valid(1, 2, 3, 4).required(),
  server_region: Joi.number().valid(1, 2, 3).required(),
  seller_id: Joi.number(),
  bundle: Joi.string().allow(''),
  ad_format: Joi.number().valid(1, 2, 3, 4),
  plm_id: Joi.number(),
  adomain: Joi.string().allow(''),
  country: Joi.string().allow(''),
  crid: Joi.string().allow('').max(2000),
  cid: Joi.string().allow('').max(2000),
  buyer_id: Joi.number()
});

export const updateTraceTaskSchema = Joi.object({
  id: Joi.number().required(),
  status: Joi.number().valid(1, 2).required()
});
