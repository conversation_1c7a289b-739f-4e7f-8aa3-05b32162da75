/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:10:51
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-12 15:29:41
 * @Description:
 */
import EditButton from '@/components/Button/EditButton';
import EllipsisPopover from '@/components/EllipsisPopover';
import PageContainer from '@/components/RightPageContainer';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { DemandAndSupplyStatusMap } from '@/constants';
import {
  PmpBreadOptions,
  PmpDealColumns,
  PmpDealSearchOption,
  PmpInternalColumns,
  PmpInternalSearchOption,
  PMPTab
} from '@/constants/strategy/pmp';
import { PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useMemo, useState } from 'react';
import { useAccess, useModel } from 'umi';
import AddInventoryDrawer from '../components/AddInventoryDrawer';
import AddPMPDeal from '../components/AddPMPDeal';
import PmpItem from './item';

const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource: dealList, reload: reloadDeal, loading: dealLoading } = useModel('usePmpDealList');
  const { dataSource: interList, reload: reloadInter, loading: interLoading } = useModel('usePmpInternalList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: allSupplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const { adSizeMapByValue, fetchAdSize, adSizeOptions } = useModel('useAdSizeOptions');
  const [currentTab, setCurrentTab] = useState(PMPTab.Inventory);
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [dealItem, setDealItem] = useState<StrategyAPI.PmpDealListItem>();
  const [interItem, setInterItem] = useState<StrategyAPI.PmpInternalListItem>();
  const [dataSource, setDataSource] = useState<(StrategyAPI.PmpDealListItem | StrategyAPI.PmpInternalListItem)[]>([]);
  const [ivtInitValue, setIvtInitValue] = useState<any>({ status: [1] });
  const [dealInitValue, setDealInitValue] = useState<any>({ status: [1] });

  const handleEditPMP = (row: any) => {
    setIsEdit(true);
    setVisible(true);
    if (+currentTab === PMPTab.Deal) {
      setDealItem(row);
    } else {
      setInterItem(row);
    }
  };

  const onOperateRender = (_: any, row: any) => {
    return (
      <EditButton onClick={() => handleEditPMP(row)} disabled={access.DisabledButton('EditPMPCode')}>
        Edit
      </EditButton>
    );
  };

  const handleGoToInventory = (params: any) => {
    setCurrentTab(PMPTab.Inventory);
    setIvtInitValue({ id: [params.pmp_id], status: [] });
  };
  const onInventoryRender = (txt: string, params: any) => {
    return (
      <HoverToolTip title={txt}>
        <span onClick={() => handleGoToInventory(params)} style={{ color: 'var(--primary-color)', cursor: 'pointer' }}>
          {txt}
        </span>
      </HoverToolTip>
    );
  };

  const supplyList = useMemo(() => {
    const arr = interList?.map(v => v.seller_id) || [];
    const all_sellers = [...new Set(arr)];
    return allSupplyList?.filter(
      (v: any) => all_sellers.includes(v.seller_id) || v.status !== DemandAndSupplyStatusMap.Testing
    );
  }, [allSupplyList, interList]);

  const defaultSearchValue = useMemo(() => {
    return currentTab === PMPTab.Deal ? dealInitValue : ivtInitValue;
  }, [ivtInitValue, dealInitValue, currentTab]);

  useEffect(() => {
    if (dealList) {
      reloadDeal();
    }
    if (interList) {
      reloadInter();
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!allSupplyList || !allSupplyList.length) {
      reloadSupply();
    }
    fetchAdSize();
  }, []);

  useEffect(() => {
    if (+currentTab === PMPTab.Deal) {
      setDataSource(dealList);
    } else {
      setDataSource(interList);
    }
  }, [currentTab, dealList, interList]);

  const supplyNameMap = useMemo(() => {
    const obj: any = {};
    supplyList?.forEach((v: any) => {
      obj[v.seller_id] = v.seller_name;
    });
    return obj;
  }, [supplyList]);

  const searchOptions = useMemo(() => {
    if (+currentTab === PMPTab.Inventory) {
      const arr = PmpInternalSearchOption.map(v => ({ ...v }));
      const iIndex = arr.findIndex(v => v.key === 'id');
      const sIndex = arr.findIndex(v => v.key === 'seller_id');
      if (iIndex !== -1) {
        arr[iIndex].options = interList?.map(v => ({ label: `${v.name}(${v.id})`, value: v.id })) || [];
      }
      if (sIndex !== -1) {
        arr[sIndex].options =
          supplyList?.map((v: any) => ({ label: `${v.seller_name}(${v.seller_id})`, value: v.seller_id })) || [];
      }
      return arr;
    } else {
      const arr = PmpDealSearchOption.map(v => ({ ...v }));
      const iIndex = arr.findIndex(v => v.key === 'id');
      const bIndex = arr.findIndex(v => v.key === 'deal_id');
      const sIndex = arr.findIndex(v => v.key === 'buyer_id');
      if (iIndex !== -1) {
        arr[iIndex].options = dealList?.map(v => ({ label: `${v.name}(${v.id})`, value: v.id })) || [];
      }
      if (bIndex !== -1) {
        const deals =
          dealList
            ?.map(v => v.deal_id)
            .join(',')
            .split(',') || [];
        arr[bIndex].options = [...new Set(deals)].filter(v => v && v.trim()).map(v => ({ label: v, value: v }));
      }
      if (sIndex !== -1) {
        arr[sIndex].options =
          demandList?.map((v: any) => ({ label: `${v.buyer_name}(${v.buyer_id})`, value: v.buyer_id })) || [];
      }
      return arr;
    }
  }, [dealList, demandList, interList, supplyList, currentTab]);

  // 当 adSizeMapByValue 变化时，更新 PmpInternalColumns
  const pmpInternalColumns = useMemo(() => {
    const arr = PmpInternalColumns.map(v => ({ ...v }));
    const index = arr.findIndex(v => v.dataIndex === 'ad_size');
    if (index !== -1) {
      arr[index].render = (_: string[]) => {
        let arr = Array.isArray(_) ? _ : [];
        arr = arr.map(v => adSizeMapByValue[v] as string);
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      };
    }
    return arr;
  }, [adSizeMapByValue]);

  const columns = useMemo(() => {
    const arr =
      +currentTab === PMPTab.Inventory ? pmpInternalColumns.map(v => ({ ...v })) : PmpDealColumns.map(v => ({ ...v }));
    const index = arr.findIndex(v => v.dataIndex === 'operate');
    const invtIndex = arr.findIndex(v => v.dataIndex === 'pmp_internal_name');
    if (index !== -1) {
      arr[index].render = onOperateRender;
    }

    if (invtIndex !== -1) {
      arr[invtIndex].render = onInventoryRender;
    }

    return arr;
  }, [currentTab, pmpInternalColumns]);

  const handleClickCreate = () => {
    setIsEdit(false);
    setDealItem(undefined);
    setInterItem(undefined);
    setVisible(true);
  };

  const handleTabChange = (tab: string | number) => {
    setIvtInitValue({ status: [1] });
    setDealInitValue({ status: [1] });
    setCurrentTab(tab as number);
  };

  return (
    <PageContainer flexDirection="column" options={PmpBreadOptions}>
      <PmpItem<StrategyAPI.PmpDealListItem | StrategyAPI.PmpInternalListItem>
        searchOptions={searchOptions}
        currentTab={currentTab}
        onTabChange={handleTabChange}
        loading={dealLoading || interLoading}
        dataSource={dataSource}
        request={+currentTab === PMPTab.Deal ? reloadDeal : reloadInter}
        columns={columns}
        defaultSearchValue={defaultSearchValue}
        btnOptions={[
          {
            label: currentTab === PMPTab.Deal ? 'Create PMP Deal' : 'Create PMP Inventory',
            type: 'primary',
            size: 'small',
            accessCode: 'CreatePMP',
            onClick: handleClickCreate,
            icon: <PlusOutlined />
          }
        ]}
      />
      <AddPMPDeal
        visible={visible && +currentTab === PMPTab.Deal}
        item={dealItem}
        isEdit={isEdit}
        onClose={() => {
          setVisible(false);
          setIsEdit(false);
          setDealItem(undefined);
          setInterItem(undefined);
        }}
        reload={reloadDeal}
        demandList={demandList}
        interList={interList}
      />
      <AddInventoryDrawer
        visible={visible && +currentTab === PMPTab.Inventory}
        item={interItem}
        isEdit={isEdit}
        onClose={() => {
          setVisible(false);
          setIsEdit(false);
          setDealItem(undefined);
          setInterItem(undefined);
        }}
        reload={reloadInter}
        supplyList={supplyList}
        supplyNameMap={supplyNameMap}
        adSizeOptions={adSizeOptions}
        adSizeMapByValue={adSizeMapByValue}
      />
    </PageContainer>
  );
};

export default Page;
