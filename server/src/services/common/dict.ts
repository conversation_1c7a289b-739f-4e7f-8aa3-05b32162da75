import { getLogger } from '@/config/log4js';
import { DictMap } from '@/constants/dict';
import { dictModel } from '@/models/common/dict';

class DictService {
  async getDict(key: string): Promise<any[]> {
    const dictItem = DictMap.find(item => item.dict_type === key);
    if (!dictItem) {
      return [];
    }

    const { aat_dict_type } = dictItem;
    let data = [];
    try {
      const getCommonDict = aat_dict_type
        ? dictModel.getAatDict
        : dictModel.getSaasDict;
      data = await getCommonDict(dictItem);
    } catch (error: any) {
      getLogger('error').error(`getDict error ${error.message}`);
      return [];
    }
    return data;
  }
}

export const dictService = new DictService();
