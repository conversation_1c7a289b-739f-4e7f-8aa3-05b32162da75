.container {
  display: flex;
  background-color: #fff;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  border-radius: 6px;
  width: 100%;
  flex-wrap: wrap;
  font-size: 16px;
  .top-country-container,
  .country-top-tnt-container {
    width: 100%;
    // min-height: 300px;
  }
  .country-top-tnt-card,
  .country-top-country-card {
    flex: 1;
    max-width: 50%;
    margin-bottom: 0px;
  }
  .country-top-tnt-card {
    margin-top: 40px;
  }
}

.tooltip-container {
  padding: 12px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.98);
  min-width: 220px;
  min-height: 88px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  .tooltip-title {
    font-size: 14px;
    font-weight: 500;
    color: #8d9799;
  }
  .ad-size-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    .ad-size-item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .rev {
        color: var(--text-color);
        margin-left: auto;
      }
    }
  }
}
.country-statistic-container {
  font-size: 1.2em;
  font-family: 'Satoshi Variable';
  text-shadow: none;
  position: relative;
  padding: 8px 16px;
  cursor: pointer;
  .country-total {
    font-family: inherit;
    line-height: normal;
  }
  .country-profit {
    font-family: inherit;
    line-height: normal;
    font-size: 90%;
    color: #8d9799;
    text-align: center;
  }
  .tooltip {
    position: absolute;
    visibility: hidden;
    background-color: #333;
    font-size: 0.7em;
    color: #fff;
    padding: 8px;
    border-radius: 4px;
    z-index: 1;
    opacity: 0.85;
    transition: opacity 0.3s ease;
    white-space: nowrap;
    top: 0%;
    left: 0;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    transform: translateY(-100%);
    &::before {
      content: '';
      position: absolute;
      top: 98%;
      left: 20%;
      transform: translateX(-50%);
      border-width: 8px 8px 0 8px;
      border-style: solid;
      border-color: #333 transparent transparent transparent;
    }
  }
  &:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }
}
@media screen and (max-width: 1024px) {
  .container {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    .country-top-tnt-card,
    .country-top-country-card {
      max-width: 100%;
    }
  }
}
@media screen and (max-width: 768px) {
  .container {
    font-size: 12px;
  }
}
