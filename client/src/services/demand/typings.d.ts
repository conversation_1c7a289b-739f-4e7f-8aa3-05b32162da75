/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:59:53
 * @Description:
 */

declare namespace DemandAPI {
  type DemandUser = {
    account_name: string;
    user_id: number;
    status: number;
    api_status: number;
    doc: string;
    token: string;
  };

  type InfoEndpointItem = {
    APAC_Banner_ad_format: number;
    APAC_Banner_connect_timeout: number;
    APAC_Banner_gzip: number;
    APAC_Banner_server_region: number;
    APAC_Banner_socket_timeout: number;
    APAC_Banner_url: string;
    APAC_Native_ad_format: number;
    APAC_Native_connect_timeout: number;
    APAC_Native_gzip: number;
    APAC_Native_server_region: number;
    APAC_Native_socket_timeout: number;
    APAC_Native_url: string;
    APAC_Video_ad_format: number;
    APAC_Video_connect_timeout: number;
    APAC_Video_gzip: number;
    APAC_Video_server_region: number;
    APAC_Video_socket_timeout: number;
    APAC_Video_url: string;
    USE_Banner_ad_format: number;
    USE_Banner_connect_timeout: number;
    USE_Banner_gzip: number;
    USE_Banner_server_region: number;
    USE_Banner_socket_timeout: number;
    USE_Banner_url: string;
    USE_Native_ad_format: number;
    USE_Native_connect_timeout: number;
    USE_Native_gzip: number;
    USE_Native_server_region: number;
    USE_Native_socket_timeout: number;
    USE_Native_url: string;
    USE_Video_ad_format: number;
    USE_Video_connect_timeout: number;
    USE_Video_gzip: number;
    USE_Video_server_region: number;
    USE_Video_socket_timeout: number;
    USE_Video_url: string;
    EUW_Banner_ad_format: number;
    EUW_Banner_connect_timeout: number;
    EUW_Banner_gzip: number;
    EUW_Banner_server_region: number;
    EUW_Banner_socket_timeout: number;
    EUW_Banner_url: string;
    EUW_Native_ad_format: number;
    EUW_Native_connect_timeout: number;
    EUW_Native_gzip: number;
    EUW_Native_server_region: number;
    EUW_Native_socket_timeout: number;
    EUW_Native_url: string;
    EUW_Video_ad_format: number;
    EUW_Video_connect_timeout: number;
    EUW_Video_gzip: number;
    EUW_Video_server_region: number;
    EUW_Video_socket_timeout: number;
    EUW_Video_url: string;
  };
  type DemandListItem = {
    buyer_id: number;
    buyer_name: string;
    integration_type: number;
    status: number;
    api_status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    profit_status: number;
    profit_id: number;
    user_id: number;
    schain_required: number;
    schain_complete: number;
    schain_hops: number;
    pass_display_manager: number;
    display_manager_filter: number;
    idfa_required: number;
    filter_mraid: number;
    demand_account_name: string;
    demand_account_status: number;
    imp_track_type: number;
    native_format: number;
    native_version: number;
    auction_type: number;
    token: string;
    reporting_url?: string;
    max_hm_ivt_ratio: number;
    max_pxl_ivt_ratio: number;
    pixl_ivt_type: number;
    hm_ivt_type: number;
    omid_track: number;
    multi_format: number;
    banner_multi_size: number;
    // 以下为Infotable字段
    auth_seller_id: number;
    auth_seller_name: string;
    dp_id: number;
    partner_id: number;
    partner_name: string;
    cs_domain?: string;
  } & InfoEndpointItem;

  type AddDemandParams = {
    buyer_name: string;
    integration_type: number;
    status: number;
  };

  type DemandEndpointItem = {
    id: number;
    buyer_id: number;
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    qps: number;
    server_region: number;
    ad_format: number;
    status: number;
    create_time: string;
    update_time: string;
  };

  type EndpointItem = {
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    server_region: number;
    ad_format: number;
  };

  type PretargetContentItem = {
    id: number;
    campaign_id: number;
    buyer_id: number;
    level: number;
    content: string;
    create_time: string;
    update_time: string;
  };

  type PretargetCampaignItem = {
    campaign_id: number;
    campaign_name: string;
    buyer_id: number;
    status: number;
    pt_flag: string;
    create_time: string;
    update_time: string;
    items: PretargetContentItem[];
    op_name: string;
    op_id: number;
  };

  type PretargetUpdateItem = {
    campaign_id: number;
    campaign_name: string;
    buyer_id: number;
    items: PretargetContentItem[];
  };

  type PretargetUpdateStatusItem = {
    campaign_id: number;
    buyer_id: number;
    status: number;
  };

  type PretargetEditItemProps = {
    name: string;
    tooltip?: string;
    rightRadioOption?: { label: string; value: number }[];
    rightText?: string;
    bottomType: 'select' | 'checkbox' | 'radio' | 'input' | 'edit' | 'number' | 'selectAll';
    bottomOption?: { label: string; value: string }[];
    defaultValue: {
      level: undefined | number;
      value: string[] | string | number;
    };
    key: string;
    maxTagCount?: number; // select 最大tag数量
    listHeight?: number; // select 下拉框高度
  };

  type PretargetEditItemType = PretargetEditItemProps & {
    onLevelChange?: (key: string, val: number) => void;
    type: 'left' | 'center' | 'right';
    onValueChange: (type: 'left' | 'right' | 'center', key: string, val: any) => void;
    keyValue: string;
  };

  type LeftType = {
    level: number | undefined;
    value: string[] | number[] | string | number;
    label: string;
    key: string;
  };

  type PretargetEditProps = {
    buyer_name: string;
    buyer_id: number | undefined;
    isButton?: boolean;
    svgSrc?: string;
    // svgTooltip?: string;
    leftOption: PretargetEditItemProps[];
    rightOption: PretargetEditItemProps[];
    centerOption: PretargetEditItemProps[];
    campaign_name?: string;
    campaignId?: number;
    op: number;
    // items?: {
    //   name: string;
    //   isDisabled?: boolean;
    //   values: string[] | number[];
    //   type: 'left' | 'right' | 'center';
    //   level: number | undefined;
    // }[];
    fetchData: () => void;
    loading: boolean;
    onVisibleChange?: () => void;
    status?: number;
  };

  type Item = {
    name: string;
    isDisabled?: boolean;
    values: string[] | number[];
    key: string;
    type: 'left' | 'right' | 'center';
    level: number | undefined;
    defaultValues: any;
    minWidth: number;
    tooltip?: string;
  };

  type DataItem = {
    campaignId: number;
    max_price: number;
    min_price: number;
    serverRegion: string;
    campaign_name: string;
    status: number;
    items: Item[];
    buyer_name?: string | undefined;
    buyer_id?: number | undefined;
    update_time: string;
    op_name: string;
  };

  type PretargetTableItem = {
    name: string;
    isDisabled?: boolean;
    values: string[] | number[];
    title?: boolean;
    content?: boolean;
    minWidth?: number;
    maxLength?: number;
  };
}
