.app-left {
  display: flex;
  width: 320px;
  height: 100%;
  flex-direction: column;
  padding-top: 16px;
  padding-right: 5px;
  background-color: #fff;
  border-radius: 6px;
  padding: 0 16px;
  .app-search {
    margin: 16px 0px;
    :global {
      .ant-input-group-addon {
        button {
          padding: 5px 8px;
        }
      }
    }
  }
  .app-item-list {
    flex: 1;
    margin-bottom: 24px;
  }
}

.app-scroll {
  margin-bottom: 12px;
  > div {
    overflow-x: hidden !important;
  }
}

.app-top {
  display: flex;
  padding-top: 16px;
  > div {
    flex: 1;
    margin-right: 8px;
  }
}

.content-empty {
  // border: 1px solid #d9d9d9;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 6px;
  border-radius: 6px;
  .empty-svg svg{
    padding-top: 12px;
  }
  span {
    padding-bottom: 12px;
    color: #8a8a8a;
  }
}