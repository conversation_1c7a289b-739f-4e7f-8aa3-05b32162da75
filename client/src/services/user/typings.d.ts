/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-15 15:16:56
 * @Description:
 */
/* eslint-disable */
// 该文件由 OneAPI 自动生成，请勿手动修改！

declare namespace UserAPI {
  type UserListItem = {
    user_id: number;
    user_token: string;
    tnt_id: number;
    tnt_type: number;
    account_name: string;
    password: string;
    status: number;
    create_time: string;
    update_time: string;
    token: string;
    domain: string;
    cs_domain: string;
    tnt_name: string;
    email: string;
    role: number;
    user_email: string;
    seller_id?: number;
    buyer_id?: number;
    type: number;
    host_prefix: string;
    pv_domain: string;
    atc_model?: number;
    cus_status: number;
    pl_status: number;
    hm_status: number;
    role_id: number;
    btn_access?: string[];
    menu_access?: string[];
    special_user_id?: number;
  };

  type LoginParams = {
    account_name: string;
    password: string;
  };
  type ResetPasswordParams = {
    old_password: string;
    new_password: string;
  };

  type ConfirmPasswordParams = {
    old_password: string;
  };

  type EditUserParams = {
    user_id: number;
    status: number;
    tnt_id: number;
    new_password?: string;
    account_name?: string;
  };

  type EmialParams = {
    account_name: string;
    email: string | string[];
    isResetPwd: boolean;
    password: string;
    send_email: number;
  };
  type BatchEmailParams = {};
  type ForceLogOutParams = {
    user_id: number;
  };
  type vaildPasswordParams = {
    password: string;
  };

  type SwitchAccountParams = {
    switch_user_id: number;
  };

  type UserInfo = {
    user_id: number;
    account_name: string;
    tnt_id: number;
    tnt_name: string;
  };
}
