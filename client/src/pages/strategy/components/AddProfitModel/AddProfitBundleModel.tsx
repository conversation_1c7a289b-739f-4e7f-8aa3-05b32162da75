import BundleTableEdit from '@/components/BundleTableEdit';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { getProfitMinValue, ProfitMaxValue, StatusMap, StatusOptions } from '@/constants';
import { ProfitBundleTypeOptions } from '@/constants/strategy/profit';
import useCustomRequest from '@/hooks/useCustomRequest';
import { addBundleProfit, updateBundleProfit } from '@/services/strategy';
import { isValidBundle } from '@/utils';
import { useModel } from '@umijs/max';
import { Form } from 'antd';
import React, { useEffect } from 'react';

interface Option {
  value: string;
  label: string;
}

interface FormValues {
  buyer_id: number;
  seller_id?: number;
  bundle: string | string[];
  profit_ratio: number;
  status: number;
  type: number;
  id: number;
}

type AddProfitBundleModelProps = {
  data: StrategyAPI.ProfitListItem | null;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadProfit: () => void;
  demandOptions: Option[];
  supplyOptions: Option[];
};

const AddProfitBundleModel: React.FC<AddProfitBundleModelProps> = ({
  data,
  visible,
  isEdit,
  handleClose,
  reloadProfit,
  demandOptions,
  supplyOptions
}) => {
  const [form] = Form.useForm<FormValues>();
  const { initialState } = useModel('@@initialState');
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);

  useEffect(() => {
    if (!visible) return;
    // mount 时，初始化表单数据
    let initialValues: Partial<FormValues> = {
      type: 4
    };

    if (isEdit) {
      initialValues = {
        ...initialValues,
        ...data
      };
    }

    form.setFieldsValue(initialValues);
  }, [isEdit, data, visible]);

  // 编写两个接口的成功逻辑：编辑和新增，在成功后关闭modal，刷新列表
  const handleSuccess = (res: any) => {
    form.resetFields();
    handleClose();
    reloadProfit();
  };

  const { run: addBundleProfitRun, loading: addLoading } = useCustomRequest(addBundleProfit, {
    onSuccess: handleSuccess
  });

  const { run: updateBundleProfitRun, loading: updateLoading } = useCustomRequest(updateBundleProfit, {
    onSuccess: handleSuccess
  });

  // 表单提交
  // 校验参数，接口请求
  const handleConfirm = async () => {
    // 两种逻辑：编辑和新增，接口是不一样的，参数只做常规的校验
    const values = await form.validateFields();
    // 去除 values 对象 value 为 undefined 或者 null 的属性
    const filteredValues = Object.fromEntries(
      Object.entries(values).filter(([_, value]) => value !== undefined && value !== null)
    );

    if (isEdit) {
      updateBundleProfitRun(filteredValues);
    } else {
      addBundleProfitRun(filteredValues);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    handleClose();
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Bundle Profit` : `Add Bundle Profit`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleCancel}
      loading={addLoading || updateLoading}
      maskClosable={true}
    >
      <Form autoComplete="off" form={form} layout="vertical">
        <Form.Item
          name="type"
          label="Type:"
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <NormalSelect options={ProfitBundleTypeOptions} disabled={isEdit} />
        </Form.Item>
        {/* 隐藏属性 id，编辑时携带 */}
        <Form.Item name="id" hidden>
          <InputNumber />
        </Form.Item>
        <Form.Item
          name="buyer_id"
          label="Advertiser:"
          rules={[
            {
              required: true,
              message: 'Please Select Advertiser'
            }
          ]}
        >
          <NormalSelect options={demandOptions} disabled={isEdit} showSearch />
        </Form.Item>
        {/* 编辑状态，且 type 为 4 时 隐藏 */}
        <Form.Item noStyle dependencies={['type']}>
          {({ getFieldValue }) => {
            const type = getFieldValue('type');
            if (type === 4) {
              return null;
            }
            return (
              <Form.Item
                name="seller_id"
                label="Publisher:"
                rules={[
                  {
                    required: true,
                    message: 'Please Select Publisher'
                  }
                ]}
              >
                <NormalSelect options={supplyOptions} disabled={isEdit} allowClear showSearch />
              </Form.Item>
            );
          }}
        </Form.Item>
        {/* 
          1. bundle 填入，输入框或文本框
          2. 创建时是支持批量的，编辑时不支持
          3. 编辑时禁用的
        */}
        <Form.Item
          name="bundle"
          label="Bundle:"
          rules={[
            {
              required: true,
              message: 'Please Select Bundle'
            }
          ]}
        >
          {isEdit ? (
            <NormalInput placeholder="Please Input Bundle" disabled={isEdit} allowClear />
          ) : (
            <BundleTableEdit
              editTitle="Edit Bundle"
              editTips="Enter the bundle (one per line)"
              deleteAllTitle="Delete All Bundle"
              deleteAllTips="Are you sure to delete all bundle?"
              contentMaxHeight={'calc(20vh)'}
              editSingle={true}
              validator={(val: string) => isValidBundle(val)}
            />
          )}
        </Form.Item>
        {/* profit_radio */}
        <Form.Item noStyle dependencies={['status']}>
          {({ getFieldValue }) => {
            const status = getFieldValue('status');
            return (
              <Form.Item
                name="profit_ratio"
                label="Profit Ratio:"
                rules={[
                  {
                    required: true,
                    message: 'Please Input Profit Ratio'
                  }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={profitMinValue}
                  max={ProfitMaxValue}
                  controls
                  type="number"
                  addonAfter="%"
                  disabled={status === StatusMap.Paused}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        {/* 编辑时支持 status 修改 */}
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddProfitBundleModel;
