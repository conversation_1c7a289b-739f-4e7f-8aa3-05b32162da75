# 1 AI Suggestions

- Optimization suggestions are automatically generated by RixEngine SaaS machine learning models after analyzing the performance data.
- Currently, the optimization suggestions are provided around 4 KPIs:「Revenue」「IVT」「QPS」「Cap」. You can click on the envelope icon on the top right corner of the console to check the latest notifications, or enter the 「AI Suggestions」module on the left navigation panel. 

![img](https://static.rixengine.com/a/platform/help/ai-guide/1-1.png)

## 1.1 Revenue

Notifications are generated automatically at 1:00 (UTC+0) every day based on the revenue performance of the previous day. Below are the different types of notifications you might receive:

**Type 1：【Rev】Advertiser has no filling**

 Advertiser has no filling, Advertiser=[Example-ADV(001)], From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

This notification means inventory sent to Advertiser Example-ADV from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00 is not filled at all.

**Suggestions：**

- Contact Advertiser Example-ADV to investigate the root cause of 0% fill rate.
- If the Advertiser's budget is not ready, stop sending inventory to Advertiser Example-ADV to save IDC cost. 

**Type 2：【Rev】Overall revenue has dropped**

 Overall revenue has dropped, Rev Diff($)=[1000], Rev Diff(%)=[50], From=[YYyy-Mm-Dd 02:00:00 ~ YYYY-MM-DD 01:00:00], To=[YYYY-MM-DD 02:00:00 ~ yyyy-mm-dd 01:00:00]

This notification means overall revenue dropped significantly compared to previous day from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00.

**Suggestions：**

- Check the performance of the past few days from coarse to coarse (such as: Certain Advertisers -> Country -> Ad Size -> Bundle) to identify where the drop is focused. Adjust inventory allocation strategies based on demand.
- Focus on the performance data of your top demand and supply partners, reach out to them if inventory or demand is adjusted.

## 1.2 IVT

Notifications are generated automatically (If you have enabled IVT detection) at 8:00 (UTC+0) every day based on IVT results of the previous day. Below are the different types of notifications you might receive:

**Type 1：【IVT】Pixalate scanning has high invalid traffic**

 pixalate scanning has high invalid traffic, Advertiser=[Example-ADV(001)], IVT(%)=[20], Rev($)=[1000], Date=[YYYY-MM-DD]

This notification means inventory sent to Example-ADV has high IVT on YYYY-MM-DD.

**Suggestions：**

- Determine if you need to reduce or block inventory for high IVT inventory. 1) If demand partner is very strict in traffic quality, you should block high IVT bundles timely and inform publishers to optimize. 2) If demand partner has no requirement on traffic quality, then you can continue monitoring.

## 1.3 QPS

Notifications are generated automatically at 1:00 (UTC+0) every day based on the inventory distribution of the previous day. Below are the different types of notifications you might receive:

**Type 1：【QPS】Advertiser QPS is highly blocked**

 Advertiser=[Example-ADV(001)], Region=[APAC], ECPR=[10], Block QPS=[100] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

This notification means high-value inventory is not sent to Advertiser Example-ADV successfully because of your QPS limit for this Advertiser from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00.


**Suggestions：**

- Check if you have set a too low QPS limit for Advertiser Example-ADV and increase the QPS limit accordingly. Adjusted QPS = Original QPS + block_qps + 100 (or 200). Try to increase QPS incrementally and smoothly (e.g. 1000->1500->2000), to avoid your demand partners' system being impacted by a surge of QPS.
- Once QPS is increased, inform your Advertiser Example-ADV about the increased QPS, in case your Advertiser has a QPS limit on their end.

**Type 2：【QPS】Advertiser QPS was set too high**

 Advertiser=[Example-ADV(001)], Region=[APAC], ECPR=[0.1], Current config QPS=[1000], Real QPS=[900] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

This notification means a large amount of low-value inventory is sent to Example-ADV from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00.

**Suggestions：**

- Contact Advertiser Example-ADV to understand their budget requirements, optimize Pretargeting to match inventory with their demand.
- Lower down the QPS limit of Advertiser Example-ADV to save IDC cost. Try to lower down the volume incrementally and smoothly (e.g. 1000->600->300), to avoid your demand partners' system being impacted by a sudden drop of QPS.

**Type 3：【QPS】Publisher QPS is highly blocked**

 Publisher=[Example-PUB(001)], Region=[APAC], ECPR=[10], Block QPS=[100] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

This notification means high-value inventory from Publisher Example-PUB is not sent to demand from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00 because of the QPS limit configured for Publisher.

**Suggesstions：**

- Increase the Publisher QPS limit to facilitate a larger flow of high-value inventory, to increase revenue. Adjusted QPS = Original QPS + block_qps + 100 (or 200). Try to increase QPS incrementally and smoothly (e.g. 1000->1500->2000), to avoid advertising system being impacted by a surge of QPS.
- Once QPS is increased, inform your Publisher Example-PUB about the increased QPS, in case your Publisher applies QPS limit on their end.

**Type 4：【QPS】Publisher QPS was set too high**

 Publisher=[Example-PUB(001)], Region=[APAC], ECPR=[0.1], Current config QPS=[1000], Real QPS=[900] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

 This notification means a large amount of low-value inventory is received from Example-PUB from YYYY-MM-DD 02:00:00 to yyyy-mm-dd 01:00:00.

**Suggestions：**

- Contact Publisher to optimize their inventory structure, to send inventory that matches with your demand.
- Lower down the QPS limit of Publisher Example-PUB to save IDC cost. Try to lower down the volume incrementally and smoothly (e.g. 1000->600->300), to avoid advertising system being impacted by a sudden drop of QPS.

## 1.4 Cap

Notifications are generated automatically when the configured Cap value is reached. Below are the different types of notifications you might receive:

**Type 1：【Cap】Daily cap Reached**

 Daily cap Reached, Advertiser=[Example-ADV(001)], Publisher=[Example-PUB(002)], Rev Cap=[100], Imp Cap=[10000], Time=[YYYY-MM-DD  hh:mm:ss]

This notification means the daily revenue cap or impression cap between Advertiser Example-ADV and Publisher Example-PUB has been reached since YYYY-MM-DD hh:mm:ss.

**Suggestions：**

- Consisder increasing or lifting daily cap when suitable

