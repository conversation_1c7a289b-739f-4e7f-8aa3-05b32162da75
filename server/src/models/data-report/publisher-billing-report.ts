/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-06-28 15:06:38
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 11:29:19
 * @Description:
 */

import { bigqueryStream } from '@/db/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

// function leftJoinPartner(condition: string) {
//   const sql = `
//     select
//         seller_id,
//         partner_id,
//         partner_name
//       from saas-373106.saas_others.seller_partner
//       ${condition ? `where ${condition}` : ''}
//   `;
//   return sql;
// }

// function joinSql(params: BillingAPI.QueryInnerParams) {
//   const {
//     dimensions,
//     group_dimension,
//     condition,
//     leftJoinCond,
//     date_time_str,
//     isBuyer
//   } = params;
//   const buyer_request = 'sum(buyer_request) as request';
//   const seller_request = 'sum(buyer_request) as request';
//   const sql = `
//      select
//       ${date_time_str ? `${date_time_str},` : ''}
//       ${dimensions ? `${dimensions},` : ''}
//       ${isBuyer ? buyer_request : seller_request},
//       sum(impression) as impression,
//       sum(seller_impression) as seller_payment_impression,
//       sum(seller_total_request) as total_request,
//       sum(seller_net_revenue) as seller_net_revenue,
//       sum(buyer_net_revenue) as buyer_net_revenue,
//        round((coalesce(sum(buyer_net_revenue), 0) - coalesce(sum(seller_net_revenue), 0)), 2) as profit,
//       (case when sum(buyer_net_revenue) = 0 then 0 else round(cast ((sum(buyer_net_revenue) - sum(seller_net_revenue)) * 100*1.0 / sum(buyer_net_revenue) as numeric), 2) end) as profit_rate
//     from ${ReportSchema}.billing_overview_report as i
//     left join (${leftJoinPartner(
//       leftJoinCond
//     )}) as j on i.seller_id = j.seller_id
//     ${condition ? `where ${condition}` : ''}
//     ${group_dimension ? `group by ${group_dimension}` : ''}
//   `;
//   return sql;
// }

class PubBillingModel {
  // /**
  //  * @deprecated 使用 queryBQDataWithCache 代替
  //  */
  // async getPublisherBillingList(
  //   options: BillingAPI.QueryInnerParams,
  //   api_url: string
  // ) {
  //   const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
  //   return await queryStackOverflow(sql, { tag: api_url });
  // }

  async newDownloadAllReport(sql: string, labels: LabelGenerationParams) {
    return await bigqueryStream(sql, labels);
  }

  // async downloadAllReport(
  //   options: BillingAPI.QueryInnerParams,
  //   api_url: string
  // ) {
  //   const sql = `${joinSql(options)} ${options.order}`;
  //   return await bigqueryStream(sql, { tag: api_url });
  // }

  // /**
  //  * @deprecated 使用 newCountList 代替
  //  */
  // async countList(options: BillingAPI.QueryInnerParams, api_url: string) {
  //   const countSql = `select count(*) as count from(${joinSql(options)}) as t`;
  //   const res = await queryStackOverflow(countSql, { tag: api_url });
  //   if (Array.isArray(res) && res.length) {
  //     return res[0];
  //   }
  //   return { count: 0 };
  // }

  // async newCountList(sql: string, tag: LabelGenerationParams) {
  //   const countSql = `select count(*) as count from(${sql}) as t`;
  //   const res = await queryBQDataWithCache(countSql, tag);
  //   return res?.[0]?.count || 0;
  // }
}

export const pubBillingModel = new PubBillingModel();
