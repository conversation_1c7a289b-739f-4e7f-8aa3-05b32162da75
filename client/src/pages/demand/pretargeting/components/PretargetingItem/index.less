.pretargeting-item-container {
  margin-bottom: 16px;
  border-bottom: 1px solid #eef0f0;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  // border-right: 1px solid #EEF0F0;
}

.pretarget-table {
  overflow-x: auto;
  border: 1px solid #eef0f0;
  border-radius: 6px;
  overflow-y: hidden;
  display: flex;
  align-items: center;
  .pretarget-table-item {
    border-right: 1px solid #eef0f0;
    > div {
      display: inline-block;
      width: 100%;
    }
  }
  .pretarget-table-title {
    display: inline-block;
    padding: 0 12px;
    border-bottom: 1px solid #eef0f0;
    height: 39px;
    line-height: 39px;
    color: #909999;
    font-weight: 700;
    background: #f8fafa;
  }
}

// 组件 PretargetingItemTitle
.pretargeting-item-title {
  display: grid;
  grid-template-columns: minmax(180px, auto) auto;
  grid-template-areas: 
    "title tools"
    "status status"
    "info info";
  grid-gap: 8px;
  align-items: center;
  padding-top: 16px;
  padding-bottom: 12px;
  font-size: 12px;

  @media screen and (min-width: 768px) {
    grid-template-columns: minmax(180px, auto) 1fr auto;
    grid-template-areas: 
      "title status tools"
      "info info .";
  }

  @media screen and (min-width: 1200px) {
    grid-template-columns: minmax(180px, auto) auto 1fr auto;
    grid-template-areas: "title status info tools";
    font-size: 13px;
    grid-gap: 16px;
  }

  // 名称区域
  .campaign-name {
    grid-area: title;
    font-size: 14px;
    font-weight: 700;
    color: var(--text-color);
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // 状态区域
  .status-container {
    grid-area: status;
    .status-tags {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-tag {
      width: max-content;
      color: #80848e;
      padding: 2px 6px 1px;
      display: inline-block;
      color: #ffffff;
      vertical-align: middle;
      border-radius: 2px;
    }
    .active-status {
      background: #1caf34;
    }
    .paused-status {
      background: #e1472e;
    }
  }

  // 信息区域
  .info-container {
    grid-area: info;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;

    .info-item {
      display: flex;
      align-items: center;
      color: var(--text-color);
      .operator-name {
        margin-left: 8px;
        display: inline-block;
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        --webkit-line-clamp: 1;
        color: var(--primary-color);
      }
      .modify-time {
        margin-left: 8px;
        color: var(--primary-color);
      }
    }
  }

  // 工具区域
  .actions-toolbar {
    grid-area: tools;
    color: #606666;
    font-size: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    align-items: center;
    min-width: 100px;
    span {
      &:last-child {
        svg {
          &:hover {
            color: #ff4d4f;
          }
        }
      }
    }
    svg {
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
