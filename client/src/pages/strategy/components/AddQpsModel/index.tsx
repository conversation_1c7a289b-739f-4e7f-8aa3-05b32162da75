/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 11:48:30
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import { addQps, updateQps } from '@/services/api';
import { fetchData, handleFilterSelect, isValidBundle } from '@/utils';
import { handleFilterNumber } from '@/utils/qps';
import Select from '@/components/Select/NormalSelect';
import { QpsLevelType, RegionListOptions, QpsAdFormatToLabel } from '@/constants/strategy/qps';
import { QpsAdFormatOptions } from '@/constants/strategy/qps';
import NormalRadio from '@/components/Radio/NormalRadio';
import BundleTableEdit from '@/components/BundleTableEdit';
import { QPSOptions } from '@/constants/strategy/qps';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { Countries, CountryOptions } from '@/constants/global-mapping/country';
import { useAccess } from '@umijs/max';
import NormalAutoComplete from '@/components/AutoComplete';

type AddQpsModelProps = {
  qps?: StrategyAPI.QpsListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadQps: () => void;
  supplyList: SupplyAPI.SupplyListItem[];
  demandList: DemandAPI.DemandListItem[];
  QpsLevelOptions: StrategyAPI.SelectOptionsType[];
};

const DefaultFormData = {
  level: 2,
  buyer_id: undefined,
  seller_id: undefined,
  app_id: undefined,
  plm_id: undefined,
  status: 1,
  qps: undefined
};

const AddQpsModel: React.FC<AddQpsModelProps> = ({
  qps,
  handleClose,
  visible,
  supplyList,
  isEdit,
  reloadQps,
  demandList,
  QpsLevelOptions
}) => {
  const access = useAccess();
  const [qpsOptions, setQpsOptions] = useState<StrategyAPI.SelectOptionsType[]>(QPSOptions);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [level, setLevel] = useState(QpsLevelOptions[0].value as number);
  const [appList, setAppList] = useState<AppAPI.AppListItem[]>([]);
  const [appId, setAppId] = useState<number | undefined>();
  const [placementList, setPlacementList] = useState<AppAPI.PlacementListItem[]>([]);

  useEffect(() => {
    if (visible) {
      form.setFieldValue('level', QpsLevelOptions[0].value);
      form.setFieldValue('bundle', []);
      setLevel(QpsLevelOptions[0].value as number);
      if (!access.isRixSystemUser && !access.isStreamLAuth) {
        let maxValue = 5000;
        // 取消 isStreamLAuth 的限制
        if (access.isBidedgeAuth) {
          maxValue = 40000;
        }
        const qpsOptions = QPSOptions.filter(item => item.value <= maxValue);
        setQpsOptions(qpsOptions);
      }
    }
    if (qps && isEdit && visible) {
      if (
        [
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['demand + ad_format'],
          QpsLevelType['demand + supply + ad_format']
        ].includes(qps.level)
      ) {
        const values = qps.ots_id.split(',').map((val: string) => QpsAdFormatToLabel[val]);
        form.setFieldsValue({ ad_format: values });
      }
      if (
        [
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['demand + country'],
          QpsLevelType['demand + supply + country']
        ].includes(qps.level)
      ) {
        const values = qps.ots_id.split(',').map((val: string) => Countries[val]);
        form.setFieldsValue({ country: values });
      }
      if (QpsLevelType['demand + bundle'] === qps.level) {
        form.setFieldsValue({ bundle: qps.ots_id });
      } else {
        const values = qps.ots_id.split(',');
        form.setFieldsValue({ bundle: values });
      }
      form.setFieldValue('bundle', qps.ots_id?.split(',') || []);
      const params = {
        ...qps,
        app_id: `${qps.app_name}(${qps.app_id})`,
        plm_id: `${qps.plm_name}(${qps.plm_id})`
      };
      form.setFieldsValue(params);
      setLevel(qps.level);
    }
  }, [qps, isEdit, visible]);

  useEffect(() => {
    if (appList && appId && appList.length) {
      const find = appList.find(item => item.app_id === appId);
      setPlacementList(find?.adslots || []);
    } else {
      setPlacementList([]);
    }
  }, [appId, appList]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    const { level, buyer_id, status, qps: num, bundle, region, ad_format, country, seller_id } = values;
    const params: any = {
      buyer_id: level !== QpsLevelType.supply ? buyer_id : 0,
      pub_id: seller_id,
      qps: parseInt(num),
      level: level,
      region
    };
    if (isEdit) {
      if (
        [
          QpsLevelType['(unlimit)supply + bundle'],
          QpsLevelType['demand + supply-bundle'],
          QpsLevelType['demand + bundle']
        ].includes(level)
      ) {
        const ots_id_Array = bundle || [];
        // 数组裁需要排序
        const ots_id = Array.isArray(ots_id_Array) ? ots_id_Array.sort().join(',') : ots_id_Array;
        params.ots_id = ots_id;
      } else if (
        [
          QpsLevelType['demand + supply + ad_format'],
          QpsLevelType['demand + supply + country'],
          QpsLevelType['demand + ad_format'],
          QpsLevelType['demand + country'],
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['(unlimit)supply + ad_format'],
        ].includes(level)
        && qps?.ots_id
      ) {
        params.ots_id = qps.ots_id;
      }
      handleEditQps({ ...params, id: qps?.id, status, ori_data: qps || {}});
    } else {
      const ots_id_Array = bundle || ad_format || country || [];
      const ots_id = Array.isArray(ots_id_Array) ? ots_id_Array.sort().join(',') : ots_id_Array;
      handleAddQps({ ...params, ots_id: ots_id });
    }
  };

  const onSuccess = (data: [] | string | undefined) => {
    message.success(typeof data === 'string' ? data : 'success');
    handleClose();
    reloadQps();
  };

  const onCancel = () => {
    handleClose();
  };

  const handleAddQps = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addQps, params, onSuccess });
  };

  const handleEditQps = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateQps, params });
  };

  const handleResetAll = () => {
    form.resetFields(['buyer_id', 'seller_id', 'app_id', 'plm_id']);
    setAppId(undefined);
    setAppList([]);
    setPlacementList([]);
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.bundle) {
      setPlacementList([]);
      setAppId(changeValue.app_id);
    }
    // 重置数据
    if (changeValue.level) {
      handleResetAll();
      setLevel(changeValue.level);
    }
  };

  const afterOpenChange = (open: boolean) => {
    !open && form.resetFields();
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit QPS` : `Create QPS`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
      afterOpenChange={afterOpenChange}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
      >
        <Form.Item
          name="level"
          label="Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <Select options={QpsLevelOptions} allowClear={false} disabled={isEdit} />
        </Form.Item>
        {![
          QpsLevelType.supply,
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['(unlimit)supply + bundle'],
          QpsLevelType['(unlimit)supply + country']
        ].includes(level) && (
          <Form.Item
            name="buyer_id"
            label="Advertiser:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Advertiser'
              }
            ]}
          >
            <Select
              placeholder="Please Select Advertiser"
              disabled={isEdit}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
            >
              {demandList &&
                demandList.map((item: DemandAPI.DemandListItem, index: number) => {
                  return (
                    <Select.Option value={item.buyer_id} key={index}>
                      {item.buyer_name}({item.buyer_id})
                    </Select.Option>
                  );
                })}
            </Select>
          </Form.Item>
        )}
        {![
          QpsLevelType.demand,
          QpsLevelType['demand + ad_format'],
          QpsLevelType['demand + country'],
          QpsLevelType['demand + bundle']
        ].includes(level) && (
          <Form.Item
            name="seller_id"
            label="Publisher:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Publisher'
              }
            ]}
          >
            <Select
              placeholder="Please Select Publisher"
              disabled={isEdit}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
            >
              {supplyList &&
                supplyList.map((item: SupplyAPI.SupplyListItem, index: number) => {
                  return (
                    <Select.Option
                      value={item.seller_id}
                      key={index}
                      disabled={item.status === DemandAndSupplyStatusMap.Testing}
                    >
                      {item.seller_name}({item.seller_id})
                    </Select.Option>
                  );
                })}
            </Select>
          </Form.Item>
        )}
        {level === QpsLevelType['demand + supply-unit'] && (
          <Form.Item
            name="plm_id"
            label="Ad Unit"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Ad Unit'
              }
            ]}
          >
            <Select
              placeholder="Please Select Ad Unit"
              disabled={isEdit}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
            >
              {placementList.map((item, index) => (
                <Select.Option value={item.plm_id} key={index}>
                  {item.plm_name}({item.plm_id})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
        {[
          QpsLevelType['demand + supply-bundle'],
          QpsLevelType['(unlimit)supply + bundle'], 
          QpsLevelType['demand + bundle']
        ].includes(level) && (
          <Form.Item
            name="bundle"
            label="Bundle:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Bundle'
              }
            ]}
          >
            <BundleTableEdit
              editTitle="Edit Content"
              editTips="Enter the content(one per line)"
              deleteAllTitle="Delete All Content"
              deleteAllTips="Are you sure to delete all content?"
              editSingle={true}
              validator={(val: string) => isValidBundle(val)}
            />
          </Form.Item>
        )}
        {[
          QpsLevelType['demand + supply + ad_format'],
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['demand + ad_format']
        ].includes(level) && (
          <Form.Item
            name="ad_format"
            label="Ad Format:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Ad Format'
              }
            ]}
          >
            <Select
              placeholder="Please Select Ad Format"
              disabled={isEdit}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
              mode="multiple"
            >
              {QpsAdFormatOptions.map(item => (
                <Select.Option value={item.value} key={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
        {[
          QpsLevelType['demand + supply + country'],
          QpsLevelType['demand + country'],
          QpsLevelType['(unlimit)supply + country']
        ].includes(level) && (
          <Form.Item
            name="country"
            label="Country:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Country'
              }
            ]}
          >
            <Select
              disabled={isEdit}
              showSearch
              filterOption={handleFilterSelect}
              mode="multiple"
              options={CountryOptions}
            />
          </Form.Item>
        )}
        <Form.Item
          name="region"
          label="Server Region:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Server Region'
            }
          ]}
        >
          <Select
            placeholder="Please Select Server Region"
            disabled={isEdit}
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            {RegionListOptions.map((item) => (
              <Select.Option value={item.value} key={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {level !== QpsLevelType['(unlimit)supply + bundle'] &&
          level !== QpsLevelType['(unlimit)supply + country'] &&
          level !== QpsLevelType['(unlimit)supply + ad_format'] && (
            <Form.Item
              name="qps"
              label="QPS:"
              validateTrigger={['onChange', 'onBlur']}
              validateFirst
              rules={[
                {
                  required: true,
                  validator: (rule, val) => {
                    const value = Number(val);
                    const valueInt = parseInt(val);
                    if (isNaN(value) || value !== valueInt) {
                      return Promise.reject('Please Input Number');
                    }
                    const minValue = 1;
                    let maxValue = 5000;
                    if (access.isRixSystemUser || access.isStreamLAuth) {
                      maxValue = 80000;
                    } else if (access.isBidedgeAuth) {
                      maxValue = 40000;
                    }
                    if (!value) {
                      return Promise.reject('Please Select or Input QPS');
                    }
                    if (value < minValue) {
                      return Promise.reject(`PS must between ${minValue} and ${maxValue}`);
                    }
                    if (value > maxValue) {
                      return Promise.reject(
                        `The qps value has exceeded ${maxValue}. Please contact the super administrator for configuration.`
                      );
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <NormalAutoComplete
                options={qpsOptions}
                placeholder="Please Select QPS"
                allowClear
                filterOption={handleFilterNumber}
              />
            </Form.Item>
          )}
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddQpsModel;
