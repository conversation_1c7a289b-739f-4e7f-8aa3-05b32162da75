/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-20 11:37:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-05 16:38:35
 * @Description:
 */

// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
// constants
import { ExportStatusDesc, ExportStatusMap, ExportStatusOptions } from '@/constants';

// ?utils
import { objectFlip } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import StatusTag from '@/components/Tag/StatusTag';
import moment from 'moment-timezone';

export const ExportBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'Export Log'
  }
];

export const ExportType = {
  1: 'Full Reporting',
  2: 'Advertiser Billing Reporting',
  3: 'Publisher Billing Reporting'
  // 4: 'Advertiser Reporting',
  // 5: 'Publisher Reporting'
};
export const ExportTypeOptions = [
  { label: 'Full Reporting', value: 1 },
  { label: 'Advertiser Billing Reporting', value: 2 },
  { label: 'Publisher Billing Reporting', value: 3 }
  // { label: 'Advertiser Reporting', value: 4 },
  // { label: 'Publisher Reporting', value: 5 }
];
export const ExportTypeDesc = objectFlip(ExportType);
export const ExportSearchOptions: TopBarSearchItem[] = [
  {
    name: 'Export From',
    type: 'select',
    key: 'type',
    mode: 'multiple',
    value: [],
    options: ExportTypeOptions
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: ExportStatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const ExportColumns: ColumnProps<DashboardAPI.ExportedReportItem>[] = [
  {
    title: 'Name',
    dataIndex: 'name',
    width: 220
  },
  {
    title: 'Export From',
    dataIndex: 'type_desc',
    width: 120
  },
  {
    title: 'Status',
    width: 80,
    dataIndex: 'status',
    render: (_, row) => {
      const diffTime = moment().diff(moment(row.create_time), 'minutes');
      let status = row.status;
      let err_msg = row.err_msg;
      let type = row.status_desc.toLowerCase();
      // 15分钟后，如果还是creating状态，视为任务failed
      if (diffTime > 15 && row.status === ExportStatusMap.Creating) {
        status = ExportStatusMap.Failed;
        type = 'failed';
        err_msg = 'Task timeout, please try again later';
      }
      return (
        <div>
          <StatusTag type={type}>{ExportStatusDesc[status]}</StatusTag>
          {status === 4 ? (
            <Tooltip title={err_msg}>
              <ExclamationCircleOutlined style={{ color: '#FA5A42', marginLeft: 8, cursor: 'pointer' }} />
            </Tooltip>
          ) : null}
        </div>
      );
    }
  },
  {
    title: 'Created At',
    dataIndex: 'create_time',
    width: 120
  }
];
