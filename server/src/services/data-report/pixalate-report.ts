/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-10-10 10:20:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-22 18:04:29
 * @Description:
 */
import { pixalateModel } from '@/models';
import { joinQueries } from '@/utils/params';
import {
  PixalateDimensions,
  PixalateMetricOrderKey
} from '@/constants/report/pixalate-report';
import { DashboardAPI } from '@/types/dashboard';
import { getOrderBy } from '@/utils/report';

class PixalateReport {
  getReportParams(formData: DashboardAPI.QueryPixalateReportParams) {
    const {
      start_date,
      end_date,
      start,
      end,
      order,
      dimension = [],
      tnt_id
    } = formData;
    let { order_key = [] } = formData;
    const normal_dimension = PixalateDimensions.filter((v) => dimension.includes(v));
    if (Array.isArray(order_key)) {
      order_key = order_key.filter(
        (v) => normal_dimension.includes(v) || PixalateMetricOrderKey.includes(v)
      );
    }
    const condition = joinQueries(
      {
        numberCols: ['seller_id', 'buyer_id'],
        arrayQueries: [
          'seller_id',
          'buyer_id',
          'app_bundle_id',
          'fraud_type',
          'country'
        ],
        extra: `day >= '${start_date}' and day <= '${end_date}' and tnt=${tnt_id}`
      },
      formData
    );
    const limit = `limit ${end - start} offset ${start}`;
    const order_str = getOrderBy(order_key, order, {
      isEmptyStr: true,
      isDefault: false
    }); 
    const dimension_arr = normal_dimension.map((v) => {
      if (v === 'month') {
        return `FORMAT_DATETIME('%Y-%m', PARSE_DATE('%Y%m%d', day)) AS month`;
      } else if (v === 'day') {
        return `format_date('%F', PARSE_DATE('%Y%m%d',day)) as day`;
      }
      return v;
    });
    const splitDemand = normal_dimension.includes('buyer_id');
    const splitSupply = normal_dimension.includes('seller_id');
    return {
      limit,
      order: order_str,
      condition,
      normal_dimension_str: dimension_arr.join(','),
      dimension: normal_dimension.join(','),
      splitDemand,
      splitSupply
    };
  }

  async getPixalateReport(option: any, api_url: string) {
    const result = await pixalateModel.getPixalateReport(option, api_url);
    let totalNum = 0;
    const data = result.map(({ total, ...rest }, index: number) => {
      if (index === 0) {
        totalNum = total;
      }
      if (rest.publisher_id === null) {
        rest.publisher_id = '-';
      }
      return { ...rest, id: index };
    });

    return {
      data,
      total: totalNum
    };
  }

  async downloadPixalateReport(options: any, api_url: string) {
    return await pixalateModel.downloadPixalateReport(options, api_url);
  }
}

export const pixalateService = new PixalateReport();
