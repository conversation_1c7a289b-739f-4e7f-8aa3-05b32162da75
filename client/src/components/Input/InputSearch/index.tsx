/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-12 16:19:08
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-18 17:03:53
 * @Description
 */
import { Input } from 'antd';
import type { SearchProps } from 'antd/lib/input/Search';
import { useRequest } from 'ahooks';
import styles from './index.less';

type InputSearchProps = SearchProps & {
  handleSearch: (val: string) => void;
  onValueChange?: (val: string) => void;
}

export default function InputSearch({ handleSearch, onValueChange, ...props }: InputSearchProps): JSX.Element {
  // @ts-ignore
  const { run } = useRequest((e) => {
    handleSearch(e);
  }, {
    debounceWait: 500
  });

  const handleSearchInput = (val: string) => {
    onValueChange && onValueChange(val);
    run(val);
  };

  return <Input.Search
    enterButton
    allowClear
    {...props}
    onSearch={(value) => handleSearchInput(value)}
    onChange={(e) => handleSearchInput(e.target.value)}
    className={styles['input-search-container']}
  />;
};

