import React, { useEffect, useState } from 'react';
import { useAccess } from 'umi';
import { Card, Button, message, Tooltip } from 'antd';
import styles from './index.less';
import AuthorizationModal from '@/components/TransferModal';
import { DemandAndSupplyStatusDesc, DemandAndSupplyStatusMap, AuthLevel as Level } from '@/constants';
import { setSupplyAuth } from '@/services/api';
import { parse } from 'query-string';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { StatusMap } from '@/constants';
import { AuthorizationColumns } from '@/constants/supply/supply-columns';
import { fetchData as httpFetchData } from '@/utils';
import OriginalTable from '@/components/Table/OriginalTable';
import useTableConstant from '@/hooks/useTableConstant';
import Modal from '@/components/Modal/NormalModal';
import InputSearch from '@/components/Input/InputSearch';
import NormalTitle from '@/components/NormalTitle';

type AuthorizationParmas = {
  loading: boolean;
  supplyDemandList: SupplyAPI.SellerDemandAuth[];
  supplyData: SupplyAPI.SupplyListItem | undefined;
  fetchData: () => void;
  demandLoading: boolean;
  demandList: DemandAPI.DemandListItem[];
  auth?: string;
};

const AuthorizationTable: React.FC<AuthorizationParmas> = ({
  loading,
  supplyDemandList,
  supplyData,
  fetchData,
  demandLoading,
  demandList: dataSource,
  auth
}) => {
  const access = useAccess();
  const { selectedRowKeys, setSelectedRowKeys, setSelectedRow, handleRowChange, handleRowClick } =
    useTableConstant<SupplyAPI.SellerDemandAuth>({
      dataSource: supplyDemandList,
      key: 'buyer_id',
      loading: loading || demandLoading,
      reload: fetchData
    });
  const { id } = parse(location.search);
  const [visible, setVisible] = useState(false);
  const [authList, setAuthList] = useState<string[]>([]);
  const [demandList, setDemandList] = useState<any[]>([]);
  const [editLoading, setEditLoading] = useState(false);
  const [demandActiveList, setDemandActiveList] = useState<SupplyAPI.SellerDemandAuth[]>([]);
  const [clearAllTag, setClearAllTag] = useState(false);
  const [filterList, setFilterList] = useState<SupplyAPI.SellerDemandAuth[]>([]);

  useEffect(() => {
    if (supplyDemandList && supplyDemandList.length) {
      // 去掉paused的授权
      const active = supplyDemandList.filter(item => item.status !== StatusMap.Paused);
      const list = active.map(item => `${item.buyer_id}`);
      setDemandActiveList(active);
      setAuthList(list);
    } else {
      setDemandActiveList([]);
      setAuthList([]);
    }
  }, [supplyDemandList]);

  useEffect(() => {
    const selecteds: any[] = selectedRowKeys.filter(v => {
      return filterList.some(item => item.buyer_id === v);
    });
    setSelectedRowKeys(selecteds);
    if (selecteds.length === 0) {
      setSelectedRow(selecteds[0]);
    }
  }, [filterList]);

  useEffect(() => {
    setFilterList(demandActiveList);
  }, [demandActiveList]);

  useEffect(() => {
    // 去掉paused的demand
    if (dataSource && dataSource.length) {
      const list = dataSource
        .filter((item: DemandAPI.DemandListItem) => item.status !== StatusMap.Paused)
        .map((item: DemandAPI.DemandListItem) => {
          return {
            ...item,
            key: `${item.buyer_id}`,
            buyer_id: `${item.buyer_id}`
          };
        });
      setDemandList(list);
    }
  }, [dataSource]);

  const handleSearch = (val: string) => {
    if (val) {
      const list = demandActiveList.filter((item: SupplyAPI.SellerDemandAuth) => {
        return (
          item.buyer_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 ||
          item.buyer_id.toString().indexOf(val) !== -1
        );
      });
      setFilterList(list);
    } else {
      setFilterList(demandActiveList);
    }
  };

  const onCancel = () => {
    setVisible(false);
  };

  const onSave = (buyer_ids: string[]) => {
    setVisible(false);
    handleDemandChange(buyer_ids);
  };

  const handleOpen = () => {
    if (supplyData?.status === DemandAndSupplyStatusMap.Testing) {
      message.warning('The testing stage is not allowed to be authorized operation, please set the status first');
      return;
    }
    setVisible(true);
  };
  // 删掉一些
  const handleUnauthorize = () => {
    const buyer_ids = supplyDemandList
      .filter(item => selectedRowKeys.indexOf(item.buyer_id) === -1)
      .map(item => `${item.buyer_id}`);
    Modal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure to deauthorize selected advertisers to ${supplyData?.seller_name}?`,
      okText: 'Deauthorize',
      cancelText: 'Cancel',
      onOk: () => {
        handleDemandChange(buyer_ids);
        setSelectedRowKeys([]);
        setSelectedRow(undefined);
        setClearAllTag(!clearAllTag);
      },
      okButtonProps: {
        danger: true
      }
    });
  };

  const handleDemandChange = (buyer_ids: string[]) => {
    const ori_buyer_ids =
      (supplyDemandList && supplyDemandList.length && supplyDemandList.map(item => item.buyer_id)) || [];
    const params = {
      buyer_ids: buyer_ids,
      pub_id: id,
      level: Level.Supply,
      ori_buyer_ids
    };
    const onSuccess = (data: any) => {
      if (data) {
        message.success('success');
        setSelectedRowKeys([]);
        setSelectedRow(undefined);
      }
    };
    const onFinally = () => {
      fetchData();
    };
    httpFetchData({ setLoading: setEditLoading, request: setSupplyAuth, params, onSuccess, onFinally });
  };

  return (
    <Card
      // title='Authorization'
      className={styles['card-container']}
      loading={loading}
    >
      <div className={styles['container']}>
        <NormalTitle
          blackName="Authorization"
          isTitle={true}
          bottom={20}
          top={20}
          num={selectedRowKeys.length ? `${selectedRowKeys.length} / ${filterList.length}` : `${filterList.length}`}
        />
        <div className={styles['top']}>
          <div className={styles['top-left']}>
            <InputSearch placeholder="Please input advertiser name or advertiser id" handleSearch={handleSearch} />
          </div>
          <div className={styles['top-right']}>
            <div>
              <Button
                danger
                type="primary"
                disabled={
                  access.DisabledButton('SupplyAuthorizeAuth') ||
                  !selectedRowKeys ||
                  !selectedRowKeys.length ||
                  supplyData?.status === StatusMap.Paused
                }
                onClick={handleUnauthorize}
              >
                Deauthorize
              </Button>
              {supplyData?.status === StatusMap.Paused ||
                (selectedRowKeys.length === 0 && (
                  <Tooltip
                    title={
                      supplyData?.status === StatusMap.Paused
                        ? `You cannot operate deauthorization because the publisher status is "Paused"`
                        : `Please select advertiser before deauthorization`
                    }
                    trigger="hover"
                  >
                    <ExclamationCircleOutlined
                      style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
                    />
                  </Tooltip>
                ))}
            </div>
            <div>
              <Button
                type="primary"
                disabled={
                  access.DisabledButton('SupplyAuthorizeAuth') || supplyData?.status === DemandAndSupplyStatusMap.Paused
                }
                onClick={handleOpen}
              >
                Authorize
              </Button>
              {supplyData?.status === DemandAndSupplyStatusMap.Paused && (
                <Tooltip
                  title={`You cannot operate authorization because the publisher status is "${
                    DemandAndSupplyStatusDesc[supplyData?.status]
                  }"`}
                  trigger="hover"
                >
                  <ExclamationCircleOutlined
                    style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
        <OriginalTable
          loading={loading || demandLoading || editLoading}
          columns={AuthorizationColumns}
          dataSource={filterList}
          rowKey="buyer_id"
          rowSelection={{
            onChange: handleRowChange,
            selectedRowKeys
          }}
          isBtnTable={false}
          scroll={{ y: 'calc(100vh - 300px)' }}
          onRow={(record: SupplyAPI.SellerDemandAuth) => ({ onClick: () => handleRowClick(record) })}
        />
      </div>
      <AuthorizationModal<SupplyAPI.SellerDemandAuth>
        visible={visible}
        onCancel={onCancel}
        onSave={onSave}
        authorizeList={authList}
        dataList={demandList}
        rowKey="buyer_id"
        leftTableColumns={AuthorizationColumns}
        rightTableColumns={AuthorizationColumns}
        leftTitle={'Unauthorized Advertisers'}
        rightTitle={'Authorized Advertisers'}
        searchKeys={['buyer_id', 'buyer_name']}
        placeholder="Please Input Advertiser Name or ID"
      ></AuthorizationModal>
    </Card>
  );
};

export default AuthorizationTable;
