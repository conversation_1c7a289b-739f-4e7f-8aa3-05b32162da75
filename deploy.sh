#!/bin/bash
set -e
env=$1
echo "$env"

function deploy() {
    local env=$1
    echo "env: $env"
    npx -y google-artifactregistry-auth --repo-config .npmrc --credential-config ~/.npmrc

    npx -y google-artifactregistry-auth --repo-config .npmrc --credential-config ~/.npmrc

    cd client && \
    yarn install --ignore-engines && \
    echo "finished install" && \
    yarn builddeploy && \
    cd ../server && \
    npm install && \
    npm run build && \
    if [ $env == 'test' ] || [ $env == 'hotfix' ]
    then
	npm run pm2-$env
    else
	npm run pm2
    fi
}

if [ -z $env ]
then
    echo "environment variable is required"
    exit 1
else
    if [ $env == 'prod' ] || [ $env == 'test' ] || [ $env == 'hotfix' ]
    then
	deploy $env
    else
	echo "environment variable is invalid"
	exit 1
    fi
fi
