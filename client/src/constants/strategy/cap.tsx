/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:19:56
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:58:39
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { StatusDesc, StatusOptions } from '@/constants';
import { formatDailyCap } from '@/utils/cap';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { Tooltip, Progress } from 'antd';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

export const CapTypeOptions = [
  { label: 'Pub', value: 1 },
  { label: 'Adv', value: 2 },
  { label: 'Adv + Pub', value: 3 },
  { label: 'Adv + Bundle', value: 4 },
  { label: 'Adv + Pub + Bundle', value: 5 }
];

export const CapTypeToString: API.StringToStringType = {
  1: 'Pub',
  2: 'Adv',
  3: 'Adv + Pub',
  4: 'Adv + Bundle',
  5: 'Adv + Pub + Bundle'
};

export const CapType = {
  'Pub': 1,
  'Adv': 2,
  'Adv + Pub': 3,
  'Adv + Bundle': 4,
  'Adv + Pub + Bundle': 5
};

export const CapSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const CapBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'Cap'
  }
];

export const CapColumns: ColumnProps<StrategyAPI.CapListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={txt ? `${txt}(${params.seller_id})` : '-'}>
        <span>{txt ? `${txt}(${params.seller_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={txt ? `${txt}(${params.buyer_id})` : '-'}>
        <span>{txt ? `${txt}(${params.buyer_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'type',
    width: 160,
    render: txt => <>{CapTypeToString[txt]}</>
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Bundle</span>
        <Tooltip title="Valid after an hour" >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'bundle',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt) => (
      <HoverToolTip title={txt}>
        <span>{txt || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Daily Rev Cap($)',
    width: 150,
    dataIndex: 'rev_cap',
    ellipsis: { showTitle: false },
    render: (_: number, row) => {
      const { percent, percentStr } = formatDailyCap(row.cur_rev, _, row.sys_update_time, '');
      return (
        <>
          <Progress
            type="circle"
            percent={percent as number}
            format={p => (
              <span style={{ fontSize: '12px' }}>{typeof percent === 'number' ? `${percent}%` : percent}</span>
            )}
            width={60}
          />
          <HoverToolTip title={percentStr} maxWidth={60}>
            <span style={{ marginLeft: '6px' }}>{percentStr}</span>
          </HoverToolTip>
        </>
      );
    }
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'account_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['account_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Updated on (sys)',
    dataIndex: 'sys_update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status_desc',
    width: 100,
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  }
];
