---
description: project structure
globs: 
alwaysApply: false
---
# Project Structure

The project is a SaaS platform built with a client-server architecture:

## Client Side
The client is a React application built with Umi.js:
- Entry point: [client/.umirc.ts](mdc:client/.umirc.ts)
- Main package configuration: [client/package.json](mdc:client/package.json)
- Routes configuration: [client/config/routes.ts](mdc:client/config/routes.ts)

### Client Directory Structure
- `client/src/`: Main source code
  - `assets/`: Static assets like images, fonts
  - `components/`: Reusable UI components
  - `constants/`: Constant values
  - `hooks/`: Custom React hooks
  - `models/`: Data models and state management
  - `pages/`: Page components corresponding to routes
  - `services/`: API service calls
  - `stores/`: State management stores
  - `types/`: TypeScript type definitions
  - `utils/`: Utility functions

## Server Side
The server is a Node.js application built with Koa:
- Entry point: [server/src/index.ts](mdc:server/src/index.ts)
- Main package configuration: [server/package.json](mdc:server/package.json)
- Process management: [server/processes.json](mdc:server/processes.json)

### Server Directory Structure
- `server/src/`:
  - `config/`: Configuration files
  - `constants/`: Constant values
  - `controllers/`: Request handlers
  - `db/`: Database connection and operations
  - `middleware/`: Koa middleware
  - `models/`: Data models
  - `routers/`: API route definitions
  - `schema/`: Data validation schemas
  - `services/`: Business logic
  - `types/`: TypeScript type definitions
  - `utils/`: Utility functions

## Key Files
- [Dockerfile](mdc:Dockerfile): Container configuration
- [deploy.sh](mdc:deploy.sh): Deployment script
- [nginx_prod.conf](mdc:nginx_prod.conf): Nginx configuration for production
- [nginx_test.conf](mdc:nginx_test.conf): Nginx configuration for test environment
