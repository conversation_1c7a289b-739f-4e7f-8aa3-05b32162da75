import { DeleteOutlined } from '@ant-design/icons';
import { Button, Dropdown, Empty, Tooltip } from 'antd';
import { type DropdownButtonProps } from 'antd/lib/dropdown';
import { useEffect, useMemo, useRef, useState } from 'react';
import RixEngineFont from '../RixEngineFont';
import styles from './index.less';
import { PresetDropdownItem, PresetDropdownStore, usePresetDropdownItems } from './persistence';

interface PresetDropdownButtonProps extends DropdownButtonProps {
  title?: string;
  persistenceKey: string;
  // 默认的 preset options，如果存在，则优先使用，同时不可以删除
  defaultPresetOptions?: PresetDropdownItem[];
  // 保存预设，返回预设的 key
  handleSavePreset?: (currentItem: PresetDropdownItem | null, store: PresetDropdownStore) => Promise<string>;
  handleRestorePreset?: (restoreItem: PresetDropdownItem, store: PresetDropdownStore) => void;
  handleDeletePreset?: (
    deleteItem: PresetDropdownItem,
    newItem: PresetDropdownItem | null,
    store: PresetDropdownStore
  ) => void;
}

const PresetDropdownButton = ({
  title,
  persistenceKey,
  handleSavePreset,
  handleRestorePreset,
  handleDeletePreset,
  defaultPresetOptions = [],
  ...props
}: PresetDropdownButtonProps) => {
  const { items, setItems } = usePresetDropdownItems(persistenceKey, []);
  const [open, setOpen] = useState(false);
  const [selectedKey, setSelectedKey] = useState<string | null>('default');
  const originalDefaultPresetOptions = useRef<string>('');

  // Initialize with default presets if they exist
  useEffect(() => {
    const isDefaultPresetOptionsChanged =
      JSON.stringify(defaultPresetOptions) !== JSON.stringify(originalDefaultPresetOptions.current);

    if (defaultPresetOptions.length > 0 && isDefaultPresetOptionsChanged) {
      setItems((prevItems: PresetDropdownItem[]) => {
        // 创建一个 Map 来存储所有项，相同 key 的新值会覆盖旧值
        const itemsMap = new Map([...prevItems, ...defaultPresetOptions].map(item => [item.key, item]));
        // 转换回数组，保持默认值在前面
        const updatedItems = Array.from(itemsMap.values());
        return updatedItems;
      });
      originalDefaultPresetOptions.current = JSON.stringify(defaultPresetOptions);
    }
  }, [defaultPresetOptions, originalDefaultPresetOptions.current]);

  const handleOpenChange = (open: boolean) => {
    setOpen(open);
  };

  const handleSaveClick = async () => {
    const currentItem = items.find(item => item.key === selectedKey) || null;
    const key = await handleSavePreset?.(currentItem, { items, setItems });
    if (key) {
      setSelectedKey(key);
    }
  };

  const handleRestoreClick = (item: PresetDropdownItem) => {
    setSelectedKey(item.key);
    handleRestorePreset?.(item, { items, setItems });
  };

  const handleDeleteClick = (item: PresetDropdownItem) => {
    const key = item.key;
    // Prevent deletion of default presets
    const isDefaultPreset = defaultPresetOptions.some(preset => preset.key === key);
    if (!isDefaultPreset) {
      setItems(items.filter(item => item.key !== key));

      const newItem = defaultPresetOptions[0] || null;
      setSelectedKey(newItem?.key || 'default');
      handleDeletePreset?.(item, newItem, { items, setItems });
    }
  };

  // 自定义 dropdown render
  const dropdownMenu = useMemo(() => {
    const allItems = items;
    return (
      <div className={styles.container}>
        {allItems.length === 0 ? (
          <Empty description="No preset available" />
        ) : (
          <div>
            {allItems.map(item => {
              const isDefaultPreset = defaultPresetOptions.some(preset => preset.key === item.key);
              const isSelected = item.key === selectedKey;
              return (
                <div
                  key={item.key}
                  className={`${styles.menuItem} ${isSelected ? styles.selected : ''}`}
                  title={item.label as string}
                >
                  <div onClick={() => handleRestoreClick(item)}>{item.label}</div>
                  {!isDefaultPreset && <DeleteOutlined onClick={() => handleDeleteClick(item)} />}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  }, [items, defaultPresetOptions, selectedKey]);

  return (
    <Dropdown.Button
      placement="bottomRight"
      icon={<RixEngineFont type="rix-edit" />}
      {...props}
      // disable custom configs
      open={open}
      onOpenChange={handleOpenChange}
      className={styles['preset-dropdown-button']}
      onClick={handleSaveClick}
      overlayClassName={styles.dropdown}
      buttonsRender={() => [
        <Tooltip title="Save Preset" key="save-preset-tooltip">
          <Button key="save" onClick={handleSaveClick} className={styles.saveButton}>
            Save
          </Button>
        </Tooltip>,
        <Tooltip title="Edit Preset" key="edit-preset-tooltip">
          <Button key="clear" icon={<RixEngineFont type="rix-edit" />} className={styles.editButton} />
        </Tooltip>
      ]}
      overlay={dropdownMenu}
    >
      {title || 'Save Preset'}
    </Dropdown.Button>
  );
};

export default PresetDropdownButton;
