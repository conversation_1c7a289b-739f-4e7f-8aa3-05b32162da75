/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:59:11
 * @Description:
 */
import BundleTableEdit from '@/components/BundleTableEdit';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import { AdFormatOptions, AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import { CountryOptions } from '@/constants/global-mapping/country';
import { ChannelOptions } from '@/constants/supply';
import { AdSizeOptions } from '@/models/useAdSizeOptions';
import { addPmpInventory, updatePmpInventory } from '@/services/api';
import { fetchData, isValidBundle } from '@/utils';
import { Button, Form, Input, message } from 'antd';
import { useEffect, useState } from 'react';

type AddInventoryProps = {
  item?: StrategyAPI.PmpInternalListItem;
  visible: boolean;
  isEdit: boolean;
  onClose: () => void;
  reload: () => void;
  supplyList?: SupplyAPI.SupplyListItem[];
  supplyNameMap?: any; // 名称映射
  adSizeOptions: AdSizeOptions[];
  adSizeMapByValue: Record<string, string>;
};

const AddInventorModel: React.FC<AddInventoryProps> = ({
  item,
  onClose,
  visible,
  isEdit,
  reload,
  supplyList,
  supplyNameMap,
  adSizeOptions,
  adSizeMapByValue
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (item && isEdit && visible) {
      const params = {
        ...item
      };
      form.setFieldsValue(params);
    } else {
      form.resetFields();
    }
  }, [item, isEdit, visible]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      handleEditInventory({ ...values, id: item?.id, ori_data: item });
    } else {
      handleAddInventory(values);
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    onClose();
    reload();
  };

  const onCancel = () => {
    form.resetFields();
    onClose();
  };

  const handleAddInventory = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addPmpInventory, params, onSuccess });
  };

  const handleEditInventory = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updatePmpInventory, params });
  };

  const isValidValue = (value: any) => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return value !== '' && value !== undefined && value !== null;
  };

  const handleGenerateName = () => {
    const editForm = form.getFieldsValue();
    editForm.bundle = editForm.bundle || [];
    const keyArr = ['seller_id', 'bundle', 'ad_format', 'country', 'ad_size'];
    const nameArr: any[] = [];
    keyArr.forEach(key => {
      if (isValidValue(editForm[key])) {
        const name = editForm[key]
          .map((val: string | number, index: number) => {
            if (key === 'country') {
              return val;
            } else if (key === 'bundle') {
              // 只取第一个bundle
              return index === 0 ? val : '';
            } else if (key === 'ad_format') {
              return AdFormatToLabel[val as number];
            } else if (key === 'ad_size') {
              return adSizeMapByValue[val as string];
            } else {
              return supplyNameMap?.[val];
            }
          })
          .filter((val: string) => val !== '')
          .join('|');
        nameArr.push(name);
      }
    });
    form.setFieldValue('name', nameArr.join('-'));
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit PMP Inventory` : `Create PMP Inventory`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="seller_id"
          label="Publisher"
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: false, message: 'Please Select Publisher' }]}
        >
          <Select
            placeholder="Please Select Publisher"
            showSearch
            mode="multiple"
            allowClear
            options={supplyList?.map(v => ({
              value: v.seller_id,
              label: `${v.seller_name}(${v.seller_id})`,
              disabled: v.status === DemandAndSupplyStatusMap.Testing
            }))}
          />
        </Form.Item>
        <Form.Item
          label="Bundle"
          name="bundle"
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: false, message: 'Please Input Bundle' }]}
        >
          <BundleTableEdit
            editTitle="Edit Bundle"
            editTips="Enter the bundle(one per line)"
            deleteAllTitle="Delete All Bundle"
            deleteAllTips="Are you sure to delete all bundle?"
            open={visible}
            contentMaxHeight={isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'}
            editSingle={true}
            validator={(val: string) => isValidBundle(val)}
          />
        </Form.Item>
        <Form.Item name="inventory_type" label="Channel Type">
          <Select options={ChannelOptions} placeholder="Please Select Channel Type" mode="multiple" allowClear />
        </Form.Item>
        <Form.Item name="ad_format" label="Ad Format">
          <Select
            options={AdFormatOptions.map(v => ({ value: +v.value, label: v.label }))}
            placeholder="Please Select Ad Format"
            mode="multiple"
            allowClear
          />
        </Form.Item>
        <Form.Item name="country" label="Country">
          <Select placeholder="Please Select Country" showSearch allowClear mode="multiple" options={CountryOptions} />
        </Form.Item>
        <Form.Item name="ad_size" label="Ad Size">
          <Select placeholder="Please Select Ad Size" showSearch mode="multiple" allowClear options={adSizeOptions} />
        </Form.Item>
        <Form.Item name="seller_deal_id" label="Publisher Deal ID">
          <Input placeholder="Please Input Publisher Deal ID" />
        </Form.Item>
        <Form.Item
          label="Name"
          name="name"
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: 'Please Input Name' }]}
        >
          <Input
            placeholder="Please Input Name"
            addonBefore={
              <Button onClick={handleGenerateName} type="text" style={{ padding: 0, height: 30 }}>
                Generate
              </Button>
            }
          />
        </Form.Item>
        <Form.Item label="Remark" name="remark">
          <Input placeholder="Please Input Remark" />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddInventorModel;
