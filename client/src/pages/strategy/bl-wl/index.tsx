/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:10:51
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-12 15:24:46
 * @Description:
 */
import React, { useEffect, useMemo, useState } from 'react';
import { useModel, useAccess } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import { BlAndWlSearchOption, BlAndWlBreadOptions, BlAndWlColumns, ListType } from '@/constants/strategy/bl-wl';
import { StatusMap } from '@/constants';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import EditButton from '@/components/Button/EditButton';
import { PlusOutlined } from '@ant-design/icons';
import AddBlAndWlDrawer from '../components/AddBlAndWlDrawer';
import { TopBarSearchItem } from '@/components/TopBar';
import { Countries } from '@/constants/global-mapping/country';
import EllipsisPopover from '@/components/EllipsisPopover';

const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource, reload, loading } = useModel('useBlAndWlList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const { adSizeOptions, adSizeMapByValue, fetchAdSize } = useModel('useAdSizeOptions');

  const tmpColumns: ColumnProps<StrategyAPI.BlAndWlListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 100,
      fixed: 'right',
      render: (txt, params) => (
        <>
          <EditButton onClick={() => handleEditBlAndWl(params)} disabled={access.DisabledButton('updateBlAndWlAuth')}>
            Edit
          </EditButton>
        </>
      )
    }
  ];

  const columns = useMemo(() => {
    const arr = [...BlAndWlColumns, ...tmpColumns];
    const index = arr.findIndex(v => v.dataIndex === 'content');
    if (index !== -1) {
      // 以后需要注意这个地方
      arr[index].render = (txt: string, params) => {
        let list = (txt && txt.split(',').filter(v => v)) || [];
        if ([ListType['Country Black List'], ListType['Country White List']].includes(params.type)) {
          list = list.map((val: string) => Countries[val] || val);
        } else if ([ListType['Ad Format Black List'], ListType['Ad Format White List']].includes(params.type)) {
          list = list.map((val: string) => AdFormatToLabel[val] || val);
        } else if ([ListType['Ad Size Black List'], ListType['Ad Size White List']].includes(params.type)) {
          list = list.map((val: string) => adSizeMapByValue[val] || val);
        }
        return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
      };
    }
    return arr;
  }, [adSizeMapByValue]);

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [blAndWl, setCurrentBlAndWl] = useState<StrategyAPI.BlAndWlListItem | undefined>(undefined);
  const [searchOptions, setSearchOptions] = useState(BlAndWlSearchOption);

  useEffect(() => {
    if (dataSource) {
      reload();
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    fetchAdSize();
    handleSearchOptions();
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    if (demandList) {
      const dOptions: any[] = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      dOptions.unshift({ label: `All Advertisers`, value: 0 });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      sOptions.unshift({ label: `All Publishers`, value: 0 });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  };

  const handleEditBlAndWl = (params: StrategyAPI.BlAndWlListItem) => {
    setCurrentBlAndWl(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentBlAndWl(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={BlAndWlBreadOptions}>
      <FrontTable<StrategyAPI.BlAndWlListItem>
        pageTitle="BL & WL"
        isFold
        searchOptions={searchOptions}
        loading={loading && dataSource && !dataSource.length}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create BL & WL',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
            accessCode: 'addBlAndWlAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }} // 非动态， 需要自己指定
        labelWidth={80}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        initialValues={{ status: [StatusMap.Active] }}
      />
      <AddBlAndWlDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadBlAndWl={reload}
        item={blAndWl}
        demandList={demandList || []}
        supplyList={supplyList || []}
        adSizeOptions={adSizeOptions}
      />
    </PageContainer>
  );
};

export default Page;
