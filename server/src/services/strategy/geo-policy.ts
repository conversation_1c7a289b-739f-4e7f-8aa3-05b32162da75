import { StatusMap } from '@/constants';
import dbUtil from '@/db/mysql';
import { geoPolicyModel } from '@/models';
import { GeoPolicyAPI } from '@/types/geo-policy';

class GeoPolicyService {
  /**
   * 获取地理策略key列表
   */
  async getGeoPolicyKeyList(
    tnt_id: number
  ): Promise<GeoPolicyAPI.GeoPolicyKeyListItem[]> {
    const defaultKeyData = await geoPolicyModel.getDefaultGeoPolicyKey({
      tnt_id
    });

    const keyList = await geoPolicyModel.getGeoPolicyKeyList(tnt_id);

    const defaultKeyId = defaultKeyData[0]?.id;

    return keyList.map(item => ({
      ...item,
      is_default: item.id === defaultKeyId ? 1 : 0,
      unique_id: `${item.id}-${item.policy_key}`
    }));
  }

  /**
   * 获取地理策略key关联列表
   */
  async getGeoPolicyKeyRelationList(
    tnt_id: number
  ): Promise<GeoPolicyAPI.GeoPolicyKeyRelationListItem[]> {
    const defaultKeyData = await geoPolicyModel.getDefaultGeoPolicyKey({
      tnt_id
    });

    // 过滤掉默认key的数据(存在时)
    const rawData = await geoPolicyModel.getGeoPolicyKeyRelationList(tnt_id);

    const defaultKeyId = defaultKeyData[0]?.id;

    return rawData.map(item => ({
      ...item,
      is_default: item.policy_key_id === defaultKeyId ? 1 : 0,
      unique_id: `${item.id}-${item.policy_key}-${item.seller_id}`
    }));
  }

  /**
   * 添加地理策略key
   */
  async addGeoPolicyKey(
    params: GeoPolicyAPI.AddGeoPolicyKeyParams
  ): Promise<boolean> {
    const { is_default, ...restParams } = params;

    return dbUtil.execTransaction(async tools => {
      const [existPolicyKeyData] =
        await geoPolicyModel.getGeoPolicyKeyByPolicyKey(restParams, tools);

      // 1. 直接创建（如果有重名的，抛出错误）
      const policyKeyData = await geoPolicyModel.addGeoPolicyKey(
        restParams,
        tools,
        existPolicyKeyData?.status === StatusMap.Paused
      );

      // 如果不是默认policy key，直接返回成功
      if (is_default !== 1) {
        return true;
      }

      // 标记默认key：插入一条数据，seller_id为0，policy_key_id为当前的policy_key_id
      // 如果重复，则更新
      return geoPolicyModel.addGeoPolicyKeyRelation(
        {
          policy_key_id: policyKeyData.id,
          seller_id: 0,
          tnt_id: params.tnt_id,
          op_id: params.op_id
        },
        tools,
        true
      );
    });
  }

  /**
   * 更新地理策略key
   */
  async updateGeoPolicyKey(
    params: GeoPolicyAPI.UpdateGeoPolicyKeyParams
  ): Promise<boolean> {
    const { is_default, ...restParams } = params;

    return dbUtil.execTransaction(async tools => {
      // 1. 直接更新 stg_geo_edge_policy_key 数据
      await geoPolicyModel.updateGeoPolicyKey(restParams, tools);

      // 获取当前默认key信息
      const [defaultData] = await geoPolicyModel.getDefaultGeoPolicyKey(
        params,
        tools
      );
      const default_policy_key = defaultData?.policy_key;

      // 2.1 处理设为默认key的情况
      if (is_default === 1) {
        // 2.1.1 如果存在默认key且是自己，提前结束流程(优化点)
        if (
          default_policy_key &&
          default_policy_key === restParams.policy_key
        ) {
          return true;
        }

        // 为当前key插入seller_id为0的默认关联数据
        // 如果重复，则更新
        return geoPolicyModel.addGeoPolicyKeyRelation(
          {
            policy_key_id: restParams.id,
            seller_id: 0,
            tnt_id: params.tnt_id,
            op_id: params.op_id
          },
          tools,
          true
        );
      }

      // 2.2 处理取消默认key的情况
      // 如果当前key是默认key，将seller_id为0的关联数据状态改为2
      if (default_policy_key === restParams.policy_key) {
        return geoPolicyModel.updateGeoPolicyKeyRelation(
          {
            id: defaultData.relation_id,
            tnt_id: restParams.tnt_id,
            op_id: restParams.op_id,
            policy_key_id: restParams.id,
            seller_id: 0,
            status: 2
          },
          tools
        );
      }

      return true;
    });
  }

  /**
   * 添加地理策略key关联
   */
  async addGeoPolicyKeyRelation(
    params: GeoPolicyAPI.AddGeoPolicyKeyRelationParams
  ): Promise<boolean> {
    return dbUtil.execTransaction(async tools => {
      // 判断active状态的 seller_id 是否存在，不存在才添加
      const [existRelationData] =
        await geoPolicyModel.getExistActiveRelationBySellerId(
          params.seller_id,
          params.tnt_id,
          tools
        );

      if (existRelationData) {
        throw new Error(
          `Publisher authorization already exists.(with policy key: ${existRelationData.policy_key})`
        );
      }

      // 添加数据
      return geoPolicyModel.addGeoPolicyKeyRelation(params, tools, true);
    });
  }

  /**
   * 更新地理策略key关联
   */
  async updateGeoPolicyKeyRelation(
    params: GeoPolicyAPI.UpdateGeoPolicyKeyRelationParams
  ): Promise<boolean> {
    return geoPolicyModel.updateGeoPolicyKeyRelation(params)
  }
}

export default new GeoPolicyService();
