/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-06-28 14:55:38
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 17:56:07
 * @Description:
 */
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { BillingScheme, DownloadBillingScheme } from '@/schema/report/billing';
import { pubBillingService } from '@/services';
import { BillingAPI } from '@/types/billing';
import { getUserIp } from '@/utils';
import { sendLarkAlertCard } from '@/utils/alert';
import { getUserRole } from '@/utils/report/getBQLabel';
import { getCtxBackResult, getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class PubBillingCtrl implements BillingAPI.PublisherCtrl {
  @validateBody(BillingScheme)
  async getPublisherBillingList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const list = await pubBillingService.getPublisherBillingList(formData, {
      tag: api_url,
      tenantId: formData.tnt_id,
      userRole: getUserRole(userType)
    });
    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }

    ctx.body = result;
  }

  @validateBody(DownloadBillingScheme)
  async downloadPublisherBillingList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const { user_id } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    try {
      const data = await pubBillingService.downloadAllReport(
        formData,
        user_id,
        {
          tag: api_url,
          tenantId: formData.tnt_id,
          userRole: getUserRole(userType)
        }
      );
      if (data && data.code === Code.ERROR_SYS) {
        result = getCtxResult('DOWNLOAD_ERROR');
      } else if (data && data.code === Code.SUCCESS) {
        result = getCtxResult('SUCCESS', data);
      }
    } catch (err: any) {
      getLogger('error').error(`downloadAllReport error=[${err?.message}]`);
      result = getCtxResult('DOWNLOAD_ERROR');

      const { cur_log_id, curUser } = ctx.state;
      const { account_name = '', tnt_id = 0, user_id = 0 } = curUser || {};
      const ip = getUserIp(ctx);
      sendLarkAlertCard({
        title: 'Saas Std Platform Download Report Error',
        data: {
          'Log ID': cur_log_id,
          IP: ip,
          'API URL': ctx.request.originalUrl,
          'Page URL': ctx.request.header.referer,
          Operator: `${account_name}(${user_id})`,
          'Tenant ID': tnt_id || 0,
          'Session ID': ctx.session!.session_id || 0,
          'User Agent': ctx.request.headers['user-agent'],
          'x-time-zone': ctx.request.headers['x-time-zone'],
          Params: ctx.request.body || {},
          Error: err,
          Result: result
        }
      });
    } finally {
      ctx.body = result;
    }
  }
}

export const pubBillingController = new PubBillingCtrl();
