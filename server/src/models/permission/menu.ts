/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:23:23
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-11 15:06:08
 * @Description:
 */

import {
  InterfaceType,
  MenuType,
  RoleTypeToNumber,
  StatusType
} from '@/constants/permission';
import dbUtils from '@/db/mysql';

class MenuModel {
  // 权限控制使用 只获取系统超管的权限
  async getAllMenu() {
    const sql = `
      select
        m.id,
        m.pid,
        m.title,
        m.type,
        m.sort,
        m.access
      from role_permission_rl as rpl
      inner join menu as m on m.id=rpl.rsc_id
      where rpl.role_id=? and tnt_id=? and m.status=?
    `;
    return await dbUtils.permissionQuery(sql, [
      RoleTypeToNumber['Super Administrator'],
      0,
      StatusType.Active
    ]);
  }

  // 只取菜单组装
  async getMenuList() {
    const sql = `
      select
        id,
        title,
        path,
        component,
        is_hide,
        icon,
        pid,
        sort,
        access,
        menu_render,
        node_type
      from menu where status = ? and type=?
    `;
    return await dbUtils.permissionQuery(sql, [
      StatusType.Active,
      MenuType.Menu
    ]);
  }

  async getGlobalInterface() {
    const sql = `select path,type from interface where type != ?`;
    return await dbUtils.permissionQuery(sql, [InterfaceType.Normal]);
  }
}

export default new MenuModel();
