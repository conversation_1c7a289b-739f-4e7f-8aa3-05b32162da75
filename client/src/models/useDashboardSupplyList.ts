/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-04 11:53:36
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-05-04 11:56:29
 * @Description:
 */
/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:56:42
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDashboardSupplyList } from '@/services/api';

export default function useSupplyList() {
  const { data, run, loading, error } = useCustomRequest(getDashboardSupplyList, {
    cacheKey: 'dashboardSupply-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
