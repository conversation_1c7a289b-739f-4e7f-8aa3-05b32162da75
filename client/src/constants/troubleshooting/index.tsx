/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-23 16:14:41
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 11:05:00
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-21 20:13:56
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-23 16:09:03
 * @Description:
 */

import { ColumnProps } from 'antd/lib/table';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import { Tooltip } from 'antd';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { objectFlip } from '@/utils';
import EllipsisPopover from '@/components/EllipsisPopover';
import StatusTag, { ColorMap } from '@/components/Tag/NewStatusTag';
import { Countries } from '@/constants/global-mapping/country';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';

export const SampleRegionOptions = [
  { label: 'USE', value: 1 },
  { label: 'APAC', value: 2 }
];

export const SampleRegionMap: API.StringToStringType = {
  1: 'USE',
  2: 'APAC',
  3: 'EUW'
};
export const SampleTabOptions = [
  { label: 'Publisher Request', value: 1 },
  { label: 'Response To Publisher', value: 3 },
  { label: 'Request Sent To Advertiser', value: 4 },
  { label: 'Advertiser Response', value: 2 }
];

export const SampleTab = {
  SupplyRequest: 1,
  DemandResponse: 2,
  SupplyResponse: 3,
  DemandRequest: 4
};

export const SampleType = {
  SupplyRequest: 1,
  DemandResponse: 2,
  SupplyResponse: 3,
  DemandRequest: 4
};

export const SampleTypeDesc = objectFlip(SampleType);

export const treeTheme = {
  scheme: 'bright',
  base00: '#ffffff',
  base01: '#303030',
  base02: '#505050',
  base03: '#b0b0b0',
  base04: '#d0d0d0',
  base05: '#e0e0e0',
  base06: '#f5f5f5',
  base07: '#ffffff',
  base08: '#fb0120',
  base09: '#fc6d24',
  base0A: '#fda331',
  base0B: '#a1c659',
  base0C: '#76c7b7',
  base0D: '#6fb3d2',
  base0E: '#d381c3',
  base0F: '#be643c'
};
export const SupplyRequestStatusMap = {
  1000: 'SUCCESS',
  2001: 'Have No Ad',
  21000: 'Decode Body Error',
  30000: 'Content Error',
  31000: 'Content Validation Error',
  40000: 'Strategy Block',
  40003: 'No Buyer Request',
  50000: 'ATC Block'
};
export const DemandResponseStatusMap = {
  1000: 'SUCCESS',
  30005: 'Decode ResponseBody Error',
  3: 'INVALID_BID_RESPONSE',
  5: 'INVALID_AUCTION_ID',
  6: 'INVALID_ADVERTISER_DOMAIN',
  8: 'MISSING_CREATIVE_ID',
  9: 'MISSING_BID_PRICE',
  100: 'BID_WAS_BELOW_AUCTION_FLOOR',
  102: 'LOST_TO_HIGHER_BID',
  200: 'CREATIVE_FILTERED_UNKNOWN_REASON',
  204: 'CREATIVE_FILTERED_INCORRECT_CREATIVE_FORMAT',
  205: 'CREATIVE_FILTERED_ADVERTISER_EXCLUSIONS',
  206: 'CREATIVE_FILTERED_APP_BUNDLE_EXCLUSIONS',
  209: 'CREATIVE_FILTERED_CATEGORY_EXCLUSIONS',
  210: 'CREATIVE_FILTERED_CREATIVE_ATTRIBUTE_EXCLUSIONS'
};

export const StatusColorMap = {
  // 1000 为 ColorMap.green
  1000: ColorMap.green
};

export const TaskAllColumns: ColumnProps<any>[] = [
  {
    title: 'Task ID',
    dataIndex: 'tag_id',
    width: 240,
    ellipsis: { showTitle: false }
  },
  {
    title: 'Advertiser',
    dataIndex: 'buyer_id',
    width: 220,
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const buyerName = row.buyer_name ? `${row.buyer_name}(${row.buyer_id})` : row.buyer_id || '-';
      return (
        <HoverTooltip title={row.buyer_name}>
          <span>{buyerName}</span>
        </HoverTooltip>
      );
    }
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_id',
    width: 220,
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const sellerName = row.seller_name ? `${row.seller_name}(${row.seller_id})` : row.seller_id || '-';
      return (
        <HoverTooltip title={row.seller_name}>
          <span>{sellerName}</span>
        </HoverTooltip>
      );
    }
  },

  {
    title: 'Request Status',
    dataIndex: 'block_reason',
    width: 180,
    render: (status: any, row) => {
      if (status === -1) return '-';
      return (
        <StatusTag
          value={status}
          style={{ width: '80%', overflow: 'hidden', textOverflow: 'ellipsis' }}
          statusDescMap={SupplyRequestStatusMap}
          statusColorMap={StatusColorMap}
          tips={SupplyRequestStatusMap[status as keyof typeof SupplyRequestStatusMap]}
        ></StatusTag>
      );
    }
  },
  {
    title: 'Bundle',
    dataIndex: 'bundle',
    width: 150,
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  // task placement id
  {
    title: 'Placement ID',
    dataIndex: 'plm_id',
    width: 140
  },
  // trace placement id
  {
    title: 'Placement ID',
    dataIndex: 'placement_id',
    width: 120
  },
  {
    title: 'Bid Status',
    dataIndex: 'bid_status',
    width: 220,
    render: (status: any, row) => {
      if (status === -1) return '-';
      return (
        <StatusTag
          value={status}
          style={{ width: '80%', overflow: 'hidden', textOverflow: 'ellipsis' }}
          statusDescMap={DemandResponseStatusMap}
          statusColorMap={StatusColorMap}
          tips={DemandResponseStatusMap[status as keyof typeof DemandResponseStatusMap]}
        />
      );
    }
  },
  {
    title: 'Cid',
    dataIndex: 'cid',
    width: 160,
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  {
    title: 'Crid',
    dataIndex: 'crid',
    width: 160,
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  {
    title: 'Adomain',
    dataIndex: 'adomain',
    width: 160,
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  // task region
  {
    title: 'Server Region',
    dataIndex: 'server_region',
    width: 140,
    render: txt => <>{SampleRegionMap[txt]}</>
  },
  // trace region
  {
    title: 'Server Region',
    dataIndex: 'region',
    width: 140,
    render: txt => <>{SampleRegionMap[txt]}</>
  },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 120,
    render: txt => <>{AdFormatToLabel[txt] || '-'}</>
  },
  {
    title: 'Country',
    dataIndex: 'country',
    width: 250,
    render: (txt, row) => {
      let list = ((txt && txt.split(',').filter((v: any) => v)) || []).map((v: any) => Countries[v]);
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Number of Traces</span>
        <Tooltip title={<span>The number of traces expected to be returned (10-100)</span>}>
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'expected_num',
    width: 180,
    render: txt => <>{txt}</>
  },
  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
          {/* {row['account_status'] === 3 ? 'Unknown' : _} */}
          {_}
        </span>
        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Created At</span>
        <Tooltip title={<span>Expired After 24 hours</span>}>
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'create_time',
    width: 180
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Status</span>
        <Tooltip title={<span>If data is expired, you can't operate it</span>}>
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'status',
    width: 120,
    fixed: 'right'
  }
];
