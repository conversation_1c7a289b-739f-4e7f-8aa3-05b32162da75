import { Form, FormInstance } from 'antd';
import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';

export interface PresetFormValues {
  presetName: string;
  timeRange: number;
  key: string;
  isDefaultPreset: boolean;
  saveAsNewPreset: boolean;
}

interface PresetConfirmModalContextType {
  visible: boolean;
  form: FormInstance<PresetFormValues>;
  showModal: () => void;
  hideModal: () => void;
  handleSave: (values: PresetFormValues) => void;
  handleCancel: () => void;
  onSaveSubscribe: (callback: (values: PresetFormValues) => void) => () => void;
  onCancelSubscribe: (callback: () => void) => () => void;
}

const PresetConfirmModalContext = createContext<PresetConfirmModalContextType | undefined>(undefined);

export const usePresetConfirmModal = () => {
  const context = useContext(PresetConfirmModalContext);
  if (!context) {
    throw new Error('usePresetConfirmModal must be used within a PresetConfirmModalProvider');
  }
  return context;
};

interface PresetConfirmModalProviderProps {
  children: React.ReactNode;
  onSave?: (values: PresetFormValues) => void;
  onCancel?: () => void;
  searchFormInstance?: FormInstance;
}

export const PresetConfirmModalProvider: React.FC<PresetConfirmModalProviderProps> = React.memo(
  ({ children, onSave: propOnSave, onCancel: propOnCancel }) => {
    const [visible, setVisible] = useState(false);
    const [saveCallbacks] = useState<Set<(values: PresetFormValues) => void>>(() => new Set());
    const [cancelCallbacks] = useState<Set<() => void>>(() => new Set());
    const [form] = Form.useForm<PresetFormValues>();
    const showModal = useCallback(() => setVisible(true), []);
    const hideModal = useCallback(() => setVisible(false), []);

    const handleSave = useCallback(
      (values: PresetFormValues) => {
        propOnSave?.(values);
        saveCallbacks.forEach(callback => callback(values));
        hideModal();
      },
      [propOnSave, saveCallbacks, hideModal]
    );

    const handleCancel = useCallback(() => {
      propOnCancel?.();
      cancelCallbacks.forEach(callback => callback());
      hideModal();
    }, [propOnCancel, cancelCallbacks, hideModal]);

    const onSaveSubscribe = useCallback(
      (callback: (values: PresetFormValues) => void) => {
        saveCallbacks.add(callback);
        return () => {
          saveCallbacks.delete(callback);
        };
      },
      [saveCallbacks]
    );

    const onCancelSubscribe = useCallback(
      (callback: () => void) => {
        cancelCallbacks.add(callback);
        return () => {
          cancelCallbacks.delete(callback);
        };
      },
      [cancelCallbacks]
    );

    const contextValue = useMemo(
      () => ({
        visible,
        form,
        showModal,
        hideModal,
        handleCancel,
        handleSave,
        onSaveSubscribe,
        onCancelSubscribe
      }),
      [visible, showModal, hideModal, handleCancel, handleSave, onSaveSubscribe, onCancelSubscribe, form]
    );

    return <PresetConfirmModalContext.Provider value={contextValue}>{children}</PresetConfirmModalContext.Provider>;
  }
);
