import dbUtil from '@/db/mysql';
import { OpenServiceAPI } from '@/types/open-service';

export type Tenant = {
  tntId: number;
  hostPrefix: string;
  csDomain: string;
};

class OpenServiceModel {
  async getWhiteBoxSupply(tntId: number): Promise<OpenServiceAPI.SupplyData[]> {
    const sql = `
      select
          sl.seller_id as seller_id,
          sl.seller_name as seller_name,
          sl.integration_type as integration_type,
          sl.relationship as relationship,
          sl.token as token,
          sl.status as status,
          sl.publisher_id as publisher_id,
          t.host_prefix as host_prefix
      from (select * from seller where seller_name = 'topon-white-seller' and tnt_id = ?) as sl
      left join tenant as t on sl.tnt_id = t.tnt_id
      order by sl.update_time desc
    `;

    return dbUtil.query(sql, [tntId]);
  }
}

export const openServiceModel = new OpenServiceModel();
