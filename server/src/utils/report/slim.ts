import { getRedisByKey } from '@/db/redisServer';
import { dictService } from '@/services/common/dict';
import { updateBQConfig } from '@rixfe/rix-tools';

/**
 * 判断 daily 表是否在更新
 * @description redis中SaaS_Lock::daily_report 大于0时，daily表在更新
 * @returns boolean
 */
export async function isDailyTableUpdating() {
  const value = (await getRedisByKey('SaaS_Lock::daily_report')) as number;
  return !!value && value > 0;
}

/**
 * update BQConfig, IsDailyTableUpdating and AdSize
 *
 */
export async function updateBQConfigAdapter() {
  const isUpdating = await isDailyTableUpdating();

  const adSizeDict = await dictService.getDict('ad_size');
  const adSizeMap: Record<string, string> = adSizeDict.reduce((acc, curr) => {
    acc[curr.value] = curr.label;
    return acc;
  }, {});

  updateBQConfig({
    IsDailyTableUpdating: isUpdating,
    AdSize: adSizeMap
  });
}

/**
 * 获取限制 limit_str
 * @param tnt_id
 * @returns
 */
export function transformLimitStr(
  tnt_id: number,
  limit?: string,
  isAll?: boolean
) {
  const allow_5w_Rows = tnt_id && [1075, 1047].includes(tnt_id);
  const limit_str = allow_5w_Rows ? '50000' : '10000';
  return isAll ? `${limit_str}` : limit || '';
}
