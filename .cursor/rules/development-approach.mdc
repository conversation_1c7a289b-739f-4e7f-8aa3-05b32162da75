---
description: project development approach
globs: 
alwaysApply: false
---
# Development Approach

## Setup and Installation

### Client Development
1. Navigate to the client directory:
```bash
cd client
```

2. Install dependencies:
```bash
yarn install --ignore-engines
```

3. Start development server:
```bash
yarn dev
```

### Server Development
1. Navigate to the server directory:
```bash
cd server
```

2. Install dependencies:
```bash
npm install
```

3. Start development server:
```bash
npm run dev
```

## Technology Stack

### Client-side
- **Framework**: React with [Umi.js](mdc:https:/umijs.org)
- **UI Components**: Ant Design Pro ([client/package.json](mdc:client/package.json))
- **State Management**: Uses Valtio and React Context
- **Build Tools**: Umi/Max framework with webpack

### Server-side
- **Framework**: Koa.js ([server/src/index.ts](mdc:server/src/index.ts))
- **Language**: TypeScript
- **Process Management**: PM2 ([server/processes.json](mdc:server/processes.json))
- **Testing**: Jest

## Code Structure Conventions

### Component Organization
- Components follow a feature-based organization
- Reusable UI components are in `client/src/components/`
- Page components are in `client/src/pages/`

### API Organization
- API routes are defined in `server/src/routers/`
- Controllers handle request/response logic in `server/src/controllers/`
- Business logic is in `server/src/services/`

## Development Workflow
1. Write code following the established patterns in the existing codebase
2. Test locally using the development server
3. Build and verify the production build
4. Deploy using the deployment script
