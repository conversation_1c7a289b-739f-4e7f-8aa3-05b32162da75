import { Modal } from "antd";

// 添加一个标记，避免重复显示弹窗
let isShowingRefreshModal = false;

// 显示刷新提示的函数
export const showRefreshModal = () => {
  if (isShowingRefreshModal) return;
  isShowingRefreshModal = true;

  Modal.confirm({
    title: 'New Version Available',
    content: 'A new version of the application is available. Please refresh the page to update.',
    okText: 'Refresh Now',
    onOk: () => {
      window.location.reload();
    },
    cancelButtonProps: { style: { display: 'none' } },
    closable: false,
    maskClosable: false
  });
};

