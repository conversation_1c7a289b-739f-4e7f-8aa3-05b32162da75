/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-21 20:13:56
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:58:52
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';

import { SampleRegionOptions, SampleType } from './index';
import { CountryOptions } from '@/constants/global-mapping/country';

export const TaskDemandSearchOption: TopBarSearchItem[] = [
  {
    name: 'Server Region',
    type: 'select',
    key: 'server_region',
    value: [],
    options: SampleRegionOptions,
    placeholder: 'Please Select Server Region',
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },

  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Bundle',
    type: 'input',
    key: 'bundle',
    value: '',
    options: [],
    placeholder: 'Please Input Bundle'
  },
  {
    name: 'Country',
    type: 'select',
    key: 'country',
    value: [],
    options: CountryOptions,
    placeholder: 'Please Select Country',
    mode: 'multiple'
  }
];

export const TaskBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Troubleshooting',
    icon: 'rix-trace'
  },
  {
    name: 'Sample Trace Task'
  }
];

export const TaskColumnKeysMap = {
  [SampleType.SupplyRequest]: [
    'tag_id',
    'seller_id',
    'status',
    'bundle',
    'plm_id',
    'server_region',
    'ad_format',
    'country',
    'expected_num',
    'account_name',
    'create_time'
  ],
  [SampleType.SupplyResponse]: [
    'tag_id',
    'buyer_id',
    'seller_id',
    'status',
    'cid',
    'crid',
    'adomain',
    'server_region',
    'ad_format',
    'country',
    'expected_num',
    'bundle',
    'plm_id',
    'account_name',
    'create_time'
  ],
  [SampleType.DemandRequest]: [
    'tag_id',
    'buyer_id',
    'seller_id',
    'status',
    'bundle',
    'plm_id',
    'plm_id',
    'server_region',
    'ad_format',
    'country',
    'expected_num',
    'account_name',
    'create_time'
  ],
  [SampleType.DemandResponse]: [
    'tag_id',
    'buyer_id',
    'seller_id',
    'status',
    'cid',
    'crid',
    'adomain',
    'server_region',
    'ad_format',
    'country',
    'expected_num',
    'account_name',
    'create_time',
    'bundle',
    'plm_id'
  ]
};
