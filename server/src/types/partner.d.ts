/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 14:12:21
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 17:59:57
 * @Description:
 */

export declare namespace PartnerAPI {
  type AddPartnerParams = {
    partner_name: string;
    type: number;
    sp_email?: string;
    dp_email?: string;
    tnt_id: number;
  }

  type UpdatePartnerParams = {
    partner_name: string;
    partner_id?: number;
    type: number;
    sp_email?: string;
    dp_email?: string;
    tnt_id: number;
  }

  type QueryPartnerAccountParams = {
    partner_id: number;
    tnt_id: number;
  }

  type CreatePartnerAccountParams = {
    partner_id: number;
    tnt_id: number;
    account_name: string;
    send_email?: string[]
    password: string;
    token: string;
    p_token: string;
    cur_user_id: number;
    brand: string;
    host_prefix: string;
    pv_domain: string;
    to_self: number;
    user_email?: string;
    role_type: number;
  }

  type EditPartnerAccountParams = {
    tnt_id: number;
    user_id: number;
    account_name: string;
    send_email?: string[]
    brand: string;
    host_prefix: string;
    pv_domain: string;
    to_self: number;
    user_email?: string;
  }
}
