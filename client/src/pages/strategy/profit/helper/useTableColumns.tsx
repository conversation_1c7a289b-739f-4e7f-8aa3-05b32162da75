import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import { ProfitBundleColumns, ProfitColumns, ProfitTab, ProfitTabType } from '@/constants/strategy/profit';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { useCallback, useMemo } from 'react';

type UseTableColumnsProps = {
  currentTab: ProfitTabType;
  operationColumn: ColumnProps<any>;
  onJumpToBundle: (params: any) => void;
};

export const useTableColumns = ({ currentTab, operationColumn, onJumpToBundle }: UseTableColumnsProps) => {
  // 获取名称列渲染器，添加高优先级配置警告
  const getNameColumnRenderer = useCallback(
    () => (txt: string, params: any) => {
      return (
        <div style={{ display: 'flex', gap: 5, alignItems: 'center' }}>
          <HoverTooltip title={`${txt}(${params.tmp_id})`}>
            <span style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
              {txt}({params.tmp_id})
            </span>
          </HoverTooltip>
          {params.exist_higher_priority && (
            <Tooltip
              title={
                <span>
                  Exists higher priority configurations, this configuration may not take effect.{' '}
                  <span style={{ color: 'var(--primary-color)', cursor: 'pointer' }} onClick={() => onJumpToBundle(params)}>
                    Click to view
                  </span>
                </span>
              }
            >
              <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', cursor: 'pointer' }} />
            </Tooltip>
          )}
        </div>
      );
    },
    [onJumpToBundle]
  );

  // 获取当前标签页的表格列配置
  const getColumns = useMemo(() => {
    const isBundleTab = currentTab === ProfitTab.bundle;
    const columns = [...(isBundleTab ? ProfitBundleColumns : ProfitColumns)];

    // 仅在非Bundle标签页添加名称列特殊渲染
    if (!isBundleTab) {
      const nameColumnIndex = columns.findIndex(col => col.dataIndex === 'name');
      if (nameColumnIndex !== -1) {
        columns[nameColumnIndex] = {
          ...columns[nameColumnIndex],
          render: getNameColumnRenderer()
        };
      }
    }

    // 添加操作列
    columns.push(operationColumn);

    return columns;
  }, [currentTab, operationColumn, getNameColumnRenderer]);

  return { getColumns };
};
