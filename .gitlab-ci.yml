variables:
  APP_NAME: saas/rixplatform
  AWS_ACCESS_KEY_ID: ${CO_AWS_ACCESS_KEY_ID}
  AWS_SECRET_ACCESS_KEY: ${CO_AWS_SECRET_ACCESS_KEY}
  CONTAINER_REGISTRY: ${CO_USE1_CONTAINER_REGISTRY}
  PROJECT_CONTAINER_REPOSITORY: ${CO_USE1_CONTAINER_REGISTRY}/saas/rix-platform
  CO_EKS_CLUSTER_1_NAME: 'algorix-use1-eks-service'
  SERVICE_NAMESPACE: 'saas-platform'
  GIT_STRATEGY: none
  LARK_BOT_WEBHOOK_URL: 'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc'
  TEST_LARK_BOT_WEBHOOK_URL: 'https://open.feishu.cn/open-apis/bot/v2/hook/60030935-5897-491d-bd6f-9f39145b9787'
  PROJECT_DIR: '/data/htdocs/saas.rix-platform'
  HOTFIX_PROJECT_DIR: '/data/htdocs/hotfix.saas.rix-platform'

workflow:
  rules:
    - if: $CI_COMMIT_TAG
    - if: $CI_COMMIT_BRANCH

stages:
  - deploy

app-test-deploy:
  stage: deploy
  tags:
    - saas
    - platform
    - test
    - use
  #rules:
  #  - if: $CI_COMMIT_BRANCH == 'test' && $CI_COMMIT_TAG =~ /^v.*/
  only:
    - test
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd $PROJECT_DIR && \
      git checkout test && \
      git fetch --all && \
      git reset --hard origin/test && \
      sh deploy.sh test && \
      exit
  after_script:
    - |
      source $PROJECT_DIR/notify_lark.sh
      notify_lark $CI_JOB_STATUS "test" $TEST_LARK_BOT_WEBHOOK_URL

app-hotfix-deploy:
  stage: deploy
  tags:
    - saas
    - platform
    - test
    - use
  #rules:
  #  - if: $CI_COMMIT_BRANCH == 'hotfix' && $CI_COMMIT_TAG =~ /^v.*/
  only:
    - hotfix
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd $HOTFIX_PROJECT_DIR && \
      export PROJECT_DIR=$(pwd) && \
      git checkout hotfix && \
      git fetch --all && \
      git reset --hard origin/hotfix && \
      sh deploy.sh hotfix && \
      exit
  after_script:
    - |
      source $HOTFIX_PROJECT_DIR/notify_lark.sh
      notify_lark $CI_JOB_STATUS "hotfix" $TEST_LARK_BOT_WEBHOOK_URL

app-prod-deploy:
  stage: deploy
  tags:
    - saas
    - platform
    - prod
    - use
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*/
  #only:
    #- /^v.*/
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd $PROJECT_DIR && \
      export PROJECT_DIR=$(pwd) && \
      git checkout master && \
      git fetch --all && \
      git reset --hard origin/master && \
      sh deploy.sh prod && \
      exit
  after_script:
    - |
      source $PROJECT_DIR/notify_lark.sh
      notify_lark $CI_JOB_STATUS "prod" $LARK_BOT_WEBHOOK_URL

app-prod-gray-deploy:
  stage: deploy
  tags:
    - saas
    - gray
    - prod
    - use
  rules:
    - if: $CI_COMMIT_TAG =~ /^g\d.*/
  #only:
  #  - /^g\d.*/
  script:
    - |
      set -e
      echo $CI_COMMIT_TAG
      echo $CI_COMMIT_BRANCH
      nvm use v18 && \
      node -v && \
      cd $PROJECT_DIR && \
      git checkout gray && \
      git fetch --all && \
      git reset --hard origin/gray && \
      sh deploy.sh prod && \
      exit
  after_script:
    - |
      source $PROJECT_DIR/notify_lark.sh
      notify_lark $CI_JOB_STATUS "prod-gray" $LARK_BOT_WEBHOOK_URL
