import React, { useState, useEffect } from 'react';
import { Switch, Button, Spin, message, Form, Radio } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import EditButton from '@/components/Button/EditButton';
import { setSupplyAuth } from '@/services/api';
import { fetchData } from '@/utils';
import Select from '@/components/Select/NormalSelect';
import Modal from '@/components/Modal/NormalModal';
import NormalRadio from '@/components/Radio/NormalRadio';
import { StatusOptions } from '@/constants';
import { useAccess } from '@umijs/max';

type AppListEditParmas = {
  status: number;
  demand_list: SupplyAPI.SellerDemandAuth[];
  appname: string;
  params: any;
  loading: boolean;
  allDemandList: DemandAPI.DemandListItem[];
  handleGetAppList: () => void;
  auth?: string;
};

const DefaultFormData = {
  status: 1,
  demand_list: []
};

const AppListEdit: React.FC<AppListEditParmas> = ({
  status,
  demand_list,
  appname = '',
  params,
  loading,
  allDemandList: demandList,
  handleGetAppList,
  auth
}) => {
  const access = useAccess();
  const [isModalVisible, setModalVisible] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    handleFillDsp();
  }, [demand_list]);

  const handleFillDsp = () => {
    if (demand_list && demand_list.length) {
      const tmp = demand_list.map(item => item.buyer_id);
      form.setFieldValue('demand_list', tmp);
    } else {
      form.setFieldValue('demand_list', []);
    }
  };
  const handleCancel = () => {
    setModalVisible(false);
  };
  const handleOk = () => {
    form.submit();
  };

  const handleOpenModal = () => {
    setModalVisible(true);
    handleFillDsp();
  };

  const onSuccess = () => {
    message.success('Success');
    handleGetAppList();
    handleCancel();
  };

  const handleEdit = (demand_ids: number[], status: number, demand_list: SupplyAPI.SellerDemandAuth[]) => {
    const ori_buyer_ids = (demand_list && demand_list.length && demand_list.map(item => item.buyer_id)) || [];
    const values = {
      ...params,
      status: status,
      buyer_ids: demand_ids,
      ori_buyer_ids
    };
    fetchData({ setLoading: setEditLoading, request: setSupplyAuth, params: values, onSuccess });
  };

  const handleFinish = (values: any) => {
    handleEdit(values.demand_list || [], values.status, demand_list);
  };
  return (
    <React.Fragment>
      <EditButton type="link" onClick={handleOpenModal} disabled={access.DisabledButton('SupplyAuthorizeAuth')}>
        Edit
      </EditButton>
      <Modal
        title={appname}
        open={isModalVisible}
        maskClosable={false}
        keyboard={false}
        width={500}
        onCancel={handleCancel}
        footer={
          <>
            <Button onClick={() => handleCancel()} disabled={loading || editLoading}>
              Cancel
            </Button>
            <Button type="primary" onClick={() => handleOk()} disabled={loading || editLoading}>
              Save
            </Button>
          </>
        }
      >
        <Spin indicator={<LoadingOutlined />} spinning={loading && isModalVisible}>
          <Form
            initialValues={DefaultFormData}
            onFinish={handleFinish}
            onFinishFailed={err => console.log(err)}
            autoComplete="off"
            form={form}
            layout="vertical"
          >
            <Form.Item name="demand_list" label="Authorized Advertiser:">
              <Select
                mode="multiple"
                optionFilterProp="children"
                filterOption={(input, option: any) =>
                  option && option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
                allowClear
              >
                {demandList &&
                  demandList.map((item: DemandAPI.DemandListItem, index: number) => (
                    <Select.Option
                      value={item.buyer_id}
                      key={index}
                    >{`${item.buyer_name}(${item.buyer_id})`}</Select.Option>
                  ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="status"
              label="Status:"
              rules={[
                {
                  required: true,
                  message: 'Please Select Status'
                }
              ]}
            >
              <NormalRadio>
                {StatusOptions.map((item, index) => (
                  <Radio key={index} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          </Form>
        </Spin>
      </Modal>
    </React.Fragment>
  );
};

export default AppListEdit;
