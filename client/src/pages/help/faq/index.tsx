/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-18 14:28:41
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-23 14:56:22
 * @Description:
 */

import React from 'react';
import MdPage from '../components/MdPage';
// @ts-ignore
import GSContent from '@/assets/md/FAQ.md';
// @ts-ignore
import EnGSContent from '@/assets/md-en/FAQ-en.md';
const FAQDoc: React.FC = () => {
  return <MdPage mdPath={GSContent} enMdPath={EnGSContent}></MdPage>;
};

export default FAQDoc;
