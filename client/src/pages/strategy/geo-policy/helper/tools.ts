import { StatusMap } from '@/constants/common';

export type PublisherOption = {
  label: string;
  value: number;
};

/**
 * 获取可用的发布商选项
 * @param supplyOptions 所有发布商选项
 * @param dataSource 已有的地理策略数据
 * @param row 当前编辑的行数据（如果是编辑模式）
 * @returns 过滤后的发布商选项
 */
export const updatePublisherOptions = (
  supplyOptions: PublisherOption[],
  dataSource: StrategyAPI.GeoPolicyKeyRelationListItem[],
  row?: StrategyAPI.GeoPolicyKeyRelationListItem
): PublisherOption[] => {
  // 快速返回：如果没有数据源，直接返回所有选项
  if (!dataSource?.length) {
    return supplyOptions;
  }

  // 收集已被使用的发布商 ID
  const existingPublishers = new Set<number>();
  const editingId = row?.id;

  // 单次遍历数据源
  for (const item of dataSource) {
    // 跳过当前编辑的项, 当前 item 状态为 paused 的时候，跳过
    if ((editingId && item.id === editingId) || item.status === StatusMap.Paused) {
      continue;
    }

    existingPublishers.add(item.seller_id);
  }

  // 如果没有已使用的发布商，直接返回所有选项
  if (existingPublishers.size === 0) {
    return supplyOptions;
  }

  // 过滤出可用的选项
  return supplyOptions.filter(option => !existingPublishers.has(option.value));
};
