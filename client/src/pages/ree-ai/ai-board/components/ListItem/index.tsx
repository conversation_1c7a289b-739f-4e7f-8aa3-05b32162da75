/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-01-03 15:09:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-20 11:25:39
 * @Description:
 */
import React from 'react';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';
import { Button, Tooltip } from 'antd';
import { AdFormatMap } from '@/constants';
import { Countries } from '@/constants/global-mapping/country';

type Props = {
  isRevenue: boolean;
  country: string;
  buyer_options: BoardAPI.BudgetAndTrafficItem['buyer_options'];
  ad_format: number;
  revenue: number; // top5 counrty revenue
  disabled: boolean;
  onClickRequest?: (
    ad_format: number,
    country: string,
    buyer_options: BoardAPI.BudgetAndTrafficItem['buyer_options']
  ) => void;
};

const ListItem: React.FC<Props> = ({
  isRevenue,
  country,
  buyer_options,
  ad_format,
  revenue,
  onClickRequest,
  disabled
}) => {
  let buyerNames = '';
  let totalRevenue = 0;
  if (buyer_options?.length) {
    buyerNames = buyer_options[0].buyer;
  }
  if (revenue) {
    totalRevenue = +revenue.toFixed(0);
  }

  const handleClick = () => {
    onClickRequest?.(ad_format, country, buyer_options);
  };

  return (
    <div className={`${styles['item-container']}  ${isRevenue ? styles['themeGreen'] : styles['themeBlue']}`}>
      <div className={styles['item-top']}>
        <div className={styles['item-top-left']}>
          <div className={styles['item-title']}>
            Your{' '}
            <b>
              {Countries[country]} {AdFormatMap[ad_format]} {isRevenue ? 'ads' : 'million requests'}
            </b>{' '}
            are performing well, would you like to request more inventory from RIX?
          </div>
        </div>
        <div className={styles['item-top-right']}>
          <Button type="primary" icon={<RixEngineFont type="rix-traffic" />} onClick={handleClick} disabled={disabled}>
            Request More Inventory
          </Button>
        </div>
      </div>
      <div className={styles['item-bottom']}>
        {buyer_options && buyer_options.length > 0 && (
          <div className={styles['buyer-container']}>
            <Tooltip
              title={
                <div className={styles['tooltip-container']}>
                  {buyer_options.map((item, index) => (
                    <div className={styles['tooltip-item']} key={index}>
                      <div className={styles['tooltip-item-left']}>{item.buyer}</div>
                    </div>
                  ))}
                </div>
              }
              placement="bottom"
            >
              <div className={`${styles['buyer-names']} ellipsis`}>{buyerNames}</div>
            </Tooltip>

            <div className="total-revenue">$ {totalRevenue}</div>
            {!isRevenue && <div>{AdFormatMap[ad_format]}</div>}
          </div>
        )}
        {/* <NormalSelect options={buyer_options} onChange={handleChange} value={value}></NormalSelect>
        <div className={styles['item-value']}>${value}</div> */}
      </div>
    </div>
  );
};
export default ListItem;
