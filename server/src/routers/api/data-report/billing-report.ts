/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-28 15:09:17
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 16:38:46
 * @Description:
 */
import Router from 'koa-router';
import { Context, Next } from 'koa';
import { advBillingController, pubBillingController } from '@/controllers';
import { setInterfaceTimeOut } from '@/utils/timeout';
import { parseDateParams, resolveCurConditionData } from '@/utils/report';
const router = new Router({ prefix: '/billing' });
const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(60 * 60 * 1000);
  await next();
};
const routers = router
  .post(
    '/getAdvertiserBillingList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    advBillingController.getAdvertiserBillingList
  )
  .post(
    '/downloadAdvertiserBillingList',
    resolveCurConditionData,
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    parseDateParams,
    advBillingController.downloadAdvertiserBillingList
  )
  .post(
    '/getPublisherBillingList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    pubBillingController.getPublisherBillingList
  )
  .post(
    '/downloadPublisherBillingList',
    resolveCurConditionData,
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    parseDateParams,
    pubBillingController.downloadPublisherBillingList
  );

export default routers;
