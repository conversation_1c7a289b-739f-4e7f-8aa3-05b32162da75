/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 19:28:40
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 10:48:52
 * @Description: e,
 */
import React, { useEffect, useState } from 'react';
import { Form, Input, Radio, message } from 'antd';
import { ScreenOrientation, PlatformOptions, Category, VastTagPlatfromOptions } from '@/constants/app-list';
import { addApp, updateApp } from '@/services/api';
import { fetchData, validUrl } from '@/utils';
import Select from '@/components/Select/NormalSelect';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';

type AddAppProps = {
  visible: boolean;
  isEdit: boolean; // 是新增还是编辑
  handleClose: () => void; // 关闭弹窗
  supply: SupplyAPI.SupplyListItem | undefined;
  fetchAppList: (isEdit?: boolean) => void;
  currentApp?: AppAPI.AppListItem | undefined;
  isIOS?: boolean;
  isSite?: boolean;
  isVastTag?: boolean;
};

const DefaultFormData = {
  app_name: '',
  bundle: '',
  store_url: '',
  category: undefined,
  platform: 1,
  screen_orientation: 1,
  ios_bundle: ''
};

function AddAppModel({
  visible,
  isEdit,
  handleClose,
  supply,
  fetchAppList,
  currentApp,
  isIOS,
  isSite,
  isVastTag
}: AddAppProps): JSX.Element {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 数据重置
    if (visible && !isEdit) {
      form.setFieldsValue(DefaultFormData);
    }
    if (visible && isEdit) {
      const category = currentApp?.category ? currentApp?.category : undefined;
      form.setFieldsValue({ ...currentApp, category });
    }
  }, [visible, isEdit, currentApp]);

  const handleConfirm = () => {
    form?.submit();
  };

  const handleFinish = (values: any) => {
    const ori_data = {} as { [key: string]: any };
    for (const key in values) {
      if (typeof values[key] === 'string') {
        values[key] = values[key].trim();
      }
      if (currentApp && currentApp[key as keyof AppAPI.AppListItem]) {
        ori_data[key] = currentApp[key as keyof AppAPI.AppListItem];
      }
    }
    if (isEdit) {
      const params = {
        ...values,
        app_id: currentApp?.app_id,
        seller_id: supply?.seller_id,
        ori_data
      };
      handleUpdateApp(params);
    } else {
      const params = {
        ...values,
        seller_id: supply?.seller_id
      };
      handleAddApp(params);
    }
  };

  const onSuccess = () => {
    message.success('success');
    fetchAppList(isEdit);
    handleClose();
  };

  const handleAddApp = (params: AppAPI.AddAppParams) => {
    fetchData({ setLoading, request: addApp, params, onSuccess });
  };

  const handleUpdateApp = (params: AppAPI.UpdateAppParams) => {
    fetchData({ setLoading, request: updateApp, params, onSuccess });
  };

  const validIOSBundle = (rule: any, value: any) => {
    const reg = /^\d+$/;
    if (!value) {
      return Promise.resolve();
    } else if (!reg.test(value)) {
      return Promise.reject(new Error('Please enter the correct bundle format,must be a numberic ID'));
    }
    return Promise.resolve();
  };

  const vaildDomain = (rule: any, value: any) => {
    const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
    if (!value) {
      return Promise.resolve();
    } else if (!reg.test(value)) {
      return Promise.reject(new Error('Please enter the correct domain format'));
    }
    return Promise.resolve();
  };
  return (
    <NormalDrawer
      blackName={isEdit ? (isSite ? `Edit Site` : `Edit App`) : isSite ? `Add Site` : `Add App`}
      grayName={`${supply?.seller_id}`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleClose}
      loading={loading}
      maskClosable={false}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
      >
        <Form.Item label="App Name:" name="app_name" rules={[{ required: true, message: 'Please input app name!' }]}>
          <Input placeholder="Please input app name" />
        </Form.Item>
        <Form.Item
          label={isSite ? 'Domain:' : 'Bundle:'}
          name="bundle"
          rules={[
            { required: true, message: isSite ? 'Please Input Domain,such as rixengine.com' : 'Please Input bundle!' },
            isIOS ? { validator: validIOSBundle } : isSite ? { validator: vaildDomain } : {}
          ]}
        >
          <Input
            placeholder={isSite ? 'Please Input Domain,such as rixengine.com' : 'Please input bundle'}
            disabled={isEdit}
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="StoreURL:"
          name="store_url"
          rules={[{ required: true, message: 'Please input StoreURL!' }, { validator: validUrl }]}
        >
          <Input placeholder="Please input StoreURL" />
        </Form.Item>
        {isIOS && (
          <Form.Item
            label="IOS Bundle:"
            name="ios_bundle"
            rules={[{ required: true, message: 'Please input ios bundle!' }]}
          >
            <Input placeholder="Please input ios bundle" disabled={isEdit} />
          </Form.Item>
        )}
        {!isIOS && (
          <>
            <Form.Item
              label="Category:"
              name="category"
              rules={[{ required: false, message: 'Please select category!' }]}
            >
              <Select placeholder="Please select category">
                {Object.keys(Category).map((key, index) => {
                  return (
                    <Select.Option key={index} value={key}>
                      {Category[key]}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item
              label="Screen Orientation:"
              name="screen_orientation"
              rules={[{ required: true, message: 'Please select screen orientation!' }]}
            >
              <NormalRadio disabled={isEdit}>
                {ScreenOrientation.map((item, index) => (
                  <Radio value={item.value} key={index}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          </>
        )}
        {!isSite && (
          <Form.Item label="Platform:" name="platform" rules={[{ required: true, message: 'Please select platform!' }]}>
            {isVastTag ? (
              <Select placeholder="Please select category" disabled={isIOS}>
                {VastTagPlatfromOptions.map((item, index) => {
                  return (
                    <Select.Option key={index} value={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            ) : (
              <NormalRadio disabled={isIOS}>
                {PlatformOptions.map((item, index) => (
                  <Radio value={item.value} key={index}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            )}
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
}

export default AddAppModel;
