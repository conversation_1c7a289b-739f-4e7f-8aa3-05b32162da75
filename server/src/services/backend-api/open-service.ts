import { commonModel, userModel } from '@/models';
import { openServiceModel } from '@/models/backend-api/open-service';
import { OpenServiceAPI } from '@/types/open-service';
import { UserAPI } from '@/types/user';

class OpenService {
  async getOneUser(options: UserAPI.LoginParams) {
    return userModel.getOneUser(options);
  }

  async getWhiteBoxSupply(
    cs_domain: string
  ): Promise<OpenServiceAPI.WhiteBoxSupply | null> {
    const tnt_id = await commonModel.getTntIdByHostName(cs_domain);

    const [supply] = await openServiceModel.getWhiteBoxSupply(tnt_id);

    if (!supply) {
      return null;
    }

    const hostPrefix = supply.host_prefix;

    // 不考虑 pv_domain
    const endpoints = {
      use: `http://${hostPrefix}.use.svr.rixengine.com/rtb?sid=${supply.seller_id}&token=${supply.token}`,
      apac: `http://${hostPrefix}.apse.svr.rixengine.com/rtb?sid=${supply.seller_id}&token=${supply.token}`,
      euw: `http://${hostPrefix}.euw.svr.rixengine.com/rtb?sid=${supply.seller_id}&token=${supply.token}`,
      global: `https://${hostPrefix}.svr.rixengine.com/rtb?sid=${supply.seller_id}&token=${supply.token}`
    };

    return {
      seller_id: supply.seller_id,
      seller_name: supply.seller_name,
      host_prefix: supply.host_prefix,
      token: supply.token,
      endpoints
    };
  }
}

export const openService = new OpenService();
