/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-14 15:31:49
 * @Description:
 */
import React from 'react';
import { Card, Descriptions } from 'antd';
import styles from './index.less';
import { SupplyCardColumns as columns } from '@/constants/supply/supply-columns';
import NormalTitle from '@/components/NormalTitle';

const SupplyCard: React.FC<{ supplyData: SupplyAPI.SupplyListItem | undefined; supplyLoading: boolean }> = ({
  supplyData,
  supplyLoading
}) => {
  return (
    <Card loading={supplyLoading} className={styles['card-container']}>
      <NormalTitle
        blackName={supplyData?.seller_name || ''}
        grayName={` ${supplyData?.seller_id}`}
        isTitle={true}
        bottom={16}
        top={16}
        toolTip={false}
      />
      <Descriptions
        className={styles['supply-descriptions-card']}
        title={''}
        size="small"
        layout="vertical"
        style={{ borderTop: 'none' }}
        column={4}
      >
        {supplyData &&
          columns &&
          columns.map((el: any, index) => {
            const text = supplyData[el.dataIndex as keyof SupplyAPI.SupplyListItem];
            return (
              <Descriptions.Item key={index} label={el.title}>
                {el.render ? el.render(text, supplyData) : text || '-'}
              </Descriptions.Item>
            );
          })}
      </Descriptions>
    </Card>
  );
};

export default SupplyCard;
