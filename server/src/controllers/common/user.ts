/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-28 17:28:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:59:57
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { userModel } from '@/models';
import { userService } from '@/services';
import { UserAPI } from '@/types/user';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';
import { v4 as uuid } from 'uuid';

import { sessionConfig } from '@/config/session';
import { RESULT } from '@/constants';
import { UserKeys } from '@/constants/user';
import { delRedisByKey, getRedisByKey, setRedisByKey } from '@/db/redis';
import {
  AutoLoginScheme,
  ConfirmPasswordScheme,
  EditDashboardUserScheme,
  EditUserAccountScheme,
  GetDashboardUserScheme,
  IsAccountNameExistsScheme,
  LoginPasswordScheme,
  ResetPasswordScheme,
  ResetUserPwdScheme,
  SwitchAccountScheme,
  VaildPasswordScheme
} from '@/schema/common';
import { getConfig, md5 } from '@/utils';
import { verifyTokenDetailed } from '@/utils/jwt';

const { encryptionStr, redisConfig } = getConfig();

class UserCtrl {
  async setUserSession(ctx: Context, userInfo: UserAPI.UserListItem) {
    const { session } = ctx;
    const { user_id, tnt_id, special_user_id, account_name, tnt_type } =
      userInfo;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    const session_id = uuid();
    const session_tmp = UserKeys.reduce(
      (acc, key) => {
        acc[key] = userInfo[key as keyof UserAPI.UserListItem] || '';
        return acc;
      },
      { session_id } as any
    );
    session!.session_id = session_id;
    session!.user_id = user_id;
    session!.tnt_id = tnt_id;
    session!.account_name = account_name || '';
    session!.special_user_id = special_user_id;
    session!.tnt_type = tnt_type || '';
    await setRedisByKey(key, session_tmp, sessionConfig.maxAge / 1000);
    getLogger('app').info(
      `${userInfo.account_name} login, redis=[${JSON.stringify(session_tmp)}]`
    );
  }

  async getUserLinkList(ctx: Context) {
    let result = { ...RESULT };
    const { special_user_id } = ctx.state.curUser || {};

    if (!special_user_id) {
      result = getCtxResult('SUCCESS', []);
    } else {
      const user_link_list = await userService.getUserLinkList(special_user_id);
      if (user_link_list) {
        result = getCtxResult('SUCCESS', user_link_list);
      }
    }
    ctx.body = result;
  }

  @validateBody(SwitchAccountScheme)
  async switchAccount(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { special_user_id } = ctx.state.curUser || {};
    const switchUserInfo = await userService.getSwitchAccount({
      switch_user_id: formData.switch_user_id,
      special_user_id
    });

    if (switchUserInfo) {
      await this.setUserSession(ctx, switchUserInfo);
      result = getCtxResult('SUCCESS', { user_id: switchUserInfo.user_id });
    } else {
      result = getCtxResult('SWITCH_ACCOUNT_ERROR');
    }
    ctx.body = result;
  }

  @validateBody(LoginPasswordScheme)
  async login(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    // const { cs_domain } = ctx.state;
    const cs_domain = 'allowed.console-t.rixengine.com';

    const user = await userService.getOneUser({
      ...formData,
      cs_domain,
      password: md5(encryptionStr + formData.password)
    });

    if (user) {
      await this.setUserSession(ctx, user);
      result = getCtxResult('SUCCESS', { user_id: user.user_id });
    } else {
      result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
    }
    ctx.body = result;
  }

  @validateBody(AutoLoginScheme)
  async authLogin(ctx: Context) {
    let result = { ...RESULT };
    const { token } = ctx.request.body;

    // 优化：统一清理 session 和 redis 的方法
    const clearSessionAndRedis = async () => {
      if (ctx.session && ctx.session.user_id && ctx.session.tnt_id) {
        const { user_id, tnt_id } = ctx.session;
        const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
        await delRedisByKey(key);
        ctx.session = null;
      }
    };
    // 根据问题抛出不同的错误状态
    // 1. 无效 token：尝试 cookie 清除，提前抛出错误状态（token 无效）
    // 2. 过期 token：尝试 cookie 清除，提前抛出错误状态（token 过期）
    const tokenResult = verifyTokenDetailed(token);

    if (tokenResult.success && tokenResult.payload) {
      const { password, ...rest } = tokenResult.payload;
      // token 验证成功，尝试获取用户信息
      const user = await userService.getOneUser({
        ...rest,
        password: md5(encryptionStr + password)
      });

      if (user && user.user_id && user.tnt_id) {
        await this.setUserSession(ctx, user);
        result = getCtxResult('SUCCESS', { user_id: user.user_id });
      } else {
        // 1101, err msg: Wrong credentials. Try again.
        result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
      }
      ctx.body = result;
      return;
    }

    // token 验证失败，根据错误类型处理
    await clearSessionAndRedis();
    if (tokenResult.error === 'expired') {
      // 1113, err msg: Your session has expired, please login again
      result = getCtxResult('FAIL_USER_SESSION_EXPIRED');
    } else {
      // 1114, err msg: Your token is invalid, please check your token
      result = getCtxResult('USER_TOKEN_INVALID');
    }

    ctx.body = result;
  }

  async logout(ctx: Context) {
    let result = { ...RESULT };
    const { user_id, tnt_id, session_id } = ctx.session!;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    const data: any = await getRedisByKey(key);
    // 同一个用户就删除 不是就不管
    if (data && data.session_id === session_id) {
      await delRedisByKey(key);
    }
    ctx.session = null;
    result = getCtxResult('SUCCESS', []);

    ctx.body = result;
  }

  @validateBody(ResetPasswordScheme)
  async resetPasswordByUserId(ctx: Context) {
    let result = { ...RESULT };
    const curUser = ctx.state.curUser || {};
    const formData = ctx.request.body;
    const { user_id, tnt_id } = ctx.session!;
    formData.user_id = user_id;
    formData.tnt_id = tnt_id;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    formData.account_name = curUser.account_name || '';
    formData.old_password = md5(encryptionStr + formData.old_password);
    formData.new_password = md5(encryptionStr + formData.new_password);
    const tmp = await userService.confirmPassword(formData);
    if (Array.isArray(tmp) && tmp.length > 0) {
      const userResult = await userService.resetPasswordByUserId(formData);
      if (userResult) {
        result = getCtxResult('SUCCESS');
        // 删除对应的redis
        await delRedisByKey(key);
        getLogger('app').info(
          `resetPasswordByUserId success formData=[${JSON.stringify(formData)}]`
        );
      }
    } else {
      result = getCtxResult('CURRENT_PASSWORD_ERROR');
    }
    ctx.body = result;
  }

  @validateBody(ConfirmPasswordScheme)
  async confirmPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { user_id, tnt_id } = ctx.session!;
    const { old_password } = formData;
    const data = await userService.confirmPassword({
      user_id,
      tnt_id,
      old_password
    });

    if (Array.isArray(data) && data.length > 0) {
      const { password } = data[0];
      if (md5(encryptionStr + old_password) === password) {
        result = getCtxResult('SUCCESS', true);
      } else {
        result = getCtxResult('CURRENT_PASSWORD_ERROR');
      }
    }
    ctx.body = result;
  }

  async currentUser(ctx: Context) {
    let result = { ...RESULT };
    const { curUser } = ctx.state;
    const data: any = { ...curUser };
    data.api_list && delete data.api_list;
    data.session_id && delete data.session_id;
    data.buyer_id && delete data.buyer_id;
    data.seller_id && delete data.seller_id;
    data.role_list && delete data.role_list;
    // role_id不能删除哈
    result = getCtxResult('SUCCESS', data);
    ctx.body = result;
  }

  async sendEmail(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id, user_id } = ctx.session!;

    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    const userInfo: any = await getRedisByKey(key);
    formData.brand = userInfo?.brand || '';
    formData.host_prefix = userInfo?.host_prefix || '';
    formData.pv_domain = userInfo?.pv_domain || '';
    formData.tnt_id = tnt_id;
    await userService.sendEmailToUser(formData);
    result = getCtxResult('SUCCESS');
    getLogger('app').info(
      `sendEmail success formData=[${JSON.stringify(formData)}]`
    );
    ctx.body = result;
  }

  @validateBody(GetDashboardUserScheme)
  async getDashboardUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;

    const list = await userService.getDashboardUser(formData);
    if (Array.isArray(list) && list.length) {
      const { password, ...rest } = list[0] || {};
      const dashboardUser = {
        ...rest,
        isCreate: !!password
      };

      result = getCtxResult('SUCCESS', [dashboardUser]);
      getLogger('app').info(
        `getSupplyUser success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(IsAccountNameExistsScheme)
  async isAccountNameExists(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const data = await userService.isAccountNameExists(formData);
    if (Array.isArray(data) && data.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
      getLogger('app').info(
        `isAccountNameExists success formData=[${JSON.stringify(formData)}]`
      );
    } else {
      result = getCtxResult('SUCCESS');
    }
    ctx.body = result;
  }

  @validateBody(EditDashboardUserScheme)
  async editDashboardUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.new_password &&
      (formData.new_password = md5(encryptionStr + formData.new_password));
    const data = await userService.editDashboardUser(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(
        `editDashboardUser success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(VaildPasswordScheme)
  async vaildPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id, user_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    formData.user_id = user_id;
    formData.password = md5(encryptionStr + formData.password);
    const data = await userModel.vaildPassword(formData);
    if (Array.isArray(data) && data.length) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(
        `vaildPassword success formData=[${JSON.stringify(formData)}]`
      );
    } else {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
    }
    ctx.body = result;
  }

  // 通用的更改账号
  @validateBody(EditUserAccountScheme)
  async updateAccountName(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const {
      brand = '',
      host_prefix = '',
      pv_domain = '',
      user_email = ''
    } = ctx.state.curUser || {};
    formData.brand = brand || 'RixEngine';
    formData.host_prefix = host_prefix;
    formData.pv_domain = pv_domain;
    formData.user_email = user_email;
    const flag = await userService.isAccountNameExists(formData);
    if (flag?.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
    } else {
      const data = await userService.updateAccountName(formData);
      if (data) {
        result = getCtxResult('SUCCESS', data);
      }
    }
    ctx.body = result;
  }

  // 通用的从重置密码
  @validateBody(ResetUserPwdScheme)
  async resetPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const {
      brand = '',
      host_prefix = '',
      pv_domain = '',
      user_email = ''
    } = ctx.state.curUser || {};
    formData.brand = brand || 'RixEngine';
    formData.host_prefix = host_prefix;
    formData.pv_domain = pv_domain;
    formData.user_email = user_email;
    const data = await userService.resetPassword(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const userCtrl = new UserCtrl();
