.notify-con {
  width: 100%;
  height: 100%;
  cursor: pointer;
  :global {
    .ant-badge {
      .ant-badge-count {
        font-size: 11px;
        height: 12px;
        line-height: 12px;
        border-radius: 6px;
      }
      .ant-badge-multiple-words {
        padding: 0 6px;
      }
    }
  }
}
.mail-icon {
  font-size: 20px;
}
.notification-drawer-container {
  :global {
    .ant-drawer-header {
      border-bottom: none;
      padding: 20px;
      .ant-drawer-title {
        color: var(--text-color);
        font-weight: 700;
        font-size: 16px;
      }
    }
    .ant-drawer-body {
      padding: 0px;
    }
    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
    }

    .ant-list {
      .ant-list-item {
        border-radius: 8px;
        padding: 12px;
        &:hover {
          background-color: #e6e8eb;
        }
        cursor: pointer;
      }
      .ant-list-item {
        .ant-list-item-meta-description {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .content-container {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 12px;
    .content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .time {
      color: '#5E6466';
    }
  }
}

.footer {
  text-align: center;
}
