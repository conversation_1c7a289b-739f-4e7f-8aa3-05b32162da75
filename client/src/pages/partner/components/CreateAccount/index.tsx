import React, { useEffect, useState } from 'react';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { Form, Checkbox, message } from 'antd';
import Input from '@/components/Input/NormalInput';
import styles from './index.less';
import { fetchData } from '@/utils';
import { createPartnerAccount, resetAccountName, restUserPwd } from '@/services/api';
import { UserType } from '@/constants';
import NormalRadio from '@/components/Radio/NormalRadio';
import { RoleType } from '@/constants/permission/role';

type Props = {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  name: string;
  item?: PartnerAPI.PartnerUser;
  type: 'Create' | 'Edit' | 'Password'; // 重置密码
  partnerData?: PartnerAPI.PartnerListItem;
  options: {label: string, value: number}[];
  disabledOpt: number[]; // 禁用的类型
}
const Page: React.FC<Props> = ({ visible, onClose, onSave, name, item, type, partnerData, options, disabledOpt }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [roleType, setRoleType] = useState(RoleType['Demand Partner']);

  useEffect(() => {
    if (type !== 'Create' && item && visible) {
      form.setFieldsValue({ account_name: item.account_name.replace('ParA_', '').replace('ParP_', '').replace('Par_', ''), role_type: item.role_type });
      setRoleType(item.role_type);
    } else {
      form.resetFields();
      const arr = options.filter(v => !disabledOpt.includes(v.value)).map(v => v.value);
      const tmp = arr[0] || RoleType['Demand Partner'];
      setRoleType(tmp);
      form.setFieldsValue({ role_type: tmp, account_name: '', password: '' });
    }
  }, [visible, item, type]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (value: any) => {
    const send_email = value.send_email?.split('\n').filter((v: string) => v && v.trim()) || [];
    // 替换
    const pre = value.role_type === RoleType['Demand Partner'] ? 'ParA' : 'ParP';
    const params = {
      to_self: value.to_self ? 1 : 2,
      account_name: `${pre}_${value.account_name}`,
      send_email,
      partner_id: partnerData?.partner_id || 0,
      user_id: item?.user_id || 0,
      type: UserType.Partner,
      password: value.password || '',
      role_type: value.role_type || RoleType['Demand Partner'] // 角色类型
    };
    const request = type === 'Create' ? createPartnerAccount : type === 'Edit' ? resetAccountName : restUserPwd;
    fetchData({ request: request, params, setLoading, onSuccess: () => {
      message.success('Success');
      onSave();
    } });
  };

  const onValueChange = (changeVal: any, allValue: any) => {
    setRoleType(allValue.role_type);
  };

  return (
    <NormalDrawer
      blackName={type === 'Edit' ? `Edit Partner Account` : type === 'Create' ? `Create Partner Account` : 'Reset Password'}
      grayName={name}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onClose}
      loading={loading}
      maskClosable={false}
      titleMaxWidth={325}
    >
      <Form
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        validateTrigger={['onChange', 'onBlur']}
        initialValues={{ account_name: '', password: '', repeat_password: '' }}
        onValuesChange={onValueChange}
      >
        <Form.Item
          name="account_name"
          label="User Name:"
          rules={[
            {
              required: true,
              message: 'Please Enter User Name'
            }
          ]}
        >
          <Input
            placeholder="Please Enter User Name"
            autoComplete="new-user"
            addonBefore={roleType === RoleType['Demand Partner'] ? 'ParA_' : 'ParP_'}
            disabled={type === 'Password'}
          />
        </Form.Item>
        <Form.Item
          name="role_type"
          label="Type:"
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <NormalRadio
            options={options.map(v => {
              const disabled = disabledOpt.includes(+v.value);
              return { ...v, disabled: type === 'Create' && disabled };
            })}
            disabled={type !== 'Create'}
          />
        </Form.Item>
        {
          type === 'Create' && (
            <Form.Item
              name="password"
              label="Password:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Input Password!'
                },
                {
                  type: 'string',
                  min: 6,
                  max: 25,
                  message: 'Please input 6 to 25 characters'
                },
                () => ({
                  validator(_, value) {
                    const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                    const chineseReg = /[\u4e00-\u9fa5]/;
                    if (!value || !chineseReg.test(value)) {
                      return Promise.resolve();
                    }
                    if (!reg.test(value)) {
                      return Promise.reject(new Error('Password must contain number and capitals'));
                    }
                    return Promise.reject(new Error('Password cannot contain Chinese'));
                  }
                })
              ]}
            >
              <Input.Password allowClear autoComplete="off" />
            </Form.Item>
          )
        }
        {
          type === 'Password' && (
            <>
              <Form.Item
                name="password"
                label="New Password:"
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                  {
                    required: true,
                    message: 'Please Input New Password!'
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters'
                  },
                  () => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      const chineseReg = /[\u4e00-\u9fa5]/;
                      if (
                        !value ||
                        (reg.test(value) && !chineseReg.test(value))
                      ) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Password must contain number and capitals'));
                      }
                      return Promise.reject(new Error('Password cannot contain Chinese'));
                    }
                  })
                ]}
                hasFeedback
              >
                <Input.Password allowClear />
              </Form.Item>
              <Form.Item
                name="repeat_password"
                label="Repeat Password:"
                validateTrigger={['onBlur']}
                dependencies={['password']}
                rules={[
                  {
                    required: true,
                    message: 'Please Repeat Password'
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters'
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      if (!value || (reg.test(value) && getFieldValue('password') === value)) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Password must contain number and capitals'));
                      }
                      return Promise.reject(new Error('Please enter the same password'));
                    }
                  })
                ]}
                hasFeedback
              >
                <Input.Password allowClear />
              </Form.Item>
            </>
          )
        }
        <Form.Item
          name="send_email"
          label="Sent to email"
          rules={[
            {
              required: false,
              message: 'Please Input email!'
            },
            () => ({
              validator(_, value) {
                if (!value) {
                  return Promise.resolve();
                }
                const emails =
                        value
                          .split('\n')
                          .filter((v: string) => v && v.trim())
                          .map((v: string) => v.trim()) || [];
                const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                const isValidate = emails?.every((v: string) => reg.test(v));
                if (!isValidate) {
                  return Promise.reject(new Error('Please enter the correct email!'));
                }
                return Promise.resolve();
              }
            })
          ]}
        >
          <Input.TextArea
            placeholder={`Please Input email (one per line)`}
            allowClear
            autoSize={{ maxRows: 10, minRows: 5 }}
          />
        </Form.Item>
        <Form.Item name="to_self" className={styles['self-email']} valuePropName="checked">
          <Checkbox>
            To Yourself
          </Checkbox>
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default Page;
