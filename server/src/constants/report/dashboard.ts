/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-06 21:05:37
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-27 15:05:54
 * @Description:
 */

/**
 * @deprecated 特殊的维度，目前用于1050租户
 */
export const SpecialDimension = ['app_name'];

export const AllDashboardDimension = [
  'day',
  'day_hour',
  'seller_id',
  'buyer_id',
  'app_bundle_id',
  'placement_id',
  'ad_format',
  'ad_width, ad_height',
  'country',
  'platform',
  'region',
  'app_name',
  'ad_domain',
  'inventory',
  'device_type',
  'res_crid',
  'seller_schain_hop',
  'seller_schain_complete'
];

// full report的指标
export const AllDashboardMetrics = [
  'buyer_net_revenue',
  'seller_net_revenue',
  'profit',
  'request',
  'total_request',
  'block_request',
  'out_request',
  'response',
  'win',
  'impression',
  'click',
  'total_seller_bid_floor',
  'fill_rate',
  'buyer_gross_revenue',
  'seller_gross_revenue',
  'ecpr',
  'adv_ecpr',
  'click_rate',
  'profit_rate',
  'win_rate',
  'buyer_gross_ecpm',
  'buyer_net_ecpm',
  'impression_rate',
  'seller_net_ecpm',
  'real_qps',
  'adv_config_qps',
  'pub_config_qps',
  'bid_price',
  'seller_payment_impression',
  'avg_dsp_cost_time',
  'avg_response_cost_time',
  'cpc'
];

export const SchainMap: Record<number, string> = {
  0: 'Incomplete',
  1: 'Complete',
};

export const DateType = {
  Null: 0,
  Day: 1,
  Hour: 2,
  Month: 3
};

export const DeviceTypeMapDesc: { [key: number]: string } = {
  1: 'Mobile/Tablet',
  2: 'PC',
  3: 'CTV',
  4: 'Phone',
  5: 'Tablet',
  6: 'Connected Device',
  7: 'Set Top Box',
  0: 'Unknown',
};
