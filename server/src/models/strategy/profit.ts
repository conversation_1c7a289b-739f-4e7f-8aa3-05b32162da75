/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-06 18:36:25
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:33:53
 * @Description:
 */
import { UserType } from '@/constants';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { ProfitAPI } from '@/types/profit';

class ProfitModel implements ProfitAPI.Profit {
  async addBundleProfit(
    params: ProfitAPI.AddBundleProfitParams
  ): Promise<boolean> {
    const {
      buyer_id,
      profit_ratio,
      tnt_id,
      op_id,
      type,
      seller_id = 0,
      bundle
    } = params;

    const bundles = Array.isArray(bundle) ? bundle : [bundle];
    const values = bundles.map(bundle => [
      buyer_id,
      seller_id,
      profit_ratio,
      op_id,
      tnt_id,
      type,
      bundle
    ]);

    const sql = `insert into profit (buyer_id, seller_id, profit_ratio, op_id, tnt_id, type, bundle) values ? on duplicate key update status=1, profit_ratio=?, op_id=?`;

    return !!(await dbUtil.query(sql, [values, profit_ratio, op_id]));
  }

  async getProfitList(
    tnt_id: number,
    cur_time_zone: string,
    profit_types: number[]
  ): Promise<
    (ProfitAPI.ProfitListItem & {
      account_type: number;
    })[]
  > {
    const accountName = `(case when u.type in (${[
      UserType.Rix_Admin,
      UserType.Rix_Data_Analyst
    ].join(
      ','
    )}) then "System" when u.status = 3 then "UnKnow" else u.account_name end) as account_name`;

    const sql = `
        select 
          pf.id as id,
          pf.buyer_id as buyer_id,
          pf.seller_id as seller_id,
          pf.profit_ratio as profit_ratio,
          pf.op_id as op_id,
          pf.status as status,
          pf.type as type,
          pf.bundle as bundle,
          ${getTzTimeSql('pf.update_time', cur_time_zone)} as update_time,
          sl.seller_name as seller_name,
          sl.status as seller_status,
          buy.buyer_name as buyer_name,
          u.status as account_status,
          u.type as account_type,
          ${accountName}
        from (select * from profit where tnt_id=?) as pf
        left join seller as sl on pf.seller_id = sl.seller_id
        left join buyer as buy on pf.buyer_id = buy.buyer_id
        left join user as u on u.user_id = pf.op_id
        where pf.type in (?)
        order by pf.update_time desc
    `;
    return await dbUtil.query(sql, [tnt_id, profit_types]);
  }

  // 支持批量编辑
  async addProfit(params: ProfitAPI.AddProfitParams): Promise<boolean> {
    const { profit_ratio, tnt_id, op_id, type, seller_id, buyer_id } = params;
    const len = Array.isArray(buyer_id)
      ? buyer_id.length
      : (seller_id as number[]).length;
    const sellers = Array.isArray(seller_id)
      ? seller_id
      : new Array(len).fill(seller_id);
    const buyers = Array.isArray(buyer_id)
      ? buyer_id
      : new Array(len).fill(buyer_id);

    const values = sellers.map((seller, index) => [
      buyers[index],
      seller,
      profit_ratio,
      op_id,
      tnt_id,
      type
    ]);

    const sql = `insert into profit (buyer_id, seller_id, profit_ratio, op_id, tnt_id, type) values ? on duplicate key update status=?, profit_ratio=?, op_id=?`;
    return !!(await dbUtil.query(sql, [values, 1, profit_ratio, op_id]));
  }

  async updateProfit(params: ProfitAPI.UpdateProfitParams): Promise<boolean> {
    const { profit_ratio, op_id, id, tnt_id, status } = params;
    const sql = `update profit set profit_ratio = ?, op_id = ?, status = ? where id = ? and tnt_id = ?`;
    return !!(await dbUtil.query(sql, [
      profit_ratio,
      op_id,
      status,
      id,
      tnt_id
    ]));
  }
}

export default new ProfitModel();
