/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:59:01
 * @Description:
 */
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import Input from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { useTitleComponentMap } from '@/components/Title';
import TocAnchor from '@/components/TocAnchor';
import {
  getProfitMinValue,
  ProfitMaxValue,
  ProfitModelOptions,
  ProfitModelType,
  StatusMap,
  StatusOptions,
  YesNoMap
} from '@/constants';
import {
  AuctionOptions,
  AuctionType,
  DefaultSchainHops,
  ImpTrackingOptions,
  ImpTrackingType,
  IvtTypeMap,
  IvtTypeOptions,
  NativeFormatOptions,
  NativeFormatType,
  NativeVersionOptions,
  NativeVersionType,
  SchainCompleteMap,
  SchainCompleteOptions
} from '@/constants/demand';
import { RatioRule, RevShareRatioRule } from '@/constants/strategy';
import { addDemand, isAccountNameExists, updateDemand } from '@/services/api';
import { fetchData, isValidName } from '@/utils';
import { handleFormError } from '@/utils/form';
import { isCustomProfitModel } from '@/utils/permission';
import { useAccess, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Form, message, Radio } from 'antd';
import { useEffect, useMemo, useState } from 'react';

type AddDemandModelProps = {
  demand?: DemandAPI.DemandListItem;
  visible: boolean;
  isEdit: boolean;
  partnerList: PartnerAPI.PartnerListItem[];
  handleClose: () => void;
  reloadDemand: () => void;
};

const DefaultFormData = {
  buyer_name: '',
  demand_account_name: '',
  integration_type: undefined,
  profit_model: ProfitModelType.Net,
  profit_ratio: 30,
  rev_share_ratio: 100,
  profit_status: StatusMap.Active,
  auction_type: AuctionType['First Price'],
  imp_track_type: ImpTrackingType.JS,
  native_format: NativeFormatType.String,
  native_version: NativeVersionType['1.2'],
  schain_required: StatusMap.Paused,
  schain_complete: SchainCompleteMap.Paused,
  is_schain_hops_unlimited: IvtTypeMap.Unlimited,
  schain_hops: DefaultSchainHops,
  pass_display_manager: StatusMap.Paused,
  display_manager_filter: StatusMap.Paused,
  filter_mraid: YesNoMap.No,
  idfa_required: StatusMap.Paused,
  pixl_ivt_type: IvtTypeMap.Unlimited,
  hm_ivt_type: IvtTypeMap.Unlimited,
  omid_track: StatusMap.Paused,
  multi_format: StatusMap.Paused,
  // 仅开放给 1052 和 1075 创建和编辑
  banner_multi_size: StatusMap.Paused
};

// 仅开放给 1052 和 1075 创建和编辑
export const isBannerMultiSizeLimitTnTs = (tnt_id: number) => {
  return [1052, 1075].includes(tnt_id);
};

const tocOptions = [
  {
    href: '#basic-info',
    title: 'Basic Info'
  },
  {
    href: '#profit-settings',
    title: 'Profit Settings'
  },
  {
    href: '#tracking-settings',
    title: 'Tracking Settings'
  },
  {
    href: '#filter-and-compatibility-strategy',
    title: 'Filter and Compatibility Strategy'
  }
];

const AddDemandModel: React.FC<AddDemandModelProps> = ({
  demand,
  handleClose,
  visible,
  isEdit,
  reloadDemand,
  partnerList
}) => {
  const [form] = Form.useForm();
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const isCustom = isCustomProfitModel(initialState?.currentUser?.tnt_id ?? 0, 'demand');
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);
  const profitModelOptions = isCustom ? ProfitModelOptions : ProfitModelOptions.filter(v => v.value === 1);
  const [loading, setLoading] = useState(false);
  const [profitModel, setProfitModel] = useState(ProfitModelType.Net);
  const [profitStatus, setProfitStatus] = useState<number>(StatusMap.Active);
  const [pixlIvtType, setPixlIvtType] = useState<number>(IvtTypeMap.Unlimited);
  const [hmIvtType, setHmIvtType] = useState<number>(IvtTypeMap.Unlimited);
  const { dataSource: integrationTypeList, reload } = useModel('useBuyerIntegrationTypeList');
  const { runAsync: checkedName } = useRequest(isAccountNameExists, {
    debounceWait: 300,
    manual: true
  });
  useEffect(() => {
    if (visible && !isEdit) {
      handleDefaultType();
    }
    if (!integrationTypeList && visible) {
      reload();
    }
  }, [integrationTypeList, visible]);

  useEffect(() => {
    if (demand && isEdit && visible) {
      demand.pixl_ivt_type = demand.max_pxl_ivt_ratio === -1 ? IvtTypeMap.Unlimited : IvtTypeMap.Limited;
      demand.hm_ivt_type = demand.max_hm_ivt_ratio === -1 ? IvtTypeMap.Unlimited : IvtTypeMap.Limited;
      const schain_hops = demand.schain_hops > 0 ? demand.schain_hops : undefined;
      form.setFieldsValue({
        ...demand,
        is_schain_hops_unlimited: schain_hops ? IvtTypeMap.Limited : IvtTypeMap.Unlimited,
        dp_id: demand.dp_id > 0 ? demand.dp_id : undefined,
        schain_hops
      });
      setProfitModel(demand.profit_model);
      setProfitStatus(demand.profit_status);
      setPixlIvtType(demand.pixl_ivt_type);
      setHmIvtType(demand.hm_ivt_type);
    } else {
      form.resetFields();
      setProfitModel(ProfitModelType.Net);
      setProfitStatus(StatusMap.Active);
      setPixlIvtType(IvtTypeMap.Unlimited);
      setHmIvtType(IvtTypeMap.Unlimited);
      handleDefaultType();
    }
  }, [demand, isEdit, visible]);

  const handleDefaultType = () => {
    if (integrationTypeList && integrationTypeList.length) {
      form.setFieldValue('integration_type', integrationTypeList[0].id);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };
  const handleFinish = ({ is_schain_hops_unlimited, schain_hops, schain_required, ...rest }: any) => {
    // 当 schain_required 为 active 时，才根据 is_schain_hops_unlimited 和 schain_hops 来设置 schain_hops
    const formSchainHops =
      schain_required === StatusMap.Active && is_schain_hops_unlimited === IvtTypeMap.Limited
        ? schain_hops
        : schain_required === StatusMap.Active
        ? 0
        : undefined;

    const newValues = {
      ...rest,
      buyer_name: rest.buyer_name?.trim(),
      demand_account_name: rest.demand_account_name?.trim(),
      schain_required,
      schain_hops: formSchainHops
    };

    if (isEdit) {
      handleEditDemand({
        hm_ivt_type: 2,
        pixl_ivt_type: 2,
        ...newValues,
        buyer_id: demand?.buyer_id,
        profit_id: demand?.profit_id,
        user_id: demand?.user_id,
        demandNameChange: demand?.buyer_name !== newValues.buyer_name,
        ori_data: demand || {}
      });
    } else {
      handleAddDemand({
        hm_ivt_type: 2,
        pixl_ivt_type: 2,
        ...newValues
      });
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadDemand();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleAddDemand = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addDemand, params, onSuccess });
  };

  const handleEditDemand = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateDemand, params });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.profit_model) {
      setProfitModel(changeValue.profit_model);
    }
    if (changeValue.profit_status) {
      setProfitStatus(changeValue.profit_status);
    }
    if (changeValue.pixl_ivt_type) {
      form.setFieldsValue({ max_pxl_ivt_ratio: 0 });
      setPixlIvtType(changeValue.pixl_ivt_type);
    }
    if (changeValue.hm_ivt_type) {
      form.setFieldsValue({ max_hm_ivt_ratio: 0 });
      setHmIvtType(changeValue.hm_ivt_type);
    }
    if (changeValue.buyer_name && !isEdit) {
      form.setFieldsValue({ demand_account_name: changeValue.buyer_name?.trim() });
      form.validateFields(['demand_account_name']);
    }
  };

  // 处理 finish 失败的逻辑
  const handleFinishFailed = useMemo(() => handleFormError(form), [form]);

  const titleComponentMap = useTitleComponentMap(tocOptions);

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Advertiser` : `Create Advertiser`}
      grayName={isEdit ? `${demand?.buyer_name}` : ''}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={false}
      titleMaxWidth={325}
      width={'max(600px, 100%)'}
      contentWrapperStyle={{
        width: '670px',
        maxWidth: '100%'
      }}
    >
      <div style={{ display: 'flex', gap: 16, alignItems: 'flex-start' }}>
        <Form
          initialValues={DefaultFormData}
          onFinish={handleFinish}
          onFinishFailed={handleFinishFailed}
          autoComplete="off"
          form={form}
          layout="vertical"
          onValuesChange={handleValueChange}
          validateTrigger={['onChange', 'onBlur']}
          style={{ flex: 1 }}
        >
          {titleComponentMap['basic-info']}
          <Form.Item
            name="buyer_name"
            label="Advertiser Name:"
            rules={[
              {
                required: true,
                message: 'Please Enter Advertiser Name'
              },
              {
                validator: (rule: any, value: any) => {
                  if (!value || isValidName(value)) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error('Only letters/numbers/Chinese characters/spaces/underscores/center lines are supported')
                  );
                }
              }
            ]}
          >
            <Input placeholder="Please Enter Advertiser Name" />
          </Form.Item>
          <Form.Item
            name="demand_account_name"
            label="Advertiser Account Name:"
            rules={[
              {
                required: true,
                message: 'Please Enter Advertiser Account Name'
              },
              {
                validator: async (_, value) => {
                  if (!value) {
                    return Promise.resolve();
                  }
                  const res = await checkedName({ account_name: `Adv_${value}` });
                  if (res.code === 0) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(`${res.message}.Please Edit!`));
                }
              }
            ]}
          >
            <Input
              placeholder="Please Enter Advertiser Account Name"
              addonBefore={isEdit ? undefined : 'Adv_'}
              disabled={isEdit}
              allowClear
            />
          </Form.Item>
          <Form.Item
            name="dp_id"
            label="Partner:"
            rules={[
              {
                required: false,
                message: 'Please Select Partner'
              }
            ]}
          >
            <NormalSelect
              options={partnerList.map(v => ({ label: `${v.partner_name}(${v.partner_id})`, value: v.dp_id }))}
              showSearch
              allowClear
            />
          </Form.Item>

          <Form.Item
            name="integration_type"
            label="Integration Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Integration Type'
              }
            ]}
          >
            <NormalRadio disabled={isEdit}>
              {integrationTypeList &&
                integrationTypeList.map((item, index) => (
                  <Radio key={index} value={item.id}>
                    {item.itg_name}
                  </Radio>
                ))}
            </NormalRadio>
          </Form.Item>
          {isEdit && (
            <Form.Item
              name="status"
              label="Status:"
              rules={[
                {
                  required: true,
                  message: 'Please Select Status'
                }
              ]}
            >
              <NormalRadio options={StatusOptions} />
            </Form.Item>
          )}
          {titleComponentMap['profit-settings']}
          <Form.Item
            name="profit_status"
            label="Profit:"
            rules={[
              {
                required: true,
                message: 'Please Select Profit Status'
              }
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          {profitStatus === StatusMap.Active && (
            <Form.Item
              name="profit_ratio"
              label="Profit Ratio:"
              rules={[
                {
                  required: true,
                  message: 'Please Input Profit Ratio'
                },
              ]}
            >
              <InputNumber min={profitMinValue} max={ProfitMaxValue} addonAfter="%" style={{ width: 120 }} />
            </Form.Item>
          )}
          <Form.Item
            name="profit_model"
            label="Profit Model:"
            rules={[
              {
                required: true,
                message: 'Please Select Profit Model'
              }
            ]}
          >
            <NormalRadio options={profitModelOptions} />
          </Form.Item>
          {profitModel === ProfitModelType['Rev Share'] && (
            <Form.Item
              name="rev_share_ratio"
              label="Rev Share Ratio:"
              rules={[
                {
                  required: true,
                  message: 'Please Input Rev Share Ratio'
                },
                RevShareRatioRule
              ]}
            >
              <InputNumber min={1} addonAfter="%" max={100} />
            </Form.Item>
          )}
          {titleComponentMap['tracking-settings']}
          <Form.Item
            name="auction_type"
            label="Auction Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Auction Type'
              }
            ]}
          >
            <NormalRadio options={AuctionOptions} />
          </Form.Item>
          <Form.Item
            name="imp_track_type"
            label="Impression Track Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Impression Track Type'
              }
            ]}
          >
            <NormalRadio options={ImpTrackingOptions} />
          </Form.Item>
          <Form.Item
            name="omid_track"
            label="OMID Track (MRC):"
            rules={[
              {
                required: true,
                message: 'Please Select OMID Track'
              }
            ]}
            tooltip="An impression is counted if it meets the MRC viewability standard, with at least 50% of the banner visible in the active browser window for a minimum of 1 second."
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          {titleComponentMap['filter-and-compatibility-strategy']}
          <Form.Item
            name="schain_required"
            label="Pass Supply Chain:"
            rules={[
              {
                required: true,
                message: 'Please Select Pass Supply Chain'
              }
            ]}
            tooltip="Whether to send schain to Demand"
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          {/* 当 schain_required 为 1 （active）时，展示 schain_complete */}
          <Form.Item noStyle dependencies={['schain_required']}>
            {({ getFieldValue }) => {
              if (getFieldValue('schain_required') === StatusMap.Active) {
                return (
                  <Form.Item
                    name="schain_complete"
                    label="Schain Complete Required:"
                    rules={[{ required: true, message: 'Please Select Schain Complete' }]}
                    tooltip="Filter requests with schain.compelete equal to 0"
                  >
                    <NormalRadio options={SchainCompleteOptions} />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          {/* is_schain_hops_unlimited: 1, 2 */}
          <Form.Item noStyle dependencies={['schain_required']}>
            {({ getFieldValue }) => {
              if (getFieldValue('schain_required') === StatusMap.Active) {
                return (
                  <Form.Item
                    name="is_schain_hops_unlimited"
                    label="Schain Hops Block:"
                    rules={[
                      {
                        required: true,
                        message: 'Please Select Schain Hops Block'
                      }
                    ]}
                    tooltip="Allow blocking requests with schain-hops, with no limit by default"
                  >
                    <NormalRadio options={IvtTypeOptions} />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          {/* 当 schain_required 为 1 （active）时，展示 schain_hops */}
          <Form.Item noStyle dependencies={['is_schain_hops_unlimited']}>
            {({ getFieldValue }) => {
              if (getFieldValue('is_schain_hops_unlimited') === IvtTypeMap.Limited) {
                return (
                  <Form.Item
                    name="schain_hops"
                    label="Schain Hops Filter:"
                    rules={[{ required: true, message: 'Please Input Schain Hops Filter' }]}
                    tooltip="Filter requests sent to Demand with more than N schain-hops, where N is between 1 and 10"
                  >
                    <InputNumber min={1} max={10} />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          <Form.Item
            name="pass_display_manager"
            label="Pass Display Manager:"
            rules={[{ required: true, message: 'Please Select Pass Display Manager' }]}
            tooltip="Whether to send displaymanager to Demand"
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          <Form.Item noStyle dependencies={['pass_display_manager']}>
            {({ getFieldValue }) => {
              // 当 pass_display_manager 为 Active 时，展示 display_manager_filter
              if (getFieldValue('pass_display_manager') === StatusMap.Active) {
                return (
                  <Form.Item
                    name="display_manager_filter"
                    label="Display Manager Filter:"
                    rules={[{ required: true, message: 'Please Select Display Manager Filter' }]}
                    tooltip="Filter requests where display manager is empty"
                  >
                    <NormalRadio options={StatusOptions} />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          <Form.Item
            name="filter_mraid"
            label="Mraid Traffic Filter:"
            rules={[
              {
                required: true,
                message: 'Please Select Filtering Mraid'
              }
            ]}
            tooltip="Filter requests that lack support for Mraid"
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          <Form.Item
            name="idfa_required"
            label="IFA Required:"
            rules={[
              {
                required: true,
                message: 'Please Select IFA Required'
              }
            ]}
            tooltip="Filter requests with an empty IFA(gaid/idfa)"
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          <Form.Item
            name="multi_format"
            label="Multi Format:"
            rules={[
              {
                required: true,
                message: 'Please Select Multi Format'
              }
            ]}
            tooltip="Support both a banner ad and a video ad format within a single BidRequest.imp at the same index (e.g. imp[0])"
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
          {isBannerMultiSizeLimitTnTs(initialState?.currentUser?.tnt_id ?? 0) && (
            <Form.Item
              name="banner_multi_size"
              label="Banner Multi Size:"
              rules={[{ required: true, message: 'Please Select Multi Format' }]}
              tooltip="Add ad size 300*250 for requests with a 320*50 banner"
            >
              <NormalRadio options={StatusOptions} />
            </Form.Item>
          )}
          <Form.Item
            name="native_format"
            label="Native Format:"
            rules={[
              {
                required: true,
                message: 'Please Select Native Format'
              }
            ]}
            tooltip="Support imp.native format conversion, String by default"
          >
            <NormalRadio options={NativeFormatOptions} />
          </Form.Item>
          <Form.Item
            name="native_version"
            label="Native Version:"
            rules={[
              {
                required: true,
                message: 'Please Select Native Version'
              }
            ]}
            tooltip="Native protocol version sent to Demand. Default value: 1.2"
          >
            <NormalRadio options={NativeVersionOptions} />
          </Form.Item>
          {access.isPixalateHuman && (
            <>
              <Form.Item
                name="pixl_ivt_type"
                label="Px IVT Block:"
                rules={[
                  {
                    required: true,
                    message: 'Please Select Profit Status'
                  }
                ]}
              >
                <NormalRadio options={IvtTypeOptions} />
              </Form.Item>
              {pixlIvtType === IvtTypeMap.Limited && (
                <Form.Item
                  name="max_pxl_ivt_ratio"
                  label="Max Px IVT:"
                  rules={[
                    {
                      required: true,
                      message: 'Please Input Max Px IVT(%)'
                    },
                    RatioRule
                  ]}
                  tooltip="Requests exceeding the configured IVT threshold will be blocked"
                >
                  <InputNumber min={0} addonAfter="%" style={{ width: 120 }} defaultValue={0} />
                </Form.Item>
              )}
              <Form.Item
                name="hm_ivt_type"
                label="Hm IVT Block:"
                rules={[
                  {
                    required: true,
                    message: 'Please Select Profit Status'
                  }
                ]}
              >
                <NormalRadio options={IvtTypeOptions} />
              </Form.Item>
              {hmIvtType === IvtTypeMap.Limited && (
                <Form.Item
                  name="max_hm_ivt_ratio"
                  label="Max Hm IVT:"
                  rules={[
                    {
                      required: true,
                      message: 'Please Input Max Hm IVT(%)'
                    },
                    RatioRule
                  ]}
                  tooltip="Requests exceeding the configured IVT threshold will be blocked"
                >
                  <InputNumber min={0} addonAfter="%" style={{ width: 120 }} defaultValue={0} />
                </Form.Item>
              )}
            </>
          )}
        </Form>
        <TocAnchor
          affix={false}
          targetOffset={20}
          showInkInFixed
          tocOptions={tocOptions}
          style={{
            position: 'sticky',
            top: 20,
            display: initialState?.isCollapsed ? 'none' : 'block'
          }}
          getContainer={() => document.getElementById('drawer-content')!}
        />
      </div>
    </NormalDrawer>
  );
};

export default AddDemandModel;
