/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:09:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 17:02:45
 * @Description:
 */

import Router from 'koa-router';
import { supplyController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/supply' });

const routers = router
  .post('/addSupply', supplyController.addSupply)
  .post('/updateSupply', supplyController.updateSupply)
  .post('/getSupplyList', supplyController.getSupplyList)
  .post('/getSupplyListWithTesting', supplyController.getSupplyListWithTesting)
  .post('/getSupplyAuth', supplyController.getSupplyAuth)
  .post('/getSupplyAppPlacement', supplyController.getSupplyAppPlacement)
  .post('/setSupplyAuth', validTesting, supplyController.setSupplyAuth)
  .post('/getSupplyEndpoint', supplyController.getSupplyEndpoint)
  .post('/getDashboardSupplyList', supplyController.getDashboardSupplyList);

export default routers;
