/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-03 11:04:07
 * @Description:
 */
import React from 'react';
import { ReactSVG } from 'react-svg';
import type { Props } from 'react-svg';
import { Tooltip } from 'antd';

type SvgIconProps = Props & {
  className?: string;
  style?:any;
  handleClick?: () => void;
  tooltip?: string;
  title?:string;
}

const SvgIcon: React.FC<SvgIconProps> = ({ className = '', src, wrap = 'svg', style = {}, handleClick = () => {}, tooltip = '', title = '' }) => {
  return (
    <>
      {
        tooltip
          ? <Tooltip title={tooltip}>
            <ReactSVG
              className={className}
              src={src}
              wrap={wrap}
              style={style}
              onClick={handleClick}
              title={title}
            ></ReactSVG>
          </Tooltip>
          : <ReactSVG
            className={className}
            src={src}
            wrap={wrap}
            style={style}
            title={title}
            onClick={handleClick}
          ></ReactSVG>
      }
    </>
  );
};

export default SvgIcon;
