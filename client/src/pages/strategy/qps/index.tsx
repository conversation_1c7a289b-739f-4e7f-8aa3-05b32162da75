/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:10:51
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:00:58
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { useModel, useAccess } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import {
  QpsSearchOption,
  QpsBreadOptions,
  QpsTabOptions,
  QpsTab,
  QpsLevelType,
  QpsLevelOptionsForAdv,
  QpsLevelOptionsForPub,
  QpsLevelOptionsForUnlimit,
  QpsColumnsForAdv,
  QpsColumnsForPub,
  QpsColumnsForUnlimit
} from '@/constants/strategy/qps';
import { StatusMap } from '@/constants';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import EditButton from '@/components/Button/EditButton';
import { PlusOutlined } from '@ant-design/icons';
import AddQpsModel from '../components/AddQpsModel';
import { TopBarSearchItem } from '@/components/TopBar';

const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource: QpsList, reload, loading } = useModel('useQpsList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');

  const tmpColumns: ColumnProps<StrategyAPI.QpsListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      fixed: 'right',
      width: 100,
      render: (txt, params) => (
        <>
          <EditButton onClick={() => handleEditQps(params)} disabled={access.DisabledButton('updateQpsAuth')}>
            Edit
          </EditButton>
        </>
      )
    }
  ];
  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps<StrategyAPI.QpsListItem>[]>([
    ...QpsColumnsForAdv,
    ...tmpColumns
  ]);
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [qps, setCurrentQps] = useState<StrategyAPI.QpsListItem | undefined>(undefined);
  const [searchOptions, setSearchOptions] = useState(QpsSearchOption);
  const [currentTab, setCurrentTab] = useState(QpsTab.advertiser);
  const [dataSource, setDataSource] = useState<StrategyAPI.QpsListItem[]>([]);

  useEffect(() => {
    reload();
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }
    handleSearchOptions();
  }, []);

  useEffect(() => {
    let tmpList: StrategyAPI.QpsListItem[] = QpsList || [];
    if (currentTab === QpsTab.advertiser) {
      tmpList = tmpList.filter((item: StrategyAPI.QpsListItem) => item.buyer_id);
      setDynamicColumns([...QpsColumnsForAdv, ...tmpColumns]);
    } else if (currentTab === QpsTab.publisher) {
      tmpList = tmpList.filter((item: StrategyAPI.QpsListItem) => item.level === QpsLevelType.supply);
      setDynamicColumns([...QpsColumnsForPub, ...tmpColumns]);
    } else {
      tmpList = tmpList.filter((item: StrategyAPI.QpsListItem) => item.level > QpsLevelType['demand + country']);
      setDynamicColumns([...QpsColumnsForUnlimit, ...tmpColumns]);
    }
    setDataSource(tmpList);
    handleSearchOptions();
  }, [currentTab, demandList, supplyList, QpsList]);

  const handleSearchOptions = () => {
    // dOptions为adv下拉框的选项，sOptions为pub下拉框的选项，tLevelOptions为level下拉框的选项
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(QpsSearchOption));
    if (demandList) {
      const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });

      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }

    const tIndex = options.findIndex(item => item.key === 'level');
    if (tIndex !== -1) {
      const tLevelOptions =
        currentTab === QpsTab.advertiser
          ? QpsLevelOptionsForAdv
          : currentTab === QpsTab.publisher
            ? QpsLevelOptionsForPub
            : QpsLevelOptionsForUnlimit;
      options[tIndex].options = tLevelOptions;
    }

    currentTab === QpsTab.advertiser
      ? setSearchOptions(options)
      : setSearchOptions(options.filter(item => item.key !== 'buyer_id'));
  };

  const handleEditQps = (params: StrategyAPI.QpsListItem) => {
    setCurrentQps(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentQps(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as string);
  };

  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={QpsBreadOptions}>
      <FrontTable<StrategyAPI.QpsListItem>
        searchOptions={searchOptions}
        loading={loading && dataSource && !dataSource.length}
        columns={dynamicColumns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label:
              currentTab === QpsTab.advertiser
                ? 'Create Advertiser QPS'
                : currentTab === QpsTab.publisher
                  ? 'Create Publisher QPS'
                  : 'Create Unlimit QPS',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
            accessCode: 'addQpsAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }} // 非动态， 需要自己指定
        labelWidth={80}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        tabOptions={QpsTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange}
        isFold={currentTab === QpsTab.advertiser}
        initialValues={{ status: [StatusMap.Active] }}
      />
      <AddQpsModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadQps={reload}
        qps={qps}
        demandList={demandList}
        supplyList={supplyList}
        QpsLevelOptions={
          currentTab === QpsTab.advertiser
            ? QpsLevelOptionsForAdv
            : currentTab === QpsTab.publisher
              ? QpsLevelOptionsForPub
              : QpsLevelOptionsForUnlimit
        }
      />
    </PageContainer>
  );
};

export default Page;
