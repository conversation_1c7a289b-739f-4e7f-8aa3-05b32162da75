import moment from 'moment';
import { PresetDropdownItem } from '../persistence';

/**
 * Deserialize data report preset value
 *
 * date need be deal with timeRange(dynamic time range)
 * @param item
 * @returns
 */
export const deserializeReportPresetValue = (item: PresetDropdownItem) => {
  const value = JSON.parse(item.value);
  const { timeRange } = item.extra || {};
  let date = [moment(value.date[0]), moment(value.date[1])];
  if (timeRange) {
    // parse timeRange
    // 1. without _ means only one day
    // 2. white _ means start and end
    if (timeRange.includes('_')) {
      const [start, end] = timeRange.split('_');
      date = [moment().add(start, 'days'), moment().add(end, 'days')];
    } else {
      date = [moment().add(timeRange, 'days'), moment().add(timeRange, 'days')];
    }
  }

  return {
    ...value,
    date
  };
};
