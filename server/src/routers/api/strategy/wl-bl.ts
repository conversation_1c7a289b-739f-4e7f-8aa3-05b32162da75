/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-04-27 15:59:57
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-13 11:40:49
 * @Description:
 */
import Router from 'koa-router';
import { blwlController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/blWl' });

const routers = router
  .post('/addBlAndWl', validTesting, blwlController.addBlAndWl)
  .post('/updateBlAndWl', blwlController.updateBlAndWl)
  .post('/getBlAndWlList', blwlController.getBlAndWlList);

export default routers;
