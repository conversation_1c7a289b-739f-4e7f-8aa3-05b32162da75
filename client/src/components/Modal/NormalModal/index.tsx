/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-22 19:12:26
 * @Description:
 */
import React from 'react';
import { Modal } from 'antd';
import type { ModalProps, ModalFuncProps } from 'antd';
import styles from './index.less';

function NormalModal(props: ModalProps): JSX.Element {
  const { className } = props;
  return <Modal {...props} className={`${styles['normal-modal-container']} ${className}`} />;
}

NormalModal.confirm = (props: ModalFuncProps) => {
  Modal.confirm({
    ...props,
    className: styles['normal-confirm-modal-container']
  });
};

NormalModal.success = (props: ModalFuncProps) => {
  Modal.success({
    ...props,
    className: styles['normal-confirm-modal-container']
  });
};
NormalModal.info = (props: ModalFuncProps) => {
  Modal.info({
    ...props,
    className: styles['normal-confirm-modal-container']
  });
};
export default NormalModal;
