.permission-page {
  :global {
    .ant-layout-content {
      background-color: #fff;
    }
    .ant-menu-title-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0;
      span {
        margin-right: 10px;
      }
    }
    .ant-layout-sider-children {
      background-color: #fff !important;
      border-bottom: 1px solid #e2eaeb;
      padding-right: 12px;
      overflow-y: auto;
    }
    .ant-menu {
      border: none;
      min-height: calc(100vh - 282px);
      max-height: calc(100vh - 282px);

      .ant-menu-item {
        height: 38px;
        font-size: 14px;
        margin-top: 12px !important;
        border-radius: 6px;
        padding: 0 12px;

        &.ant-menu-item-selected {
          .ant-menu-title-content {
            background-color: inherit !important;
          }
        }

        &:hover,
        &:focus,
        &.ant-menu-item-active {
          background: var(--background-color) !important;

          .ant-menu-title-content {
            background-color: inherit !important;
          }
        }
      }
    }
    .ant-spin-nested-loading {
      background-color: #fff;
    }
  }

  padding: 0;
  max-height: 100%;
  .add-role-btn {
    width: 100%;
    height: 36px;
    position: sticky;
    top: 0;
    z-index: 2;
  }
  .menu-btn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      color: #8d9799;
      font-size: 16px;
    }
    .edit-btn {
      margin: 0;
      &:hover {
        color: var(--primary-color-hover);
      }
    }
    .delete-btn {
      margin: 0 0 0 8px;
      &:hover {
        color: red;
      }
    }
  }
  .role-item-name {
    font-size: 14px;
    color: var(--primary-1);
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.permission-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding-left: 40px;
  button {
    align-self: flex-end;
    margin: 12px 0;
  }
  :global {
    .ant-radio-group {
      background-color: #fff;
    }
  }
  .permission-top {
    display: flex;
    justify-content: flex-start;
    color: var(--text-color);
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 12px;
  }
}
