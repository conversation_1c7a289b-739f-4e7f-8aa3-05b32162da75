# 1 AI Suggestions

- SaaS平台算法自动分析流量数据为用户提供专业的运营建议。
- 目前平台针对「Revenue」「IVT」「QPS」「Cap」四项指标提供监控预警及运营优化建议，可通过右上角的信封图标快速查看近期接收到的建议，或者通过左侧菜单栏进入「AI Suggestions」模块分类查看所有建议。

![img](https://static.rixengine.com/a/platform/help/ai-guide/1-1.png)

## 1.1 Revenue

平台在每日1时（UTC+0）根据前一日平台收入情况发送收入预警。以下为可能出现的预警类型：

**类型1：【Rev】Advertiser has no filling**

 Advertiser has no filling, Advertiser=[Example-ADV(001)], From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，发送至上游Example-ADV的广告请求均未被填充。

**建议运营人员：**

- 联系上游Example-ADV定位广告填充率为0的原因，及时沟通优化广告填充。
- 如果上游相应预算还没有ready， 则停止向上游Example-ADV转发无法填充的请求，节省IDC成本。

**类型2：【Rev】Overall revenue has dropped**

 Overall revenue has dropped, Rev Diff($)=[1000], Rev Diff(%)=[50], From=[YYyy-Mm-Dd 02:00:00 ~ YYYY-MM-DD 01:00:00], To=[YYYY-MM-DD 02:00:00 ~ yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，平台总流水收入与前一日相比降幅较大。

**建议运营人员：**

- 查看最近几天平台数据情况，由粗维度到细维度（例：特定上游->国家->广告格式->尺寸->bundle）逐级排查流水变化趋势，根据上游预算调整送量策略以恢复平台收入。
- 分析平台数据，关注重点上下游流量收入变化情况，必要时可与上下游沟通流量与预算是否有调整。

## 1.2 IVT

平台在每日8时（UTC+0）根据前一日平台IVT检测情况向开启IVT检测服务的用户发送IVT预警。以下为可能出现的预警类型：

**场景1：【IVT】Pixalate scanning has high invalid traffic**

 pixalate scanning has high invalid traffic, Advertiser=[Example-ADV(001)], IVT(%)=[20], Rev($)=[1000], Date=[YYYY-MM-DD]

接收到该预警说明在 YYYY-MM-DD 一日向上游Example-ADV转发的流量请求IVT较高，需要重点关注流量质量。

**建议运营人员：**

- 根据实际业务需求判断是否对相应高IVT的上下游做减量或屏蔽处理。  1）如果上游对流量质量要求很严格，则需要及时进行相应高IVT的bundle流量屏蔽， 同时通知相关下游IVT质量过高，需要进行流量质量优化。 2）如果上游对流量质量无要求，则可以继续观察。

## 1.3 QPS

平台在每日1时（UTC+0）根据前一日平台流量情况发送QPS预警。以下为可能出现的预警类型：

**类型1：【QPS】Advertiser QPS is highly blocked**

 Advertiser=[Example-ADV(001)], Region=[APAC], ECPR=[10], Block QPS=[100] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，由于针对上游配置的qps限制，送至上游Example-ADV高价值流量请求未能成功转发。

**建议运营人员：**

- 查看是否为上游Example-ADV设置过低的QPS阈值，适当上调上游Example-ADV的QPS阈值，修改qps =  原来qps + block_qps + 100（或200），过程中尽可能采取阶梯式上调的操作，平缓地完成增量（例：1000->1500->2000），以避免大幅度调整QPS对上游预算和算法模型造成影响。
- 若完成QPS增量调整，建议及时联系上游Example-ADV同步QPS调整情况，避免因上游侧QPS限制影响收入。  

**类型2：【QPS】Advertiser QPS was set too high**

 Advertiser=[Example-ADV(001)], Region=[APAC], ECPR=[0.1], Current config QPS=[1000], Real QPS=[900] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，平台转发了大量送至上游Example-PUB的低价值请求。

**建议运营人员：**

- 联系上游Example-ADV沟通预算分布情况，优化pretargeting, 筛选与上游预算相匹配的流量
- 适当下调上游Example-ADV的QPS阈值，减少低价值请求的转发，节省IDC成本。过程中尽可能采取阶梯式下调的操作，平缓地完成减量（例：1000->600->300），以避免大幅度调整QPS影响流量训练。 

**类型3：【QPS】Publisher QPS is highly blocked**

 Publisher=[Example-PUB(001)], Region=[APAC], ECPR=[10], Block QPS=[100] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，由于针对下游配置的QPS限制，来自下游Example-PUB高价值流量请求未能成功转发。

**建议运营人员：**

- 适当上调下游Example-PUB的QPS阈值，增加高价值流量请求的接收和转发，提升广告收益。修改qps =  原来qps + block_qps + 100（或200），过程中尽可能采取阶梯式上调的操作，平缓地完成增量（例：1000->1500->2000），以避免大幅度调整QPS对算法模型造成影响。 
- 若完成QPS增量调整，建议及时联系下游Example-PUB同步QPS调整情况，避免因下游侧QPS限制影响收入。  

**类型4：【QPS】Publisher QPS was set too high**

 Publisher=[Example-PUB(001)], Region=[APAC], ECPR=[0.1], Current config QPS=[1000], Real QPS=[900] , From=[YYYY-MM-DD 02:00:00], To=[yyyy-mm-dd 01:00:00]

接收到该预警说明从 YYYY-MM-DD 02:00:00 至 yyyy-mm-dd 01:00:00 之间，平台转发了大量来自下游Example-PUB的低价值请求。

**建议运营人员：**

- 联系下游优化送量，根据平台预算进行相应的流量输送，以便提升流量填充率和收入。
- 适当下调下游Example-PUB的QPS阈值，减少低价值请求的转发，节省IDC成本。过程中尽可能采取阶梯式下调的操作，平缓地完成减量（例：1000->600->300），以避免大幅度调整QPS影响流量训练。 

## 1.4 Cap

平台实时监控不同上下游设置的Cap完成情况，同步发送Cap预警。以下为可能出现的预警类型：

**类型1：【Cap】Daily cap Reached**

 Daily cap Reached, Advertiser=[Example-ADV(001)], Publisher=[Example-PUB(002)], Rev Cap=[100], Imp Cap=[10000], Time=[YYYY-MM-DD  hh:mm:ss]

接收到该预警说明在 YYYY-MM-DD  hh:mm:ss 时上游Example-ADV与下游Example-PUB间的广告交易已经达到预算数或者展示数的日上限。

**建议运营人员：**

- 根据实际业务需求判断是否增加或者放开针对该上下游的每日Cap上限。

