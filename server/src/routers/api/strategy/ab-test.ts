/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-21 15:57:45
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-26 16:19:53
 * @Description:
 */
import Router from 'koa-router';
import { abTestCtrl } from '@/controllers';
import { parseDateParams } from '@/utils/report';
const router = new Router({ prefix: '/ab-test' });

const routers = router
  .post('/addABTest', parseDateParams, abTestCtrl.addABTest)
  .post('/updateABTest', parseDateParams, abTestCtrl.updateABTest)
  .post('/getABTestList', abTestCtrl.getABTestList);

export default routers;
