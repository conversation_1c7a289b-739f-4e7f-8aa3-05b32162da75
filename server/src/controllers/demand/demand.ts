/*
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2022-12-05 11:50:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 19:43:21
 * @FilePath: /saas.rix-platform/server-ts/src/controllers/demand/demand.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Context } from 'koa';
import { demandService } from '@/services';
import { DemandAPI } from '@/types/demand';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import {
  AddDemandScheme,
  UpdateDemandScheme,
  GetDemandEndpointScheme,
  SetDemandEndpointScheme,
  OpeartePretargetCampaignScheme,
  SetDemandAuthScheme
} from '@/schema/demand';
import { syncBuyerPartner2Gcp } from './utils';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class DemandCtrl implements DemandAPI.DemandCtrlInterface {
  async getDemandList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await demandService.getDemandList(
      ctx.session!.tnt_id,
      cur_time_zone,
      false
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async getDemandListWithTesting(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await demandService.getDemandList(
      ctx.session!.tnt_id,
      cur_time_zone,
      true
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddDemandScheme)
  async addDemand(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const [demandName, demandUserName] = await Promise.all([
      demandService.isDemandNameExists(
        formData.buyer_name,
        ctx.session!.tnt_id
      ),
      demandService.isDemandAccountExists(
        `Adv_${formData.demand_account_name}`,
        ctx.session!.tnt_id
      )
    ]);
    if (demandName?.length || demandUserName?.length) {
      result = getCtxResult(
        demandName?.length ? 'BUYER_NAME_EXISTS' : 'BUYER_USER_NAME_EXISTS'
      );
    } else {
      const list = await demandService.addDemand(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addDemand success formData=[${JSON.stringify(formData)}]`
        );
      }
      await syncBuyerPartner2Gcp(ctx.session!.tnt_id);
    }
    ctx.body = result;
  }

  @validateBody(UpdateDemandScheme)
  async updateDemand(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const [demandName, demandUserName] = await Promise.all([
      demandService.isDemandNameExists(
        formData.buyer_name,
        ctx.session!.tnt_id,
        formData.buyer_id
      ),
      demandService.isDemandAccountExists(
        formData.demand_account_name,
        ctx.session!.tnt_id,
        formData.user_id
      )
    ]);

    if (demandName?.length || demandUserName?.length) {
      result = getCtxResult(
        demandName?.length ? 'BUYER_NAME_EXISTS' : 'BUYER_USER_NAME_EXISTS'
      );
    } else {
      const list = await demandService.updateDemand(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateDemand success formData=[${JSON.stringify(formData)}]`
        );
        syncBuyerPartner2Gcp(ctx.session!.tnt_id);
      }
    }
    ctx.body = result;
  }

  @validateBody(GetDemandEndpointScheme)
  async getDemandEndpoint(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await demandService.getDemandEndpoint(
      ctx.session!.tnt_id,
      formData.buyer_id
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(SetDemandEndpointScheme)
  async setDemandEndpoint(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    const endpoint = JSON.parse(formData.endpoint);
    // eslint-disable-next-line max-len
    const list = await demandService.setDemandEndpoint(
      endpoint,
      formData.buyer_id,
      ctx.session!.tnt_id
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `setDemandEndpoint success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(GetDemandEndpointScheme)
  async getPretargetCampaign(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await demandService.getPretargetCampaign(
      formData.buyer_id,
      formData.campaign_id,
      ctx.session!.tnt_id,
      formData.cur_time_zone
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(OpeartePretargetCampaignScheme)
  async updatePretargetCampaign(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.items = formData.items ? JSON.parse(formData.items) : [];
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const list = await demandService.updatePretargetCampaign(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updatePretargetCampaign success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(OpeartePretargetCampaignScheme)
  async updatePretargetStatus(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const list = await demandService.updatePretargetStatus(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updatePretargetStatus success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(OpeartePretargetCampaignScheme)
  async addPretargetCampaign(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.items = formData.items ? JSON.parse(formData.items) : [];
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const list = await demandService.addPretargetCampaign(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `addPretargetCampaign success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(GetDemandEndpointScheme)
  async getDemandAuth(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await demandService.getDemandAuth(
      formData.buyer_id,
      ctx.session!.tnt_id
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(SetDemandAuthScheme)
  async setDemandAuth(ctx: Context) {
    let result = { ...RESULT };
    const { buyer_id, authIds, ori_seller_ids } = ctx.request.body;

    const auth_ids = authIds || [];
    const list = await demandService.setDemandAuth(
      buyer_id,
      auth_ids,
      ctx.session!.user_id,
      ctx.session!.tnt_id
    );

    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`setDemandAuth ${buyer_id} success,
      ori_seller_ids=[${ori_seller_ids ? ori_seller_ids.sort() : []}],
      cur_seller_ids=[${authIds ? authIds.sort() : []}]
      `);
    }
    ctx.body = result;
  }
}

export default new DemandCtrl();
