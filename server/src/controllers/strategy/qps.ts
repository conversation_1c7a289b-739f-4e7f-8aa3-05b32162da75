/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-20 15:40:08
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 19:14:26
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import { AddQpsSchema, UpdateQpsSchema } from '@/schema/strategy/qps';
import { qpsService } from '@/services';
import { QpsAPI } from '@/types/qps';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class QpsCtrl implements QpsAPI.QpsCtrlInterface {
  async getQpsList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await qpsService.getQpsList(
      ctx.session!.tnt_id,
      cur_time_zone
    );
    if (Array.isArray(list)) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddQpsSchema)
  async addQps(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.op_id = ctx.session!.user_id;
    formData.tnt_id = ctx.session!.tnt_id;

    try {
      const list = await qpsService.addQpsWithTransaction(formData);
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `addQps success formData=[${JSON.stringify(formData)}]`
      );
    } catch (error: any) {
      if (error.code === 'QPS_EXISTS') {
        result = getCtxResult('QPS_EXISTS', []);
      } else if (error.code === 'QPS_DUPLICATED_WHEN_PAUSED') {
        result = getCtxResult('QPS_DUPLICATED_WHEN_PAUSED', error.data);
      } else {
        throw error;
      }
    }
    // 注释代码暂时不删，后期可能会新增all region的逻辑,则讲上述代码改为注释代码
    // if (data && data.length > 0) {
    //   if (count !== 0 && data[0].status !== StatusMap.Paused) {
    //     result = getCtxResult('QPS_EXISTS', []);
    //   } else {
    //     if (data && data.length > 0 && data[0].status === StatusMap.Paused) {
    //       formData.id = data[0].id;
    //       const list = await qpsService.updateDuplicatedQps(formData);
    //       if (list) {
    //         result = getCtxResult('QPS_DUPLICATED_WHEN_PAUSED', list);
    //         getLogger('app').info(
    //           `updateQps success formData=[${JSON.stringify(formData)}]`
    //         );
    //       }
    //     } else {
    //       const list = await qpsService.addQps(formData);
    //       if (list) {
    //         result = getCtxResult('SUCCESS', list);
    //         getLogger('app').info(
    //           `addQps success formData=[${JSON.stringify(formData)}]`
    //         );
    //       }
    //     }
    //   }
    // } else {
    //   if (count === 0) {
    //     const list = await qpsService.addQps(formData);
    //     if (list) {
    //       result = getCtxResult('SUCCESS', list);
    //       getLogger('app').info(
    //         `addQps success formData=[${JSON.stringify(formData)}]`
    //       );
    //     }
    //   } else {
    //     result = getCtxResult('QPS_EXISTS', []);
    //   }
    // }

    ctx.body = result;
  }

  @validateBody(UpdateQpsSchema)
  async updateQps(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    try {
      const list = await qpsService.updateQpsWithTransaction(formData);
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updateQps success formData=[${JSON.stringify(formData)}]`
      );
    } catch (error: any) {
      if (error.code === 'QPS_EXISTS') {
        result = getCtxResult('QPS_EXISTS');
        getLogger('app').info(
          `updateQps error formData=[${JSON.stringify(formData)}]`
        );
      } else {
        throw error;
      }
    }

    ctx.body = result;
  }
}

export default new QpsCtrl();
