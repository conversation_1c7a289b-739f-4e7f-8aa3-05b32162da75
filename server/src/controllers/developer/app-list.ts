/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:30:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 11:56:39
 * @Description:
 */
import { Context } from 'koa';
import { AppListAPI } from '@/types/app-list';
import { appService } from '@/services';
import { Code, Message, CodeProps } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import {
  AddAppScheme,
  EditAppScheme,
  AddPlacementScheme
} from '@/schema/developer';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class AppCtrl implements AppListAPI.AppCtrlInterface {
  //  seller_id + bundle唯一，seller_id + app_name + platform唯一
  @validateBody(AddAppScheme)
  async addApp(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const countMaxTagApp = await appService.countSellerApp(formData);
    if (Array.isArray(countMaxTagApp) && countMaxTagApp.length) {
      result = getCtxResult('MAX_TAG_APP_LIMIT', []);
      ctx.body = result;
      return;
    }
    const res = await appService.isAppExists(formData);
    if (res.flag) {
      result = getCtxResult(res.type as CodeProps, []);
    } else {
      const list = await appService.addApp(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(`addApp success formData=[${JSON.stringify(formData)}]`);
      }
    }
    ctx.body = result;
  }

  @validateBody(AddPlacementScheme)
  async addPlacement(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const data = await appService.isPlacementExists(formData);
    if (data && data.length) {
      result = getCtxResult('PLACEMENT_EXISTS', data);
    } else {
      const list = await appService.addPlacement(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addPlacement success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  @validateBody(EditAppScheme)
  async updateApp(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const res = await appService.isAppExists(formData);
    if (res.flag) {
      result = getCtxResult(res.type as CodeProps, []);
    } else {
      const list = await appService.updateApp(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateApp success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  @validateBody(AddPlacementScheme)
  async updatePlacement(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.op_id = ctx.session!.user_id;
    formData.tnt_id = ctx.session!.tnt_id;
    const data = await appService.isPlacementExists(formData);
    if (data && data.length) {
      result = getCtxResult('PLACEMENT_EXISTS', data);
    } else {
      const list = await appService.updatePlacement(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updatePlacement success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  async getAppList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const list = await appService.getAppList(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`getAppList success tnt_id=[${tnt_id}]`);
    }
    ctx.body = result;
  }

  async getAllPlacementList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const list = await appService.getAllPlacementList(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`getAllPlacementList success tnt_id=[${tnt_id}]`);
    }
    ctx.body = result;
  }
}

export default new AppCtrl();
