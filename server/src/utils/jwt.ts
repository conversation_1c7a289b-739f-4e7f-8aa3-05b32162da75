import AES from 'crypto-js/aes';
import Utf8 from 'crypto-js/enc-utf8';
import jwt from 'jsonwebtoken';
import { jwtConfig } from '../config/jwt';

interface JwtPayload {
  account_name: string;
  password: string;
  cs_domain: string;
}

interface TokenVerifyResult {
  success: boolean;
  payload?: JwtPayload;
  error?: 'invalid' | 'expired' | 'malformed';
}

/**
 * 生成JWT token（单token模式）
 * @param payload 载荷数据
 * @returns JWT token字符串
 */
export const generateToken = (payload: JwtPayload): string => {
  // 确保 payload 中包含必要的字段
  const tokenPayload = {
    ...payload
  };

  // 使用 AES 加密 payload，这是可逆的加密算法
  const encryptedPayload = AES.encrypt(
    JSON.stringify(tokenPayload),
    jwtConfig.secret
  ).toString();

  return jwt.sign({ data: encryptedPayload }, jwtConfig.secret, {
    expiresIn: jwtConfig.expiresIn, // 使用配置的10分钟
    issuer: jwtConfig.issuer,
    audience: jwtConfig.audience
  } as jwt.SignOptions);
};

/**
 * 验证并解密 token，返回详细的验证结果
 * @param token JWT 字符串
 * @returns 验证结果，包含成功状态、payload 和错误类型
 */
export const verifyTokenDetailed = (token: string): TokenVerifyResult => {
  try {
    // 先验证 token 的合法性，获取加密后的 payload
    const data: any = jwt.verify(token, jwtConfig.secret);
    const encryptedPayload = data.data;

    // 使用 AES 解密，还原出原始 payload
    const decryptedBytes = AES.decrypt(encryptedPayload, jwtConfig.secret);
    const decryptedPayload = JSON.parse(decryptedBytes.toString(Utf8));

    return {
      success: true,
      payload: decryptedPayload
    };
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return {
        success: false,
        error: 'expired'
      };
    } else if (error instanceof jwt.JsonWebTokenError) {
      return {
        success: false,
        error: 'invalid'
      };
    } else {
      return {
        success: false,
        error: 'malformed'
      };
    }
  }
};
