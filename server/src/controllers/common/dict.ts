import { GetDictScheme } from '@/schema/common';
import { dictService } from '@/services/common/dict';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class DictCtrl {
  /**
   * 获取字典数据
   * @param ctx
   */
  @validateBody(GetDictScheme)
  async getDict(ctx: Context) {
    const { dict_type } = (ctx.request.query|| {}) as {
      dict_type: string;
    };
    const data = await dictService.getDict(dict_type);
    ctx.body = getCtxResult('SUCCESS', data);
  }
}

export const dictCtrl = new DictCtrl();
