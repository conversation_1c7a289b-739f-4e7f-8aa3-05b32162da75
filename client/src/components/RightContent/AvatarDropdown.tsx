/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-18 15:08:39
 * @Description:
 */
import { LoginPath } from '@/constants';
import { ProfileTab } from '@/constants/base/my-profile';
import { logOut } from '@/services/api';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Dropdown } from 'antd';
import type { ItemType } from 'antd/lib/menu/hooks/useItems';
import { stringify } from 'querystring';
import type { MenuInfo } from 'rc-menu/lib/interface';
import React, { useEffect, useMemo, useState } from 'react';
import { history, useAccess, useLocation, useModel, useSearchParams } from 'umi';
import styles from './index.less';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};
type permissionProps = {
  access?: string;
};
const DropdownMenuItems: (ItemType & permissionProps)[] = [
  {
    label: 'My Profile',
    key: 'my-profile',
    access: 'ProfilePermission'
  },
  {
    label: 'My Account',
    key: 'my-account',
    access: 'AccountPermission'
  },
  {
    label: 'Sign Out',
    key: 'logout'
  }
];
const AvatarDropdown: React.FC<GlobalHeaderRightProps> = () => {
  const access = useAccess();
  const [searchParams] = useSearchParams();
  const { initialState, setInitialState } = useModel('@@initialState');
  const { setPreviousUrl, setBackUrl } = useModel('usePreviousUrl');

  const location = useLocation();
  const [permissionItems, setPermissionItems] = useState(DropdownMenuItems);
  useEffect(() => {
    // 判断权限
    const accessItems = DropdownMenuItems.filter(item => !item.access || access[item.access as string]).map(
      (item: any) => {
        return {
          label: item.label,
          key: item.key
        };
      }
    );
    setPermissionItems(accessItems);
    // 保存上一页的路由
    const unListen = history.listen(() => {
      if (location && location.pathname !== LoginPath) {
        setPreviousUrl(`${location.pathname}${decodeURI(location.search)}`);
      } else {
        setPreviousUrl('');
      }
    });
    return () => {
      unListen();
    };
  }, []);

  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    logOut();
    const { search, pathname } = location;
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login') {
      const obj = {
        pathname: '/user/login',
        search: stringify({
          redirect: pathname + search
        })
      };
      history.replace(obj);
    }
  };

  const onMenuClick = (event: MenuInfo) => {
    if (event.key === 'logout') {
      setInitialState(s => ({ ...s, currentUser: undefined, isCollapsed: initialState?.isCollapsed || false }));
      loginOut();
      return;
    } else if (event.key === 'my-profile') {
      location.pathname === '/my-profile'
        ? setBackUrl('/')
        : setBackUrl(`${location.pathname}${decodeURI(location.search)}`);
      const tab = searchParams.get('tab');
      const menuKey = searchParams.get('menuKey');
      history.push(`/my-profile?tab=${tab || ProfileTab.Info}&menuKey=${menuKey || ''}`);
    } else if (event.key === 'my-account') {
      history.push('/my-account');
    }
  };
  // 没有登录
  if (!initialState || !initialState.currentUser || !initialState.currentUser.account_name) {
    // 重定向到登录页面
    if (location.pathname !== LoginPath) {
      history.push(LoginPath);
    }
    return null;
  }

  const displayName = useMemo(() => {
    const { account_name, tnt_id, special_user_id } = initialState.currentUser || {};
    // 如果存在 special_user_id，则显示 tnt_id
    if (special_user_id && tnt_id !== 1083) {
      return `(T${tnt_id}) ${account_name}`;
    }
    return account_name;
  }, [initialState.currentUser]);

  return (
    <>
      <Dropdown
        placement="bottomLeft"
        menu={{ items: permissionItems, onClick: onMenuClick }}
        dropdownRender={(menus: React.ReactNode) => (
          <div className={styles['dropdown-container']}>
            <div className={styles['top']}>
              <Avatar
                size="large"
                style={{ marginTop: 8 }}
                icon={<UserOutlined style={{ color: 'var(--primary-color)' }} />}
                alt="avatar"
              />
              <span className={styles['name']}>ID: {displayName}</span>
            </div>
            {menus}
          </div>
        )}
      >
        <span className={`${styles.action} ${styles.account}`}>
          <Avatar
            size="small"
            className={styles.avatar}
            icon={<UserOutlined style={{ color: 'var(--primary-color)' }} />}
            alt="avatar"
          />
          <span className={`${styles.name} anticon`}>{displayName}</span>
        </span>
      </Dropdown>
    </>
  );
};

export default AvatarDropdown;
