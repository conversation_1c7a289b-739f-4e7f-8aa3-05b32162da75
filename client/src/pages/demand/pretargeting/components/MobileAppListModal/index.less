.btn-svg {
  font-size: 14px;
  cursor: pointer;
  display: inline-block;
  vertical-align: middle;
  line-height: 20px;
  padding-left: 5px;
  padding: 0px 24px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  display: inline-block;
}

.footer {
  height: 50px;
  display: flex;
  align-items: center;
  .content-bottom {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 10px 0px;
  }
  .copy-container {
    width: 240px;
    background: #0db4be;
    height: 32px;
    line-height: 32px;
    text-align: center;
    color: #fff;
    margin-top: 12px;
    cursor: pointer;
    text-align: center;
    border-radius: 6px;
  }
}

.app-model-container {
  :global {
    .ant-modal-body {
      padding: 0px !important;
      padding-top: 24px !important;
    }
  }
}

.container {
  width: 100%;
  padding: 0px 24px;

  .bar {
    height: 1px;
    background-color: #eef0f0;
    margin: 24px 0px;
  }

  .long-content-more-container {
    min-height: 380px;

    .content-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 8px;

      .content-item {
        line-height: 28px;
        color: var(--text-color);
        font-size: 14px;
      }

      .app-name {
        max-width: 200px;
        border: 1px solid #d9d9d9;
        padding: 0px 8px;
        border-radius: 8px;
        color: var(--text-color);
      }
    }
  }
}
