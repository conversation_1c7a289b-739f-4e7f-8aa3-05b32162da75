/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-19 10:57:02
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 18:35:09
 * @Description:
 */
import { ProfitMaxValue, SpecialProfitMinValue } from '@/constants';
import {
  ChannelType,
  CustomPlacement,
  DeviceType,
  ProfitModel,
  Relationship
} from '@/constants/supply';
import Joi from 'joi';

const scheme = {
  seller_name: Joi.string().required(),
  integration_type: Joi.number().integer().required(),
  relationship: Joi.number()
    .integer()
    .valid(...Relationship)
    .required(),
  cus_status: Joi.number()
    .integer()
    .valid(...CustomPlacement)
    .required(),
  profit_ratio: Joi.number()
    .integer()
    .min(SpecialProfitMinValue)
    .max(ProfitMaxValue)
    .required(),
  profit_model: Joi.number()
    .integer()
    .valid(...ProfitModel)
    .required(),
  rev_share_ratio: Joi.when('profit_model', {
    is: 2,
    then: Joi.number().integer().min(1).max(100).required()
  }),
  sp_id: Joi.number().integer(),
  publisher_id: Joi.string().min(0).max(256),
  support_omid: Joi.number().integer().required(),
  adomain_filter: Joi.number().integer().valid(1, 2).required(),
  native_strict_validation: Joi.number().integer().valid(1, 2).required()
};

export const AddPublisherScheme = Joi.object({
  ...scheme,
  channel_type: Joi.number()
    .integer()
    .valid(...ChannelType)
    .required(),
  device_type: Joi.number()
    .integer()
    .valid(...DeviceType)
    .required()
});

export const UpdatePublisherScheme = Joi.object({
  ...scheme,
  status: Joi.number().integer().valid(1, 2, 3).required(),
  profit_id: Joi.number().integer().required(),
  seller_id: Joi.number().integer().required()
});

export const GetSupplyAuthScheme = Joi.object({
  seller_id: Joi.number().integer().required()
});

export const SetSupplyAuthScheme = Joi.object({
  level: Joi.number().integer().valid(1, 2, 3).required(),
  buyer_ids: Joi.array().items(Joi.number().integer()),
  pub_id: Joi.number().integer().required(),
  app_id: Joi.number().integer(),
  plm_id: Joi.number().integer(),
  status: Joi.number().integer().valid(1, 2)
});
