/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-02-10 17:52:16
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-31 11:48:09
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import type { MenuProps } from 'antd';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusDesc } from '@/constants';

export type ProfileListItem = {
  account_name: string;
  status: number;
  user_id: number;
  op_user_id: number;
  role_id: number[];
  pms_id: number[];
  email: string;
  role_name: string;
  is_login: number;
  op_status: number;
};

export const ProfileColumnOptions: ColumnProps<ProfileListItem>[] = [
  {
    title: 'User Name',
    dataIndex: 'account_name',
    width: 160,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status',
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  },
  {
    title: 'Role',
    width: 100,
    dataIndex: 'role_id',
    ellipsis: { showTitle: false },
    render: (_, row) => (
      <HoverToolTip title={row.role_name}>
        <span>{row.role_name}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Email',
    width: 150,
    dataIndex: 'email',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Created By',
    width: 150,
    dataIndex: 'op_user_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['op_status'] === 3 ? '#cccc' : '' }}>
          {row['op_status'] === 3 ? 'Unknown' : row['op_status'] === 0 ? '-' : _}
        </span>
        {row['op_status'] === 3 && (
          <Tooltip title={row['op_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  }
];

export const DocsItems: MenuProps['items'] = [
  {
    key: '1',
    label: <span>Guide</span>,
    children: [
      {
        key: '1-1',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/get-started">
            Get-Started
          </a>
        )
      },
      {
        key: '1-2',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/operational-guide">
            Operational Guide
          </a>
        )
      },
      {
        key: '1-3',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/ai-guide">
            AI Suggestions Guide
          </a>
        )
      },
      {
        key: '1-4',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/faq">
            FAQ
          </a>
        )
      }
    ]
  },
  {
    type: 'divider'
  },
  {
    key: '2',

    label: <span>Integration</span>,
    children: [
      {
        key: '2-1',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/openrtb">
            OpenRTB API
          </a>
        )
      },

      {
        type: 'divider'
      },
      {
        key: '2-3',
        type: 'group',
        label: <span>SDK</span>,
        children: [
          {
            key: '2-3-1',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/androidsdk">
                Android SDK
              </a>
            )
          },
          {
            key: '2-3-2',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/iossdk">
                IOS SDK
              </a>
            )
          }
        ]
      },

      {
        type: 'divider'
      },
      {
        key: '2-5',
        type: 'group',
        label: <span>Publisher</span>,
        children: [
          // {
          //   key: '2-5-0',
          //   label: (
          //     <a target="_blank" rel="noopener noreferrer" href="/help/c2s">
          //       C2S
          //     </a>
          //   )
          // },

          // {
          //   key: '2-5-2',
          //   label: (
          //     <a target="_blank" rel="noopener noreferrer" href="/help/maxjs-tag">
          //       MaxJS-Tag
          //     </a>
          //   )
          // },
          {
            key: '2-5-3',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/vasttag">
                VastTag API
              </a>
            )
          },
          {
            key: '2-5-4',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/mw-jstag">
                MWJS-Tag API
              </a>
            )
          }
        ]
      }
    ]
  },
  {
    type: 'divider'
  },
  {
    key: '3',
    label: <span>Reporting API</span>,
    children: [
      {
        key: '3-1',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/sspapi-v2">
            Publisher
          </a>
        )
        // children: [
        //   {
        //     key: '3-1-1',
        //     label: (
        //       <a target="_blank" rel="noopener noreferrer" href="/help/sspapi">
        //         V1
        //       </a>
        //     )
        //   },
        //   {
        //     key: '3-1-2',
        //     label: (
        //       <a target="_blank" rel="noopener noreferrer" href="/help/sspapi-v2">
        //         V2
        //       </a>
        //     )
        //   }
        // ]
      },
      {
        key: '3-2',
        label: (
          <a target="_blank" rel="noopener noreferrer" href="/help/demandapi-v2">
            Advertiser
          </a>
        )
        // children: [
        //   {
        //     key: '3-2-1',
        //     label: (
        //       <a target="_blank" rel="noopener noreferrer" href="/help/demandapi">
        //         V1
        //       </a>
        //     )
        //   },
        //   {
        //     key: '3-2-2',
        //     label: (
        //       <a target="_blank" rel="noopener noreferrer" href="/help/demandapi-v2">
        //         V2
        //       </a>
        //     )
        //   }
        // ]
      },
      {
        key: '3-3',
        label: "Partner API",
        children: [
          {
            key: '3-3-1',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/supply-partner-api">
                Supply Partner API
              </a>
            )
          },
          {
            key: '3-3-2',
            label: (
              <a target="_blank" rel="noopener noreferrer" href="/help/demand-partner-api">
                Demand Partner API
              </a>
            )
          }
        ]
      }
    ]
  }
];

export const UnReadType = {
  all: 0,
  unread: 1,
  read: 2
};
export const UnReadDesc: { [key: number]: string } = {
  0: 'All',
  1: 'UnViewed',
  2: 'Viewed'
};
export const UnReadOptions = [
  {
    label: 'All',
    value: 0
  },

  {
    label: 'UnViewed',
    value: 1
  },
  {
    label: 'Viewed',
    value: 2
  }
];
