/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-18 16:23:40
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-22 19:03:03
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { Badge, List } from 'antd';
import { useModel, history } from '@umijs/max';

import { fetchData } from '@/utils';
import { updateNotificationStatus } from '@/services/common';

import { MailOutlined } from '@ant-design/icons';
import NormalDrawer from '../Drawer/NormalDrawer';
import StatusTag from '../Tag/StatusTag';
import NormalModal from '../Modal/NormalModal';
import { UnReadType } from '@/constants/base';

const NotificationIcon: React.FC = () => {
  const { dataSource: dataList, reload, loading } = useModel('useNotificationList');
  const [dataSource, setDataSource] = useState(dataList || []);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentItem, setCurrentItem] = useState({} as any);
  const [unreadCount, setUnreadCount] = useState(0);
  useEffect(() => {
    if (!dataList) {
      reload();
    }
  }, []);
  useEffect(() => {
    if (Array.isArray(dataList)) {
      setDataSource(dataList?.slice(0, 50) || []);
      setUnreadCount(dataList.filter(item => item.unread === UnReadType.unread).length);
    }
  }, [dataList]);
  const handleClick = () => {
    setDrawerVisible(true);
  };

  const onSuccess = () => {
    reload();
  };
  const handleClickItem = (e: any, item: CommonAPI.NotificationListItem) => {
    e.preventDefault();
    setModalVisible(true);
    setCurrentItem(item);
    if (item.unread === UnReadType.unread) {
      fetchData({ request: updateNotificationStatus, params: { msg_id: item.id }, onSuccess });
    }
  };

  const handleClickMore = (e: any) => {
    e.preventDefault();
    history.push('/ree-ai/alert');
    setDrawerVisible(false);
  };

  const handleChange = (value: number) => {
    const newData = dataList?.filter(item => value === UnReadType.all || item.unread === value);
    setDataSource(newData || []);
  };
  return (
    <>
      <div className={styles['notify-con']} onClick={handleClick}>
        <Badge count={unreadCount} offset={[5, -3]} overflowCount={50}>
          <MailOutlined className={styles['mail-icon']} />
        </Badge>
      </div>
      <NormalDrawer
        blackName={`Notification`}
        grayName={''}
        onClose={() => setDrawerVisible(false)}
        open={drawerVisible}
        showFooter={false}
        loading={loading}
        maskClosable={false}
        mask={false}
        style={{ top: 63 }}
        width="min(100%, 424px)"
        footer={
          <div className={styles['footer']}>
            <a onClick={handleClickMore}>More</a>
          </div>
        }
        className={styles['notification-drawer-container']}
      >
        <List
          split
          loading={false}
          itemLayout="horizontal"
          rowKey={'id'}
          dataSource={dataSource}
          renderItem={item => (
            <List.Item onClick={e => handleClickItem(e, item)}>
              <List.Item.Meta
                avatar={<StatusTag type={item.unread === UnReadType.unread ? 'blocked' : ''} />}
                title={<a href="#">{item.title}</a>}
                description={
                  <div className={styles['content-container']}>
                    <div className={styles['content']}>{item.content}</div>
                    <div className={styles['time']}>{item.create_time}</div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </NormalDrawer>

      <NormalModal open={modalVisible} onCancel={() => setModalVisible(false)} footer={null} title={currentItem.title}>
        <div>{currentItem.content}</div>
      </NormalModal>
    </>
  );
};
export default NotificationIcon;
