/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 17:01:49
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-15 14:33:04
 * @Description:
 */

import { Breadcrumb, BreadcrumbProps } from 'antd';
import { history } from 'umi';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';
import { useModel } from '@umijs/max';
import { useEffect, useState } from 'react';

export type BreadcrumbItem = {
  icon?: string;
  name?: string;
  url?: string;
  onClick?: (e: MouseEvent | null) => void;
};

type DefaultBreadcrumbProps = BreadcrumbProps & {
  options?: BreadcrumbItem[];
  isAuto?: boolean; // 是否根据路由的配置生产图标 默认true
};

function NormalBreadcrumb({ options, isAuto = true, ...rest }: DefaultBreadcrumbProps): JSX.Element {
  const { initialState } = useModel('@@initialState');
  const [finalOptions, setFinalOptions] = useState<BreadcrumbItem[]>(options || []);

  useEffect(() => {
    // 菜单路径
    const originalUrl = location.pathname;
    // 长度
    const arr = originalUrl.split('/');
    const len = arr.length;
    const iconMap: any = {};
    initialState?.allMenuIcon?.forEach(v => {
      iconMap[v.path] = v;
    });
    if (isAuto || !options) {
      const finArr: BreadcrumbItem[] = [];
      for (let i = 0; i < len; i++) {
        const tmp = arr.slice(0, i + 1).join('/');
        const obj = iconMap[tmp];
        if (obj) {
          finArr.push({ ...obj, url: obj.path, name: obj.title, icon: i <= 1 ? obj.icon : '' });
        }
      }
      setFinalOptions(finArr);
    } else {
      setFinalOptions(options || []);
    }
  }, [options, isAuto]);

  const handleClick = (url?: string, onClick?: (e: MouseEvent) => void) => {
    if (onClick) {
      // 暂时用 null 代替，后续有其他的参数也可以传入
      onClick(null as any);
      return;
    }

    if (url) {
      if (url.includes('/supply/') || url.includes('/demand/')) {
        history.go(-1);
        return;
      }
      history.push(url);
    }
  };

  return (
    <Breadcrumb {...rest} className={`${styles['bread-container']} ${rest.className || ''}`}>
      {finalOptions.map((item, index) => {
        return (
          <Breadcrumb.Item key={index} className={styles['bread-item']} onClick={() => handleClick(item.url, item.onClick)}>
            <span
              className={index === finalOptions.length - 1 ? styles['last'] : styles['active']}
            >
              {item.icon && <RixEngineFont type={`${item.icon}`} style={{ fontSize: 20, marginRight: 6 }} />}
              {item.name}
            </span>
          </Breadcrumb.Item>
        );
      })}
    </Breadcrumb>
  );
}

export default NormalBreadcrumb;
