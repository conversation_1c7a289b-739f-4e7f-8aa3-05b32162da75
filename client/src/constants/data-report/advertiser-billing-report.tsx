/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-27 21:59:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 10:35:15
 * @Description:
 */

// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem, CheckboxUniqueKeyOptionsType } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';

// ?components
import HoverToolTip from '@/components/ToolTip/HoverTooltip';

// ?utils
import { formatMoney } from '@/utils';
import { Countries, CountryOptions } from '../global-mapping/country';
import { AdFormatOptions, AdFormatToLabel } from '../global-mapping/ad-format';
import { FormatExportValueMapType } from '@/utils/export-file';

export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'Billing Report'
  },
  {
    name: 'Advertiser'
  }
];

const DimensionsOptions = [
  { label: 'Date', value: 'day' },
  { label: 'Month', value: 'month' },
  { label: 'Advertiser', value: 'buyer_id' },
  { label: 'Publisher', value: 'seller_id' },
  { label: 'Adv Partner', value: 'partner_id' },
  { label: 'Bundle', value: 'app_bundle_id' },
  { label: 'Country', value: 'country' },
  { label: 'Ad Format', value: 'ad_format' },
  { label: 'Ad Size', value: 'ad_size' },
];
export const SearchOptions: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 183
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Adv Partner',
    key: 'partner_id',
    type: 'selectAll',
    mode: 'multiple',
    value: []
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'app_bundle_id',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Country',
    key: 'country',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: CountryOptions
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    mode: 'multiple',
    value: '',
    type: 'select',
    options: AdFormatOptions
  },
  {
    name: 'Ad Size',
    key: 'ad_size',
    mode: 'multiple',
    value: [],
    type: 'select',
    options: [],
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: DimensionsOptions
  }
];

export const DefaultDimension = ['day', 'buyer_id'];
export const CheckboxUniqueKeyOptions: CheckboxUniqueKeyOptionsType[] = [
  {
    key: 'columns',
    value: ['day', 'month'] // 这两个值二选一
  }
];

export const AllColumns: ColumnProps<DashboardAPI.BillingListItem>[] = [
  {
    title: 'Date',
    dataIndex: 'date',
    width: 130,
    key: 'day',
    fixed: 'left',
    sorter: (a, b) => {
      const a1 = new Date(a.date).getTime();
      const b1 = new Date(b.date).getTime();
      return a1 - b1;
    }
  },
  {
    title: 'Month',
    key: 'month',
    dataIndex: 'date',
    width: 130,
    sorter: (a, b) => {
      const a1 = new Date(a.date).getTime();
      const b1 = new Date(b.date).getTime();
      return a1 - b1;
    }
  },
  {
    title: 'Advertiser',
    width: 180,
    dataIndex: 'buyer_id',
    key: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.buyer}>
        <span>{params.buyer}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher',
    width: 180,
    dataIndex: 'seller_id',
    key: 'seller_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.seller}>
        <span>{params.seller}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Adv Partner',
    dataIndex: 'partner_id',
    width: 180,
    ellipsis: { showTitle: false },
    render: (_, params) => {
      const str = _ ? `${params.partner_name || '-'}(${_})` : _ || '-';
      return (
        <HoverToolTip title={str}>
          <span>{str}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Advertiser Net Revenue',
    width: 220,
    dataIndex: 'buyer_net_revenue',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Impression(ADM)',
    width: 150,
    dataIndex: 'impression',
    sorter: (a, b) => +a.impression - +b.impression,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Bundle',
    dataIndex: 'app_bundle_id',
    width: 150,
    key: 'app_bundle_id'
  },
  {
    title: 'Country',
    dataIndex: 'country',
    width: 150,
    key: 'country',
    render: (txt: string) => <>{Countries[txt]}</>
  },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 150,
    key: 'ad_format',
    render: (txt: string) => <>{AdFormatToLabel[txt]}</>
  },
  {
    title: 'Ad Size',
    dataIndex: 'ad_size',
    width: 150,
    key: 'ad_size',
  },
  // {
  //   title: 'Payment Impression',
  //   width: 180,
  //   dataIndex: 'seller_payment_impression',
  //   sorter: true,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>
  // },
  {
    title: 'Out Request',
    width: 160,
    dataIndex: 'request',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Response',
    width: 160,
    dataIndex: 'response',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  }
];
export const DefaultColumnKeys = [
  // 'buyer_gross_revenue',
  'buyer_net_revenue',
  'impression',
  'seller_payment_impression',
  'request',
  'response'
];

export const FormatExportValueMap: FormatExportValueMapType = {
  country: (value: string) => Countries[value as keyof typeof Countries],
  ad_format: (value: string) => AdFormatToLabel[value],
  partner_id: (partner_id: string, row: Record<string, any>) => {
    const { partner_name } = row;
    // 检查partner_id和partner_name是否存在，以及partner_id中是否包含partner_name
    if (partner_id && partner_name && !String(partner_id).includes(partner_name)) {
      return `${partner_name}(${partner_id})`;
    }
    return partner_id;
  }
};
