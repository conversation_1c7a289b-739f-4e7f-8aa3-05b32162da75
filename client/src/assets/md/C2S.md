# 「RixEngine」 C2S（client to server）API 对接文档 (For 自研内嵌 SDK) 

## 接口和实现文档（自研内嵌 APP 广告SDK API文档）

> LAST UPDATED 2023-8-12

### 1. 请求 URL

- 测试节点: ` http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d`
- 正式节点: `https://{subdomain}.svr.rixengine.com/rtb?sid={YOUR_SID}&token={YOUR_TOKEN}`
- `{subdomain}，{YOUR_SID}，{YOUR_TOKEN}`，app_id（应用 ID），adslot_id(广告位 ID），由平台生成分配，具体参数联系运营人员申请获得
- 测试节点可以用以下测试参数进行测试：

```Java
public static String *SID*= "36057"; // sid
public static String *TOKEN*= "c976563eb2f1f134222d2730294e552d"; // token

public static String *APP_ID*= "102512"; // app_id
// 4种广告类型广告位
public static String *BANNER_AD_PID*= "201304"; //banner AD id
public static String *NATIVE_AD_PID*= "201307";//native AD id
public static String *REWARD_VIDEO_AD_PID*= "201306";//reward video AD id
public static String *INTERSTITIAL_AD_PID*= "201299";//interstitial AD id
```

### 2. 请求方式

- Method: `POST`
- Header: Content-Type Value: `application/json`
- 类 oRTB 协议，精简化对接，适应 APP 开发对接

### 3. 请求参数

| **父节点** | **字段** | **类型** | **必需** | **描述** |
| --- | --- | --- | --- | --- |
|  | id | `string` | 是 | 唯一出价请求 ID。由 sdk 生成。 |
|  | app_id | `string` | 是 | 应用程序在 **RixEngine 系统** 中的唯一标识符。 |
|  | adslot_id | `string` | 是 | **RixEngine 系统**中广告位的唯一标识符 |
|  | adtype | `Int` | 是 | <a href="#2. 广告类型">广告类型<a/>，详见列表 |
|  | sdkv | `string` | 是 | sdk 版本号（只含数字与"."符号） |
|  | omidpv | `string` | 否 | omsdk partner version，IAB 官方认证版本号，比如"3.0.0" |
|  | bundle | `string` | 是 | app 包名 |
|  | app_name | `string` | 是 | app 的名字 |
|  | device | `object` | 是 | 设备信息 |
| device | user_agent | `string` | 是 | user agent |
| device | did | `string` | 否 | IMEI。 |
| device | dpid | `string` | 否 | Android ID。 |
| device | mac | `string` | 否 | MAC 地址。 |
| device | ifa | `string` | 是 | 广告标识符 |
| device | oaid | `string` | 否 | 匿名设备标识符 |
| device | geo | `object` | 否 | 地理信息 |
| device.geo | latitude | `float` | 否 | 纬度 |
| device.geo | longitude | `float` | 否 | 经度 |
| device.geo | country | `string` | 否 | 三位国家代码 [ISO-3166-1-alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) |
| device.geo | region | `string` | 否 | Region code using [ISO-3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) |
| device | devicetype | `int` | 是 | 设备类型. Refer to list <a href="#3. 设备类型">Device Type</a> |
| device | language | `string` | 否 | 语言. [ISO-639-1-alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) |
| device | os | `int` | 是 | 设备操作系统. “1” = IOS , “2” = Android, “0” = 未知. |
| device | osv | `string` | 是 | 系统版本. |
| device | make | `string` | 否 | 设备品牌. |
| device | model | `string` | 否 | 设备型号. |
| device | screen_width | `int` | 否 | 屏幕的物理宽度（以像素为单位）。 |
| device | screen_height | `int` | 否 | 屏幕的物理高度（以像素为单位）。 |
| device | screen_density | `int` | 否 | 屏幕像素. |
| device | connectiontype | `int` | 是 | 网络类型. “1” = WI FI, “2” = 2G, “ 3” = 3G, “4” = 4G, “5” = 5G, “0” = 未知. |
| regs | coppa | `int` | 否 | 是否受美国 FTC 制定的 COPPA 法规约束, 0 = 否, 1 = 是. |
| regs | gdpr | `int` | 否 | 是否受欧盟制定的 GDPR 法规约束, 0 = 否, 1 = 是. |
| regs | gdpr_consent | `string` | 否 | 用户同意信息。 |
| regs | us_privacy | `string` | 否 | 适用于受 CCPA 规定约束的请求。 必须遵循[美国隐私字符串格式](https://github.com/InteractiveAdvertisingBureau/USPrivacy/blob/master/CCPA/US Privacy String.md) |

##### 3.1 请求示例

```JSON
{
  "id": "fgasikutgasurebuifgsiuafgisua",
  "app_id": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
  "adslot_id": "171764",
  "sdkv": "3.0.0",
  "adtype": 1,
  "device": {
      "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; it-it; GT-S5570I Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 (Mobile; afma-sdk-a-v6.1.0),gzip(gfe)",
      "did": "2536643785678345673845",
      "dpid": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "mac": "5C-FF-35-0B-95-B5",
      "ifa": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "oaid": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "geo": {
          "lat": 27.3333,
          "lon": 88.6167,
          "country": "USA",
          "region": "CA"
      },
      "devicetype": 1,
      "language": "CN",
      "os": 2,
      "osv": "9.0.1",
      "make": "xiaomi",
      "model": "hongmi",
      "screen_width": 640,
      "screen_height": 1136,
      "screen_density": 326,
      "connectiontype": 4
  },
  "regs": {
      "coppa": 0,
      "gdpr": 1,
      "gdpr_consent": "",
      "us_privacy": "1YN-"
  }
}
```

### 4. 响应返回字段

| **父节点** | **字段** | **类型** | **必需** | **描述** |
| --- | --- | --- | --- | --- |
|  | err_no | `int` | 是 | 错误码. Refer to list <a href="#4. 错误码以及错误信息">Err No and Msg</a> |
|  | err_msg | `string` | 是 | 错误信息 |
|  | data | `object` | 否 | 广告信息 |
| data | id | `string` | 是 | 广告响应唯一 ID |
| data | ads | `objetc array` | 是 | 广告列表 |
| data.ads | adm_type | `int` | 是 | 广告数据格式, 详见<a href="#1. 广告数据格式">列表</a> |
| data.ads | crid | `string` | 是 | 广告素材 id |
| data.ads | cid | `string` | 是 | 广告 id |
| data.ads | adomain | `string array` | 否 | 广告主域名 |
| data.ads | cat | `string array` | 否 | 广告类型 |
| data.ads | bundle | `string` | 否 | 投放广告的包名 |
| data.ads | adm | `string` | 是 | 广告代码 banner 为 htmlVideo 为 VastNative 为 html 或 JSON (需要将${AUCTION_PRICE}替换为实际的价格) |
| data.ads | width | `int` | 是 | 广告宽度 |
| data.ads | height | `int` | 是 | 广告高度 |
| data.ads | imptrackers | `array` | 是 | 展示上报链接(如果数据为空就不用上报)，需要将${AUCTION_PRICE}替换为实际的价格 |
| data.ads | clicktrackers | `array` | 是 | 点击上报链接 （如果数据为空就不用上报），需要将${AUCTION_PRICE}替换为实际的价格 |
| data.ads | deeplink | `string` | 否 | deeplink |
| data.ads | video_ext | `object` | 否 | video 广告额外字段 |
| data.ads. video_ext | skip | `bool` | 否 | 视频是否可以跳过 (仅 adtype 为 video 时返回) |
| data.ads. video_ext | skipafter | `int` | 否 | 跳过之前视频必须播放的秒数，仅适用于可跳过的广告。 (仅 adtype 为 video 时返回) |
| data.ads. video_ext | mute | `bool` | 否 | 是否默认静音播放 |
| data.ads. video_ext | close | `bool` | 否 | 用于指示激励视频是否允许关闭 |
| data.ads | banner_ext | `object` | 否 | banner 广告额外字段 |
| data.ads | native_ext | `object` | 否 | native 广告额外字段 |
| data.ads. native_ext | omid | `object` | 否 | omsdk 相关字段 |
| data.ads. native_ext. omid | vendorKey | `string` | 是 | 密钥 |
| data.ads. native_ext. omid | javascriptResourceUrl | `string` | 是 | 验证脚本地址 |
| data.ads. native_ext. omid | verificationParameters | `string` | 是 | 验证参数 |
| data.ads. native_ext | asset_type | `int` | 否 | <a href="#5. Native 素材类型">素材类型</a>，默认未知 |
| data.ads. native_ext | source | `string` | 否 | 广告来源， 默认为空 |
| data.ads | price | `float` | 否 | 出价，单位为美元。当 price 为 0 时，代表不返回出价，则无需替换${AUCTION_PRICE}。 |
| data.ads | nurl | `string` | 否 | 竞价成功通知 url，需要将${AUCTION_PRICE}替换为实际的价格 |
| data.ads | burl | `string` | 否 | 计费通知 url，需要将${AUCTION_PRICE}替换为实际的价格 |

### 5. 广告请求案例，以下所有涉及的源代码都可以进行参考修改开发

#### 5.1 Banner 请求案例

 banner请求url

```apl
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 banner请求参数

```JSON
{
    "id": "b4f6dba2-70a5-4edf-a264-8ab8486295db|1676448348578",
    "adtype": 1,
    "app_id": "102512",
    "adslot_id": "201304",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 banner返回

```JSON
{
    "data": {
        "id": "b4f6dba2-70a5-4edf-a264-8ab8486295db|1676448348578",
        "ads": [
            {
                "cid": "249",
                "bundle": "algorix.co",
                "adm": "<!DOCTYPE html><html><head> <meta charset='UTF-8'> <meta content='width=device-width, initial-scale=1.0' name='viewport'> <meta content='ie=edge' http-equiv='X-UA-Compatible'> <title></title></head><body><style> body {  margin: 0 } .mbm {  position: absolute;  top: 0;  left: 0;  width: 100%;  height: 100%;  background: url('https://ww0.svr-algorix.com/pic/b591ff9d6cf836e8494846579391af66.jpg') center no-repeat;  background-size: contain } .mbmad {  font-size: 12px;  position: absolute;  top: 0;  left: 0;  color: #eae3e3;  background: #333;  z-index: 9999;  opacity: 0.5; }</style><div> <span class=mbmad>AD</span> <img src='https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwC3ywWvTAmh7jDeA3ftgi0unrRaczw0tmhGZHPDZo43FrzAu8HJHWBPHLSQKRx7nPUM96HZB_Bp5MdsqYz2YjsvL8L2z5ShPtZ3gnXE0qt4dcmGfgon7HjqCcq0ckOXZnOUMKSQT5kuvn-G0y0nMmwKckhzLgzWxaRP-cnVhRAVZNRR__g-D28CatW3ec-UQ-wgTKU3DyKZfpNghZcxmlHcyCQzO_9zGDG1k15ssLMjdbWaboO5t7MRO5wQ1HunkHXc1e_1tWUdPGbjj4565tbC2ilqOVGHbMTNJWr8cBw9qk6iXJyjv1wWBZ8hW7XiGuF4scLeZudrH_nLAKmeZJ13CIQ6kksXzLEYJ03l8MyH8-CR9lMC5XzRk8Hynli1VKE4nmztOtbApC6feI2o_cEmoia-3JAMkxXIxzCOBMx-yCi7eoUsoh8rvhrporI6Xg%3D%3D' width='0' height='0' style='display:none'>  <a class=mbm id=imgbtn></a> <script>  document.getElementById('imgbtn').addEventListener('touchstart', function (e) {   e.preventDefault;   new Image().src = '{adx_click_url}';   new Image().src = 'https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwC3ywWvTAmh7jDeA3ftgi0unrRaczw0tmhGZHPDZo43FrzAu8HJHWBPHLSQKRx7nPUM96HZB_Bp5MdsqYz2YjsvL8L2z5ShPtZ3gnXE0qt4dcmGfgon7HjqCcq0ckOXZnOUMKSQT5kuvn-G0y0nMmwKckhzLgzWxaRP-cnVhRAVZNRR__g-D28CatW3ec-UQ-wgTKU3DyKZfpNghZcxmlHcyCQzO_9zGDG1k15ssLMjdbWaboO5t7MRO5wQ1HunkHXc1e_1tWUdPGbjj4565tbC2ilqOVGHbMTNJWr8cBw9qk6iXJyjv1wWBZ8hW7XiGuF4scLeZudrH_nLAKmeZJ13CIQ6kksXzLEYJ03l8MyH8-CR9lMC5XzRk8Hynli1VKE4nmztOtbApC6feI2o_cEmoia-3JAMkxXIxzCOBMx-yCi7eoUsoh8rvhrporI6Xg%3D%3D';      setTimeout(() => {    top.location = 'https://www.algorix.co/';   }, 50);  }, true); </script></div></body></html><img src=\"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}\" width=\"1\" height=\"1\" style=\"display:none;\"><script type=\"text/javascript\">!function(){(new Image).src=\"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}\"}();</script><script type=\"text/javascript\">!function(){(new Image).src=\"https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXmHzMWNJa9\"}();</script>",
                "width": 320,
                "height": 50,
                "price": 0.109,
                "adm_type": 1,
                "crid": "590_8179",
                "adomain": [
                    "algorix.co"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
                ],
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_ycinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.2 激励视频请求案例

 激励视频请求url

```api
https://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 激励视频请求参数

```JSON
{
    "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
    "adtype": 4,
    "app_id": "102512",
    "adslot_id": "201306",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 激励视频返回

```JSON
{
    "data": {
        "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
        "ads": [
            {
                "cid": "227",
                "bundle": "com.lazada.android",
                "adm": "
<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    <Ad id="AlgoriX-324ade2d8c53e5b37571aa04588a9de6">
        <InLine>
            <AdSystem>Algorix</AdSystem>
            <AdTitle>BELI SEKARANG</AdTitle>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
            </Impression>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=impress]]>
            </Impression>
            <Error>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=[ERRORCODE]]]>
            </Error>
            <Creatives>
                <Creative>
                    <Linear>
                        <Duration>00:00:20</Duration>
                        <MediaFiles>
                            <MediaFile width="1280" height="720" type="video/mp4" bitrate="1179" delivery="progressive" scalable="true" maintainAspectRatio="true">
                                <![CDATA[https://ww0.svr-algorix.com/pic/bf9c24d0b527d1598803dc35248633e8.mp4]]>
                            </MediaFile>
                        </MediaFiles>
                        <TrackingEvents>
                            <Tracking event="start">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=start]]>
                            </Tracking>
                            <Tracking event="midpoint">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=midpoint]]>
                            </Tracking>
                            <Tracking event="firstQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=firstQuartile]]>
                            </Tracking>
                            <Tracking event="thirdQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=thirdQuartile]]>
                            </Tracking>
                            <Tracking event="complete">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=complete]]>
                            </Tracking>
                        </TrackingEvents>
                        <VideoClicks>
                            <ClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </ClickThrough>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=click]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
                            </ClickTracking>
                        </VideoClicks>
                    </Linear>
                </Creative>
                <Creative>
                    <CompanionAds>
                        <Companion width="1280" height="720">
                            <StaticResource creativeType="image/png">
                                <![CDATA[https://ww0.svr-algorix.com/pic/449a2920e990ea5beff62b6295c97c8c.png]]>
                            </StaticResource>
                            <CompanionClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </CompanionClickThrough>
                            <TrackingEvent>
                                <Tracking event="creativeView">
                                    <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=companioncreativeView]]>
                                </Tracking>
                            </TrackingEvent>
                            <CompanionClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
                            </CompanionClickTracking>
                        </Companion>
                    </CompanionAds>
                </Creative>
            </Creatives>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXnHzMWNJa9]]>
            </Impression>
        </InLine>
    </Ad>
</VAST>
",
                "width": 1280,
                "height": 720,
                "price": 1.009,
                "adm_type": 3,
                "crid": "515_2143",
                "adomain": [
                    "lazada.sg"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
                ],
                "video_ext": {
                    "skip": false,
                    "mute": false,
                    "close": false,
                    "skipafter": 5
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-RsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

###### 开发者需要自行开发 VAST 协议解析器和内嵌视频播放器，参考 IAB 美国互动广告局官方标准进行开发（重要）：

https://iabtechlab.com/standards/vast/

VAST 协议最低需要支持到 2.0，最好支持 3.0，4.0

激励视频和插屏视频广告都需要用到 VAST 协议解析器和内嵌视频播放器

#### 5.3 插屏请求案例

 插屏请求url

```api
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 插屏请求参数

```JSON
{
    "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
    "adtype": 3,
    "app_id": "102512",
    "adslot_id": "201299",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 插屏返回

```JSON
{
    "data": {
        "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
        "ads": [
            {
                "cid": "227",
                "cat": [
                    "IAB24"
                ],
                "bundle": "com.lazada.android",
                "adm": "
<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    <Ad id="AlgoriX-46b8f8b972c42d012a4b85a56af7a8fb">
        <InLine>
            <AdSystem>Algorix</AdSystem>
            <AdTitle>BELI SEKARANG</AdTitle>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
            </Impression>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=impress]]>
            </Impression>
            <Error>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=[ERRORCODE]]]>
            </Error>
            <Creatives>
                <Creative>
                    <Linear>
                        <Duration>00:00:20</Duration>
                        <MediaFiles>
                            <MediaFile width="480" height="320" type="video/mp4" bitrate="834" delivery="progressive" scalable="true" maintainAspectRatio="true">
                                <![CDATA[https://ww0.svr-algorix.com/pic/100ef65516fa50aafd5676ae6f6b5fed.mp4]]>
                            </MediaFile>
                        </MediaFiles>
                        <TrackingEvents>
                            <Tracking event="start">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=start]]>
                            </Tracking>
                            <Tracking event="midpoint">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=midpoint]]>
                            </Tracking>
                            <Tracking event="firstQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=firstQuartile]]>
                            </Tracking>
                            <Tracking event="thirdQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=thirdQuartile]]>
                            </Tracking>
                            <Tracking event="complete">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=complete]]>
                            </Tracking>
                        </TrackingEvents>
                        <VideoClicks>
                            <ClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </ClickThrough>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=click]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
                            </ClickTracking>
                        </VideoClicks>
                    </Linear>
                </Creative>
                <Creative>
                    <CompanionAds>
                        <Companion width="480" height="320">
                            <StaticResource creativeType="image/jpeg">
                                <![CDATA[https://ww0.svr-algorix.com/pic/9a504019771756e8db6dc81ae82a39e9.jpg]]>
                            </StaticResource>
                            <CompanionClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </CompanionClickThrough>
                            <TrackingEvent>
                                <Tracking event="creativeView">
                                    <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=companioncreativeView]]>
                                </Tracking>
                            </TrackingEvent>
                            <CompanionClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
                            </CompanionClickTracking>
                        </Companion>
                    </CompanionAds>
                </Creative>
            </Creatives>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXnHzMWNJa9]]>
            </Impression>
        </InLine>
    </Ad>
</VAST>
",
                "width": 480,
                "height": 320,
                "price": 1.009,
                "adm_type": 3,
                "crid": "515_2141",
                "adomain": [
                    "lazada.sg"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
                ],
                "video_ext": {
                    "skip": true,
                    "mute": true,
                    "close": false,
                    "skipafter": 5
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBe5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.4 原生请求案例

 原生请求url

```Plain
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 原生请求参数

```JSON
{
    "id": "0f1eb423-0e05-42c1-944b-b4fab3f17f14|1676448436983",
    "adtype": 5,
    "app_id": "102512",
    "adslot_id": "201307",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 原生返回

```JSON
{
    "data": {
        "id": "0f1eb423-0e05-42c1-944b-b4fab3f17f14|1676448436983",
        "ads": [
            {
                "cid": "103",
                "bundle": "test.bestai.TextScanner",
                "adm": "
{
    "title": {
        "value": "Text Scanner"
    },
    "cta": {
        "value": "MORE"
    },
    "desc": {
        "value": "A scanner app, using OCR technology."
    },
    "icon": {
        "width": 50,
        "url":"https://ww0.svr-algorix.com/pic/5e52f36602b3e7488ca5b3bbb4c84935.png",
        "height": 50
    },
    "main": [
        {
            "width": 1200,
            "url":"https://ww0.svr-algorix.com/pic/22f6310cc6a0da84c91feae6fbe0a5c0.jpg",
            "height": 627
        }
    ],
    "link": {
        "url":"https://play.google.com/store/apps/details?id=com.bestai.TextScanner&clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057"
    }
}
",
                "width": 1200,
                "height": 627,
                "price": 0.109,
                "adm_type": 2,
                "crid": "160_8124",
                "adomain": [
                    "test.bestai.TextScanner"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D",
                   "https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXmHzMWNJa9"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D",
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}"
                ],
                "native_ext": {
                    "omid": {
                        "vendorKey": "iabtechlab.com-omid",
                        "javascriptResourceUrl":"https://s3-us-west-2.amazonaws.com/updated-omsdk-files/compliance-js/omid-validation-verification-script-v1-ALGORIXCO-06272022.js",
                        "verificationParameters": "iabtechlab-Algorixco"
                    },
                    "asset_type": 1
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerepdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.5 原生广告广告素材 Adm 字段格式

| **父节点** | **字段** | **类型**       | **必需** | **描述**                         |
| ---------- | -------- | -------------- | -------- | -------------------------------- |
|            | title    | `object`       | 否       | 标题。                           |
| title      | value    | `string`       | 是       | 标题文本，最长 90 字节。         |
|            | cta      | `object`       | 否       | "call to action"按钮             |
| cta        | value    | `string`       | 是       | 按钮文本，最长 15 字节。         |
|            | desc     | `object`       | 否       | 描述。                           |
| desc       | value    | `string`       | 是       | 描述文本，最长 140 字节          |
|            | icon     | `object`       | 否       | icon 图片，比例 1:1              |
| icon       | url      | `string`       | 是       | icon 图片链接                    |
| icon       | width    | `int`          | 是       | icon 尺寸(单位：px）             |
| icon       | height   | `int`          | 是       | icon 尺寸(单位：px）             |
|            | main     | `object array` | 否       | 大图图片                         |
| main       | url      | `string`       | 是       | 大图图片链接                     |
| main       | width    | `int`          | 是       | 大图宽度，通常为 1200(单位：px） |
| main       | height   | `int`          | 是       | 大图高度，通常为 627(单位：px）  |
|            | link     | `object`       | 是       | 点击落地页                       |
| link       | url      | `string`       | 是       | 落地页链接                       |

### 6. 广告流程处理和渲染方式

#### 6.1 Banner 广告

1. **向布局中添加 AlxBannerView（banner 广告参考名称)**

要展示横幅广告，首先要将 **AlxBannerView** 放置到您希望用于展示广告的 `Activity` 或 `Fragment` 的布局中。最简单的方法是向相应的 XML 布局文件添加一个。以下是展示 Activity 的 **AlxBannerView** 的示例:

```XML
# activity_banner.xml
<com.alxad.api.AlxBannerView
    android:id="@+id/ad_banner"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" />
```

您也可以以编程方式创建 **AlxBannerView:**

```Java
AlxBannerView bannerAdView = new AlxBannerView(this);
```

2. **加载广告**

AlxBannerView 设置完毕后，下一步是加载广告。这是通过 AlxBannerView 类中的 `loadAd()` 方法完成的。这需要添加广告位参数

以下示例展示了如何在 `Activity` 的 `onCreate()` 方法中加载广告：

**MainActivity (节选)**

```Java
public class BannerActivity extends AppCompatActivity {

    private AlxBannerView bannerView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_banner);
        bannerView = (AlxBannerView) findViewById(R.id.ad_banner);

        //设置关闭图标隐藏
        bannerView.setBannerCanClose(false);
        //设置刷新事件间隔为30秒，0表示不自动刷新
        bannerView.setBannerRefresh(30);
        //加载广告
        bannerView.loadAd("172746", bannerViewAdListener);
    }

    @Override
    protected void onDestroy() {
        if (bannerView != null) {
            bannerView.destroy();
        }
        super.onDestroy();
    }

    private AlxBannerViewAdListener bannerViewAdListener=new AlxBannerViewAdListener() {
        @Override
        public void onAdLoaded() {
            //广告加载成功
        }

        @Override
        public void onAdError(int errorCode, String errorMsg) {
            //广告加载失败
        }

        @Override
        public void onAdClicked() {

        }

        @Override
        public void onAdShow() {

        }

        @Override
        public void onAdClose() {

        }
    };

}
```

**广告事件**：`AlxBannerViewAdListener`是 Banner 广告的事件回调监听

| 方法        | 说明             |
| ----------- | ---------------- |
| onAdLoaded  | 广告加载成功回调 |
| onAdError   | 广告加载失败回调 |
| onAdClicked | 广告点击回调     |
| onAdShow    | 广告展示回调     |
| onAdClose   | 广告关闭回调     |

**效果图**

![](https://static.rixengine.com/a/platform/help/c2s/6.1-2-1.png)

广告尺寸：小尺寸 320*50 ，大尺寸 320*480

**注意（重要）：Banner 广告的广告物料 adm 素材不要在后台或者用户不可见的状态下使用 webview 提前 loadhtml()，这样会导致展示判断错误，产生 IVT（无效流量）问题，影响广告主对 APP 的可信度。必须在用户可见广告控件 view 的时候才能用 webview loadhtml()加载展示出广告，而不是在后台预先加载\*\***html\***\*素材。**

#### 6.2 激励视频广告

1. **创建激励视频广告对象**

创建 AlxRewardVideoAD 对象，示例代码如下所示：

```Java
public class RewardVideoActivity extends AppCompatActivity {
    private AlxRewardVideoAD rewardAD;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_reward_video);
        createRewardAd();
    }

    private void createRewardAd(){
        rewardAD = new AlxRewardVideoAD();
    }
 }
```

2. **加载广告**

调用 AlxRewardVideoAD 的 load() 方法来加载广告，示例代码如下所示：

```Java
/**
 * 加载激励广告
 */
public void loadAd() {
    rewardAD.load(this, "172750", listener);
}

private AlxRewardVideoADListener listener = new AlxRewardVideoADListener() {
    @Override
    public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
        //广告加载成功
    }
    @Override
    public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
        //广告加载失败
    }

    @Override
    public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
    }

    @Override
    public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {

    }

    @Override
    public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {

    }

    @Override
    public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {

    }

    @Override
    public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
    }

    @Override
    public void onReward(AlxRewardVideoAD var1) {
    }

    @Override
    public void onRewardVideoCache(boolean isSuccess) {
    }
};
```

3. 展示广告

调用 AlxRewardVideoAD 的 showVideo(Activity activity)方法来显示广告，在此之前需要调用 isReady()方法来确认广告是否已经加载完成。示例代码如下所示：

```Java
public void showAd(){
    if(rewardAD != null  && rewardAD.isReady()){
        rewardAD.showVideo(this);
    }
}
```

**广告事件**：`AlxRewardVideoADListener`是激励广告的事件回调监听

| 方法                         | 说明                 |
| ---------------------------- | -------------------- |
| onRewardedVideoAdLoaded      | 广告加载成功回调     |
| onRewardedVideoAdFailed      | 广告加载失败回调     |
| onRewardedVideoAdPlayClicked | 广告点击回调         |
| onRewardedVideoAdPlayStart   | 广告展示回调         |
| onRewardedVideoAdPlayEnd     | 广告播放完成回调     |
| onRewardedVideoAdPlayFailed  | 广告视频播放失败回调 |
| onReward                     | 广告下发激励回调     |
| onRewardedVideoAdClosed      | 广告关闭回调         |

效果

<video src="https://static.rixengine.com/a/platform/help/c2s/6.2-3-1.mp4" controls width="375"></video>

#### 6.3 插屏广告

1. **创建插屏广告对象**

创建 AlxInterstitialAD 对象，示例代码如下所示：

```Java
public class InterstitialActivity extends AppCompatActivity {
    private AlxInterstitialAD interstitialAD;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_interstitial);
        createInterstitialAd();
    }

    private void createInterstitialAd(){
        interstitialAD = new AlxInterstitialAD();
    }
}
```

2. **加载广告**

调用 AlxInterstitialAD 的 load() 方法来加载广告，示例代码如下所示：

```Java
/**
 * load Ad
 */
public void loadAd() {
    interstitialAD.load(this, "172748", listener);
}

private AlxInterstitialADListener listener = new AlxInterstitialADListener() {
    @Override
    public void onInterstitialAdLoaded() {
        //广告加载成功
    }

    @Override
    public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
        //广告加载失败
    }

    @Override
    public void onInterstitialAdClicked() {
    }

    @Override
    public void onInterstitialAdShow() {
    }

    @Override
    public void onInterstitialAdClose() {
    }

    @Override
    public void onInterstitialAdVideoStart() {
    }

    @Override
    public void onInterstitialAdVideoEnd() {
    }

    @Override
    public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
    }

};
```

3. **展示广告**

调用 AlxInterstitialAD 的 show(Activity activity)方法来显示广告，在此之前需要调用 isReady()方法来确认广告是否已经加载完成。示例代码如下所示：

```Java
private void showAd(){
    if (interstitialAD != null && interstitialAD.isReady()) {
        interstitialAD.show(this);
    }
}
```

**广告事件**：`AlxInterstitialADListener`是激励广告的事件回调监听

| 方法                       | 说明                 |
| -------------------------- | -------------------- |
| onInterstitialAdLoaded     | 广告加载成功回调     |
| onInterstitialAdLoadFail   | 广告加载失败回调     |
| onInterstitialAdClicked    | 广告点击回调         |
| onInterstitialAdShow       | 广告展示回调         |
| onInterstitialAdVideoStart | 广告视频播放开始回调 |
| onInterstitialAdVideoEnd   | 广告视频播放完成回调 |
| onInterstitialAdVideoError | 广告视频播放异常回调 |
| onInterstitialAdClose      | 广告关闭回调         |

#### 6.4 原生广告

1. **创建 AlxNativeAdLoader 对象**

创建 AlxNativeAdLoader 对象，示例代码如下所示：

```Java
private AlxNativeAdLoader loader;
@Override
protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_native);
    loader = new AlxNativeAdLoader.Builder(this, "172943").build();
}
```

2. **加载广告**

AlxNativeAdLoader 提供 loadAd() 方法加载广告，示例代码如下所示：

```Java
private void loadAd() {
    loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
        @Override
        public void onAdFailed(int errorCode, String errorMsg) {
            //广告加载失败
        }

        @Override
        public void onAdLoaded(List<AlxNativeAd> ads) {
            //广告加载成功
        }
    });
}
```

3. **展示原生广告**

以下是展示原生广告的步骤：

a. 定义原生广告布局。

 需要自定义一个布局用于展示AlxNativeAd中的素材。

 说明：必须将AlxNativeAdView作为原生广告的根布局，否则会影响广告收益。

 对于使用RelativeLayout来展示素材视图的原生广告，其视图层次结构示例如下：

```XML
<com.alxad.api.nativead.AlxNativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    ... >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        ... >
        <!-- 多媒体视图 -->
        <com.alxad.api.nativead.AlxMediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            ... />
        <RelativeLayout
            ... >
        <TextView
            android:id="@+id/ad_title"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            ... />
        <!-- 其他素材 -->
        ...
        </RelativeLayout>
        <!-- 其他素材 -->
        ...
    </RelativeLayout>
</com.alxad.api.nativead.AlxNativeAdView>
```

b. 注册和填充素材视图

获取到 AlxNativeAdView 对象后，需要注册和填充素材。代码如下:

```Java
private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
        ImageView logo = (ImageView) nativeView.findViewById(R.id.ad_logo);
        ImageView icon = (ImageView) nativeView.findViewById(R.id.ad_icon);
        TextView title = (TextView) nativeView.findViewById(R.id.ad_title);
        TextView description = (TextView) nativeView.findViewById(R.id.ad_desc);
        TextView source = (TextView) nativeView.findViewById(R.id.ad_source);
        Button callToAction = (Button) nativeView.findViewById(R.id.ad_call_to_action);
        ImageView close = (ImageView) nativeView.findViewById(R.id.ad_close);
        AlxMediaView mediaView = (AlxMediaView) nativeView.findViewById(R.id.ad_media);

        //注册和填充标题素材视图
        nativeView.setTitleView(title);
        title.setText(nativeAd.getTitle());

        //注册和填充多媒体素材视图
        nativeView.setMediaView(mediaView);
        mediaView.setMediaContent(nativeAd.getMediaContent());

        //注册和填充其他素材视图
        nativeView.setDescriptionView(description);
        nativeView.setIconView(icon);
        nativeView.setCallToActionView(callToAction);
        nativeView.setCloseView(close);
        nativeView.setAdSourceView(source);
        description.setText(nativeAd.getDescription());
        logo.setImageBitmap(nativeAd.getAdLogo());

        if (TextUtils.isEmpty(nativeAd.getAdSource())) {
            source.setVisibility(View.GONE);
        } else {
            source.setVisibility(View.VISIBLE);
            source.setText(nativeAd.getAdSource());
        }
        if (TextUtils.isEmpty(nativeAd.getCallToAction())) {
            callToAction.setVisibility(View.GONE);
        } else {
            callToAction.setVisibility(View.VISIBLE);
            callToAction.setText(nativeAd.getCallToAction());
        }

        nativeAd.setNativeEventListener(new AlxNativeEventListener() {
            @Override
            public void onAdClicked() {

            }

            @Override
            public void onAdImpression() {

            }

            @Override
            public void onAdClosed() {

            }
        });

        //视频广告监听器
        if (nativeAd.getMediaContent() != null && nativeAd.getMediaContent().hasVideo()) {
            nativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {
                @Override
                public void onVideoStart() {
                }

                @Override
                public void onVideoEnd() {
                }

                @Override
                public void onVideoPlay() {
                }

                @Override
                public void onVideoPause() {
                }

                @Override
                public void onVideoMute(boolean isMute) {
                }
            });
        }

        //注册原生广告对象
        nativeView.setNativeAd(nativeAd);
    }
```

依次设置其他要展示的广告素材。

**AlxMediaView**用于展示多媒体素材。如果获取的广告含有视频素材，则视频会在**AlxMediaView**内播放。否则**AlxMediaView**会显示一个图片素材。

c. 向 AlxNativeAdView 注册原生广告对象。

示例代码如下所示：

```Java
nativeView.setNativeAd(nativeAd);
```

d. 展示 AlxNativeAdView

将 AlxNativeAdView 添加到界面即可展示原生广告。示例代码如下所示：

```Java
private void loadAd() {
   AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
   loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
         @Override
         public void onAdLoaded(List<AlxNativeAd> ads) {
             ……
             AlxNativeAd nativeAd = ads.get(0);

             //获取AlxNativeAdView视图
             AlxNativeAdView nativeAdView =(AlxNativeAdView) getLayoutInflater().inflate(R.layout.native_ad_template, null);

             //注册和填充原生广告素材
             initNativeAdView(nativeAd,nativeAdView);

             //将AlxNativeAdView添加到界面
             FrameLayout adFrameLayout = (FrameLayout) findViewById(R.id.ad_container);
             adFrameLayout.removeAllViews();
             adFrameLayout.addView(nativeAdView);
             ……
           }
     });
}

private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
     ……
     //注册和填充标题素材视图
     nativeView.setTitleView(title);
     title.setText(nativeAd.getTitle());

      //注册和填充多媒体素材视图
      nativeView.setMediaView(mediaView);
      mediaView.setMediaContent(nativeAd.getMediaContent());

      //注册和填充其他素材视图
      ……

      //注册原生广告对象
      nativeView.setNativeAd(nativeAd);
}
```

4. **监听广告事件**

为原生广告添加事件监听器的示例代码如下所示：

```Java
nativeAd.setNativeEventListener(new AlxNativeEventListener() {
    @Override
    public void onAdClicked() {
        //广告点击时调用
    }

    @Override
    public void onAdImpression() {
        //广告曝光时调用
    }

    @Override
    public void onAdClosed() {
        //广告关闭时调用
    }
});
```

5. **销毁广告**

当原生广告不再展示时，应将其销毁。示例代码如下所示：

```Java
nativeAd.destroy();
```

**下面是对：`AlxNativeAd`类进行详细说明**

| 方法                   | 说明                                                       |
| ---------------------- | ---------------------------------------------------------- |
| getCreateType          | 广告素材类型【如：大图、小图、组图、视频、其他：未知类型】 |
| getAdSource            | 广告来源                                                   |
| getAdLogo              | 广告logo                                                   |
| getTitle               | 广告标题                                                   |
| getIcon                | 广告小图标                                                 |
| getImages              | 广告内容多图素材                                           |
| getMediaContent        | 广告多媒体素材                                             |
| destroy                | 广告销毁                                                   |
| setNativeEventListener | 广告事件监听器                                             |

##### 大图文模版

展示形式如下图所示：

![](https://static.rixengine.com/a/platform/help/c2s/6.4-5-1.png)

### 7. 支持主流应用商店和浏览器跳转，DeepLink 第三方应用拉起打开技术细节：

####  7.1 Google Play、华为商店、小米商店、三星商店等跳转，Google Play商店和Deeplink跳转必须支持，其他商店可以不支持，非必须。

 主流应用商店包名：

```Java
/**
 * 谷歌商店的包名
 */
public static final String GOOGLE_PLAY_APP_PACKAGE_NAME = "com.android.vending";

/**
 * 华为应用商店的包名
 */
public static final String HUAWEI_MARKET_APP_PACKE_NAME = "com.huawei.appmarket";

/**
 * 三星应用商店的包名
 */
public static final String SUMSUNG_MARKET_APP_PACK_NAME = "com.sec.android.app.samsungapps";

/**
 * 小米应用商店的包名
 */
public static final String XIAOMI_MARKET_APP_PACK_NAME = "com.xiaomi.market";
/**
 * oppo应用商店的包名
 */
public static final String OPPO_MARKET_APP_PACK_NAME = "com.oppo.market";
/**
 * vivo应用商店的包名
 */
public static final String VIVO_MARKET_APP_PACK_NAME = "com.bbk.appstore";
```

 参考代码：

```Java
/**
 * 打开跳转到应用商店
 *
 * @return true 打开成功，false打开失败
 */
public static boolean isStartAppStore(Context context, String url) {
    if (context == null || TextUtils.isEmpty(url)) {
        return false;
    }

    try {
        //根据url判断是哪个应用商店
        int appStoreType = -1;//商店类型
        String lowerUrl = url.toLowerCase();//将url转换成小写，然后做统一判断
        if (lowerUrl.contains("appmarket://details?")
                || lowerUrl.contains("market://com.huawei.appmarket.applink?")
                || lowerUrl.contains("hiapplink://com.huawei.appmarket?")) {//仅支持华为应用商店
            appStoreType = APP_STORE_HUAWEI;
        } else if (lowerUrl.startsWith("http://www.samsungapps.com/appquery/appDetail.as?")
                || lowerUrl.startsWith("http://apps.samsung.com/appquery/appDetail.as?")) {//三星应用商店
            appStoreType = APP_STORE_SAMSUNG;
        } else if (lowerUrl.startsWith("https://play.google.com/store/apps/details?")
                || lowerUrl.startsWith("http://play.google.com/store/apps/details?")) {//谷歌应用商店
            appStoreType = APP_STORE_GOOGLE;
        } else if (lowerUrl.contains("market://details?")) {//谷歌,华为,小米 通用的应用商店
            appStoreType = APP_STORE_COMMON;
        }
        if (appStoreType == -1) {
            return false;
        }

        //打开应用商店判断
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        if (appStoreType == APP_STORE_HUAWEI) {//华为
            if (AlxUtil.isAppInstalled(context, AlxConst.HUAWEI_MARKET_APP_PACKE_NAME)) {
                intent.setPackage(AlxConst.HUAWEI_MARKET_APP_PACKE_NAME);
            }
        } else if (appStoreType == APP_STORE_SAMSUNG) {//三星
            if (AlxUtil.isAppInstalled(context, AlxConst.SUMSUNG_MARKET_APP_PACK_NAME)) {
                intent.setPackage(AlxConst.SUMSUNG_MARKET_APP_PACK_NAME);
            }
        } else if (appStoreType == APP_STORE_GOOGLE) {//google
            if (AlxUtil.isAppInstalled(context, AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME)) {
                intent.setPackage(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME);
            }
        } else {
            if (AlxUtil.isAppInstalled(context, AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME)) {//优先使用google 商店
                intent.setPackage(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME);
            } else {
                String manufacturer = Build.MANUFACTURER;
                if ("xiaomi".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.XIAOMI_MARKET_APP_PACK_NAME)) {//小米应用商店
                        intent.setPackage(AlxConst.XIAOMI_MARKET_APP_PACK_NAME);
                    }
                } else if ("huawei".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.HUAWEI_MARKET_APP_PACKE_NAME)) {//华为应用商店
                        intent.setPackage(AlxConst.HUAWEI_MARKET_APP_PACKE_NAME);
                    }
                } else if ("oppo".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.OPPO_MARKET_APP_PACK_NAME)) {//oppo应用商店
                        intent.setPackage(AlxConst.OPPO_MARKET_APP_PACK_NAME);
                    }
                } else if ("vivo".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.VIVO_MARKET_APP_PACK_NAME)) {//vivo应用商店
                        intent.setPackage(AlxConst.VIVO_MARKET_APP_PACK_NAME);
                    }
                }
            }
        }
        intent.setData(Uri.parse(url));
        context.startActivity(intent);
        return true;
    } catch (Exception e) {
        AlxAgent.onError(e);
        AlxLog.e(AlxLogLevel.ERROR, TAG, e.getMessage());
    }
    return false;
}
```

####  7.2 手机浏览器跳转

 对于点击广告后的落地地址是非跳转应用商店（Market协议）的广告需要跳转系统浏览器或者内置浏览器，

 参考代码：

```Java
/**
 * 打开浏览器，内置或者系统浏览器
 *
 * @param context
 * @param url
 * @return
 */
public static boolean isStartBrowser(Context context, String url, AlxTracker tracker) {
    try {
        //内置WebView
        if (context != null && !TextUtils.isEmpty(url)) {
            if (AlxConfig.ALX_IS_USE_INNER_BROWSER) {
                AlxLog.d(AlxLogLevel.MARK, TAG, "isStartBrowser():webview");
                AlxWebActivity.startWeb(context, new AlxWebActivity.Builder().setLoad(url).setTracker(tracker));
                return true;
            }
        }
        AlxLog.d(AlxLogLevel.MARK, TAG, "isStartBrowser():browser");
        //跳转到外部浏览器
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setData(Uri.parse(url));
        context.startActivity(intent);
        return true;
    } catch (Exception e) {
        AlxLog.e(AlxLogLevel.ERROR, TAG, "isStartBrowser():error:" + e.getMessage());
        return false;
    }
}
```

####  7.3 电商APP，社交APP，短视频APP等DeepLink跳转拉起

针对跳转第三方APP，比如微信，Facebook，淘宝，京东，Lazada等APP，需要实现Deeplink功能。

参考代码如下：

```Java
/**
 * 打开deeplink
 *
 * @param deepLink
 * @return null代表成功，如果不是null代表打开错误并返回相应的错误信息
 */
public static String isStartDeepLink(Context context, String deepLink) {
    if (TextUtils.isEmpty(deepLink)) {
        return "deeplink is empty";
    }
    if (context == null) {
        return "context params is null";
    }
    try {
        PackageManager packageManager = context.getApplicationContext().getPackageManager();
        if (packageManager == null) {
            return "package is empty";
        }
        Intent intent = Intent.parseUri(deepLink, 0);
        if (intent.resolveActivity(packageManager) != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            return null;
        } else {
            // 加载 landing page url
            return "deeplink open failed";
        }
    } catch (Exception e) {
        e.printStackTrace();
        AlxLog.e(AlxLogLevel.ERROR, TAG, "startDeepLink-error:" + e.getMessage());
        return e.getMessage();
    }
}
```

### 8. 广告流程数据上报技术细节

#### 8.1 上报 url 中的宏占位符表

| 宏占位符表       |                           |
| ---------------- | ------------------------- |
| 占位符           | 说明                      |
| ${AUCTION_PRICE} | 竞价占位符                |
| [ERRORCODE]      | Vast 协议中的错误码占位符 |

示例如下所示：

```JSON
{
...
"imptrackers": [
   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArAXZOJ8s1weaUN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMFzSti9WZ1_j7Gygi4Tm-Lz5utvKXxWnMEnyfEtF7mGPxcUfJe68DIKRCeiSNRXAWGRjmBpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=**${AUCTION_PRICE}**",
],
"adm":"<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    ...
    <Error><![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=**[ERRORCODE]**]]>
     </Error>
         ...
      </VAST>"
...
}
```

#### 8.2 上报步骤如下

1. 根据相应的事件在返回的 json 数据中找到对应的 url 数组

​		如： 广告曝光事件，对应 json 数据的 imptrackers:[] 字段

2. 在 url 字符串中搜索是否存在有需要替换的宏，如果存在就将宏占位符替换成对应的值。见上面的`宏占位符表`

3. 将 url 上报数组全部替换后，使用 get 请求方式发起请求，即完成上报

### 9. GDPR，CCPA，CCOPA 隐私方案处理

1. **GDPR**

作为开发者，您需要在自己的 `app` 里面整合 `Consent Management Platform (CMP)`，并且根据` IAB Europe` 的`“Mobile In-App CMP API v1.0: Transparency & Consent Framework”` 和`“IAB Tech Lab – CMP API v2”`要求，获取用户的同意。

如果您使用的是自建的 `CMP`，您需要将收集到的用户同意信息储存在 `SharedPreferences` 里面（使用 `IABTCF_gdprApplies` 和`IABTCF_TCString` 这两个 `key`）。

` Transparency and Consent Framework (TCF) v2:``**Key** `

**类型描述**

![](https://static.rixengine.com/a/platform/help/c2s/9-1-1.png)

`RixEngine`是 `IAB Europe` 透明度和同意框架（TCF）的供应商之一。更多关于`RixEngine` 对 `GDPR` 的合规，您可以参考 `RixEngine` 的隐私政策。

`自研SDK`需要从 `SharedPreferences` 的 `IABTCF_gdprApplies` 和 `IABTCF_TCString` 这两个 `key` 里面读取 `GDPR` 相关的用户同意字段。您也可以使用下列的 `SDK` 函数手动传入 `GDPR`相关的用户同意字段。注意：请尽量在 `SDK` 初始化之后调用。

如果您的 `app` 已经接入了 `CMP`，并且将用户同意字段储存在 `SharedPreferences` 的

`IABTCF_gdprApplies` 和 `IABTCF_TCString` 这两个 `key` 里面，您就不需要再调用下列函数。

参考代码示例：

```Java
AlxAdSDK.setSubjectToGDPR(TRUE);
AlxAdSDK.setUserConsent(<#Base64ConsentString#>); // Base64 加密的 consent string
```

`"subject to GDPR"` 标志可以设置为 `YES / TRUE`（用户受到 `GDPR` 法规保护）或 `NO / FALSE` （用户不受到 `GDPR` 法规保护）。 只有在应用程序已经判断 `GDPR` 是否适用于用户时，才能调用此函数。 如果这个功能没有被调用，`RixEngine` 认为应用程序没有做出这样的判断，因此 `RixEngine` 会自行判断 `GDPR` 的适用性。

`setUserConsent` 函数提供`“0”`（用户不同意），`“1”`（用户同意）或更详细的同意信息

（`Base64` 加密的 `consent string`）。 这个更详细的同意信息是在 `IAB` 支持的透明和同意框架 中描述的同意信息，详情可参考`[https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework](https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework)`

2. **CCPA**

`California Consumer Privacy Act (CCPA`) 的发布是为了提升和加强加利福尼亚用户个人隐私信息使用的透明度和管控。作为开发者，您需要对加利福尼亚州的用户的个人隐私信息的使用获取同意。更多关于 `RixEngine` 对 `CCPA` 的合规，您可以参考 `RixEngine` 的隐私政策。

**注意:**如果您是从以前版本升级并且已经设置隐私标志位，请不要担心。新版本的 `SDK` 依

然会使用以前版本中设置的值，无需再次手工设置。但是我们推荐您使用新的方法。

您可以参考下面的代码示例传入 `US Privacy` 标志。注意：请尽量在 `自研SDK` 初始化之后调用。

`比如 自研SDK.subjectToUSPrivacy("1YYY")`; `subjectToUSPrivacy` 接受字符串值（比如`"1YYY"`）。 此标志采用的字符串要求符合 `IAB` 的美国隐私字符串格式。

3. **年龄相关的合规要求**

根据相关法律法规（`COPPA`，`GDPR` 等），如果您的 `app` 是面向儿童用户，您需要使用下列

函数标注出用户是儿童。您有责任决定和标注您的 `app` 的某些用户是儿童还是所有用户都

视作儿童。

参考示例代码：

```Java
AlxAdSDK.setBelowConsentAge(TRUE);
```

`belowConsentAge` 标志采用布尔值，可以为 `TRUE`（用户在相关法规下的定义是儿童）或

`FALSE`（用户在相关法规下的定义不是儿童）。

### 10. IAB OMSDK 集成，Open Measurement SDK 支持第三方可见度和验证衡量

IAB 官方标准技术文档如下：

https://iabtechlab.com/standards/open-measurement-sdk/

集成 OMSDK 后需要经过 IAB 官方技术认证，每年需要交付数千美金的会员费，达标后会受到业界和广告主的认证，广告预算的投放即广告填充率，广告 ECPM 不会受到限制。业界知名企业如 Google Admob，Facebook Audience Network，Max，IronSource，InMobi，AlogriX-RixEngine，Mintegral 等均通过 IAB Open Measurement 官方白名单认证。（非必须集成，但建议集成）

### 11. 广告唯一标识获取（重要）

 包含Google体系的GAID，硬核联盟体系（华为 小米 Oppo Vivo）OAID

####  11.1 GAID获取方式（必须）：

​     获取GMS中的广告唯一标识GAID示例代码：

```Java
public class GAIDTool {
    public static final class AdInfo {
        private final String advertisingId;
        private final boolean limitAdTrackingEnabled;

        AdInfo(String advertisingId, boolean limitAdTrackingEnabled) {
            this.advertisingId = advertisingId;
            this.limitAdTrackingEnabled = limitAdTrackingEnabled;
        }

        public String getId() {
            return this.advertisingId;
        }

        public boolean isLimitAdTrackingEnabled() {
            return this.limitAdTrackingEnabled;
        }
    }

    public static AdInfo getAdvertisingIdInfo(Context context) throws Exception {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw new IllegalStateException(
                    "Cannot be called from the main thread");
        }

        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME, 0);
        } catch (Exception e) {
            e.printStackTrace();
            // throw e;
        }

        AdvertisingConnection connection = new AdvertisingConnection();
        Intent intent = new Intent(
                "com.google.android.gms.ads.identifier.service.START");
        intent.setPackage("com.google.android.gms");
        try{
            if (context.bindService(intent, connection, Context.BIND_AUTO_CREATE)) {
                try {
                    AdvertisingInterface adInterface = new AdvertisingInterface(
                            connection.getBinder());
                    AdInfo adInfo = new AdInfo(adInterface.getId(),
                            adInterface.isLimitAdTrackingEnabled(true));
                    return adInfo;
                } catch (Exception exception) {
                    throw exception;
                } finally {
                    context.unbindService(connection);
                }
            }
        }catch (Exception e){
            throw e;
        }
        throw new IOException("Google Play connection failed");
    }

    private static final class AdvertisingConnection implements
            ServiceConnection {
        boolean retrieved = false;
        private final LinkedBlockingQueue<IBinder> queue = new LinkedBlockingQueue<IBinder>(
                1);

        public void onServiceConnected(ComponentName name, IBinder service) {
            try {
                this.queue.put(service);
            } catch (InterruptedException localInterruptedException) {
                localInterruptedException.printStackTrace();
            }
        }

        public void onServiceDisconnected(ComponentName name) {
        }

        public IBinder getBinder() throws InterruptedException {
            if (this.retrieved) {
                throw new IllegalStateException();
            }
            this.retrieved = true;
            return (IBinder) this.queue.take();
        }
    }

    private static final class AdvertisingInterface implements IInterface {
        private IBinder binder;

        public AdvertisingInterface(IBinder pBinder) {
            binder = pBinder;
        }

        public IBinder asBinder() {
            return binder;
        }

        public String getId() throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            String id;
            try {
                data.writeInterfaceToken("com.google.android.gms.ads.identifier.internal.IAdvertisingIdService");
                binder.transact(1, data, reply, 0);
                reply.readException();
                id = reply.readString();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return id;
        }

        public boolean isLimitAdTrackingEnabled(boolean paramBoolean)
                throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            boolean limitAdTracking;
            try {
                data.writeInterfaceToken("com.google.android.gms.ads.identifier.internal.IAdvertisingIdService");
                data.writeInt(paramBoolean ? 1 : 0);
                binder.transact(2, data, reply, 0);
                reply.readException();
                limitAdTracking = 0 != reply.readInt();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return limitAdTracking;
        }
    }
}
```

####  11.2 OAID获取方式（非必须）：

 由中国*移动安全联盟*(MSA)发起并制定标准的安卓端用户识别ID，主要针对国内，官方地址：

 http://www.msa-alliance.cn/

### 12. MRaid 移动富媒体创意广告支持（非必须）

IAB 官方标准文档：

https://iabtechlab.com/standards/mobile-rich-media-ad-interface-definitions-mraid/

Mraid 支持最低 2.0，支持到 3.0 效果更佳。

## 附录

### 1. 广告数据格式

| 值  | 描述       |
| --- | ---------- |
| 1   | `HTML `    |
| 2   | `JSON`     |
| 3   | `VAST XML` |

### 2. 广告类型

| 值  | 描述                                                               |
| --- | ------------------------------------------------------------------ |
| 1   | `Banner`                                                           |
| 2   | `MREC`广告                                                         |
| 3   | 插屏广告（包括：1: 全屏视频广告 2: `banner`(用 webView 加载)广告） |
| 4   | 激励视频                                                           |
| 5   | 原生广告                                                           |

### 3. 设备类型

| **值** | **描述** |
| ------ | -------- |
| 0      | 未知     |
| 1      | 手机     |
| 2      | 平板     |

### 4. 错误码以及错误信息

| **值** | **描述**               |
| ------ | ---------------------- |
| 1000   | 成功                   |
| 2001   | 无填充                 |
| 3001   | 广告类型与广告位不匹配 |
| 3002   | 广告位未生效           |
| 3003   | `App`未生效            |
| 3004   | `device`信息有误       |
| 3005   | `reg`信息有误          |

### 5. Native 素材类型

| **值** | **描述** |
| ------ | -------- |
| 0      | 未知     |
| 1      | 大图     |
| 2      | 小图     |
| 3      | 组图     |
| 4      | 视频     |

### 6. 第三方适配器源代码示例（适配主流聚合广告平台 Admob/MAX/IronSouce/Topon 第三方自定义适配器）：

#### 6.1 Admob适配器源代码：

#####   Banner:

```Java
package com.admob.custom.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationBannerAd;
import com.google.android.gms.ads.mediation.MediationBannerAdCallback;
import com.google.android.gms.ads.mediation.MediationBannerAdConfiguration;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Banner Adapter
 */
public class AlxBannerAdapter extends Adapter implements MediationBannerAd {

    private static final String TAG = "AlxBannerAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<MediationBannerAd, MediationBannerAdCallback> mMediationLoadCallback;
    private MediationBannerAdCallback mMediationEventCallback;

    AlxBannerView mBannerView;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback, List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadBannerAd(@NonNull MediationBannerAdConfiguration configuration, @NonNull MediationAdLoadCallback<MediationBannerAd, MediationBannerAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadBannerAd");
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    load(context);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    @NonNull
    @Override
    public View getView() {
        return mBannerView;
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private void load(Context context) {
        mBannerView = new AlxBannerView(context);
        // auto refresh ad  default = open = 1, 0 = close
        mBannerView.setBannerRefresh(0);
        //mBannerView.setBannerRefresh(15);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mMediationLoadCallback != null) {
                    mMediationEventCallback = mMediationLoadCallback.onSuccess(AlxBannerAdapter.this);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onAdClicked() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdImpression();
                    mMediationEventCallback.onAdOpened();
                }
            }

            @Override
            public void onAdClose() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdClosed();
                }
            }
        };
        mBannerView.loadAd(unitid, alxBannerADListener);
    }


    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
```

#####   Native:

```Java
package com.admob.custom.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.formats.NativeAd;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationNativeAdCallback;
import com.google.android.gms.ads.mediation.MediationNativeAdConfiguration;
import com.google.android.gms.ads.mediation.UnifiedNativeAdMapper;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AlxNativeAdapter extends Adapter {
    private static final String TAG = "AlxNativeAdapter";

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<UnifiedNativeAdMapper, MediationNativeAdCallback> mMediationLoadCallback;
    private MediationNativeAdCallback mMediationEventCallback;

    private AlxNativeAd nativeAd;
    private CustomNativeAdMapper nativeAdMapper;

    @Override
    public void initialize(@NonNull Context context, @NonNull InitializationCompleteCallback initializationCompleteCallback, @NonNull List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadNativeAd(@NonNull MediationNativeAdConfiguration configuration, @NonNull MediationAdLoadCallback<UnifiedNativeAdMapper, MediationNativeAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadNativeAd " + Thread.currentThread().getName());
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    loadAds(context, unitid);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    private void loadAds(final Context context, String adId) {
        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(context, adId).build();
        loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
            @Override
            public void onAdFailed(int errorCode, String errorMsg) {
                Log.i(TAG, "onAdLoadedFail:" + errorCode + ";" + errorMsg);
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onAdLoaded(List<AlxNativeAd> list) {
                Log.i(TAG, "onAdLoaded:");

                if (list == null || list.isEmpty()) {
                    loadError(100, "no data ads");
                    return;
                }

                try {
                    nativeAd = list.get(0);
                    if (nativeAd == null) {
                        loadError(100, "no data ads");
                        return;
                    }

                    nativeAdMapper = new CustomNativeAdMapper(context, nativeAd);
                    if (mMediationLoadCallback != null) {
                        Log.i(TAG, "onAdLoaded:listener-ok");
                        mMediationEventCallback = mMediationLoadCallback.onSuccess(nativeAdMapper);
                    }
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                    loadError(101, e.getMessage());
                }

            }
        });
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private class CustomNativeAdMapper extends UnifiedNativeAdMapper {

        private AlxNativeAd bean;
        private Context context;
        private AlxNativeAdView mRootView;

        public CustomNativeAdMapper(Context context, AlxNativeAd bean) {
            this.bean = bean;
            this.context = context;
            bindListener();
            init();
        }

        private void init() {
            if (bean == null) {
                return;
            }
            setHeadline(bean.getTitle());
            setBody(bean.getDescription());
            setPrice(bean.getPrice() + "");
            setAdvertiser(bean.getAdSource());
            setCallToAction(bean.getCallToAction());
            setIcon(new SimpleImage(bean.getIcon()));
            setImages(getImageList());
            setHasVideoContent(bean.getMediaContent().hasVideo());

            mRootView = new AlxNativeAdView(context);
            AlxMediaView mediaView = new AlxMediaView(context);
            mediaView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            mediaView.setMediaContent(bean.getMediaContent());
            mRootView.setMediaView(mediaView);
            setMediaView(mediaView);
        }

        @Override
        public void trackViews(@NonNull View view, @NonNull Map<String, View> map, @NonNull Map<String, View> map1) {
            Log.i(TAG, "trackViews");
            if (view instanceof ViewGroup) {
                Log.i(TAG, "trackViews: rootView is ViewGroup");
                ViewGroup rootView = (ViewGroup) view;
                try {
                    if (mRootView != null) {
                        rootView.removeView(mRootView);
                    }
                    if (mRootView == null) {
                        mRootView = new AlxNativeAdView(context);
                    }
                    mRootView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                    if (map != null && !map.isEmpty()) {
                        for (Map.Entry<String, View> entry : map.entrySet()) {
                            Log.i(TAG, "register:key=" + entry.getKey());
                            mRootView.addView(entry.getKey(), entry.getValue());
                        }
                    }
                    if (map1 != null && !map1.isEmpty()) {
                        for (Map.Entry<String, View> entry : map1.entrySet()) {
                            Log.i(TAG, "register2:key=" + entry.getKey());
                        }
                    }
                    mRootView.setNativeAd(bean);
                    rootView.addView(mRootView, 0);
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, e.getMessage());
                }
            } else {
                Log.i(TAG, "trackViews: rootView is other");
            }
        }

        @Override
        public void untrackView(@NonNull View view) {
            Log.i(TAG, "untrackView");
        }

        private List<NativeAd.Image> getImageList() {
            List<NativeAd.Image> imageList = new ArrayList<>();
            if (bean.getImages() != null && bean.getImages().size() > 0) {
                for (AlxImage item : bean.getImages()) {
                    if (item != null) {
                        imageList.add(new SimpleImage(item));
                    }
                }
            }
            return imageList;
        }

        public AlxNativeAdView getAlgorixAdView() {
            return mRootView;
        }

        private void bindListener() {
            if (bean == null) {
                return;
            }
            bean.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    Log.d(TAG, "onAdClick");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.reportAdClicked();
                    }
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "onAdShow");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.reportAdImpression();
                        mMediationEventCallback.onAdOpened();
                    }
                }

                @Override
                public void onAdClosed() {
                    Log.d(TAG, "onAdClose");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.onAdClosed();
                    }
                }
            });
        }


        private class SimpleImage extends NativeAd.Image {

            private AlxImage image;

            public SimpleImage(AlxImage image) {
                this.image = image;
            }

            @Override
            public double getScale() {
                return 0;
            }

            @Nullable
            @Override
            public Drawable getDrawable() {
                return null;
            }

            @Nullable
            @Override
            public Uri getUri() {
                if (image != null) {
                    return Uri.parse(image.getImageUrl());
                }
                return null;
            }
        }
    }

    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    @NonNull
    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @NonNull
    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
```

##### Interstitial:

```Java
package com.admob.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationInterstitialAd;
import com.google.android.gms.ads.mediation.MediationInterstitialAdCallback;
import com.google.android.gms.ads.mediation.MediationInterstitialAdConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Interstitial Adapter
 */
public class AlxInterstitialAdapter extends Adapter implements MediationInterstitialAd {

    private static final String TAG = "AlxInterstitialAdapter";

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<MediationInterstitialAd, MediationInterstitialAdCallback> mMediationLoadCallback;
    private MediationInterstitialAdCallback mMediationEventCallback;

    AlxInterstitialAD interstitialAd;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback, List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadInterstitialAd(@NonNull MediationInterstitialAdConfiguration configuration, @NonNull MediationAdLoadCallback<MediationInterstitialAd, MediationInterstitialAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadInterstitialAd");
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    preloadAd(context);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    @Override
    public void showAd(@NonNull Context context) {
        Log.i(TAG, "alx showAd");
        if (interstitialAd != null) {
            if (!interstitialAd.isReady()) {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdFailedToShow(new AdError(1, "isReady: Ad not loaded or Ad load failed"
                            , AlxAdSDK.getNetWorkName()));
                }
                return;
            }
            if (context != null && context instanceof Activity) {
                interstitialAd.show((Activity) context);
            } else {
                Log.i(TAG, "context is not an Activity");
                interstitialAd.show(null);
            }
        }
    }

    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private void preloadAd(final Context context) {
        interstitialAd = new AlxInterstitialAD();
        interstitialAd.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mMediationLoadCallback != null) {
                    mMediationEventCallback = mMediationLoadCallback.onSuccess(AlxInterstitialAdapter.this);
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdImpression();
                    mMediationEventCallback.onAdOpened();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdClosed();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
            }
        });
    }


    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
```

##### RewardVideo:

```Java
package com.admob.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationRewardedAd;
import com.google.android.gms.ads.mediation.MediationRewardedAdCallback;
import com.google.android.gms.ads.mediation.MediationRewardedAdConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;
import com.google.android.gms.ads.rewarded.RewardItem;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Reward Video Adapter
 */
public class AlxRewardVideoAdapter extends Adapter implements MediationRewardedAd {
    private final String TAG = "AlxRewardVideoAdapter";
    private static final String ALX_AD_UNIT_KEY = "parameter";

    private AlxRewardVideoAD alxRewardVideoAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;
    private Context mContext;
    private MediationAdLoadCallback<MediationRewardedAd, MediationRewardedAdCallback> mediationAdLoadCallBack;
    private MediationRewardedAdCallback mMediationRewardedAdCallback;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback
            , List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.e(TAG, "alx initialize...");
        for (MediationConfiguration configuration : list) {
            Bundle serverParameters = configuration.getServerParameters();
            String serviceString = serverParameters.getString(ALX_AD_UNIT_KEY);
            if (!TextUtils.isEmpty(serviceString)) {
                parseServer(serviceString);
            }
        }
        if (initSDk(context)) {
            initializationCompleteCallback.onInitializationSucceeded();
        } else {
            initializationCompleteCallback.onInitializationFailed("alx sdk init error");
        }
    }

    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        String[] splits = versionString.split("\\.");

        if (splits.length >= 3) {
            int major = Integer.parseInt(splits[0]);
            int minor = Integer.parseInt(splits[1]);
            int micro = Integer.parseInt(splits[2]) * 100 + Integer.parseInt(splits[3]);
            return new VersionInfo(major, minor, micro);
        }

        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        String[] splits = versionString.split("\\.");
        if (splits.length >= 3) {
            int major = Integer.parseInt(splits[0]);
            int minor = Integer.parseInt(splits[1]);
            int micro = Integer.parseInt(splits[2]);
            return new VersionInfo(major, minor, micro);
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public void showAd(Context context) {
        Log.e(TAG, "alx showAd...");
        if (!(context instanceof Activity)) {
            Log.e(TAG, "context is not Activity");
            mMediationRewardedAdCallback.onAdFailedToShow(new AdError(1,
                    "An activity context is required to show Sample rewarded ad."
                    , AlxAdSDK.getNetWorkName())
            );
            return;
        }
        mContext = context;
        if (!alxRewardVideoAD.isReady()) {
            mMediationRewardedAdCallback.onAdFailedToShow(new AdError(1, "No ads to show."
                    , AlxAdSDK.getNetWorkName()));
            return;
        }
        alxRewardVideoAD.showVideo((Activity) context);
    }

    @Override
    public void loadRewardedAd(MediationRewardedAdConfiguration configuration
            , MediationAdLoadCallback<MediationRewardedAd, MediationRewardedAdCallback> mediationAdLoadCallback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx loadRewardedAd");
        Context context = configuration.getContext();
        mediationAdLoadCallBack = mediationAdLoadCallback;
        Bundle serverParameters = configuration.getServerParameters();
        String serviceString = serverParameters.getString(ALX_AD_UNIT_KEY);
        if (!TextUtils.isEmpty(serviceString)) {
            parseServer(serviceString);
        }
        initSDk(context);
    }

    private boolean initSDk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx unitid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx sid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx appid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx token is empty"
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx host is empty", AlxAdSDK.getNetWorkName()));
            return false;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //sdk init success, begin load ad
                    alxRewardVideoAD = new AlxRewardVideoAD();
                    alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {
                        @Override
                        public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdLoaded");
                            if (mediationAdLoadCallBack != null)
                                mMediationRewardedAdCallback = (MediationRewardedAdCallback) mediationAdLoadCallBack
                                        .onSuccess(AlxRewardVideoAdapter.this);
                        }


                        @Override
                        public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                            Log.d(TAG, "onRewardedVideoAdFailed: " + errMsg);
                            if (mediationAdLoadCallBack != null) mediationAdLoadCallBack
                                    .onFailure(new AdError(errCode, errMsg, AlxAdSDK.getNetWorkName()));
                        }

                        @Override
                        public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                            if (mMediationRewardedAdCallback != null && mContext instanceof Activity) {
                                ((Activity) mContext).runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        // runOnUiThread
                                        mMediationRewardedAdCallback.reportAdImpression();
                                        mMediationRewardedAdCallback.onAdOpened();
                                        mMediationRewardedAdCallback.onVideoStart();
                                    }
                                });
                            }
                        }

                        @Override
                        public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdPlayEnd: ");
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.onVideoComplete();
                        }

                        @Override
                        public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                            Log.d(TAG, "onShowFail: " + errMsg);
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.onAdFailedToShow(
                                        new AdError(errCode, errMsg, AlxAdSDK.getNetWorkName()));
                        }

                        @Override
                        public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdClosed: ");
                            if (mMediationRewardedAdCallback != null) {
                                mMediationRewardedAdCallback.onAdClosed();
                            }
                        }

                        @Override
                        public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdPlayClicked: ");
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.reportAdClicked();
                        }

                        @Override
                        public void onReward(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onReward: ");
                            if (mMediationRewardedAdCallback != null) {
                                mMediationRewardedAdCallback.onUserEarnedReward(new RewardItem() {
                                    @Override
                                    public String getType() {
                                        return "";
                                    }

                                    @Override
                                    public int getAmount() {
                                        return 1;
                                    }
                                });
                            }
                        }
                    });
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

}
```

##### AlxMetaInf:

```Java
package com.admob.custom.adapter;

/**
 * 适配器版本信息
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";

}
```

#### 6.2  Max适配器源代码:

#####   Banner Native Interstitial RewardVideo 四合一

```Java
package com.applovin.mediation.adapters;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaContent;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.applovin.impl.sdk.utils.BundleUtils;
import com.applovin.mediation.MaxAdFormat;
import com.applovin.mediation.adapter.MaxAdViewAdapter;
import com.applovin.mediation.adapter.MaxAdapterError;
import com.applovin.mediation.adapter.MaxInterstitialAdapter;
import com.applovin.mediation.adapter.MaxNativeAdAdapter;
import com.applovin.mediation.adapter.MaxRewardedAdapter;
import com.applovin.mediation.adapter.listeners.MaxAdViewAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxInterstitialAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxNativeAdAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxRewardedAdapterListener;
import com.applovin.mediation.adapter.parameters.MaxAdapterInitializationParameters;
import com.applovin.mediation.adapter.parameters.MaxAdapterResponseParameters;
import com.applovin.mediation.nativeAds.MaxNativeAd;
import com.applovin.mediation.nativeAds.MaxNativeAdView;
import com.applovin.sdk.AppLovinPrivacySettings;
import com.applovin.sdk.AppLovinSdk;
import com.applovin.sdk.AppLovinSdkUtils;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Applovin ads AlgoriX Adapter
 */
public class AlgorixMediationAdapter extends MediationAdapterBase implements MaxAdViewAdapter, MaxInterstitialAdapter, MaxRewardedAdapter, MaxNativeAdAdapter {
    private static final String TAG = "AlgorixMediationAdapter";
    private static final String ADAPTER_VERSION = "3.8.0";

    private static final int DEFAULT_IMAGE_TASK_TIMEOUT_SECONDS = 10;

    private static final AtomicBoolean initialized = new AtomicBoolean();
    private static InitializationStatus status;

    private AlxBannerView bannerAD;
    private AlxInterstitialAD interstitialAD;
    private AlxRewardVideoAD rewardVideoAD;
    private AlxNativeAd nativeAD;
    private AlxNativeAdView nativeAdView;

    public AlgorixMediationAdapter(AppLovinSdk appLovinSdk) {
        super(appLovinSdk);
    }

    @Override
    public void initialize(MaxAdapterInitializationParameters parameters, Activity activity, final OnCompletionListener onCompletionListener) {
        Log.d(TAG, "initialize alx sdk……");
        Log.d(TAG, "alx-applovin-adapter-version:" + ADAPTER_VERSION);
        try {
            status = InitializationStatus.INITIALIZING;
            Context context = (activity != null) ? activity.getApplicationContext() : getApplicationContext();

            Bundle bundle = parameters.getCustomParameters();
            String host = bundle.getString("host");
            String appid = bundle.getString("appid");
            String sid = bundle.getString("sid");
            String token = bundle.getString("token");
            String debug = bundle.getString("isdebug");
            Boolean isDebug = null;
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }

            Log.d(TAG, "alx-applovin-init:host=" + host + " token=" + token + "  sid=" + sid + " appid=" + appid);

            if (TextUtils.isEmpty(host) || TextUtils.isEmpty(appid) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(token)) {
                Log.d(TAG, "initialize alx params: host or appid or sid or token is null");
                status = InitializationStatus.DOES_NOT_APPLY;
                onCompletionListener.onCompletion(status, null);
            } else {
                if(isDebug != null) {
                    AlxAdSDK.setDebug(isDebug.booleanValue());
                }
                AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                    @Override
                    public void onInit(boolean isOk, String msg) {
                        status = InitializationStatus.INITIALIZED_SUCCESS;
                        onCompletionListener.onCompletion(status, null);
                    }
                });
                // // set GDPR
                // // Subject to GDPR Flag: Please pass a Boolean value to indicate if the user is subject to GDPR regulations or not.
                // // Your app should make its own determination as to whether GDPR is applicable to the user or not.
                // AlxAdSDK.setSubjectToGDPR(true);

                if (parameters != null) {
                    // Set GDPR Consent value
                    String strGDPRConsent = "0";
                    if (TextUtils.isEmpty(parameters.getConsentString())) {
                        if (AppLovinPrivacySettings.hasUserConsent(context)) {
                            try {
                                SharedPreferences mPreferences = PreferenceManager.getDefaultSharedPreferences(context);
                                strGDPRConsent = mPreferences.getString("IABTCF_TCString", "");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (TextUtils.isEmpty(strGDPRConsent)) {
                                strGDPRConsent = "1";
                            }
                        }
                        AlxAdSDK.setUserConsent(strGDPRConsent);
                    } else {
                        AlxAdSDK.setUserConsent(parameters.getConsentString());
                    }
                    Log.i(TAG, "Max parameter hasUserConsent:" + parameters.hasUserConsent()
                            + " getConsentString:" + parameters.getConsentString()
                            + " isAgeRestrictedUser:" + parameters.isAgeRestrictedUser()
                            + " hasUserConsent-2:" + AppLovinPrivacySettings.hasUserConsent(context));
                }

                // // set COPPA true or false
                // AlxAdSDK.setBelowConsentAge(true);
                // // set CCPA
                // AlxAdSDK.subjectToUSPrivacy("1YYY");

            }
        } catch (Exception e) {
            Log.d(TAG, "initialize alx error:" + e.getMessage());
            status = InitializationStatus.INITIALIZED_FAILURE;
            onCompletionListener.onCompletion(status, null);
        }
    }

    @Override
    public String getSdkVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getAdapterVersion() {
        return ADAPTER_VERSION;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        if (bannerAD != null) {
            bannerAD.destroy();
            bannerAD = null;
        }

        if (interstitialAD != null) {
            interstitialAD.destroy();
            interstitialAD = null;
        }

        if (rewardVideoAD != null) {
            rewardVideoAD.destroy();
            rewardVideoAD = null;
        }

        if (nativeAD != null) {
            nativeAD.destroy();
            nativeAD = null;
        }

        if (nativeAdView != null) {
            nativeAdView.destroy();
            nativeAdView = null;
        }
    }

    //banner load
    @Override
    public void loadAdViewAd(MaxAdapterResponseParameters parameters, MaxAdFormat maxAdFormat, Activity activity, final MaxAdViewAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadAdViewAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onAdViewAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        bannerAD = new AlxBannerView(activity);
        bannerAD.setBannerRefresh(0);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (listener != null) {
                    listener.onAdViewAdLoaded(bannerAD);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.e(TAG, "onAdError: errCode=" + errorCode + ";errMsg=" + errorMsg);
                if (listener != null) {
                    listener.onAdViewAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onAdClicked() {
                if (listener != null) {
                    listener.onAdViewAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (listener != null) {
                    listener.onAdViewAdDisplayed();
                }
            }

            @Override
            public void onAdClose() {
                if (listener != null) {
                    listener.onAdViewAdHidden();
                }
            }
        };
        // 320 * 50 banner
        bannerAD.loadAd(adId, alxBannerADListener);
        // MREC
        //bannerAD.loadAd(adId, AlxBannerView.AlxAdParam.FORMAT_MREC, alxBannerADListener);
    }

    //interstitial ad load
    @Override
    public void loadInterstitialAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxInterstitialAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadInterstitialAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onInterstitialAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        interstitialAD = new AlxInterstitialAD();
        interstitialAD.load(activity, adId, new AlxInterstitialADListener() {
            @Override
            public void onInterstitialAdLoaded() {
                if (listener != null) {
                    listener.onInterstitialAdLoaded();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                Log.e(TAG, "onInterstitialAdLoadFail: errCode=" + errorCode + ";errMsg=" + errorMsg);
                if (listener != null) {
                    listener.onInterstitialAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (listener != null) {
                    listener.onInterstitialAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (listener != null) {
                    listener.onInterstitialAdDisplayed();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (listener != null) {
                    listener.onInterstitialAdHidden();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {

            }
        });
    }

    //interstitial ad show
    @Override
    public void showInterstitialAd(MaxAdapterResponseParameters parameters, Activity activity, MaxInterstitialAdapterListener listener) {
        Log.d(TAG, "showInterstitialAd");
        if (interstitialAD != null && interstitialAD.isReady()) {
            interstitialAD.show(activity);
        } else {
            Log.d(TAG, "showInterstitialAd: ad no ready");
            listener.onInterstitialAdDisplayFailed(MaxAdapterError.AD_NOT_READY);
        }
    }

    //reward ad load
    @Override
    public void loadRewardedAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxRewardedAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadRewardedAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onRewardedAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        rewardVideoAD = new AlxRewardVideoAD();
        rewardVideoAD.load(activity, adId, new AlxRewardVideoADListener() {
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdLoaded");
                if (listener != null) {
                    listener.onRewardedAdLoaded();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.e(TAG, "onRewardedVideoAdFailed: errCode=" + errCode + ";errMsg=" + errMsg);
                if (listener != null) {
                    listener.onRewardedAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayStart");
                if (listener != null) {
                    listener.onRewardedAdDisplayed();
                    listener.onRewardedAdVideoStarted();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayEnd");
                if (listener != null) {
                    listener.onRewardedAdVideoCompleted();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                Log.d(TAG, "onRewardedVideoAdPlayFailed");
                if (listener != null) {
                    listener.onRewardedAdDisplayFailed(new MaxAdapterError(errCode, errMsg));
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdClosed");
                if (listener != null) {
                    listener.onRewardedAdHidden();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayClicked");
                if (listener != null) {
                    listener.onRewardedAdClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                Log.d(TAG, "onReward");
                if (listener != null) {
                    listener.onUserRewarded(getReward());
                }
            }
        });
    }

    //reward ad
    @Override
    public void showRewardedAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxRewardedAdapterListener listener) {
        Log.d(TAG, "showRewardedAd");
        if (rewardVideoAD != null && rewardVideoAD.isReady()) {
            rewardVideoAD.showVideo(activity);
        } else {
            Log.d(TAG, "showRewardedAd: ad no ready");
            listener.onRewardedAdDisplayFailed(MaxAdapterError.AD_NOT_READY);
        }
    }

    //native ad
    @Override
    public void loadNativeAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxNativeAdAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadNativeAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onNativeAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        Context applicationContext = (activity != null) ? activity.getApplicationContext() : getApplicationContext();
        NativeAdListener nativeAdListener = new NativeAdListener(parameters, applicationContext, listener);
        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(activity, adId).build();
        loader.loadAd(new AlxAdParam.Builder().build(), nativeAdListener);
    }

    private class NativeAdListener implements AlxNativeAdLoadedListener {
        private MaxAdapterResponseParameters parameters;
        private MaxNativeAdAdapterListener listener;
        final Bundle serverParameters;
        private Context context;

        public NativeAdListener(MaxAdapterResponseParameters parameters, Context context, MaxNativeAdAdapterListener listener) {
            this.parameters = parameters;
            this.context = context;
            this.listener = listener;
            serverParameters = parameters.getServerParameters();
        }

        @Override
        public void onAdFailed(int errorCode, String errorMsg) {
            Log.e(TAG, "native-onAdLoadedFail: errCode=" + errorCode + ";errMsg=" + errorMsg);
            if (listener != null) {
                listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
            }
        }

        public void reportEvent(int event, String desc) {
//            if (alxNativeAD != null) {
//                alxNativeAD.reportEvent(event, desc);
//            }
        }

        @Override
        public void onAdLoaded(List<AlxNativeAd> ads) {
            if (ads == null || ads.isEmpty()) {
                if (listener != null) {
                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                }
                return;
            }
            nativeAD = ads.get(0);
            if (nativeAD == null) {
                if (listener != null) {
                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                }
                return;
            }

            String templateName = BundleUtils.getString("template", "", serverParameters);
            final boolean isTemplateAd = AppLovinSdkUtils.isValidString(templateName);
            if (isTemplateAd && TextUtils.isEmpty(nativeAD.getTitle())) {
                e("Native ad (" + nativeAD + ") does not have required assets.");
                listener.onNativeAdLoadFailed(new MaxAdapterError(-5400, "Missing Native Ad Assets"));
                return;
            }

            nativeAD.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    if (listener != null) {
                        listener.onNativeAdClicked();
                    }
                }

                @Override
                public void onAdImpression() {
                    if (listener != null) {
                        listener.onNativeAdDisplayed(null);
                    }
                }

                @Override
                public void onAdClosed() {
                }
            });

            getCachingExecutorService().execute(new Runnable() {
                @Override
                public void run() {
                    Future<Drawable> iconDrawableFuture = null;
                    try {
                        if (nativeAD.getIcon() != null && !TextUtils.isEmpty(nativeAD.getIcon().getImageUrl()) && context != null) {
                            reportEvent(202, "[max] icon load");
                            iconDrawableFuture = createDrawableFuture(nativeAD.getIcon().getImageUrl(), context.getResources());
                            reportEvent(203, "[max] icon success");
                        }
                    } catch (Throwable th) {
                        reportEvent(204, "[max] icon error:" + th.getMessage());
                        e("Image fetching tasks failed", th);
                    }

                    Future<Drawable> imageDrawableFuture = null;
                    try {
                        if (nativeAD.getImages() != null && nativeAD.getImages().size() > 0) {
                            AlxImage image = nativeAD.getImages().get(0);
                            if (image != null && !TextUtils.isEmpty(image.getImageUrl()) && context != null) {
                                reportEvent(202, "[max] image load");
                                imageDrawableFuture = createDrawableFuture(image.getImageUrl(), context.getResources());
                                reportEvent(203, "[max] image success");
                            }
                        }
                    } catch (Throwable th) {
                        reportEvent(204, "[max] image error:" + th.getMessage());
                        e("Image fetching tasks failed", th);
                    }

                    Drawable iconDrawable = null;
                    Drawable mediaViewImageDrawable = null;
                    try {
                        // Execute and timeout tasks if incomplete within the given time
                        int imageTaskTimeoutSeconds = BundleUtils.getInt("image_task_timeout_seconds", DEFAULT_IMAGE_TASK_TIMEOUT_SECONDS, parameters.getServerParameters());
                        if (iconDrawableFuture != null) {
                            iconDrawable = iconDrawableFuture.get(imageTaskTimeoutSeconds, TimeUnit.SECONDS);
                        }
                        if (imageDrawableFuture != null) {
                            mediaViewImageDrawable = imageDrawableFuture.get(imageTaskTimeoutSeconds, TimeUnit.SECONDS);
                        }
                    } catch (Throwable th) {
                        e("Image fetching tasks failed", th);
                    }

                    final MaxNativeAd.MaxNativeAdImage icon = iconDrawable != null ? new MaxNativeAd.MaxNativeAdImage(iconDrawable) : null;
                    final Drawable mediaDrawable = mediaViewImageDrawable;

                    AppLovinSdkUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (nativeAD == null) {
                                if (listener != null) {
                                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                                }
                                return;
                            }

                            View mediaView = null;
                            AlxMediaContent mediaContent = nativeAD.getMediaContent();
                            if (mediaContent != null) {
                                AlxMediaView alxMediaView = new AlxMediaView(context);
                                alxMediaView.setMediaContent(mediaContent);
                                mediaView = alxMediaView;
                            } else if (mediaDrawable != null && context != null) {
                                ImageView imageView = new ImageView(context);
                                imageView.setImageDrawable(mediaDrawable);
                                mediaView = imageView;
                            }

                            ImageView logoView = null;
                            if (nativeAD.getAdLogo() != null && context != null) {
                                logoView = new ImageView(context);
                                logoView.setImageBitmap(nativeAD.getAdLogo());
                            }

                            MaxNativeAd.Builder maxBuilder = new MaxNativeAd.Builder()
                                    .setAdFormat(MaxAdFormat.NATIVE)
                                    .setTitle(nativeAD.getTitle())
                                    .setBody(nativeAD.getDescription())
                                    .setCallToAction(nativeAD.getCallToAction())
                                    .setAdvertiser(nativeAD.getAdSource())
                                    .setIcon(icon)
                                    .setMediaView(mediaView)
                                    .setOptionsView(logoView);

                            if (AppLovinSdk.VERSION_CODE >= 11_04_03_99) {
                                maxBuilder.setMainImage(new MaxNativeAd.MaxNativeAdImage(mediaDrawable));
                            }

                            MaxNativeAd maxNativeAd = new MaxAlgorixNativeAd(maxBuilder);

                            reportEvent(205, "[max] max show");
                            Log.d(TAG, "Native ad fully loaded:");
                            if (listener != null) {
                                listener.onNativeAdLoaded(maxNativeAd, null);
                            }
                        }
                    });
                }
            });
        }
    }

    private class MaxAlgorixNativeAd extends MaxNativeAd {

        public MaxAlgorixNativeAd(Builder builder) {
            super(builder);
        }

        @Override
        public void prepareViewForInteraction(final MaxNativeAdView maxNativeAdView) {
            final AlxNativeAd nativeAD = AlgorixMediationAdapter.this.nativeAD;
            if (nativeAD == null) {
                e("Failed to register native ad view. Native ad is null");
                return;
            }

            nativeAdView = new AlxNativeAdView(maxNativeAdView.getContext());
            View mainView = maxNativeAdView.getMainView();
            maxNativeAdView.removeView(mainView);
            nativeAdView.addView(mainView);
            maxNativeAdView.addView(nativeAdView);

            nativeAdView.setIconView(maxNativeAdView.getIconImageView());
            nativeAdView.setTitleView(maxNativeAdView.getTitleTextView());
            nativeAdView.setAdSourceView(maxNativeAdView.getAdvertiserTextView());
            nativeAdView.setDescriptionView(maxNativeAdView.getBodyTextView());
            nativeAdView.setCallToActionView(maxNativeAdView.getCallToActionButton());

            View mediaView = getMediaView();
            if (mediaView instanceof AlxMediaView) {
                nativeAdView.setMediaView((AlxMediaView) mediaView);
            } else if (mediaView instanceof ImageView) {
                nativeAdView.setImageView(mediaView);
            }
            nativeAdView.setNativeAd(nativeAD);
        }
    }

}
```

####  6.3  IronSource适配器源代码:

##### Banner:

```Java
package com.ironsource.adapters.custom.algorix;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.ironsource.mediationsdk.ISBannerSize;
import com.ironsource.mediationsdk.adunit.adapter.BaseBanner;
import com.ironsource.mediationsdk.adunit.adapter.listener.BannerAdListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

public class AlgoriXCustomBanner extends BaseBanner<AlgoriXCustomAdapter> {
    public static final String TAG = "AlgoriXCustomBanner";

    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    private AlxBannerView mBannerView;
    private BannerAdListener mListener;

    public AlgoriXCustomBanner(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @Override
    public void loadAd(final AdData adData, final Activity activity, ISBannerSize isBannerSize, BannerAdListener listener) {
        Log.d(TAG, "loadAd");
        mListener = listener;
        algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
            @Override
            public void onInitSuccess() {
                String unitid = (String) adData.getConfiguration().get("unitid");
                Log.d(TAG, "onInitSuccess: unitid :" + unitid);
                if (TextUtils.isEmpty(unitid)) {
                    if (mListener != null) {
                        mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, 1, "unitid is empty");
                    }
                    return;
                }
                requestBanner(unitid, activity);
            }

            @Override
            public void onInitFailed(int i, String s) {
                Log.d(TAG, "onInitFailed: errorCode=" + i + ";errorMsg=" + s);
                if (mListener != null) {
                    mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, i, s);
                }
            }
        });
    }

    private void requestBanner(String unitid, Activity activity) {
        mBannerView = new AlxBannerView(activity);
        mBannerView.setBannerRefresh(0);
        AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mListener != null) {
                    FrameLayout.LayoutParams params;
                    if (mBannerView.getLayoutParams() != null && mBannerView.getLayoutParams() instanceof FrameLayout.LayoutParams) {
                        params = (FrameLayout.LayoutParams) mBannerView.getLayoutParams();
                    } else {
                        params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//                        params.gravity = Gravity.CENTER;
                    }
                    mListener.onAdLoadSuccess(mBannerView, params);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                if (mListener != null) {
                    mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errorCode, errorMsg);
                }
            }

            @Override
            public void onAdClicked() {
                if (mListener != null) {
                    mListener.onAdClicked();
                    mListener.onAdLeftApplication();
                }
            }

            @Override
            public void onAdShow() {
                if (mListener != null) {
                    mListener.onAdOpened();
                }
            }

            @Override
            public void onAdClose() {
            }
        };
        mBannerView.loadAd(unitid, alxBannerADListener);
    }

    @Override
    public void destroyAd(AdData adData) {
        if (mBannerView != null) {
            mBannerView.destroy();
        }
    }
}
```

##### Interstitial:

```Java
package com.ironsource.adapters.custom.algorix;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.ironsource.mediationsdk.adunit.adapter.BaseInterstitial;
import com.ironsource.mediationsdk.adunit.adapter.listener.InterstitialAdListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

/**
 * IronSource 激励广告适配器
 */
public class AlgoriXCustomInterstitial extends BaseInterstitial<AlgoriXCustomAdapter> {

    private static final String TAG = "AlgoriXCustomInterstitial";
    private AlxInterstitialAD alxInterstitialAD;
    private String unitid = "";
    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    InterstitialAdListener mInterstitialAdListener;
    private Context mContext;

    public AlgoriXCustomInterstitial(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void loadAd(final AdData adData, Activity activity, InterstitialAdListener interstitialAdListener) {
        Log.d(TAG, "loadAd:");
        mContext = activity;
        try {
            mInterstitialAdListener = interstitialAdListener;
            algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
                @Override
                public void onInitSuccess() {
                    unitid = (String) adData.getConfiguration().get("unitid");
                    Log.d(TAG, "onInitSuccess: unitid :" + unitid);

                    startAdLoad(mContext);
                }

                @SuppressLint("LongLogTag")
                @Override
                public void onInitFailed(int i, String s) {
                    Log.d(TAG, "Init Failed errCode:" + i + " errMsg: " + s);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @SuppressLint("LongLogTag")
    private void startAdLoad(Context context) {
        Log.d(TAG, "startAdLoad:");
        alxInterstitialAD = new AlxInterstitialAD();
        alxInterstitialAD.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdLoadSuccess();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errorCode, errorMsg);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdShowSuccess();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdClosed();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {
                if (mInterstitialAdListener != null) {
                }
            }

            @Override
            public void onInterstitialAdVideoEnd() {
                if (mInterstitialAdListener != null) {

                }
            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
                if (mInterstitialAdListener != null) {
                }
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public void showAd(AdData adData, InterstitialAdListener interstitialAdListener) {
        if (mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            if (alxInterstitialAD != null) {
                alxInterstitialAD.show(activity);
            }
        } else {
            Log.e(TAG, "context is not an Activity");
        }

    }

    @Override
    public boolean isAdAvailable(AdData adData) {
        if (alxInterstitialAD != null) {
            return alxInterstitialAD.isReady();
        }
        return false;

    }

}
```

##### RewardVideo:

```Java
package com.ironsource.adapters.custom.algorix;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.RewardedVideoAdListener;
import com.ironsource.mediationsdk.adunit.adapter.BaseRewardedVideo;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

/**
 * IronSource 激励广告适配器
 */
public class AlgoriXCustomRewardedVideo extends BaseRewardedVideo<AlgoriXCustomAdapter> {

    private static final String TAG = "AlgoriXCustomRewardedVideo";
    private AlxRewardVideoAD alxRewardVideoAD;

    private String unitid = "";
    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    RewardedVideoAdListener mRewardedVideoAdListener;

    private Context mContext;

    public AlgoriXCustomRewardedVideo(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void showAd(AdData adData, RewardedVideoAdListener listener) {
        if(mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            if(alxRewardVideoAD != null){
                alxRewardVideoAD.showVideo(activity);
            }
        }else {
            Log.e(TAG, "context is not an Activity");
        }

    }

    @Override
    public boolean isAdAvailable(AdData adData) {
        if (alxRewardVideoAD != null) {
            return alxRewardVideoAD.isReady();
        }
        return false;

    }

    @SuppressLint("LongLogTag")
    @Override
    public void loadAd(final AdData adData, Activity activity, RewardedVideoAdListener rewardedVideoAdListener) {
        Log.d(TAG, "loadAd:");
        mContext = activity;
        mRewardedVideoAdListener = rewardedVideoAdListener;
        algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
            @Override
            public void onInitSuccess() {
                unitid = (String) adData.getConfiguration().get("unitid");
                Log.d(TAG, "onInitSuccess: unitid :" + unitid);

                startAdLoad(mContext);
            }

            @Override
            public void onInitFailed(int i, String s) {
                Log.d(TAG, "Init Failed errCode:" + i + " errMsg: " + s);
            }
        });
    }
    @SuppressLint("LongLogTag")
    private void startAdLoad(Context context){
        Log.d(TAG, "startAdLoad:");
        alxRewardVideoAD = new AlxRewardVideoAD();
        alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {

                Log.d(TAG, "onRewardedVideoAdLoaded:");
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdLoadSuccess();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.d(TAG, "onRewardedVideoAdFailed:");
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errCode, errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdStarted();
                    mRewardedVideoAdListener.onAdOpened();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdEnded();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdShowFailed(errCode,errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdClosed();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdRewarded();
                }

            }
        });
    }
}
```

##### AlgoriXCustomAdapter：

```Java
package com.ironsource.adapters.custom.algorix;


import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxSdkInitCallback;
import com.ironsource.mediationsdk.adunit.adapter.BaseAdapter;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrors;

import java.util.Map;

public class AlgoriXCustomAdapter extends BaseAdapter {

    private static final String TAG = "AlgoriXCustomAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = AlxMetaInf.ADAPTER_SDK_HOST_URL;
    private Boolean isDebug = null;
    private NetworkInitializationListener mNetworkInitializationListener;

    @Override
    public void init(AdData adData, Context context, NetworkInitializationListener networkInitializationListener) {
        Log.d(TAG, "alx-ironsource-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "AlgoriX SDK Init");

        try {
            mNetworkInitializationListener = networkInitializationListener;
            if (parseServer(adData)) {
                Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

                if (isDebug != null) {
                    AlxAdSDK.setDebug(isDebug.booleanValue());
                }
                AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                    @Override
                    public void onInit(boolean isOk, String msg) {
                        if (isOk) {
                            mNetworkInitializationListener.onInitSuccess();
                        } else {
                            mNetworkInitializationListener.onInitFailed(AdapterErrors.ADAPTER_ERROR_MISSING_PARAMS, "AlxSdk Init Failed");
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private boolean parseServer(AdData adData) {
        try {
            Map<String, Object> serverExtras = adData.getConfiguration();
//            if (serverExtras.containsKey("host")) {
//                host = (String) serverExtras.get("host");
//            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mNetworkInitializationListener != null) {
                mNetworkInitializationListener.onInitFailed(AdapterErrors.ADAPTER_ERROR_MISSING_PARAMS, "alx host | unitid | token | sid | appid is empty");
            }
            return false;
        }
        return true;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getAdapterVersion() {
        return AlxMetaInf.ADAPTER_VERSION;
    }
}
```

##### AlxMetaInf：

```Java
package com.ironsource.adapters.custom.algorix;

/**
 * IronSource 适配器版本信息
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    //String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "https://alpha.svr.rixengine.com/rtb";

}
```

#### 6.4  Topon 适配器代码

##### Banner：

```Java
package com.anythink.custom.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.banner.unitgroup.api.CustomBannerAdapter;

import java.util.Map;

/**
 * TopOn Banner广告适配器
 */
public class AlxBannerAdapter extends CustomBannerAdapter {
    private static final String TAG = "AlxBannerAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;
    AlxBannerView mBannerView;


    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> localExtras) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        } else {
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx apppid | token | sid | appid is empty.");
            }
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }

            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    Log.i(TAG, "sdk onInit:" + isOk);
                    loadAd(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadAd(Context context) {
        mBannerView = new AlxBannerView(context);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onAdClicked() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdShow();
                }
            }

            @Override
            public void onAdClose() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdClose();
                }
            }
        };
        // auto refresh ad  default = open = 1, 0 = close
        mBannerView.setBannerRefresh(0);
        mBannerView.loadAd(unitid, alxBannerADListener);
    }

    @Override
    public View getBannerView() {
        return mBannerView;
    }

    @Override
    public void destory() {
        if (mBannerView != null) {
            mBannerView.destroy();
            mBannerView = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

}
```

##### Native：

```Java
package com.anythink.custom.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaContent;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.anythink.nativead.api.ATNativePrepareInfo;
import com.anythink.nativead.unitgroup.api.CustomNativeAd;
import com.anythink.nativead.unitgroup.api.CustomNativeAdapter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TopOn 信息流广告适配器
 */
public class AlxNativeAdapter extends CustomNativeAdapter {
    private final String TAG = AlxNativeAdapter.class.getSimpleName();

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startAdLoad(final Context context) {
        AlxNativeAdLoadedListener loadListener = new AlxNativeAdLoadedListener() {
            @Override
            public void onAdFailed(int errorCode, String errorMsg) {
                Log.i(TAG, "onAdLoadedFail:" + errorCode + ";" + errorMsg);
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onAdLoaded(List<AlxNativeAd> ads) {
                if (ads == null || ads.isEmpty()) {
                    if (mLoadListener != null) {
                        mLoadListener.onAdLoadError("100", "no fill");
                    }
                    return;
                }

                AlgorixNativeAd[] result = new AlgorixNativeAd[ads.size()];
                boolean isOk = false;
                try {
                    for (int i = 0; i < ads.size(); i++) {
                        AlxNativeAd item = ads.get(i);
                        AlgorixNativeAd bean = new AlgorixNativeAd(context, item);
                        bean.setAdData();
                        result[i] = bean;
                    }
                    isOk = true;
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                    isOk = false;
                    if (mLoadListener != null) {
                        mLoadListener.onAdLoadError("101", e.getMessage());
                    }
                }
                if (isOk) {
                    if (mLoadListener != null) {
                        mLoadListener.onAdCacheLoaded(result);
                    }
                }
            }
        };

        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(context, unitid).build();
        loader.loadAd(new AlxAdParam.Builder().build(), loadListener);
    }

    private class AlgorixNativeAd extends CustomNativeAd {

        private Context mContext;

        private AlxNativeAd mNativeAd;
        private AlxNativeAdView mAdContainer;
        private AlxMediaView mMediaView;

        public AlgorixNativeAd(Context context, AlxNativeAd nativeAd) {
            mContext = context.getApplicationContext();
            mNativeAd = nativeAd;
        }

        public void setAdData() {
            if (mNativeAd == null) {
                return;
            }
            bindListener();

            setTitle(mNativeAd.getTitle());
            setDescriptionText(mNativeAd.getDescription());

            String iconUrl = "";
            String imageUrl = "";
            if (mNativeAd.getIcon() != null) {
                iconUrl = mNativeAd.getIcon().getImageUrl();
            }
            List<String> list = new ArrayList<>();
            List<AlxImage> imageList = mNativeAd.getImages();
            if (imageList != null && imageList.size() > 0) {
                AlxImage image0 = imageList.get(0);
                if (image0 != null) {
                    imageUrl = image0.getImageUrl();
                }
                for (AlxImage item : imageList) {
                    if (item != null && item.getImageUrl() != null) {
                        list.add(item.getImageUrl());
                    }
                }
            }
            setIconImageUrl(iconUrl);
            setMainImageUrl(imageUrl);
            setImageUrlList(list);
            setAdFrom(mNativeAd.getAdSource());
            setCallToActionText(mNativeAd.getCallToAction());
        }

        @Override
        public Bitmap getAdLogo() {
            if (mNativeAd != null) {
                return mNativeAd.getAdLogo();
            }
            return null;
        }

        @Override
        public void prepare(View view, ATNativePrepareInfo nativePrepareInfo) {
            if (view == null) {
                return;
            }

            try {
                if (mAdContainer == null) {
                    return;
                }
                if (nativePrepareInfo != null) {
                    List<View> clickViewList = nativePrepareInfo.getClickViewList();
                    if (clickViewList != null && !clickViewList.isEmpty()) {
                        for (int i = 0; i < clickViewList.size(); i++) {
                            String key = String.valueOf(1000 + i);
                            mAdContainer.addView(key, clickViewList.get(i));
                        }
                    }
                }
                if (mMediaView != null) {
                    mAdContainer.setMediaView(mMediaView);
                }
                mAdContainer.setNativeAd(mNativeAd);
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            }
        }

        @Override
        public boolean isNativeExpress() {
            Log.d(TAG, "isNativeExpress");
            return false;
        }

        @Override
        public ViewGroup getCustomAdContainer() {
            Log.d(TAG, "getCustomAdContainer");
            mAdContainer = new AlxNativeAdView(mContext);
            return mAdContainer;
        }

        @Override
        public View getAdMediaView(Object... objects) {
            Log.d(TAG, "getAdMediaView");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
                mMediaView = new AlxMediaView(mContext);
                if (mNativeAd != null && mNativeAd.getMediaContent() != null) {
                    mNativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {

                        @Override
                        public void onVideoStart() {
                            notifyAdVideoStart();
                        }

                        @Override
                        public void onVideoEnd() {
                            notifyAdVideoEnd();
                        }
                    });
                    mMediaView.setMediaContent(mNativeAd.getMediaContent());
                }
                return mMediaView;
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
            return null;
        }

        @Override
        public void clear(View view) {
            Log.d(TAG, "clear");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
        }

        @Override
        public void destroy() {
            Log.d(TAG, "destroy");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
                if (mAdContainer != null) {
                    mAdContainer.destroy();
                    mAdContainer = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
        }

        private void bindListener() {
            if (mNativeAd == null) {
                return;
            }
            mNativeAd.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    notifyAdClicked();
                }

                @Override
                public void onAdImpression() {
                    notifyAdImpression();
                }

                @Override
                public void onAdClosed() {
                    notifyAdDislikeClick();
                }
            });
        }

    }

    @Override
    public void destory() {
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }
}
```

##### Interstitial：

```Java
package com.anythink.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.interstitial.unitgroup.api.CustomInterstitialAdapter;

import java.util.Map;

/**
 * TopOn 插屏广告适配器
 */
public class AlxInterstitialAdapter extends CustomInterstitialAdapter {

    private static final String TAG = "AlxInterstitialAdapter";

    private AlxInterstitialAD alxInterstitialAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void startAdLoad(Context context) {
        alxInterstitialAD = new AlxInterstitialAD();
        alxInterstitialAD.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdShow();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdClose();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoStart();
                }
            }

            @Override
            public void onInterstitialAdVideoEnd() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoEnd();
                }
            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoError(String.valueOf(errorCode), errorMsg);
                }
            }
        });
    }


    @Override
    public void show(Activity activity) {
        if (alxInterstitialAD != null) {
            alxInterstitialAD.show(activity);
        }
    }

    @Override
    public void destory() {
        if (alxInterstitialAD != null) {
            alxInterstitialAD.destroy();
            alxInterstitialAD = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public boolean isAdReady() {
        if (alxInterstitialAD != null) {
            return alxInterstitialAD.isReady();
        }
        return false;
    }
}
```

##### RewardVideo：

```Java
package com.anythink.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.rewardvideo.unitgroup.api.CustomRewardVideoAdapter;

import java.util.Map;

/**
 * TopOn 激励广告适配器
 */
public class AlxRewardVideoAdapter extends CustomRewardVideoAdapter {

    private static final String TAG = "AlxRewardVideoAdapter";
    private AlxRewardVideoAD alxRewardVideoAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void startAdLoad(Context context) {
        alxRewardVideoAD = new AlxRewardVideoAD();
        alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {

            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errCode + "", errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayStart();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayEnd();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errCode + "", errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdClosed();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onReward();
                }
            }

        });
    }

    @Override
    public void show(Activity activity) {
        if (alxRewardVideoAD != null) {
            alxRewardVideoAD.showVideo(activity);
        }
    }


    @Override
    public void destory() {
        if (alxRewardVideoAD != null) {
            alxRewardVideoAD.destroy();
            alxRewardVideoAD = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public boolean isAdReady() {
        if (alxRewardVideoAD != null) {
            return alxRewardVideoAD.isReady();
        }
        return false;
    }
}
```

##### AlxMetaInf:

```Java
package com.anythink.custom.adapter;

/**
 * 适配器版本信息
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";
}
```

### 7. 第三方聚合后台配置流程（仅供参考）：

##### 7.1 Admob

1.创建应用（如果已经有请忽略）

1).首先在 Admob 官网登录，添加第三方广告平台，具体按照 Admob 官网说明文档进行调整[https://apps.admob.com/](https://apps.admob.com/) 如果没有创建应用，首先创建一个应用（下面是测试用例）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-1.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-2.png)

2.)然后添加创建对应的广告单元

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-3.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-4.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-5.png)

2.添加 Alx 中介组

1).首先创建 Alx 中介组

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-6.png)

2).创建添加 Alx 广告单元，支持 激励视频广告,插屏广告,横幅广告(以激励视频为例)

填入名称 Alx 激励视频中介 选择广告单元类型为激励广告

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-7.png)

3).然后点击-添加广告单元 选择对应的应用和广告单元

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-8.png)

4).点击- 完成

到下一个界面广告瀑布流-添加自定义事件

输入名称和价格

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-9.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-10.png)

5).点击继续

输入一下广告单元信息（重要）

然后输入映射广告单元信息：

输入 com.admob.custom.adapter.AlxRewardVideoAdapter (激励视频)

支持的具体 Adapter 类名:

激励视频 com.admob.custom.adapter.AlxRewardVideoAdapter

横幅广告 com.admob.custom.adapter.AlxBannerAdapter

插屏广告 com.admob.custom.adapter.AlxInterstitialAdapter



    参数为：
```
{

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
其中 appid unitid token sid 在 RixEngine 平台申请（具体可咨询运营对接人员）

appid 对应 RixEngine 平台的 App ID

token 对应 RixEngine 平台的 Token

sid 对应 RixEngine 平台的 ID

unitid 对应 RixEngine 平台的 Placement ID

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-11.png)

6.)输入完成后点击-完成

到以下界面

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-12.png)

7)..点击保存按钮

，然后点击关闭回到主页面就完成了

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.1-13.png)

##### 7.2 MAX

1.首先在官网 [Applovin ｜ Everything you need to grow your mobile apps（](https://www.applovin.com/)[applovin.com](https://www.applovin.com/)[）](https://www.applovin.com/) ，创建添加自己的 app 应用，具体按照 MAX 官网说明文档进行调整,如果没有创建应用，首先创建一个应用（下面是测试用例）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-1.png)

创建一个 android App

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-2.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-3.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-4.png)

2.添加完 app 后，回到主界面然后点击 NetWorks，点击图中的

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-5.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-6.png)

3.选择 SDK 接入方式 ，输入 Custom Networks Name 并且输入适配器包路径

com.applovin.mediation.adapters.AlgorixMediationAdapter （重要，路径一定要对）

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-7.png)

---

---

4.把 Rixengine 压缩包里面的适配器文件拷贝到项目中对应的的目录如下图



![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-8.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-9.png)

5.导入 Rixengine Sdk 和添加混淆配置

将本 SDK 压缩包内的 rixengine._._.\*.aar 复制到 Application Module/libs 文件夹(没有的话须手动创建), 并将以下代码添加到您 Moudle app 的 build.gradle 中：

//Unity 导出的项目 Application 名一般为 unityLibrary
```
repositories {

    flatDir {
    
        dirs 'libs'
    
    }

}

depedencies {

    compile(name: 'rixengine.*.*.*', ext: 'aar') //要自行改成具体版本号

}
```
在 App 文件夹下的.pro 文件(Android 中一般叫 proguard-rules.pro，Unity 导出的为 proguard-unity.txt)中添加：
```

-keep class com.rixengine.\*_ {_;}

-keep class admob.custom.adapter.\*_ {_;}

-keep class anythink.custom.adapter.\*_ {_;}

-keep class com.tradplus.custom.adapter.\*_ {_;}

-keep class com.applovin.mediation.adapters.\*_ {_;}

-keep class com.ironsource.adapters.\*_ {_;}

```

6.创建完 NetWorks 后，点击右侧的 Ad Units 依次创建广告位 支持 4 种类型

BANNER，INTER，NATIVE，REWARD 。

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-10.png)

**创建 NATIVE 类型广告时要注意选择 Manual（自渲染）这个很重要，不然会没有填充**

**具体代码实现可以参考 demo 中的集成案例**

```Java
mAdLoader = new MaxNativeAdLoader(MAX_NATIVE_AD, context);mAdLoader.setNativeAdListener(mMaxNativeAdListener);mAdLoader.loadAd(createNativeAdView()); //Manual (自渲染)
```

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-11.png)

---

7．创建完广告位之后，点击 banner（以 BANNER 为例）

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-12.png)

---

拉到下面，点击图中的

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-13.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-14.png)

Enable 图中的

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-15.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-16.png)

并且填入广告参数 广告参数在 Rixengine 平台申请

**Custom Parameters 格式**
```
{

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
---

appid 对应 Rixengine 平台的 App ID

token 对应 Rixengine 平台的 Token

sid 对应 Rixengine 平台的 ID

united 对应 Rixengine 平台的 Placement ID

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-17.png)

8.输入完成后 点击保存即可。

---

9.广告集成测试 可以调整图中的

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-18.png)

来优先显示 Rixengine 广告

---

##### 7.3 IronSource

1.首先在[ironSource | Turning Apps Into Scalable Businesses (](https://www.is.com/)[is.com](https://www.is.com/)[)](https://www.is.com/)官网登录，创建添加自己的 app 应用，具体按照 IronSource 官网说明文档进行调整,如果没有创建应用，首先创建一个应用（下面是测试用例）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-1.png)

2.添加完 app 后，回到主界面然后点击 SDK NetWorks

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-2.png)

3.然后在点击下图中的 Available Networks ，选择 Custom Adapter

（PS：刚开始可能看到不到 Custom Adapter 选项，需要找相关的 AM 开通）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-3.png)

4．输入 Network Key : 15b958455 (重要，这个是 AlgoriX 专用的 Network key)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-4.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-5.png)

5.把 AlgoriX 压缩包里面的适配器文件拷贝到项目中对应的的目录如下图

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-6.png)



6．输入对应的 sid 和 token 在 Rixengine SSP 平台申请（具体可咨询运营对接人员）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-7.png)

IronSource 平台比较特殊 适配器里面的 host 需要手动去更改

host 对应 SSP 平台的 host

appid 对应 SSP 平台的 App ID

token 对应 SSP 平台的 Token

sid 对应 SSP 平台的 ID

unitid 对应 SSP 平台的 Placement ID

7.添加完成后点击图中的 Setup

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-8.png)

8.输入对应的 appid 和 unitid

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-9.png)

9.然后点击图中的 Algorix 图标，看看对应的广告位是否生效（确保为 Active）

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-10.png)

10.然后到 Meidiation 界面能看到具体的排序

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-11.png)

11.可以通过手动更改 ecpm 的值，来调整广告的加载排序

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-12.png)

12.测试聚合是否成功，对于已经上线的 app 可以在 Testing 里面的 Mdeiation Testing 添加一个测试设备。

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-13.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-14.png)

13.然后选择对应的广告源，点 Test 按钮开启就好。

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-15.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-16.png)

##### 7.4 Topon

1. 第一步： 聚合 sdk 包获取地址

[https://docs.toponad.com/#/zh-cn/android/download/package?\_t=UnCmEcPpebJEXBdkOHG7dVe114c4837z](https://docs.toponad.com/#/zh-cn/android/download/package?_t=UnCmEcPpebJEXBdkOHG7dVe114c4837z)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-1.png)

注意： 选择最新版本的 sdk, 非中国内地版本， 接入方式建议选 gradle。

聚合广告平台选择： 华为、Mintegral 、UnityAds 、Admob

1. 第二步：接入文档请参考如下链接 ：

[https://docs.toponad.com/#/zh-cn/android/android_doc/android_sdk_config_access](https://docs.toponad.com/#/zh-cn/android/android_doc/android_sdk_config_access)

       主要是在topon上面配置一下自定义广告平台，添加广告适配器映射、自定义广告参数

输入映射广告单元信息：

com.anythink.custom.adapter.AlxRewardVideoAdapter (激励视频)

现在暂时支持四种广告类型，具体 Adapter 类名:

激励视频 com.anythink.custom.adapter.AlxRewardVideoAdapter

横幅广告 com.anythink.custom.adapter.AlxBannerAdapter

插屏广告 com.anythink.custom.adapter.AlxInterstitialAdapter

原生广告 com.anythink.custom.adapter.AlxNativeAdapter

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-2.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-3.png)

配置完后再添加自定义广告参数

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-4.png)

**图中的 参数格式**
```
{

"host": "http://testaa.rixengine.com/rtb",

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
---

host 对应 SSP 平台的 host

appid 对应 SSP 平台的 App ID

token 对应 SSP 平台的 Token

sid 对应 SSP 平台的 ID

united 对应 SSP 平台的 Placement ID

1. Rixengine SDK android 集成说明：

1). 将本 SDK 压缩包内的 rixengine._._.\*.aar 复制到 Application Module/libs 文件夹(没有的话须手动创建), 并将以下代码添加到您 Moudle app 的 build.gradle 中
```
repositories {

    flatDir {
    
        dirs 'libs'
    
    }

}

depedencies {

           compile(name: 'rixengine.*.*.*', ext: 'aar') //要自行改成具体版本号

}
```
2).将 com 目录的适配器代码放到你们的 Android 工程代码 src/main/java 源代码目录中，注意是整个包路径拷贝过去，包全路径位：com.anythink.custom.adapter

具体如下图所示：

![](https://static.rixengine.com/a/platform/help/c2s/6.2-1-1.png)

4.Android 混淆规则配置（若在 Android 调用开屏广告，打包需要添加）：

-dontwarn com.rixengine.** -keep class com.rixengine.** {_;} -keep class com.anythink.\*\* {_;} -keep public class com.anythink.network.** -keepclassmembers class com.anythink.network.** { public _; } -dontwarn com.anythink.hb.** -keep class com.anythink.hb.**{ _;}\***\*-dontwarn com.anythink.china.api.** -keep class com.anythink.china.api.**{ \*;}\*\***-keep class com.anythink.myoffer.ui.**{ \*;} -keepclassmembers public class com.anythink.myoffer.ui.** { public \*; }

## 更新日志

| 日期 | 更新内容 |
| --- | --- |
| 2021.09.27 | `response` 去掉 `dltrackers`、`dlerrtrackers`、`imperrtrackers` 字段 |
| 2021.09.29 | `Response` 添加 `data.debug` 字段 |
| 2021.10.22 | 删除`data.debug`字段新增`data.ads.video_ext`、`data.ads.banner_ext`、`data.ads.native_ext`字段 |
| 2021.10.25 | 请求新增广告类型字段`adtype`，用于校验广告位与广告类型是否匹配返回的 `data.ads.adtype `字段改名为` data.ads.adm_type`, 字段类型由`String`改为`int` |
| 2021.10.26 | 更新错误码列表 |
| 2021.11.22 | 更新`native`协议 |
| 2022.02.15 | 返回出价信息以及`nurl`、`burl`信息，新增`data.ads.price`、`data.ads.nurl`、`data.ads.burl`字段 |
| 2022.06.29 | 添加 ` data.ads.``video_ext.mute ` 字段，针对视频广告返回是否默认静音播放 |
| 2022.07.18 | 针对 `3.2.8` 以上版本添加` data.ads.native_ext.omid` 字段，用于返回` Native` 类型广告 `omsdk` 所需的三个参数：`vendorKey`、`javascriptResourceUrl`、`verificationParameters`。`Banner` 和 `Video` 广告的 `omid` 验证脚本已集成在 `adm` 中。 |
| 2022.08.08 | 新增 ` data.ads.``video_ext.close  `字段，用于指示激励视频是否允许关闭。 |
| 2022.11.07 | 新增 `native_ext.asset_type` 和 `native_ext.source` 字段。用于分别返回 原生广告素材类型以及广告来源。 |
| 2022.12.29 | 新增 `omidpv`字段，用于传递 `omsdk partner version` |
| 2023.08.12 |  |

Copyright © 2023 RixEngine. All Rights Reserved
