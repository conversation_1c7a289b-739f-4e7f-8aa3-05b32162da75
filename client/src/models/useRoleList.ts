/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-10 16:08:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-10 16:38:19
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllRole } from '@/services/api';

export default function useRoleList() {
  const { data, run, loading, error } = useCustomRequest(getAllRole, {
    cacheKey: 'role-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading
  };
}
