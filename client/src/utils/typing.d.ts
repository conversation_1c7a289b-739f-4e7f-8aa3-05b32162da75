/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-03-07 22:29:43
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-06-27 21:16:32
 * @Description:
 */

import type { LazyExoticComponent, ComponentType } from 'react';
import type { Outlet } from '@umijs/max';

declare namespace DynamicRoutes {
  // 后端返回的路由数据为 RouteRaw[]
  interface RouteRaw {
    sort: number;
    id: number;
    pid: number;
    path: string;
    title: string;
    component: string;
    icon?: string;
    is_hide: number;
    access: string;
    menu_render: number;
  }

  // 前端根据后端返回数据生成的路由数据
  interface Route {
    sort: number;
    id: string;
    parentId: 'ant-design-pro-layout' | string;
    name: string;
    path: string;
    file?: string;
    children?: Route[];
    icon?: string;
    hideInMenu?: boolean;
    access?: string;
    menuRender?: boolean;
  }

  // 前端根据后端返回数据生成的React.lazy懒加载组件或Outlet（一级路由）
  type RouteComponent = LazyExoticComponent<ComponentType<any>> | typeof Outlet;

  interface ParsedRoutes {
    [key: number]: Route;
  }

  // 此类型用于 Object.assign(routeComponents, parsedRoutes)，合并路由组件
  interface ParsedRouteComponent {
    [key: number]: RouteComponent;
  }

  // parseRoutes 函数的返回值
  interface ParseRoutesReturnType {
    routes: DynamicRoutes.ParsedRoutes;
    routeComponents: DynamicRoutes.ParsedRouteComponent;
  }
}
