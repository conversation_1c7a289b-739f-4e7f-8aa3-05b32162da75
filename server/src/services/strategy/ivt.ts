/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-09 19:42:09
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-10 10:13:31
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-16 18:43:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-04 15:22:43
 * @Description:
 */
import { ivtModel } from '@/models';
import { IVTAPI } from '@/types/ivt';

class IvtServices implements IVTAPI.IvtServices {
  async isIvtExist(params: IVTAPI.CheckIvtParams) {
    return ivtModel.isIvtExist(params);
  }

  async getIvtList(tnt_id: number) {
    return await ivtModel.getIvtList(tnt_id);
  }

  async addIvt(params: IVTAPI.AddIvtParams): Promise<Boolean> {
    return ivtModel.addIvt(params);
  }

  async updateIvt(
    params: IVTAPI.UpdateIvtParams,
    isDuplicated?: boolean
  ): Promise<Boolean> {
    return isDuplicated ? ivtModel.addIvt(params, true) : ivtModel.updateIvt(params);
  }
}
export const ivtService = new IvtServices();
