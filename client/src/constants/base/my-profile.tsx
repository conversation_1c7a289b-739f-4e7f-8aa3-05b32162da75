/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-02 11:47:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-06-05 15:03:55
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import RixEngineFont from '@/components/RixEngineFont';
type MenuItem = Required<MenuProps>['items'][number];
export const TimeZone: string = 'UTC+0 London,Iceland,Greewich Mean Time';
export const ProfileBreadOptions: BreadcrumbItem[] = [
  {
    name: 'My Profile'
  }
];
export const ProfileTabOptions = [
  { label: 'General Info', value: '1' }
  // { label: 'Accounts&Permissions', value: '2' }
];

export const ProfileTabToString: { [key: string]: string } = {
  '1': 'General Info'
};
export const ProfileTab = {
  Info: '1',
  Management: '2'
};

export const ProfileMenuOptions = [
  {
    label: 'Manage Users',
    key: 'manage-users',
    accessCode: 'ManageUsersAuth'
  },
  {
    label: 'Role Permissions',
    key: 'permissions',
    accessCode: 'RolePermissionsAuth'
  }
  // {
  //   label: 'User Permissions',
  //   key: 'custom-permissions',
  //   accessCode: 'CustomPermissionsAuth'
  // }
];
export const ProfileDropdownMenu = <Menu items={ProfileMenuOptions}></Menu>;
export const ProfileMenuKey = {
  ManageUsers: 'manage-users',
  Permissions: 'permissions',
  CusPermissions: 'custom-permissions'
};

// Manage Users
export const ManageUserTab = {
  All: 1,
  Advertiser: 2,
  Publisher: 3
};
export const ManageUserTabOptions = [
  {
    label: 'All',
    value: 1
  },
  {
    label: 'Advertiser',
    value: 2
  },
  {
    label: 'Publisher',
    value: 3
  }
];

export const PermissionTab = {
  Functional: 1,
  Data: 2
};
export const PermissionTabOptions = [
  {
    label: 'Functional',
    value: 1
  },
  {
    label: 'Data',
    value: 2
  }
];

export const PmsOverrideType = {
  Yes: 1,
  No: 2
};
export const PmsOverrideTypeOptions = [
  {
    label: 'Yes',
    value: 1
  },
  {
    label: 'No',
    value: 2
  }
];
export const SendEmailTyle = {
  Yes: 1,
  No: 2
};

export const SendEmailTyleOptions = [
  {
    label: 'Yes',
    value: 1
  },
  {
    label: 'No',
    value: 2
  }
];

export const UserType = {
  Administrator: 1,
  'Ordinary User': 2
};
