/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-03 15:45:49
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { InputNumber } from 'antd';
import type { InputNumberProps } from 'antd';
import styles from './index.less';

function InputNumberNormal(props: InputNumberProps): JSX.Element {
  return (
    <InputNumber
      {...props}
      className={styles['normal-input-number-container']}
    />
  );
}

export default InputNumberNormal;
