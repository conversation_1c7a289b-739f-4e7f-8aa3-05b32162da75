import Joi from 'joi';
import { AdFormats } from '@/constants';
import { Country } from '@/constants/country';
import { dictService } from '@/services/common/dict';

const PmpDealSchema = {
  pmp_id: Joi.number().integer().required(),
  buyer_id: Joi.number().integer().required(),
  name: Joi.string().max(200).min(1).required(),
  deal_id: Joi.string().max(128).required(),
  bidfloor: Joi.number().min(0).required(),
  auction_type: Joi.number().valid(1, 2, 0).required()
};
export const AddPmpDealSchema = Joi.object({
  ...PmpDealSchema
});

export const EditPmpDealSchema = Joi.object({
  ...PmpDealSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().valid(1, 2).required()
});

const PmpInternalSchema = {
  name: Joi.string().max(200).min(1).required(),
  seller_id: Joi.array().items(Joi.number().integer()),
  inventory_type: Joi.array().items(Joi.number().valid(1, 2)),
  bundle: Joi.array().items(Joi.string()),
  ad_format: Joi.array().items(Joi.number().valid(...AdFormats)),
  country: Joi.array().items(Joi.string().valid(...Object.keys(Country))),
  ad_size: Joi.array().items(Joi.number()).external(async (value) => {
    if (!value) return value;
    const adSizeDict = await dictService.getDict('ad_size');
    const validSizes = adSizeDict.map(item => Number(item.value));

    for (const size of value) {
      if (!validSizes.includes(size)) {
        throw new Error(`Invalid ad_size: ${size}`);
      }
    }
    return value;
  }),
  seller_deal_id: Joi.string().max(128).min(0),
  remark: Joi.string().min(0)
};

export const addPmpInventorySchema = Joi.object({
  ...PmpInternalSchema
});

export const EditPmpInternalSchema = Joi.object({
  ...PmpInternalSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().valid(1, 2).required()
});
