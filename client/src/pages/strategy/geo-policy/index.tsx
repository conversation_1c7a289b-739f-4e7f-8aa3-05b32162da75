import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import { StatusMap } from '@/constants/common';
import { GeoPolicyBreadOptions, GeoPolicyTab, GeoPolicyTabOptions } from '@/constants/strategy/geo-policy';
import { useCurrentTabState } from '@/hooks/useCurrentTabState';
import { useOptionsData } from '@/hooks/useOptionsData';
import { useCallback, useMemo, useRef } from 'react';
import AddGeoPolicyKeyDrawer, { type KeyDrawerToolRef } from './AddGeoPolicyKeyDrawer';
import AddGeoPolicyKeyRelationDrawer, { type KeyRelationDrawerToolRef } from './AddGeoPolicyKeyRelationDrawer';
import { useGeoPolicyData } from './helper/useGeoPolicyData';
import { useSearchOptions } from './helper/useSearchOptions';
import { useTableColumns } from './helper/useTableColumns';
import { useTableOperations } from './helper/useTableOperations';

export default function GeoPolicy() {
  const [currentTab, setCurrentTab] = useCurrentTabState(GeoPolicyTab.auth, GeoPolicyTabOptions);
  // 管理发布商数据，用于创建和编辑的 publisher 的限制
  const { supplyOptions } = useOptionsData(['supply']);

  // 使用自定义hook管理数据
  const { processedData, loading, reloadData, policyKeyOptions, relationDataSource } = useGeoPolicyData({
    currentTab
  });

  // 获取当前tab的搜索选项
  const currentSearchOptions = useSearchOptions(supplyOptions, policyKeyOptions, currentTab);

  // 这样可以避免 全局刷新渲染
  const keyDrawerToolRef = useRef<KeyDrawerToolRef>(null);
  const relationDrawerToolRef = useRef<KeyRelationDrawerToolRef>(null);

  // 使用自定义hook管理表格操作
  const { getOperationColumn, getExpandButtons } = useTableOperations({
    currentTab,
    keyDrawerToolRef,
    relationDrawerToolRef
  });
  // 获取操作列
  const operationColumn = useMemo(() => getOperationColumn(), [getOperationColumn]);

  // 使用自定义hook管理表格列
  const { currentColumns } = useTableColumns({
    currentTab,
    operationColumn
  });

  // 获取表格扩展按钮
  const tableExpandButtons = useMemo(() => getExpandButtons(), [getExpandButtons]);

  const handleSuccess = useCallback(() => {
    reloadData();
  }, [reloadData]);

  return (
    <PageContainer flexDirection="column" options={GeoPolicyBreadOptions}>
      <FrontTable<StrategyAPI.GeoPolicyKeyListItem | StrategyAPI.GeoPolicyKeyRelationListItem>
        tabOptions={GeoPolicyTabOptions}
        defaultTab={currentTab}
        onTabChange={setCurrentTab as any}
        isFold
        searchOptions={currentSearchOptions}
        loading={loading}
        columns={currentColumns}
        dataSource={processedData || []}
        rowKey={'unique_id'}
        request={reloadData}
        btnOptions={tableExpandButtons}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={100}
        initialValues={{ status: [StatusMap.Active] }}
      />
      <AddGeoPolicyKeyDrawer drawerToolRef={keyDrawerToolRef} onSuccess={handleSuccess} />
      <AddGeoPolicyKeyRelationDrawer
        drawerToolRef={relationDrawerToolRef}
        onSuccess={handleSuccess}
        supplyOptions={supplyOptions}
        relationDataSource={relationDataSource}
        policyKeyOptions={policyKeyOptions}
      />
    </PageContainer>
  );
}
