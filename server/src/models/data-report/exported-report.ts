/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-17 18:45:46
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-29 16:22:05
 * @Description:
 */
import moment from 'moment-timezone';
import dbUtils from '@/db/mysql';
import { StatusTypeMap } from '@/constants/report/exported-report';
import { getTzTimeSql } from '@/constants/time-zone';

class ExportLogModel {
  async getExportLog(user_id: number, cur_time_zone: string) {
    const startTime = moment()
      .startOf('day')
      .subtract(6, 'day')
      .format('YYYY-MM-DD');
    const sql = `
      select 
        id,name,type,status,query_condition,${getTzTimeSql(
          'create_time',
          cur_time_zone
        )} as create_time,err_msg
      from export_log 
      where user_id=? and DATE_FORMAT(create_time, '%Y-%m-%d') >= ?
      order by create_time desc`;
    return await dbUtils.permissionQuery(sql, [user_id, startTime]);
  }

  async updateExportLog(sql: string) {
    return await dbUtils.permissionQuery(sql);
  }

  async createExportLog(
    user_id: number,
    condition: string,
    type: number,
    fileName: string,
    path: string
  ) {
    const sql = `insert into export_log (status, query_condition, type, user_id, name,path)
    values ?`;
    return await dbUtils.permissionQuery(sql, [
      [[StatusTypeMap.Creating, condition, type, user_id, fileName, path]]
    ]);
  }

  async getExportLogByName(name: string, user_id: number) {
    const sql = `select path,name,status,create_time from export_log where name=? and user_id=? limit 1`;
    return await dbUtils.permissionQuery(sql, [name, user_id]);
  }
}

export const exportLogModel = new ExportLogModel();
