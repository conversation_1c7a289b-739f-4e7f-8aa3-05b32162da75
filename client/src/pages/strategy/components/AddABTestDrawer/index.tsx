/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 11:40:26
 * @Description:
 */
import styles from './index.less';
import { useEffect, useMemo, useState } from 'react';
import { Form, Radio, message, Space, DatePicker } from 'antd';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import moment, { Moment } from 'moment-timezone';
import { TimeZoneMap } from '@/constants/base/time-zone';
import { useModel } from '@umijs/max';

import { addABTest, updateABTest } from '@/services/api';
import { fetchData, handleFilterSelect } from '@/utils';

import { StatusOptions, OptionsType, DemandAndSupplyStatusMap, getProfitMinValue, ProfitMaxValue } from '@/constants';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { ABTestTab, ContentLabelDesc } from '@/constants/strategy/ab-test';
import { CountryOptions } from '@/constants/global-mapping/country';

import Select from '@/components/Select/NormalSelect';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import { AdSizeOptions } from '@/models/useAdSizeOptions';

type AddProps = {
  row?: StrategyAPI.ABTestListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadABTest: () => void;
  supplyList: SupplyAPI.SupplyListItem[];
  demandList: DemandAPI.DemandListItem[];
  profitList: StrategyAPI.ProfitListItem[];
  adSizeOptions: AdSizeOptions[];
  currentTab: number;
  blackName?: string;
};

const DefaultFormData = {
  buyer_id: undefined,
  seller_id: undefined,
  content: '',
  expire_time: ''
};

const AddABTestDrawer: React.FC<AddProps> = ({
  row,
  handleClose,
  visible,
  isEdit,
  reloadABTest,
  demandList,
  supplyList,
  profitList,
  adSizeOptions,
  currentTab,
  blackName
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { initialState } = useModel('@@initialState');
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);
  const demandOptions = useMemo(() => {
    return demandList?.map(item => ({
      label: item.buyer_name,
      value: item.buyer_id
    }));
  }, [demandList]);

  const supplyOptions = useMemo(() => {
    return supplyList?.map(item => {
      return {
        label: item.seller_name,
        value: item.seller_id,
        status: item.status
      };
    });
  }, [supplyList]);

  useEffect(() => {
    if (row && isEdit && visible) {
      let content = row.content;
      if (content) {
        content = JSON.parse(content);
      }
      const { seller_id, buyer_id } = row;
      const default_profit = getDefaultProfit(seller_id, buyer_id);

      form.setFieldsValue({
        ...row,
        content,
        seller_id: currentTab === ABTestTab.TransferFormat ? 0 : seller_id,
        default_profit,
        expire_time: moment(row.expire_time),
        ad_format: `${row.ad_format ? row.ad_format : ''}` || undefined
      });
    } else {
      form.resetFields();
      if (currentTab === ABTestTab.TransferFormat && !isEdit) {
        form.setFieldsValue({
          content: [
            { status: 1, ratio: 50 },
            { status: 2, ratio: 50 }
          ],
          seller_id: 0
        });
      }
    }
  }, [row, isEdit, visible, currentTab]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    const { content } = values;
    let contentJSON = '';
    if (content && content.length) {
      contentJSON = JSON.stringify(content);
    }

    const expire_time = Array.isArray(values.expire_time) ? values.expire_time?.[0] : values.expire_time;
    const params = {
      ...values,
      content: contentJSON,
      expire_time: expire_time?.format('YYYY-MM-DD HH:mm:ss'),
      ori_data: row || {}
    };

    if (isEdit) {
      handleEditABTest({ ...params, id: row?.id, type: currentTab });
    } else {
      handleAddABTest({ ...params, type: currentTab });
    }
  };

  const onSuccess = () => {
    message.success('success');

    reloadABTest();
    handleClose();
  };

  const onCancel = () => {
    handleClose();
  };

  const handleAddABTest = (params: any) => {
    fetchData({ setLoading, request: addABTest, params, onSuccess });
  };

  const handleEditABTest = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateABTest, params });
  };

  const getDefaultProfit = (seller_id: number, buyer_id: number) => {
    // 先找 seller_demand 类型的匹配
    let demandProfit = 0;
    for (const profit of profitList) {
      // type: seller_demand
      if (profit.seller_id === seller_id && profit.buyer_id === buyer_id) {
        return profit.profit_ratio;
      }
      // type: demand, 先记录下来,如果没有找到 seller_demand 再用
      if (profit.buyer_id === buyer_id && !profit.seller_id) {
        demandProfit = profit.profit_ratio;
      }
    }
    return demandProfit;
  };
  const handleValueChange = (changeValue: any, allValues: any) => {
    // 重置数据
    const { buyer_id = 0, seller_id = 0, content = [] } = allValues;
    if (profitList?.length && seller_id && buyer_id) {
      const default_profit = getDefaultProfit(seller_id, buyer_id);
      form.setFieldsValue({ default_profit });
    }

    // 处理 TransferFormat 的 ratio 联动
    if (currentTab === ABTestTab.TransferFormat && content && content.length >= 2) {
      // 检查是哪个字段变化了
      const changedField = Object.keys(changeValue)[0];
      if (changedField === 'content') {
        const changedContent = changeValue.content;

        // 判断是哪个 ratio 变化了
        if (changedContent && changedContent[0]?.ratio !== undefined) {
          const newRatio = changedContent[0].ratio;
          if (newRatio !== null) {
            form.setFieldsValue({
              content: [
                { ...content[0], ratio: newRatio },
                { ...content[1], ratio: 100 - Number(newRatio) }
              ]
            });
          }
        } else if (changedContent && changedContent[1]?.ratio !== undefined) {
          const newRatio = changedContent[1].ratio;
          if (newRatio !== null) {
            form.setFieldsValue({
              content: [
                { ...content[0], ratio: 100 - Number(newRatio) },
                { ...content[1], ratio: newRatio }
              ]
            });
          }
        }
      }
    }
  };
  const disabledDate = (currentDate: Moment) => {
    return currentDate < moment().startOf('day') || currentDate > moment().endOf('days').add(9, 'days');
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit ${blackName}` : `Create ${blackName}`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
      >
        {/* TransferFormat 时，隐藏 Publisher 选择并设置固定值为0 */}
        <Form.Item
          name="seller_id"
          label="Publisher:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Publisher'
            }
          ]}
          hidden={currentTab === ABTestTab.TransferFormat}
        >
          <Select
            placeholder="Please Select Publisher"
            disabled={isEdit}
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            {supplyOptions &&
              supplyOptions.map((item: any, index: number) => {
                return (
                  <Select.Option
                    value={item.value}
                    key={index}
                    disabled={item.status === DemandAndSupplyStatusMap.Testing}
                  >
                    {item.label}({item.value})
                  </Select.Option>
                );
              })}
          </Select>
        </Form.Item>
        <Form.Item
          name="buyer_id"
          label="Advertiser:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Advertiser'
            }
          ]}
        >
          <Select
            placeholder="Please Select Advertiser"
            disabled={isEdit}
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            {demandOptions &&
              demandOptions.map((item: OptionsType, index: number) => {
                return (
                  <Select.Option value={item.value} key={index}>
                    {item.label}({item.value})
                  </Select.Option>
                );
              })}
          </Select>
        </Form.Item>

        {currentTab === ABTestTab.Profit && (
          <Form.Item label="Profit Ratio (Default)" required name="default_profit">
            <InputNumber defaultValue={0} disabled type="number" addonAfter="%" style={{ width: 100 }} />
          </Form.Item>
        )}
        {currentTab === ABTestTab.BidFloor && (
          <>
            <Form.Item
              name="country"
              label="Country"
              rules={[
                {
                  required: true,
                  message: 'Please Select Country'
                }
              ]}
            >
              <Select
                placeholder="Please Select Country"
                filterOption={handleFilterSelect}
                showSearch
                options={CountryOptions}
              />
            </Form.Item>
            <Form.Item
              name="ad_format"
              label="Ad Format"
              rules={[
                {
                  required: true,
                  message: 'Please Select Ad Format'
                }
              ]}
            >
              <Select
                options={AdFormatOptions}
                placeholder="Please Select Ad Format"
                filterOption={handleFilterSelect}
              />
            </Form.Item>
            <Form.Item
              name="ad_size"
              label="Ad Size"
              rules={[
                {
                  required: true,
                  message: 'Please Select Ad Size'
                }
              ]}
            >
              <Select
                placeholder="Please Select Ad Size"
                filterOption={handleFilterSelect}
                options={adSizeOptions}
                showSearch
              />
            </Form.Item>
          </>
        )}

        {currentTab === ABTestTab.TransferFormat ? (
          <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => true}>
            {() => {
              return (
                <>
                  <div className={styles['content-label']}>
                    <span className={styles['label']}>{`${ContentLabelDesc[currentTab]} & Traffic Ratio`}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', gap: '16px' }}>
                    <Space align="baseline">
                      <Form.Item label="Transfer Format" name={['content', 0, 'status']} hidden>
                        <InputNumber disabled />
                      </Form.Item>

                      <Form.Item
                        label="Active(B2N/B2V)"
                        name={['content', 0, 'ratio']}
                        rules={[{ required: true, message: 'Please Input Traffic Ratio' }]}
                      >
                        <InputNumber min={0} max={100} type="number" addonAfter="%" />
                      </Form.Item>
                    </Space>

                    <Space align="baseline">
                      <Form.Item label="Transfer Format" name={['content', 1, 'status']} hidden>
                        <InputNumber disabled />
                      </Form.Item>

                      <Form.Item
                        label="Pause"
                        name={['content', 1, 'ratio']}
                        rules={[{ required: true, message: 'Please Input Traffic Ratio' }]}
                      >
                        <InputNumber min={0} max={100} type="number" addonAfter="%" />
                      </Form.Item>
                    </Space>
                  </div>
                </>
              );
            }}
          </Form.Item>
        ) : (
          <Form.Item noStyle>
            <Form.List
              name="content"
              rules={[
                {
                  validator: async (_, content) => {
                    if (!content || !content?.length) {
                      return Promise.reject(new Error('Please Add A/B Test Content'));
                    } else if (Array.isArray(content) && content.length) {
                      const total = content.filter(Boolean).reduce((prev, cur) => {
                        return prev + cur.ratio;
                      }, 0);
                      if (total > 100) {
                        return Promise.reject(new Error('Total Traffic Ratio Must Less Than 100%'));
                      }
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              {(fields, { add, remove }, { errors }) => {
                return (
                  <>
                    <div className={styles['content-label']}>
                      <span className={styles['label']}>{`${ContentLabelDesc[currentTab]} & Traffic Ratio`}</span>
                      <PlusCircleOutlined onClick={() => add()} className={styles['icon']} />
                    </div>

                    {fields.map(field => (
                      <Space key={field.key} align="baseline" style={{ paddingLeft: 12 }} size={24}>
                        <Form.Item
                          noStyle
                          shouldUpdate={(prevValues, curValues) =>
                            prevValues.buyer_id !== curValues.buyer_id || prevValues.seller_id !== curValues.seller_id
                          }
                        >
                          {() => (
                            <Form.Item
                              {...field}
                              label={ContentLabelDesc[currentTab]}
                              name={[field.name, currentTab === ABTestTab.Profit ? 'profit' : 'bid_floor']}
                              rules={[{ required: true, message: `Please Input ${ContentLabelDesc[currentTab]}` }]}
                            >
                              <InputNumber
                                min={currentTab === ABTestTab.Profit ? profitMinValue : 0}
                                max={currentTab === ABTestTab.Profit ? ProfitMaxValue : 100}
                                type="number"
                                addonAfter={currentTab === ABTestTab.Profit || undefined}
                              />
                            </Form.Item>
                          )}
                        </Form.Item>

                        <Form.Item
                          {...field}
                          label="Traffic Ratio"
                          name={[field.name, 'ratio']}
                          rules={[{ required: true, message: 'Please Input Traffic Ratio' }]}
                        >
                          <InputNumber min={0} max={100} type="number" addonAfter="%" />
                        </Form.Item>

                        <MinusCircleOutlined onClick={() => remove(field.name)} style={{ color: 'red' }} />
                      </Space>
                    ))}

                    <Form.ErrorList errors={errors} />
                  </>
                );
              }}
            </Form.List>
          </Form.Item>
        )}

        <Form.Item
          name="expire_time"
          validateTrigger={['onChange', 'onBlur']}
          label={
            <span>
              Expiration Time&nbsp;
              <span style={{ color: 'rgba(0, 0, 0, 0.45)' }}>{`(${
                TimeZoneMap[initialState?.timeZone || 'Etc/UTC']
              })`}</span>
            </span>
          }
          required
          className={styles['expire-time']}
          wrapperCol={{ span: 14 }}
          rules={[
            {
              required: true,
              message: 'Please Select Expiration Time'
            }
          ]}
        >
          <DatePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            disabledDate={disabledDate}
            popupClassName={styles['dates-picker-container']}
          />
        </Form.Item>

        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddABTestDrawer;
