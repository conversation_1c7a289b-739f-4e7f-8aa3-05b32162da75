/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-22 19:18:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 19:21:17
 * @Description:
 */
import Jo<PERSON> from 'joi';

const addQpsSchema = {
  buyer_id: Joi.number().integer().required(),
  seller_id: Joi.number().integer().required(),
  rev_cap: Joi.number().min(0).required(),
  type: Joi.number().valid(1, 2, 3, 4, 5).required(),
  bundle: Joi.when('type', {
    is: Joi.number().valid(4, 5),
    then: Joi.string().min(1).required()
  })
};
export const AddCapSchema = Joi.object({ ...addQpsSchema });

export const UpdateCapSchema = Joi.object({
  ...addQpsSchema,
  status: Joi.number().integer().valid(1, 2).required()
});
