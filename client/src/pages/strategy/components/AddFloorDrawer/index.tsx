/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:59:15
 * @Description:
 */
import { useEffect, useMemo, useState } from 'react';
import { Form, Radio, message } from 'antd';

import { addFloor, updateFloor } from '@/services/api';
import { fetchData, handleFilterSelect } from '@/utils';

import { FloorType } from '@/constants/strategy/floor';
import { StatusOptions, StatusMap, OptionsType, DemandAndSupplyStatusMap } from '@/constants';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { CountryOptions } from '@/constants/global-mapping/country';

import Select from '@/components/Select/NormalSelect';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalInput from '@/components/Input/NormalInput';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import { useModel } from '@umijs/max';

type AddQpsModelProps = {
  floor?: StrategyAPI.FloorListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadFloor: () => void;
  supplyList: SupplyAPI.SupplyListItem[];
  demandList: DemandAPI.DemandListItem[];
  FloorTypeOptions: StrategyAPI.SelectOptionsType[];
  allPlmList: StrategyAPI.FloorPlmItem[];
  sellerId?: number;
};

const DefaultFormData = {
  type: 2,
  buyer_id: undefined,
  seller_id: undefined,
  app_id: undefined,
  plm_id: undefined,
  status: 1,
  floor: undefined
};

const AddFloorDrawer: React.FC<AddQpsModelProps> = ({
  floor,
  handleClose,
  visible,
  isEdit,
  reloadFloor,
  demandList,
  supplyList,
  FloorTypeOptions,
  allPlmList,
  sellerId
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState(FloorTypeOptions[0].value as number);
  const [supplyId, setSupplyId] = useState<number>();
  const [disabledSupply, setDisabledSupply] = useState(false);
  const [isCustom, setIsCustom] = useState(false);
  const { initialState } = useModel('@@initialState');
  const demandOptions = useMemo(() => {
    return demandList?.map(item => ({
      label: item.buyer_name,
      value: item.buyer_id
    }));
  }, [demandList]);

  const supplyOptions = useMemo(() => {
    return supplyList?.map(item => {
      return {
        label: item.seller_name,
        value: item.seller_id,
        status: item.status
      };
    });
  }, [supplyList]);

  const placementOptions = useMemo(() => {
    return allPlmList
      ?.filter(item => item.seller_id === supplyId)
      .map(item => ({
        label: `${item.plm_id}/${item.plm_name}/${item.app_name}`,
        value: item.plm_id
      }));
  }, [allPlmList, supplyId]);

  useEffect(() => {
    if (visible) {
      form.setFieldValue('type', FloorTypeOptions[0].value);
      setType(FloorTypeOptions[0].value as number);
    }
    if (floor && isEdit && visible) {
      form.setFieldsValue(floor);
      setType(floor.type);
    }
    if (!floor && !isEdit && visible && sellerId) {
      setDisabledSupply(true);
      setSupplyId(sellerId);
      form.setFieldsValue({ seller_id: sellerId });
    }
  }, [floor, isEdit, visible, sellerId]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      handleEditFloor({ ...values, id: floor?.id, op_id: initialState?.currentUser?.user_id, ori_data: floor || {} });
    } else {
      let { plm_id } = values;
      if (!isCustom && !Array.isArray(plm_id) && plm_id) {
        plm_id = [+plm_id];
      }

      handleAddFloor({ ...values, op_id: initialState?.currentUser?.user_id, plm_id });
    }
  };

  const onSuccess = () => {
    message.success('success');
    setDisabledSupply(false);
    reloadFloor();
    handleClose();
  };

  const onCancel = () => {
    setDisabledSupply(false);
    handleClose();
  };

  const handleAddFloor = (params: any) => {
    console.log('params', params);
    fetchData({ setLoading, request: addFloor, params, onSuccess });
  };

  const handleEditFloor = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateFloor, params });
  };

  const handleResetAll = () => {
    const fileds = ['buyer_id', 'app_id', 'plm_id', 'ad_format'];
    if (!sellerId && isEdit && visible && floor) {
      fileds.push('seller_id');
    }
    form.resetFields(fileds);
  };

  const handleValueChange = (changeValue: any) => {
    // 重置数据
    if (changeValue.type) {
      handleResetAll();
      setType(changeValue.type);
    }
    if (changeValue.seller_id) {
      const sullpy = supplyList?.find(item => item.seller_id === changeValue.seller_id);
      form.setFieldsValue({ plm_id: undefined });
      setIsCustom(sullpy?.cus_status === StatusMap.Active);
      setSupplyId(changeValue.seller_id);
    }
  };

  const afterOpenChange = (open: boolean) => {
    !open && form.resetFields();
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Bid Floor` : `Create Bid Floor`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
      afterOpenChange={afterOpenChange}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
      >
        <Form.Item
          name="type"
          label="Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <Select options={FloorTypeOptions} allowClear={false} disabled={isEdit} />
        </Form.Item>

        {![FloorType['Ad Format + Country']].includes(type) && (
          <Form.Item
            name="seller_id"
            label="Publisher:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Publisher'
              }
            ]}
          >
            <Select
              placeholder="Please Select Publisher"
              disabled={isEdit || disabledSupply}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
            >
              {supplyOptions &&
                supplyOptions.map((item: any, index: number) => {
                  return (
                    <Select.Option
                      value={item.value}
                      key={index}
                      disabled={item.status === DemandAndSupplyStatusMap.Testing}
                    >
                      {item.value}({item.label})
                    </Select.Option>
                  );
                })}
            </Select>
          </Form.Item>
        )}
        {[FloorType['Pub + Adv + Ad Unit + Country'], FloorType['Pub + Adv + Ad Format + Country']].includes(type) && (
          <Form.Item
            name="buyer_id"
            label="Advertiser:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Advertiser'
              }
            ]}
          >
            <Select
              placeholder="Please Select Advertiser"
              disabled={isEdit}
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
              mode="multiple"
            >
              {demandOptions &&
                demandOptions.map((item: OptionsType, index: number) => {
                  return (
                    <Select.Option value={item.value} key={index}>
                      {item.value}({item.label})
                    </Select.Option>
                  );
                })}
            </Select>
          </Form.Item>
        )}
        {[FloorType['Pub + Ad Unit + Country'], FloorType['Pub + Adv + Ad Unit + Country']].includes(type) &&
          (isCustom ? (
            <Form.Item
              name="plm_id"
              label="Ad Unit:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: isCustom ? 'Please Select Ad Unit' : 'Please Input Ad Unit ID'
                }
              ]}
            >
              <Select
                placeholder="Please Select Ad Unit"
                disabled={isEdit}
                showSearch
                optionFilterProp="children"
                filterOption={handleFilterSelect}
                mode="multiple"
              >
                {placementOptions &&
                  placementOptions.map((item, index) => (
                    <Select.Option value={item.value} key={index}>
                      {`${item.label}`}
                    </Select.Option>
                  ))}
              </Select>
            </Form.Item>
          ) : (
            <Form.Item
              name="plm_id"
              label="Unit ID:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Input Ad Unit ID'
                },
                {
                  validator: (rule, value) => {
                    const plm_id = +value;
                    if (!isNaN(plm_id)) {
                      return Promise.resolve();
                    }
                    return Promise.reject('Please Input Correct Ad Unit ID');
                  }
                }
              ]}
            >
              <NormalInput placeholder="Please Input Ad Unit ID" disabled={isEdit} />
            </Form.Item>
          ))}

        {[
          FloorType['Ad Format + Country'],
          FloorType['Pub + Ad Format + Country'],
          FloorType['Pub + Adv + Ad Format + Country']
        ].includes(type) && (
          <Form.Item
            name="ad_format"
            label="Ad Format:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Ad Format'
              }
            ]}
          >
            <Select placeholder="Please Select Ad Format" disabled={isEdit} showSearch>
              {AdFormatOptions.map(item => (
                <Select.Option value={+item.value} key={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}

        <Form.Item
          name="country"
          label="Country:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Country'
            }
          ]}
        >
          <Select
            disabled={isEdit}
            showSearch
            filterOption={handleFilterSelect}
            mode="multiple"
            options={CountryOptions}
          />
        </Form.Item>

        <Form.Item
          name="bid_floor"
          label="Bid Floor:"
          validateTrigger={['onChange', 'onBlur']}
          validateFirst
          rules={[{ required: true, message: 'Please Input Bid Floor' }]}
        >
          <InputNumber style={{ width: '150px' }} addonAfter="$" min={0} precision={4} max={10000} />
        </Form.Item>

        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddFloorDrawer;
