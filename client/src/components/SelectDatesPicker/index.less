.container {
  width: 100%;
  height: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  &:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    border-right-width: 1px;
    outline: 0;
  }
  :global {
    .ant-select {
      box-shadow: none;
    }
    .ant-select .ant-select-selector {
      border: none;
      outline: none;
      box-shadow: none;
    }
    .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize) .ant-select-selector {
      border: none;
      box-shadow: none;
    }
  }
}
