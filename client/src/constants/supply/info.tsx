/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-12-06 16:38:18
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:58:42
 * @Description:
 */

import { history, Link } from 'umi';
import type { InfoBarTabsProps } from '@/components/InfoBar';
import StatusTag from '@/components/Tag/NewStatusTag';
import { DemandAndSupplyStatusDesc, StatusDesc, StatusMap } from '..';
import { ChannelType, DeviceType, RelationshipType, RevTrackTypeDesc } from './index';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';
import Title from '@/components/Title';
export const SupplyInfoTabs: InfoBarTabsProps<SupplyAPI.SupplyListItem> = [
  {
    title: 'Basic',
    key: 'basic',
    access: 'SupplyPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />
    },
    rowNum: 3,
    children: [
      {
        title: <Title>Basic Info</Title>,
        key: 'basic-info',
        columns: [
          {
            title: 'ID',
            dataIndex: 'seller_id'
          },
          {
            title: 'Publisher',
            dataIndex: 'seller_name',
            access: 'SupplyDetailPermission',
            render: (text, row, maxWidth) => {
              return (
                <HoverToolTip title={text} maxWidth={maxWidth}>
                  <div style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
                    <Link to={`/supply/publisher/detail?id=${row.seller_id}`} state={{ row }} rel="noreferrer">
                      {text}
                    </Link>
                  </div>
                </HoverToolTip>
              );
            }
          },
          {
            title: 'Partner',
            width: 100,
            dataIndex: 'partner_id',
            render: (_, row, maxWidth) => {
              const val = +_ > 0 ? `${row.partner_name}(${_})` : '-';
              return (
                <HoverToolTip title={val} maxWidth={maxWidth}>
                  <span>{val}</span>
                </HoverToolTip>
              );
            }
          },
          {
            title: 'Publisher ID',
            width: 100,
            dataIndex: 'publisher_id',
            render: (_, row, maxWidth) => {
              const val = _ ? `${row.publisher_id}` : '-';
              return (
                <HoverToolTip title={val} maxWidth={maxWidth}>
                  <span>{val}</span>
                </HoverToolTip>
              );
            }
          },
          {
            title: 'Relationship',
            dataIndex: 'relationship',
            render: text => <>{RelationshipType[text]}</>
          },
          {
            title: 'Channel Type',
            dataIndex: 'channel_type',
            render: text => <>{ChannelType[text]}</>
          },
          {
            title: 'Device Type',
            dataIndex: 'device_type',
            render: text => <>{DeviceType[text]}</>
          },
          {
            title: 'Integration Type',
            dataIndex: 'integration_type_desc'
          },
          {
            title: 'Custom Placement',
            dataIndex: 'cus_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Status',
            width: 85,
            dataIndex: 'status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          }
        ]
      },
      {
        title: <Title>Profit Settings</Title>,
        key: 'profit-settings',
        columns: [
          {
            title: 'Profit',
            dataIndex: 'profit_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Profit(%)',
            dataIndex: 'profit_ratio',
            render: text => <>{text}</>
          },
          {
            title: 'Profit Model',
            dataIndex: 'profit_model',
            render: _ => <StatusTag value={_} statusDescMap={{ 1: 'Net' }} />
          }
        ]
      },
      {
        title: <Title>Tracking Settings</Title>,
        key: 'tracking-settings',
        columns: [
          {
            title: 'Revenue Tracking Type',
            dataIndex: 'rev_track_type',
            render: text => <>{RevTrackTypeDesc[text]}</>
          },
          {
            title: 'NURL',
            dataIndex: 'pass_nurl',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'BURL',
            dataIndex: 'pass_burl',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'LURL',
            dataIndex: 'pass_lurl',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Support OMID',
            dataIndex: 'support_omid',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          }
        ]
      },
      {
        title: <Title>Filter and Compatibility Strategy</Title>,
        key: 'filter-and-compatibility-strategy',
        columns: [
          {
            title: 'Banner Multi Size',
            dataIndex: 'banner_multi_size',
            tooltip: () => 'Add ad size 300*250 for requests with a 320*50 banner',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Native Size Filter',
            dataIndex: 'native_strict_validation',
            tooltip: () => 'Filter ads that do not match the traffic size and length',
            render: status => {
              return status !== '-' ? <StatusTag value={status} statusDescMap={StatusDesc} /> : '-';
            }
          },
          {
            title: 'Adomain Filter',
            dataIndex: 'adomain_filter',
            tooltip: () => 'Filter ads with empty adomain',
            render: status => {
              return status !== '-' ? <StatusTag value={status} statusDescMap={StatusDesc} /> : '-';
            }
          }
        ]
      }
    ]
  },
  {
    title: 'Endpoint',
    key: 'endpoint',
    access: 'SupplyEndpointPermission',
    children: [
      {
        title: 'USE: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: row => {
          if (row.pv_domain) {
            return `http://us.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.use.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://us.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${text}`;
              }
              return `http://${row.host_prefix}.use.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            }
          }
        ]
      },
      {
        title: 'APAC: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: row => {
          if (row.pv_domain) {
            return `http://ap.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.apse.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://ap.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `http://${row.host_prefix}.apse.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            }
          }
        ]
      },
      {
        title: 'EUW: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: row => {
          if (row.pv_domain) {
            return `http://eu.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.euw.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://eu.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `http://${row.host_prefix}.euw.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            }
          }
        ]
      },
      {
        title: 'Global: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: row => {
          if (row.pv_domain) {
            return `https://bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `https://${row.host_prefix}.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `https://bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `https://${row.host_prefix}.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            }
          }
        ]
      }
    ]
  },
  {
    title: 'Account',
    key: 'account',
    access: 'SupplyAccountPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" />,
      onClick: row => {
        history.push(`/supply/publisher/account?id=${row!.seller_id}&u_id=${row!.user_id}&name=${row!.seller_name}`, {
          row
        });
      }
    },
    children: [
      {
        title: 'Publisher Reporting',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            showCopy: true,
            copyText: row => {
              return row.cs_domain || '';
            },
            dataIndex: 'reporting_url',
            render: (txt, params) => {
              return params.cs_domain || '';
            }
          },
          {
            title: 'User Name',
            showCopy: true,
            dataIndex: 'seller_account_name'
          },

          {
            title: 'Status',
            width: 85,
            dataIndex: 'seller_account_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Password',
            dataIndex: 'seller_id',
            render: (txt, row) => {
              return (
                <a
                  href="#"
                  onClick={e => {
                    e.preventDefault();
                    history.push(
                      `/supply/publisher/account?id=${row!.seller_id}&u_id=${row!.user_id}&name=${row!.seller_name}`,
                      {
                        row
                      }
                    );
                  }}
                >
                  {row.seller_account_status && row.seller_account_status === StatusMap.Active ? 'Edit' : 'Create'}
                  <RixEngineFont type="rix-edit-detail" style={{ marginLeft: 5 }}></RixEngineFont>
                </a>
              );
            }
          }
        ]
      },
      {
        title: 'Reporting API',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'Doc',
            dataIndex: 'doc',
            width: 150,
            ellipsis: { showTitle: false },
            render: txt => (
              <a target="_blank" href="/help/sspapi-v2" rel="noreferrer">
                SSP Report API
              </a>
            )
          },
          {
            title: 'Sid',
            showCopy: true,
            dataIndex: 'seller_id'
          },
          {
            title: 'API Status',
            width: 100,
            dataIndex: 'api_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Token',
            dataIndex: 'token',
            showCopy: true
          }
        ]
      }
    ]
  },
  {
    title: 'Authorization',
    key: 'authorization',
    access: 'SupplyDetailPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
      onClick: row => {
        history.push(`/supply/publisher/detail?id=${row!.seller_id}&u_id=${row!.user_id}&name=${row!.seller_name}`, {
          row
        });
      }
    },
    type: 'table',
    rowKey: 'auth_buyer_id',
    tableData: [],
    columns: [
      {
        title: 'Advertiser',
        dataIndex: 'auth_buyer_name',
        ellipsis: { showTitle: false },
        width: 150,
        render: _ => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        )
      },
      {
        title: 'Advertiser ID',
        width: 80,
        dataIndex: 'auth_buyer_id',
        ellipsis: { showTitle: false },
        render: _ => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        )
      }
    ]
  }
];
