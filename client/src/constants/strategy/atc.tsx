/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-07 10:51:14
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-07 15:54:24
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';

export const AtcBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'ATC'
  }
];

export const AtcModelOptions = [
  {
    label: 'IDC-Efficient Model(Lazy)',
    value: 1
  },
  {
    label: 'Revenue-Priority Model(Turbo)',
    value: 2
  },
  {
    label: 'System Automated Optimization',
    value: 3
  },
  {
    label: 'System Automated Optimization Plus',
    value: 4,
    disabled: true
  }
];

export const AtcModelDesc: { [key: number]: string } = {
  1: 'IDC-Efficient Model(Lazy)',
  2: 'Revenue-Priority Model(Turbo)',
  3: 'System Automated Optimization',
  4: 'System Automated Optimization Plus'
};
