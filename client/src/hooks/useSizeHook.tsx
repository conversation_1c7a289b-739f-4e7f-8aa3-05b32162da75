/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-10-10 15:37:39
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-12-26 17:00:17
 * @Description:
 */
import React, { useState, useEffect } from 'react';
type SizeProps = {
  width?: number;
  height?: number;
}
export default (props: SizeProps) => {
  const width = props.width || window.innerWidth;
  const height = props.height || window.innerHeight;
  const [size, setSize] = useState({ width, height });

  const handleResize = () => {
    const { offsetHeight, offsetWidth } = document.body;
    setSize({
      width: offsetWidth,
      height: offsetHeight
    });
  };

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return {
    size, setSize
  };
};
