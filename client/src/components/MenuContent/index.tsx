import { type SiderMenuProps } from '@ant-design/pro-layout/lib/components/SiderMenu/SiderMenu';

import styles from './index.less';

// 合作 logo 展示
const CooperationLogo = () => {
  return (
    <div className={styles.cooperationLogo}>
      <img src="/img/rix-logo.png" alt="rix-logo" />
    </div>
  );
};

interface MenuContentProps extends SiderMenuProps {
  // 是否为 topon 租户，才展示合作 logo
  isToponTenant: boolean;
  defaultDom: React.ReactNode;
}

const MenuContent = ({ collapsed, defaultDom, isToponTenant }: MenuContentProps) => {

  return (
    <div className={styles.menuContent}>
      <div className={styles.menuContentMain}>{defaultDom}</div>
      {isToponTenant && !collapsed && <CooperationLogo />}
    </div>
  );
};

export default MenuContent;
