/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:16:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-26 17:28:45
 * @Description:
 */

import { StatusMap } from '@/constants';
import { CapType } from '@/constants/strategy';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { CapAPI } from '@/types/cap';
import { formatDateTime } from '@/utils/index';
import { buildSQLWhereClause } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

class CapModel implements CapAPI.Cap {
  // eslint-disable-next-line max-len
  async isCapExists(params: CapAPI.IsExistParams): Promise<any[]> {
    const { id, seller_id, buyer_id, type, tnt_id, bundle } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', seller_id],
      ['buyer_id = ?', buyer_id],
      ['bundle = ?', bundle || ''],
      ['tnt_id = ?', tnt_id],
      ['type = ?', type],
      ['id != ?', id ? id : null]
    ]);
    const sql = `select id from cap ${clause} limit 1`;
    return await dbUtil.query(sql, values);
  }

  async getCapList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<CapAPI.CapListItem[]> {
    const tSql = `select buyer_id from buyer where tnt_id=? and status=?`;
    const testingBuyer = await dbUtil.query(tSql, [tnt_id, StatusMap.Testing]);
    let testingBuyerIds = [];
    if (Array.isArray(testingBuyer) && testingBuyer.length > 0) {
      testingBuyerIds = testingBuyer.map(item => item.buyer_id);
    }

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      [
        'buyer_id not in (?)',
        testingBuyerIds.length > 0 ? testingBuyerIds : null
      ]
    ]);

    const sql = `
      select
        c.seller_id,
        c.buyer_id,
        c.id as id,
        c.type,
        c.rev_cap,
        c.cur_rev,
        c.op_id,
        c.status as status,
        c.cap_status,
        c.bundle,
        ${getTzTimeSql('c.update_time', cur_time_zone)} as update_time,
        ${getTzTimeSql('c.sys_update_time', cur_time_zone)} as sys_update_time,
        s.seller_name,
        b.buyer_name,
        a.account_name,
        a.status as account_status,
        a.type as account_type
      from (select * from cap ${clause}) as c
      left join (select seller_name, seller_id from seller) as s on s.seller_id=c.seller_id
      left join (select buyer_name, buyer_id from buyer) as b on b.buyer_id=c.buyer_id
      left join user as a on a.user_id=c.op_id 
      order by c.update_time desc;
    `;
    return await dbUtil.query(sql, values);
  }

  async addCap(params: CapAPI.AddCapParams): Promise<boolean> {
    const default_sys_update_time = moment()
      .tz('Etc/UTC')
      .subtract(1, 'days')
      .format('YYYY-MM-DD HH:mm:ss');
    const { type, seller_id, buyer_id, rev_cap = 0.0, op_id, tnt_id } = params;
    const bundle = [
      CapType['Adv + Bundle'],
      CapType['Adv + Pub + Bundle']
    ].includes(type)
      ? params.bundle || ''
      : '';
    const sql = `insert into cap (seller_id, buyer_id, type, rev_cap, status, cap_status, op_id, sys_update_time, tnt_id, bundle) 
      values ?`;

    return !!(await dbUtil.query(sql, [
      [
        [
          seller_id,
          buyer_id,
          type,
          rev_cap,
          1,
          1,
          op_id,
          default_sys_update_time,
          tnt_id,
          bundle || ''
        ]
      ]
    ]));
  }

  async updateCap(params: CapAPI.updateCapParams): Promise<boolean> {
    const { id, rev_cap = 0.0, status = 1, op_id, tnt_id, type } = params;
    const update_time = formatDateTime(new Date(), 'yyyy-MM-dd hh:mm:ss');
    // 重置
    const sql = `
      update cap set 
        rev_cap=?,
        status=?,
        op_id=?,
        update_time=?
      where id=? and tnt_id=? and type=?`;
    const values = [rev_cap, status, op_id, update_time, id, tnt_id, type];
    return !!(await dbUtil.query(sql, values));
  }
}

export default new CapModel();
