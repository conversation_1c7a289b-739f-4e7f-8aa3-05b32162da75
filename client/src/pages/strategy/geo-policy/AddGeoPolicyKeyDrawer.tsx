import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import { StatusOptions } from '@/constants/common';
import { GeoDefaultKeyMap, GeoDefaultKeyOptions } from '@/constants/strategy/geo-policy';
import { addGeoPolicyKey, updateGeoPolicyKey } from '@/services/api';
import { fetchData } from '@/utils';
import { Form, FormInstance, message } from 'antd';
import { useImperativeHandle, useRef, useState } from 'react';

export type KeyDrawerToolRef = {
  openDrawer: (params?: StrategyAPI.GeoPolicyKeyListItem) => void;
  closeDrawer: () => void;
  form: FormInstance;
};

type AddGeoPolicyKeyDrawerProps = {
  drawerToolRef?: React.RefObject<KeyDrawerToolRef>;
  // 接口成功的回调
  onSuccess?: () => void;
};

const DefaultFormData = {
  is_default: GeoDefaultKeyMap.No,
  policy_key: '',
  remark: ''
};

const AddGeoPolicyKeyDrawer = ({ drawerToolRef, onSuccess }: AddGeoPolicyKeyDrawerProps) => {
  const [form] = Form.useForm<StrategyAPI.AddGeoPolicyKeySchema & { status?: number }>();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const initialValuesRef = useRef<StrategyAPI.GeoPolicyKeyListItem | null>(null);

  useImperativeHandle(
    drawerToolRef,
    () => ({
      openDrawer: (row?: StrategyAPI.GeoPolicyKeyListItem) => {
        setOpen(true);
        form.resetFields();
        if (row) {
          form.setFieldsValue({ ...row });
          initialValuesRef.current = row;
        } else {
          form.setFieldsValue(DefaultFormData);
          initialValuesRef.current = null;
        }
        setIsEdit(!!row);
      },
      closeDrawer: () => {
        setOpen(false);
      },
      form
    }),
    [drawerToolRef, form]
  );

  const handleConfirm = async () => {
    const values = await form.validateFields();

    fetchData({
      setLoading,
      request: isEdit ? updateGeoPolicyKey : addGeoPolicyKey,
      params: values,
      onSuccess: () => {
        setOpen(false);
        message.success('Success');
        onSuccess?.();
      }
    });
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Policy Key` : `Create Policy Key`}
      onConfirm={handleConfirm}
      open={open}
      onClose={() => {
        setOpen(false);
        setIsEdit(false);
      }}
      loading={loading}
      maskClosable={true}
    >
      <Form form={form} layout="vertical">
        <Form.Item name="id" hidden>
          <NormalInput />
        </Form.Item>
        <Form.Item
          label="Policy Key"
          name="policy_key"
          rules={[{ required: true, message: 'Please input policy key' }]}
        >
          <NormalInput placeholder="Please input policy key" disabled={isEdit} />
        </Form.Item>
        <Form.Item label="Policy Name" name="remark">
          <NormalInput placeholder="Please input policy name" />
        </Form.Item>
        <Form.Item label="Default Policy Key" name="is_default">
          <NormalRadio options={GeoDefaultKeyOptions} />
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status">
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddGeoPolicyKeyDrawer;
