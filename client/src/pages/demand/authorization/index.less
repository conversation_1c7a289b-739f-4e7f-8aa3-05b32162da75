.card-container {
  border-radius: 6px;
  border: none;
  margin-right: 16px;
  :global {
    .ant-card-body {
      padding-top: 1px;
    }
    .ant-card-head-title {
      font-weight: 700;
      font-size: 16px;
      color: var(--text-color);
    }
    .ant-card-head {
      border-bottom: none;
    }
  }
}

.container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  // padding-top: 16px;
  .top {
    display: flex;
    align-items: center;
    padding-bottom: 16px;
    .top-left {
      width: 500px;
      :global {
        .ant-input-search .ant-btn {
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0 6px 6px 0px;
        }
      }
    }
    .top-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      > div {
        &:first-child {
          margin-right: 12px;
        }
      }
      :global {
        .ant-btn {
          border: none;
        }
      }
    }
  }
}
