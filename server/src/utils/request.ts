/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-05 14:21:10
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-05 17:38:14
 * @Description:
 */
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { getLogger } from '@/config/log4js';

export const post = async function (
  config: AxiosRequestConfig
): Promise<AxiosResponse> {
  if (!config.url) throw new Error('url is required');
  return await axios.post(config.url, config.data, {
    headers: config.headers || { 'Content-Type': 'application/json' }
  });
};

const request = axios.create({
  timeout: 120000
});
request.interceptors.response.use((resp) => {
  if (resp.status !== 200 || !resp.data) {
    return Promise.reject(resp);
  }
  return resp.data;
}, (err) => {
  getLogger('error').error(`request failed, err=[${JSON.stringify(err)}, ${err.message}]`);
  Promise.reject(err);
});
request.interceptors.request.use((req) => req);

export default request;
