/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-02 14:28:00
 * @Description:
 */
import InfoBar from '@/components/InfoBar';
import type { OperateRenderItem } from '@/components/OperateRender';
import OperateRender from '@/components/OperateRender';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { DemandAndSupplyStatusMap } from '@/constants';
import { SupplyBreadOptions, SupplySearchOption } from '@/constants/supply';
import { SupplyInfoTabs } from '@/constants/supply/info';
import { SupplyColumns, profileRadioColumn } from '@/constants/supply/supply-columns';
import { isBannerMultiSizeLimitTnTs } from '@/pages/demand/components/AddDemandDrawer';
import { getSupplyAuth } from '@/services/supply';
import { fetchData } from '@/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import React, { useEffect, useMemo, useState } from 'react';
import { history, useModel } from 'umi';
import AddSupplyModel from '../components/AddSupplyDrawer';
import styles from './index.less';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const currentTntId = initialState?.currentUser?.tnt_id ?? 0;

  const { dataSource: supplyList, reload, loading } = useModel('useSupplyListWithTesting');
  const { sellerPartnerList: partnerList, reload: reloadPartner, loading: partnerLoading } = useModel('usePartnerList');
  const [currentRow, setCurrentRow] = useState<SupplyAPI.SupplyListItem | undefined>(undefined);
  const [activeKey, setActiveKey] = useState('basic');
  const [supplyInfoTabs, setSupplyInfoTabs] = useState(SupplyInfoTabs);
  const handleEditSupply = (params: SupplyAPI.SupplyListItem) => {
    setCurrentSupply(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentSupply(undefined);
    setCurrentRow(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleEndpoint = (params: SupplyAPI.SupplyListItem) => {
    history.push(`/supply/publisher/endpoint?id=${params.seller_id}`, { row: params });
  };
  const handleOpenDetail = (params: SupplyAPI.SupplyListItem) => {
    history.push(`/supply/publisher/detail?id=${params.seller_id}`, { row: params });
  };
  const handleOpenSupplyAccount = (params: SupplyAPI.SupplyListItem) => {
    history.push(`/supply/publisher/account?id=${params.seller_id}&u_id=${params.user_id}&name=${params.seller_name}`, {
      row: params
    });
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditSupply,
      icon: <RixEngineFont type="edit" />,
      accessCode: 'updateSupplyAuth'
    },
    {
      label: 'Endpoint',
      onClick: handleEndpoint,
      icon: <RixEngineFont type="rix-endpoint" />,
      accessCode: 'SupplyEndpointPermission'
    },
    {
      label: 'Detail',
      onClick: handleOpenDetail,
      icon: <RixEngineFont type="rix-detail" />,
      accessCode: 'SupplyDetailPermission'
    },
    {
      label: 'Account',
      onClick: handleOpenSupplyAccount,
      icon: <RixEngineFont type="rix-account" />,
      accessCode: 'SupplyAccountPermission'
    }
  ];
  const tmpColumns2: ColumnProps<SupplyAPI.SupplyListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 150,
      fixed: 'right',
      onCell: () => {
        return {
          onClick: e => e.stopPropagation()
        };
      },
      render: (txt, params) => <OperateRender btnOptions={OperateOptions} params={params} />
    }
  ];

  // -----
  // custom
  const supplyColumns = useMemo(() => {
    const isCustom = currentTntId === 1059;
    const isBannerMultiSize = isBannerMultiSizeLimitTnTs(currentTntId);

    const CustomColumns = [...SupplyColumns];
    if (isCustom) {
      CustomColumns.push(profileRadioColumn);
    }

    // isBannerMultiSize 为 false，添加过滤项 banner_multi_size
    const filterItems: any[] = [!isBannerMultiSize && 'banner_multi_size'].filter(Boolean);

    return CustomColumns.filter(item => !filterItems.includes(item.dataIndex));
  }, [currentTntId]);

  const columns = [...supplyColumns, ...tmpColumns2];
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [supply, setCurrentSupply] = useState<SupplyAPI.SupplyListItem | undefined>(undefined);
  const { dataSource: integrationTypeList, reload: reloadIntegrationType } = useModel('useSellerIntegrationTypeList');
  const [searchOptions, setSearchOptions] = useState(SupplySearchOption);
  const [dataSource, setDataSource] = useState<SupplyAPI.SupplyListItem[]>([]);
  const [infoLoading, setInfoLoading] = useState(false);
  useEffect(() => {
    if (!supplyList) {
      reload();
    }
    if (!integrationTypeList) {
      reloadIntegrationType();
    }
    if (!partnerList?.length) {
      reloadPartner();
    }
  }, []);

  useEffect(() => {
    // 如果 supplyList 存在，则添加 cs_domain 字段
    if (supplyList) {
      const csDomain = initialState?.currentUser?.cs_domain || location.hostname;
      setDataSource(
        supplyList.map((item: SupplyAPI.SupplyListItem) => ({
          ...item,
          cs_domain: csDomain
        }))
      );
    } else {
      setDataSource([]);
    }
  }, [supplyList]);

  useEffect(() => {
    if (currentRow) {
      const tabs = [...SupplyInfoTabs];
      const baseIndex = tabs.findIndex(item => item.key === 'basic' && item.titleIcon);
      if (baseIndex !== -1) {
        if (!isBannerMultiSizeLimitTnTs(currentTntId)) {
          const children = tabs[baseIndex].children;
          const filterIndex = children?.findIndex(item => item.key === 'filter-and-compatibility-strategy') || -1;

          if (filterIndex !== -1 && children) {
            const filterSection = children[filterIndex];
            filterSection.columns = filterSection.columns?.filter(item => item.dataIndex !== 'banner_multi_size');
          }
        }

        tabs[baseIndex].titleIcon!.onClick = row => {
          setCurrentRow(undefined);
          handleEditSupply(row!);
        };
      }

      setSupplyInfoTabs(tabs);
      if (activeKey === 'authorization') {
        getSupplyAuthList();
      }
    }
  }, [currentRow, currentTntId]);

  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    if (integrationTypeList && integrationTypeList.length) {
      const tmp = integrationTypeList.map(item => {
        return {
          label: item.itg_name,
          value: item.id
        };
      });
      const index = options.findIndex(item => item.key === 'integration_type');
      if (index !== -1) {
        options[index].options = tmp;
      }
    }
    if (supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  }, [integrationTypeList, supplyList]);
  const normalEmptyRender = () => <span> </span>;

  const handleClickRow = (record: SupplyAPI.SupplyListItem) => {
    if (record.seller_id === currentRow?.seller_id) {
      setCurrentRow(undefined);
    } else {
      setCurrentRow(record);
    }
  };
  const getSupplyAuthList = () => {
    fetchData({
      setLoading: setInfoLoading,
      request: getSupplyAuth,
      params: { seller_id: currentRow?.seller_id },
      onSuccess: (data: SupplyAPI.SellerDemandAuth[]) => {
        if (data) {
          const index = supplyInfoTabs.findIndex(item => item.key === 'authorization');
          const tabs = [...supplyInfoTabs];
          if (index !== -1) {
            tabs[index].tableData = data.map(item => {
              return {
                auth_buyer_id: item.buyer_id,
                auth_buyer_name: item.buyer_name
              };
            });
          }
          setSupplyInfoTabs(tabs);
        }
      }
    });
  };
  const handleInfoTabChange = (activeKey: string) => {
    setActiveKey(activeKey);
    if (activeKey === 'authorization') {
      const index = supplyInfoTabs.findIndex(item => item.key === 'authorization');
      if (index !== -1) {
        getSupplyAuthList();
      }
    }
  };
  return (
    <PageContainer flexDirection="column" options={SupplyBreadOptions}>
      <FrontTable<SupplyAPI.SupplyListItem>
        pageTitle="Publisher List"
        searchOptions={searchOptions}
        isFold={true}
        loading={loading || partnerLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'seller_id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create Publisher',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
            accessCode: 'addSupplyAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={125}
        emptyRender={supplyList && supplyList.length ? normalEmptyRender : undefined}
        initialValues={{ status: [DemandAndSupplyStatusMap.Active, DemandAndSupplyStatusMap.Testing] }}
        onRow={record => {
          return {
            onClick: () => handleClickRow(record)
          };
        }}
        rowClassName={record => {
          return record.seller_id === currentRow?.seller_id ? styles['row-selected'] : '';
        }}
        allowSaveSearch={true}
      />
      <AddSupplyModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadSupply={reload}
        supply={supply}
        partnerList={partnerList}
      />
      <InfoBar<SupplyAPI.SupplyListItem>
        loading={infoLoading}
        dataSource={currentRow}
        title={currentRow?.seller_name || ''}
        tabs={supplyInfoTabs}
        width={600}
        handleClose={() => setCurrentRow(undefined)}
        defaultActiveKey={activeKey}
        handleTabChange={handleInfoTabChange}
      />
    </PageContainer>
  );
};

export default Page;
