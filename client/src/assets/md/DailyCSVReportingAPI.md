# RixEngine Daily CSV Reporting API
## Definition
* Endpoint: https://{host}.rpt.{domain}/report/api/csv/v1
* Request Method: POST
* Request Authorization: Please contact your account manager and add to request header. 
* Timezone: UTC+0

## Request
|     Name      |    Type    | Required | Description                    | Available Values |   Default   |
|:-------------:|:----------:|:--------:|:-------------------------------|:-----------------|:-----------:|
| date | Array\<string\> |   Yes    | date string array,format YYYY-MM-DD. | ['2024-11-18']       |     -    |

## Response
* All of the requests will be responded with HTTP
* Content-Type: application/json

### Response Schema
|  Attribute  |     Type      | Description                      |
|:-----------:|:-------------:|:------------------------------------------------------------|
| status.code |    integer    | Server response status, when it is zero, indicates that the server responded correctly to the request. When it is not zero, you can find specific error messages in status.msg. |
| status.msg  |    string     | Server response description.                                       |
| timestamp|string | UTC time, which is used to indicate the time when the server responds to the current request. |
|    data     | array or null | Server response content.    |
* status code see <a href='#Appendix'>Appendix</a>
* data details see  <a href='#Response Data Schema'>Response Data Schema</a>

### Response Data Schema
|   Attribute   |   Type    | Description                                              |
|:-------------:|:---------:|:---------------------------------------------------------|
|      date      |   string    | Date,format YYYY-MM-DD                                   |
|  path  |   string   | Download link for the corresponding report                                  |
|    expire_time    |  string  | Download link expiration time (1 hour)                                           |

### Example
```powershell
### POST
curl -H "x-authorization: {YOUR_AUTHORIZATION}" -H "Content-Type: application/json" \
-d '{"date": ["2024-11-18"]}' \
-X POST "https://{host}.rpt.{domain}/report/api/csv/v1"
```

```json
{
  "status": {
    "code": 0,
    "msg": "success"
  },
  "timestamp": "Wed Dec 03 07:20:12 +00:00 2024",
  "data": [
    {
      "path": "https://{host}.rpt.{domain}/report/api/file/666.csv",
      "date": "2024-11-18",
      "expire_time": "Wed Dec 03 07:20:12 +00:00 2024"
    }
  ]
}
```

## Appendix

#### Status Code
| Code       |       Message                                                |
| :-----: | :----------------------------------------------------------- |
|     0      | success                                                      |
|    1000    | system error                                                 |
|    1001    | invalid params.                                              |
|    2000    | token authorized failed.                                     |

#### Ad Format Schema
|Value|Label 
|:-----:|:------------------|
|1|Banner |
|2|Native  |
|3|Video    |
|4|Rewarded Video |

#### Platform Schema
|Value|Label 
|:-----:|:------------------|
|0|Unknown|
|1|iOS|
|2|Android|
|3|Other|
|4|Linux|
|5|MacOS|
|6|Windows|
|11|tvOS|
|12|Roku|
|13|Amazon|
|14|Microsoft|
|15|Samsung Smart TV|
|16|LG Smart TV|
|17|Sony Playstation|
|18|Vizio|
|19|Philips Smart TV|
|50|Tizen|
|51|KaiOS|

#### Countries
|Value|Label 
|:------:|:------------------|
|AFG|Afghanistan|
|ALB|Albania|
|DZA|Algeria|
|ASM|American Samoa|
|AND|Andorra|
|AGO|Angola|
|AIA|Anguilla|
|ATA|Antarctica|
|ATG|Antigua and Barbuda|
|ARG|Argentina|
|ARM|Armenia|
|ABW|Aruba|
|AUS|Australia|
|AUT|Austria|
|AZE|Azerbaijan|
|BHS|Bahamas|
|BHR|Bahrain|
|BGD|Bangladesh|
|BRB|Barbados|
|BLR|Belarus|
|BEL|Belgium|
|BLZ|Belize|
|BEN|Benin|
|BMU|Bermuda|
|BTN|Bhutan|
|BOL|Bolivia|
|BIH|Bosnia and Herzegovina|
|BWA|Botswana|
|BRA|Brazil|
|IOT|British Indian Ocean Territory|
|VGB|British Virgin Islands|
|BRN|Brunei|
|BGR|Bulgaria|
|BFA|Burkina Faso|
|BDI|Burundi|
|KHM|Cambodia|
|CMR|Cameroon|
|CAN|Canada|
|CPV|Cape Verde|
|CYM|Cayman Islands|
|CAF|Central African Republic|
|TCD|Chad|
|CHL|Chile|
|CHN|China|
|CXR|Christmas Island|
|CCK|Cocos Islands|
|COL|Colombia|
|COM|Comoros|
|COK|Cook Islands|
|CRI|Costa Rica|
|HRV|Croatia|
|CUB|Cuba|
|CUW|Curacao|
|CYP|Cyprus|
|CZE|Czech Republic|
|COD|Democratic Republic of the Congo|
|DNK|Denmark|
|DJI|Djibouti|
|DMA|Dominica|
|DOM|Dominican Republic|
|TLS|East Timor|
|ECU|Ecuador|
|EGY|Egypt|
|SLV|El Salvador|
|GNQ|Equatorial Guinea|
|ERI|Eritrea|
|EST|Estonia|
|ETH|Ethiopia|
|FLK|Falkland Islands|
|FRO|Faroe Islands|
|FJI|Fiji|
|FIN|Finland|
|FRA|France|
|PYF|French Polynesia|
|GAB|Gabon|
|GMB|Gambia|
|GEO|Georgia|
|DEU|Germany|
|GHA|Ghana|
|GIB|Gibraltar|
|GRC|Greece|
|GRL|Greenland|
|GRD|Grenada|
|GUM|Guam|
|GTM|Guatemala|
|GGY|Guernsey|
|GIN|Guinea|
|GNB|Guinea-Bissau|
|GUY|Guyana|
|HTI|Haiti|
|HND|Honduras|
|HKG|Hong Kong(China)|
|HUN|Hungary|
|ISL|Iceland|
|IND|India|
|IDN|Indonesia|
|IRN|Iran|
|IRQ|Iraq|
|IRL|Ireland|
|IMN|Isle of Man|
|ISR|Israel|
|ITA|Italy|
|CIV|Ivory Coast|
|JAM|Jamaica|
|JPN|Japan|
|JEY|Jersey|
|JOR|Jordan|
|KAZ|Kazakhstan|
|KEN|Kenya|
|KIR|Kiribati|
|XKX|Kosovo|
|KWT|Kuwait|
|KGZ|Kyrgyzstan|
|LAO|Laos|
|LVA|Latvia|
|LBN|Lebanon|
|LSO|Lesotho|
|LBR|Liberia|
|LBY|Libya|
|LIE|Liechtenstein|
|LTU|Lithuania|
|LUX|Luxembourg|
|MAC|Macau|
|MKD|Macedonia|
|MDG|Madagascar|
|MWI|Malawi|
|MYS|Malaysia|
|MDV|Maldives|
|MLI|Mali|
|MLT|Malta|
|MHL|Marshall Islands|
|MRT|Mauritania|
|MUS|Mauritius|
|MYT|Mayotte|
|MEX|Mexico|
|FSM|Micronesia|
|MDA|Moldova|
|MCO|Monaco|
|MNG|Mongolia|
|MNE|Montenegro|
|MSR|Montserrat|
|MAR|Morocco|
|MOZ|Mozambique|
|MMR|Myanmar|
|NAM|Namibia|
|NRU|Nauru|
|NPL|Nepal|
|NLD|Netherlands|
|ANT|Netherlands Antilles|
|NCL|New Caledonia|
|NZL|New Zealand|
|NIC|Nicaragua|
|NER|Niger|
|NGA|Nigeria|
|NIU|Niue|
|PRK|North Korea|
|MNP|Northern Mariana Islands|
|NOR|Norway|
|OMN|Oman|
|PAK|Pakistan|
|PLW|Palau|
|PSE|Palestine|
|PAN|Panama|
|PNG|Papua New Guinea|
|PRY|Paraguay|
|PER|Peru|
|PHL|Philippines|
|PCN|Pitcairn|
|POL|Poland|
|PRT|Portugal|
|PRI|Puerto Rico|
|QAT|Qatar|
|COG|Republic of the Congo|
|REU|Reunion|
|ROU|Romania|
|RUS|Russia|
|RWA|Rwanda|
|BLM|Saint Barthelemy|
|SHN|Saint Helena|
|KNA|Saint Kitts and Nevis|
|LCA|Saint Lucia|
|MAF|Saint Martin|
|SPM|Saint Pierre and Miquelon|
|VCT|Saint Vincent and the Grenadines|
|WSM|Samoa|
|SMR|San Marino|
|STP|Sao Tome and Principe|
|SAU|Saudi Arabia|
|SEN|Senegal|
|SRB|Serbia|
|SYC|Seychelles|
|SLE|Sierra Leone|
|SGP|Singapore|
|SXM|Sint Maarten|
|SVK|Slovakia|
|SVN|Slovenia|
|SLB|Solomon Islands|
|SOM|Somalia|
|ZAF|South Africa|
|KOR|South Korea|
|SSD|South Sudan|
|ESP|Spain|
|LKA|Sri Lanka|
|SDN|Sudan|
|SUR|Suriname|
|SJM|Svalbard and Jan Mayen|
|SWZ|Swaziland|
|SWE|Sweden|
|CHE|Switzerland|
|SYR|Syria|
|TWN|Taiwan(China)|
|TJK|Tajikistan|
|TZA|Tanzania|
|THA|Thailand|
|TGO|Togo|
|TKL|Tokelau|
|TON|Tonga|
|TTO|Trinidad and Tobago|
|TUN|Tunisia|
|TUR|Turkey|
|TKM|Turkmenistan|
|TCA|Turks and Caicos Islands|
|TUV|Tuvalu|
|VIR|U.S. Virgin Islands|
|UGA|Uganda|
|UKR|Ukraine|
|ARE|United Arab Emirates|
|GBR|United Kingdom|
|USA|United States|
|URY|Uruguay|
|UZB|Uzbekistan|
|VUT|Vanuatu|
|VAT|Vatican|
|VEN|Venezuela|
|VNM|Vietnam|
|WLF|Wallis and Futuna|
|ESH|Western Sahara|
|YEM|Yemen|
|ZMB|Zambia|
|ZWE|Zimbabwe|

<div style="text-align: center;">
    <span style="color: #4e6e8e; ">Copyright © 2024 RixEngine. All Rights Reserved</span>
</div>
