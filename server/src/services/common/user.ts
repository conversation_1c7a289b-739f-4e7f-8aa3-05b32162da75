/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:45:32
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-10 10:27:36
 * @Description:ion.tnt_id
 */

import { StatusMap, UserType } from '@/constants';
import { RoleTypeToNumber, StatusType } from '@/constants/permission';
import { commonModel, userModel } from '@/models';
import { UserAPI } from '@/types/user';
import { getCsDomain, md5, sendEmail, getConfig } from '@/utils';

const { encryptionStr } = getConfig();

class UserService {
  async getSwitchAccount(options: {
    switch_user_id: number;
    special_user_id: number;
  }) {
    const { switch_user_id, special_user_id } = options;

    // 1. 获取 特殊用户信息
    const [special_user_info] =
      (await userModel.getUserInfoByUserId(special_user_id)) || [];
    if (!special_user_info || special_user_info.sa_status !== StatusType.Active)
      return null; // 如果特殊用户信息不存在，直接返回 null

    const { linked_user_ids = '' } = special_user_info;
    const linked_user_ids_arr = linked_user_ids
      .split(',')
      .filter(Boolean)
      .map(Number);

    // 2. 判断 特殊用户 的 linked_user_ids_arr 是否包含 switch_user_id
    // 如果 switch_user_id 不在 linked_user_ids_arr 中，直接返回 null
    if (!linked_user_ids_arr.includes(switch_user_id)) return null;

    // 3. 获取 切换用户信息
    // 如果切换用户信息不存在，直接返回 null
    const [switchUserInfo] =
      (await userModel.getUserInfoByUserId(switch_user_id)) || [];
    if (!switchUserInfo) return null;

    // 4. 获取 切换用户详情信息
    const detailUserInfo =
      (await this.getOneUser({
        account_name: switchUserInfo.account_name,
        password: switchUserInfo.password,
        cs_domain: switchUserInfo.cs_domain
      })) || {};

    return {
      ...detailUserInfo,
      special_user_id
    }; // 返回切换用户信息
  }

  async getUserLinkList(special_user_id: number) {
    const [special_user_info] =
      (await userModel.getUserInfoByUserId(special_user_id)) || [];
    if (!special_user_info || special_user_info.sa_status !== StatusType.Active)
      return [];

    const { linked_user_ids = '' } = special_user_info;
    const linked_user_ids_arr = linked_user_ids
      .split(',')
      .filter(Boolean)
      .map(Number);

    const user_link_list =
      (await userModel.getUserLinkList(linked_user_ids_arr)) || [];

    return user_link_list;
  }

  async confirmPassword(
    option: UserAPI.ConfirmPasswordParams
  ): Promise<UserAPI.UserListItem[]> {
    return await userModel.confirmPassword(option);
  }

  async getOneUser(options: UserAPI.LoginParams) {
    const data = await userModel.getOneUser(options);
    if (data.length) {
      let user = data[0];
      const { user_id, tnt_id, type, role_id, sa_status } = user;
      let seller_id: any[] = [];
      let buyer_id: any[] = [];
      let cus_status = 2; // 默认关闭
      if ([UserType.Demand, UserType.Supply].includes(type)) {
        const result = await commonModel.getBuyerOrSellerByID({
          user_id,
          tnt_id,
          type
        });
        // 改成数组存储 方便统一 其他地方得做兼容
        if (result?.length) {
          seller_id = result[0].seller_id ? [result[0].seller_id] : [];
          buyer_id = result[0].buyer_id ? [result[0].buyer_id] : [];
          cus_status = result[0].cus_status || 2;
        }
      }
      if (
        type === UserType.Partner &&
        role_id === RoleTypeToNumber['Supply Partner']
      ) {
        const result = await commonModel.getSellerParentByUsrID({
          user_id,
          tnt_id
        });
        seller_id = result.map((v: any) => v.seller_id);
        const cur_status_arr = result.map((v: any) => v.cus_status);
        cus_status = cur_status_arr.includes(StatusMap.Active)
          ? StatusMap.Active
          : StatusMap.Paused;
      }

      if (
        type === UserType.Partner &&
        role_id === RoleTypeToNumber['Demand Partner']
      ) {
        const result = await commonModel.getBuyerParentByUsrID({
          user_id,
          tnt_id
        });
        buyer_id = result.map((v: any) => v.buyer_id);
      }

      // 设置特殊用户id
      if (tnt_id === 1083 && sa_status === StatusType.Active) {
        user.special_user_id = user_id;
      }

      user = {
        ...user,
        seller_id: [...new Set(seller_id)],
        buyer_id: [...new Set(buyer_id)],
        cus_status
      };
      return user;
    }
    return null;
  }

  async resetPasswordByUserId(
    option: UserAPI.ResetPasswordParams
  ): Promise<boolean> {
    return await userModel.resetPasswordByUserId(option);
  }

  async isAccountNameExists(
    params: UserAPI.validAccountNameExistsParams
  ): Promise<any[]> {
    return await userModel.isAccountNameExists(params);
  }

  async sendEmailToUser(params: any) {
    return await userModel.sendEmailToUser(params);
  }

  async getDashboardUser(params: any) {
    return await userModel.getDashboardUser(params);
  }

  async editDashboardUser(params: any) {
    return await userModel.editDashboardUser(params);
  }

  async updateAccountName(params: UserAPI.EditUserAccountParams) {
    const {
      send_email = [],
      brand,
      host_prefix,
      pv_domain,
      user_email = '',
      to_self,
      account_name
    } = params;
    const flag = await userModel.updateAccountName(params);
    if (flag) {
      const email = [
        ...send_email,
        to_self === StatusType.Active ? user_email : ''
      ]
        .filter(v => v && v.trim())
        .join(',');
      if (email.length) {
        const subject_str = 'New Account';
        const cs_domain = getCsDomain(pv_domain, host_prefix);
        const TestEnv = ['dev', 'test', 'development'];
        const env = TestEnv.includes(process.env.NODE_ENV || 'prod')
          ? `(${process.env.NODE_ENV})`
          : '';
        const domain = `https://${cs_domain}`;
        sendEmail({
          from: `${brand || 'RixEngine'}<<EMAIL>>`,
          to: `${email || ''}`,
          subject: `${env}${brand} ${subject_str} (${account_name})`,
          html: `
                            <p>Congratulations,${
                              brand || 'RixEngine'
                            } Account had been successfully changed!</p>
                            <p>Platform: ${domain}</p>
                            <p>Username: ${account_name}</p>
                `
        });
      }
    }
    return flag;
  }

  async resetPassword(params: UserAPI.ResetUserPwdParams) {
    const {
      send_email = [],
      brand,
      host_prefix,
      pv_domain,
      user_email = '',
      to_self,
      password,
      account_name
    } = params;
    const flag = await userModel.resetPassword({
      ...params,
      password: md5(encryptionStr + password)
    });
    if (flag) {
      const email = [
        ...send_email,
        to_self === StatusType.Active ? user_email : ''
      ]
        .filter(v => v && v.trim())
        .join(',');
      if (email.length) {
        const subject_str = 'New Password';
        const cs_domain = getCsDomain(pv_domain, host_prefix);
        const TestEnv = ['dev', 'test', 'development'];
        const env = TestEnv.includes(process.env.NODE_ENV || 'prod')
          ? `(${process.env.NODE_ENV})`
          : '';
        const domain = `https://${cs_domain}`;
        sendEmail({
          from: `${brand || 'RixEngine'}<<EMAIL>>`,
          to: `${email || ''}`,
          subject: `${env}${brand} ${subject_str} (${account_name})`,
          html: `
                            <p>Congratulations,${
                              brand || 'RixEngine'
                            } Account password had been successfully changed!</p>
                            <p>Platform: ${domain}</p>
                            <p>Username: ${account_name}</p>
                            <p>Password: ${password}</p>
                        `
        });
      }
    }
    return flag;
  }
}

export const userService = new UserService();
