/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:09:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 15:54:59
 * @Description:
 */

import Router from 'koa-router';
import { demandController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/demand' });

const routers = router
  .post('/addDemand', demandController.addDemand)
  .post('/updateDemand', validTesting, demandController.updateDemand)
  .post('/getDemandList', demandController.getDemandList)
  .post('/getDemandListWithTesting', demandController.getDemandListWithTesting)
  .post('/getDemandEndpoint', demandController.getDemandEndpoint)
  .post('/setDemandEndpoint', validTesting, demandController.setDemandEndpoint)
  .post('/getPretargetCampaign', demandController.getPretargetCampaign)
  .post(
    '/updatePretargetCampaign',
    validTesting,
    demandController.updatePretargetCampaign
  )
  .post(
    '/updatePretargetStatus',
    validTesting,
    demandController.updatePretargetStatus
  )
  .post(
    '/addPretargetCampaign',
    validTesting,
    demandController.addPretargetCampaign
  )
  .post('/getDemandAuth', demandController.getDemandAuth)
  .post('/setDemandAuth', validTesting, demandController.setDemandAuth);

export default routers;
