/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-26 20:44:52
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-05-26 20:46:47
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllSupplyPlacement } from '@/services/api';

export default function useAllPlacementsList() {
  const { data, run, loading, error } = useCustomRequest(getAllSupplyPlacement, {
    cacheKey: 'all-plm-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
