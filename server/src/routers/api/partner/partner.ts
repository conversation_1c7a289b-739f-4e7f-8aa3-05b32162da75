/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 15:52:26
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 15:52:28
 * @Description:
 */

import Router from 'koa-router';
import { partnerCtrl } from '@/controllers';

const router = new Router({ prefix: '/partner' });

const routers = router
  .post('/addPartner', partnerCtrl.addPartner)
  .post('/updatePartner', partnerCtrl.updatePartner)
  .post('/getPartnerList', partnerCtrl.getPartnerList)
  .post('/getAccount', partnerCtrl.getPartnerAccount)
  .post('/createAccount', partnerCtrl.createPartnerAccount);

export default routers;
