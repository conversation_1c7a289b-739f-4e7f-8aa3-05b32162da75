import { RESULT } from '@/constants';
import {
  AddGeoPolicyKeyRelationSchema,
  AddGeoPolicyKeySchema,
  UpdateGeoPolicyKeyRelationSchema,
  UpdateGeoPolicyKeySchema
} from '@/schema/strategy/geo-policy';
import { geoPolicyService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class GeoPolicyCtrl {
  @validateBody(UpdateGeoPolicyKeyRelationSchema)
  async updateGeoPolicyKeyRelation(ctx: Context) {
    let result = { ...RESULT };

    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    const res = await geoPolicyService.updateGeoPolicyKeyRelation(formData);
    if (res) {
      result = getCtxResult('SUCCESS', res);
    }
    ctx.body = result;
  }

  @validateBody(AddGeoPolicyKeyRelationSchema)
  async addGeoPolicyKeyRelation(ctx: Context) {
    let result = { ...RESULT };

    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    const res = await geoPolicyService.addGeoPolicyKeyRelation(formData);
    if (res) {
      result = getCtxResult('SUCCESS', res);
    }
    ctx.body = result;
  }

  async getGeoPolicyKeyRelationList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;

    const list = await geoPolicyService.getGeoPolicyKeyRelationList(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(UpdateGeoPolicyKeySchema)
  async updateGeoPolicyKey(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    try {
      const res = await geoPolicyService.updateGeoPolicyKey(formData);
      if (res) {
        result = getCtxResult('SUCCESS', res);
      }
    } catch (error: any) {
      if (error.code === 'ER_DUP_ENTRY') {
        result = getCtxResult('POLICY_KEY_EXISTS');
      }
    }
    ctx.body = result;
  }

  @validateBody(AddGeoPolicyKeySchema)
  async addGeoPolicyKey(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;

    try {
      const res = await geoPolicyService.addGeoPolicyKey(formData);
      if (res) {
        result = getCtxResult('SUCCESS', res);
      }
    } catch (error: any) {
      if (error.code === 'ER_DUP_ENTRY') {
        result = getCtxResult('POLICY_KEY_EXISTS');
      }
    }
    ctx.body = result;
  }

  async getGeoPolicyKeyList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;

    const list = await geoPolicyService.getGeoPolicyKeyList(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }
}

export default new GeoPolicyCtrl();
