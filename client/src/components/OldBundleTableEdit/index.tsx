/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 11:15:33
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 17:41:03
 * @Description:
 */

import styles from './index.less';
import { useEffect, useRef, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Spin } from 'antd';

import NormalModal from '@/components/Modal/NormalModal';
import BundleInput from './BundleInput';
import LongContentMore from '../LongContentMore';
import useSizeHook from '@/hooks/useSizeHook';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';

type BundleTableEditProps = {
  onValueChange: (val: string[]) => void;
  editTitle: string;
  editTips: string;
  defaultList: string[];
  deleteAllTitle: string;
  deleteAllTips: string;
  disabled?: boolean;
  defaultShowNum?: number; // 默认显示数据
  open?: boolean; // 是否显示
  contentMaxHeight?: number | string; // 内容最大高度
  editSingle?: boolean; // 是否是单个编辑
  isBidFloor?: boolean; // 是否是底价框
  handleAddFloor?: (map: { [key: string]: number }) => void;
  defaultFloorMap?: { [key: string]: number };
  loading?: boolean;
};

export default function BundleTableEdit({
  onValueChange,
  editTitle,
  editTips,
  defaultList,
  deleteAllTitle,
  deleteAllTips,
  disabled = false,
  defaultShowNum = 100,
  open = true,
  contentMaxHeight = 'calc(100vh - 500px)',
  editSingle = false,
  isBidFloor = false,
  handleAddFloor,
  defaultFloorMap,
  loading = false
}: BundleTableEditProps): JSX.Element {
  const { size } = useSizeHook({ width: 0, height: 0 });
  const divRef = useRef<HTMLDivElement | null>(null);
  const [contentList, setContentList] = useState<string[]>([]);
  const [bundleVisible, setBundleVisible] = useState(false);
  // 显示数据
  const [showList, setShowList] = useState<string[]>([]);
  const [maxWidth, setMaxWidth] = useState(376);
  const [maxHeight, setMaxHeight] = useState<string | number>(contentMaxHeight);
  const [isEdit, setIsEdit] = useState(false);
  const [currentEditBundle, setCurrentEditBundle] = useState<string>('');
  const [floorMap, setFloorMap] = useState<{ [key: string]: number }>({});
  useEffect(() => {
    const offsetWidth = divRef.current?.offsetWidth || 376;
    setMaxWidth(offsetWidth);
  }, [divRef.current]);

  useEffect(() => {
    const minHeight = 700; // 至少显示200px的高度
    if (size.height < minHeight) {
      setMaxHeight(200);
    } else {
      setMaxHeight(contentMaxHeight);
    }
    const offsetWidth = divRef.current?.offsetWidth || 376;
    setMaxWidth(offsetWidth);
  }, [size]);

  useEffect(() => {
    // 需要倒序显示
    const data = [...defaultList].reverse();
    setContentList(data);
  }, [defaultList]);
  useEffect(() => {
    if (defaultFloorMap) {
      setFloorMap(defaultFloorMap);
    }
  }, [defaultFloorMap]);
  const handleSaveBundle = (val: string[] | any) => {
    setContentList(val);
    // 需要翻转一遍
    const data = [...val].reverse();
    onValueChange(data);
  };

  const handleCloseBundle = () => {
    console.log('close');
    setCurrentEditBundle('');
    setBundleVisible(false);
    setIsEdit(false);

    // if(isEdit) {
    //   setContentList([]);
    // }
  };

  const handleOpenBundle = () => {
    if (!disabled) {
      setBundleVisible(true);
    }
  };

  const handleClearBundle = () => {
    if (contentList.length && !disabled) {
      NormalModal.confirm({
        title: deleteAllTitle,
        content: deleteAllTips,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          // 清空数据
          setContentList([]);
          onValueChange([]);
          setFloorMap({});
          handleAddFloor?.({});
        },
        okButtonProps: {
          danger: true
        }
      });
    }
  };
  const handleDeleteBundle = (item: string) => {
    if (!disabled) {
      NormalModal.confirm({
        title: 'Tips',
        content: `Are you sure to delete  ”${item}“ ?`,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          console.log('delete', floorMap);
          const arr = contentList.filter(val => val !== item);
          delete floorMap[item];
          setFloorMap(floorMap);
          handleAddFloor?.(floorMap);
          // 删除数据
          setContentList(arr);
          const data = [...arr].reverse();
          onValueChange(data);
        },
        okButtonProps: {
          danger: true
        }
      });
    }
  };

  const handleEditBundle = (item: string) => {
    setIsEdit(true);
    setCurrentEditBundle(item);
    // isBidFloor && setFloorMap()
  };
  const handleShowDataChange = (val: string[] | number[]) => {
    setShowList(val as string[]);
  };

  const onBidFloorChange = (map: { [key: string]: number }) => {
    setFloorMap(map);
    handleAddFloor?.(map);
  };

  return (
    <div className={styles['content-container']} ref={divRef}>
      <Spin spinning={loading}>
        <div className={styles['content-btn']}>
          <RixEngineFont
            type="rix-edit"
            onClick={() => handleOpenBundle()}
            style={{ fontSize: 18 }}
            className={disabled ? styles['svg-disabled'] : styles['svg-default']}
          />
          <RixEngineFont
            type="rix-trash"
            onClick={() => handleClearBundle()}
            style={
              contentList.length > 0 ? { cursor: 'pointer', fontSize: 20 } : { cursor: 'not-allowed', fontSize: 22 }
            }
            className={!disabled && contentList.length > 0 ? styles['svg-trash'] : styles['svg-disabled']}
          />
        </div>
        {contentList.length > 0 ? (
          <LongContentMore
            contentMaxHeight={maxHeight}
            defaultShowNum={defaultShowNum}
            open={open}
            onShowDataChange={handleShowDataChange}
            dataSource={contentList}
            style={{ maxWidth: maxWidth - 60 - 45 }}
          >
            {isBidFloor && (
              <div className={isBidFloor ? styles['bid-content-body-header'] : styles['content-body']}>
                <div className={styles['left']} style={{ width: 60 }}>
                  {/* 倒序显示 */}
                  <span></span>
                </div>
                <div className={styles['center']} style={{ width: maxWidth - 60 - 45 }}>
                  <span style={{ maxWidth: maxWidth - 60 - 45 }} className={styles['ellipsis']}>
                    Country
                  </span>
                  <span style={{ maxWidth: maxWidth - 60 - 45 }} className={styles['ellipsis']}>
                    Bid Floor
                  </span>
                </div>
                {/* 占位 */}
                <div style={{ width: 45, fontSize: 20 }}></div>
                <div style={{ width: 45, fontSize: 20 }}></div>
                {/* <RixEngineFont
                  type=""
                  style={{ width: 45, fontSize: 20 }}
                  className={!disabled ? styles['svg-trash'] : styles['svg-disabled']}
                  disabled={disabled}
                />
                {editSingle && (
                  <RixEngineFont
                    type=""
                    style={{ width: 45, fontSize: 20 }}
                    className={!disabled ? styles['svg-trash'] : styles['svg-disabled']}
                    disabled={disabled}
                  />
                )} */}
              </div>
            )}
            {showList.map((item, index) => (
              <div className={isBidFloor ? styles['bid-content-body'] : styles['content-body']} key={index}>
                <div className={styles['left']} style={{ width: 60 }}>
                  {/* 倒序显示 */}
                  <span>{index + 1}</span>
                </div>
                <div className={styles['center']} style={{ width: maxWidth - 60 - 45 }}>
                  <HoverTooltip title={item} maxWidth={maxWidth - 60 - 45} placement="top">
                    <span style={{ maxWidth: maxWidth - 60 - 45 }} className={styles['ellipsis']}>
                      {item}
                    </span>
                  </HoverTooltip>
                  {isBidFloor && (
                    <HoverTooltip title={item} maxWidth={maxWidth - 60 - 45} placement="top">
                      <span style={{ maxWidth: maxWidth - 60 - 45 }} className={styles['ellipsis']}>
                        {floorMap![item]}
                      </span>
                    </HoverTooltip>
                  )}
                </div>

                {editSingle && (
                  <RixEngineFont
                    type="rix-edit"
                    onClick={() => handleEditBundle(item)}
                    style={{ cursor: 'pointer', width: 45, fontSize: 20 }}
                    className={!disabled ? styles['svg-default'] : styles['svg-disabled']}
                    disabled={disabled}
                  />
                )}
                <RixEngineFont
                  type="rix-trash"
                  onClick={() => handleDeleteBundle(item)}
                  style={{ cursor: 'pointer', width: 45, fontSize: 20 }}
                  className={!disabled ? styles['svg-trash'] : styles['svg-disabled']}
                  disabled={disabled}
                />
              </div>
            ))}
          </LongContentMore>
        ) : (
          <div className={styles['content-empty']}>
            <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
            <span>No Data</span>
          </div>
        )}
      </Spin>

      <BundleInput
        visible={bundleVisible}
        handleCancel={handleCloseBundle}
        handleOk={handleSaveBundle}
        defaultList={contentList}
        title={editTitle}
        tips={editTips}
        validator={val => !!val}
        isReverse={true}
        isEdit={isEdit}
        currentEditBundle={currentEditBundle}
        isBidFloor={isBidFloor}
        onBidFloorChange={onBidFloorChange}
        floorMap={floorMap}
        setFloorMap={setFloorMap}
      ></BundleInput>
    </div>
  );
}
