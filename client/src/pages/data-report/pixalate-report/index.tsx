/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-10-09 17:36:19
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-14 12:01:51
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { useModel } from '@umijs/max';
import BackTable from '@/components/Table/BackTable';
import {
  PixalateAllColumns, PixalateBreadOptions, PixalateChangeableColumns,
  DefaultMetrics, DefaultDimension, DefaultColumnKeys, PixalateSearchOption,
  PixalateCheckboxUniqueKeyOptions
} from '@/constants/data-report/pixalate-report';
import PageContainer from '@/components/RightPageContainer';
import type { SearchResultItem, TopBarSearchItem } from '@/components/TopBar';
import { fetchData, downloadCsv } from '@/utils';
import { ColumnProps } from 'antd/lib/table';
import moment, { Moment } from 'moment';
import { SorterResult } from 'antd/lib/table/interface';
import { getPixalateReportList, downloadPixalateReport } from '@/services/api';

const Page: React.FC = () => {
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyList');
  const [columns, setColumns] = useState<ColumnProps<DashboardAPI.PixalateReportItem>[]>(PixalateChangeableColumns);
  const [searchOptions, setSearchOptions] = useState(PixalateSearchOption);
  const [dataSource, setDataSource] = useState<API.BackResult<DashboardAPI.PixalateReportItem>>({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<SorterResult<DashboardAPI.PixalateReportItem>>({
    columnKey: 'day',
    order: 'descend'
  });
  const [allColumns, setAllColumns] = useState<ColumnProps<DashboardAPI.PixalateReportItem>[]>(PixalateAllColumns);
  // 显示那些列
  const [columnKeys, setColumnKeys] = useState([...DefaultColumnKeys, ...DefaultMetrics]);

  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
  }, []);

  useEffect(() => {
    if (Array.isArray(demandList) && Array.isArray(supplyList)) {
      handleSearchOptions(true);
    } else {
      handleSearchOptions(false);
    }
  }, [demandList, supplyList]);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<DashboardAPI.PixalateReportItem>[] = PixalateAllColumns.map(v => {
        return {
          ...v,
          sortOrder: v.sorter && sortedInfo.columnKey === v.dataIndex ? sortedInfo.order : null
        };
      });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter(v => columnKeys.includes((v.key as string) || (v.dataIndex as string)));
      setColumns(tmp);
    }
  };

  // 默认的值
  const handleSearchOptions = (isFetch: boolean) => {
    const options: TopBarSearchItem[] = PixalateSearchOption.map(v => ({ ...v }));
    if (Array.isArray(demandList)) {
      const dOptions: any[] = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (Array.isArray(supplyList)) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    const index = options.findIndex(item => item.key === 'date');
    if (index !== -1) {
      const start_date = moment().subtract(3, 'day');
      const end_date = moment().subtract(1, 'day');
      options[index].value = [start_date, end_date];
      const start_date_str = start_date.format('YYYYMMDD');
      const end_date_str = end_date.format('YYYYMMDD');
      const params = { start: 0, end: 50, start_date: start_date_str, end_date: end_date_str, dimension: ['day'] };
      setDefaultParams(params);
      const tmp = {
        date: [start_date, end_date],
        dimension: [...DefaultDimension]
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
      if (isFetch) {
        getTableData(params);
      }
    }
    setSearchOptions(options);
  };

  const handleExport = (
    columns: ColumnProps<DashboardAPI.PixalateReportItem>[],
    sourceData?: API.BackResult<DashboardAPI.PixalateReportItem>
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map(item => {
      return {
        label: item.title,
        value: item.dataIndex
      };
    });
    const data = sourceData?.data || dataSource.data;
    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(item => item.key === 'dimension' && Array.isArray(item.value) && item.value.length > 0);
    const dim: any[] = dimension?.value || [];
    const column_keys = [...dim, ...DefaultMetrics];
    setColumnKeys(column_keys);
  };
  const handleSearchChange = (start: number, end: number, val: SearchResultItem[]) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params });
  };

  const handleGetSearchParams = (start: number, end: number, val: SearchResultItem[]) => {
    const tmp = val.filter(
      (item) => (Array.isArray(item.value) && item.value.length > 0) || (!Array.isArray(item.value) && item.value)
    );
    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach((item) => {
      if (item.key === 'app_bundle_id') {
        if (Array.isArray(item.value) && item.value.length) {
          params[item.key] = item.value.filter((v: string) => v && v.trim());
        }
      } else if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map((v) => v.format('YYYYMMDD'));
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else {
        params[item.key] = item.value;
      }
    });
    return params;
  };

  const onSuccess = (
    data: API.BackResult<DashboardAPI.PixalateReportItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.PixalateReportItem>[]
  ) => {
    const tmp = data.data.map(item => {
      item.sivt_imp_rate = item.sivt_imp_rate || '0';
      item.givt_imp_rate = item.givt_imp_rate || '0';
      if (item.buyer_id && demandList) {
        const buyer = demandList.find((v: any) => v.buyer_id === +item.buyer_id);
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
        item.buyer_id = item.buyer;
      }
      if (item.seller_id && supplyList) {
        const seller = supplyList.find((v: { seller_id: number }) => v.seller_id === +item.seller_id);
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
        item.seller_id = item.seller;
      }
      return item;
    });
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key
    };
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.PixalateReportItem>[]
  ) => {
    const { order, order_key } = getOrderAndKey();
    const params = {
      start: 0,
      end: 50,
      order_key,
      order,
      ...value
    };

    fetchData({
      setLoading,
      request: getPixalateReportList,
      params,
      onSuccess: data => onSuccess(data, isDownloadAll, columns)
    });
  };

  const handleDownloadAll = (data: any, columns: ColumnProps<DashboardAPI.PixalateReportItem>[]) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmp = {
      ...params,
      order_key,
      order
    };
    fetchData({
      setLoading,
      request: downloadPixalateReport,
      params: tmp,
      onSuccess: data => onSuccess({ data, total: 0 }, true, columns)
    });
  };

  const handleSortChange = (page: number, size: number, search: SearchResultItem[], sorter: any) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });
    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['day'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    getTableData(params);
  };

  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate > moment().startOf('day') ||
      currentDate < moment('20230927')
    );
  };

  const handleSearchValueChange = (changeValue: any) => {
    const dimension = changeValue?.dimension;
    if (!dimension) return;

    const currentSortColumn = sortedInfo.columnKey;
    const targetColumn = dimension.includes('month')
      ? 'month'
      : dimension.includes('day')
      ? 'day'
      : null;

    if (
      targetColumn &&
      currentSortColumn !== targetColumn &&
      (currentSortColumn === 'day' || currentSortColumn === 'month')
    ) {
      setSortedInfo({
        columnKey: targetColumn,
        order: sortedInfo.order,
      });
    }
  };

  return (
    <PageContainer flexDirection="column" options={PixalateBreadOptions}>
      <BackTable<DashboardAPI.PixalateReportItem>
        pageTitle="Tool1 Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        handleSearchValueChange={handleSearchValueChange}
        checkboxUniqueKeyOptions={PixalateCheckboxUniqueKeyOptions}
        tableData={dataSource}
        loading={loading}
        getTableData={getTableData}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        defaultBoxItemWidth={160}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        defaultDates={defaultSearchValue.date}
        dateRangeKeys={[
          'Yesterday',
          '3 Days',
          '7 Days',
          'This Month',
          'Last Month',
          'Last 3 Months',
        ]}
        downloadUrl="/fraud_type_document.csv"
        downloadUrlTips="Fraud Type Document"
      />
    </PageContainer>
  );
};

export default Page;
