/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-22 20:17:55
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-23 17:03:31
 * @Description: 文档语言切换
 */

import React, { useEffect, useState } from 'react';
import { Dropdown } from 'antd';
import styles from './index.less';
import { CheckOutlined, DownOutlined } from '@ant-design/icons';
import { LangOptions, LangMap } from '@/constants/base/lang';
import { history, useLocation } from '@umijs/max';
type Props = {
  disabled?: boolean;
  onLangChange?: (value: string) => void;
};
const LangDropdown: React.FC<Props> = ({ disabled = false, onLangChange }) => {
  const [open, setOpen] = useState(false);
  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const currentLang = params.get('lang') || 'zh';
  const [lang, setLang] = useState(currentLang);
  useEffect(() => {
    const params = new URLSearchParams(search);
    const lang = params.get('lang') || 'zh';
    setLang(lang);
  }, [search]);
  useEffect(() => {
    onLangChange?.(lang);
  }, [lang]);
  const handleClick = (value: string) => {
    if (disabled) return;
    setOpen(false);
    setLang(value);
    value !== lang && history.replace({ pathname: location.pathname, search: `?lang=${value}` });
  };

  const onOpenChange = (open: boolean) => {
    if (disabled) return;
    setOpen(open);
  };

  return (
    <Dropdown
      placement="bottomLeft"
      trigger={['click']}
      open={open}
      onOpenChange={onOpenChange}
      menu={{
        items: [],
        selectable: true,
        defaultSelectedKeys: [lang]
      }}
      dropdownRender={() => (
        <div className={styles['lang-container']}>
          {LangOptions.map((v, index) => {
            return (
              <div
                key={index}
                title={v.label}
                onClick={() => handleClick(v.value)}
                className={v.value === lang ? styles['active'] : ''}
              >
                <span className={styles['ellipsis']}>{v.label}</span>
                {v.value === lang && <CheckOutlined style={{ color: '#0db4be', fontWeight: 600 }} />}
              </div>
            );
          })}
        </div>
      )}
    >
      <div className={`${styles['lang']} ${disabled ? styles['lang-disabled'] : ''}`}>
        <span>{LangMap[lang]}</span>
        <DownOutlined />
      </div>
    </Dropdown>
  );
};

export default LangDropdown;
