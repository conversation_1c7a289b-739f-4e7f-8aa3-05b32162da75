# Full Report API

## Definition

### Full Report API

---

| Name | Endpoint Address                                                         |
| ---- | ------------------------------------------------------------------------ |
| API  | https://iion.console.rixengine.com/backend-api/full-report/getFullReport |

### Request Details

---

- Request Method: POST
- Request Authorization: Please contact your account manager and add to request header.

| Name            | Description             |
| --------------- | ----------------------- |
| x-user-id       | user id, alias user_id  |
| x-authorization | user token, alias token |

- Default Timezone: UTC+0

### Request Limit

---

- Maximum number of requests per day：50.
- Request Day: last six month to current day.
- Request Date interval: within 15 days.

## Data Schema

---

### Request Data Schema

---

| Name | Type | Required | Description | Available Values | Default |
| :-: | :-: | :-: | :-- | :-- | :-: |
| start_date | YYYYMMDD | Yes | Start date,format YYYYMMDD. | ******** |  |
| end_date | YYYYMMDD | Yes | End date,format YYYYMMDD. | ******** |  |
| start | number | No | Paging limit, used with end.Maximum 500 per page: end - start <= 500. | 0-499. eg: { start:0,end:50 } indicates the data from the first to the fiftieth entry. | 0 |
| end | number | No | Paging limit, used with start.Maximum 500 per page: end - start <= 500 | 1-500 | 50 |
| order | array\<string\> | No | Data sorting, Ascending or descending order | 'asc' \| 'desc' | desc |
| order_key | array\<string\> | No | Sorting key,Refer to <a href='#Order Key'>Order Key </a> | ['date'], ['date','request'] | date |
| timezone | string | No | Timezone | From UTC-12 to UTC+12, eg:UTC-12,UTC-11,UTC-1,UTC+0,UTC+1,UTC+2... | UTC+0 |
| dimensions | array\<string\> | No | The key of report dimension, refer to <a href='#Dimensions Key'>Dimensions Key</a>.If there is no such parameter, it is the total metrics within the time interval. | ['day','bunlde'] |  |
| adv_id | array<number\> | No | Filter by advertiser ids. | [30761,31048] |  |
| pub_id | array<number\> | No | Filter by publisher ids. | [36558,36460] |  |
| bundle | string | No | Filter by bundles. Then bundles separated by commas. | 'com.fidget.toy.pop.it.fungame,com.fidget.toy.pop.it.fungame' |  |
| unit_id | string | No | Filter by Unit ID | '202131' |  |
| ad_format | array<number\> | No | Filter by ad format. Refer to <a href='#Ad Format Key'>Ad Format Key</a> | [1,2,3,4] |  |
| country | array<string\> | No | Filter by country abbreviations.Refer to <a href='#Country Abbreviations'>Country Abbreviations</a> | ['AFG','ALB'] |  |

### Response Data Schema

---

| Attribute | Type | Description |
| :-: | :-: | :-- |
| date | date | Date,format YYYY-MM-DD |
| buyer_net_revenue | float | Estimated net revenue |
| request | integer | Request count |
| response | float | Response count |
| impression | integer | Impression count |
| click | integer | Ad clicks |
| bid_floor | float | Minimum bid for this impression expressed in CPM |
| bundle | string | App bundle id. It exists only when the dimensions parameter contains bundle |
| unit_id | string | Ad Unit ID. It exists only when the dimensions parameter contains unit_id |
| unit_name | string ｜ null | Ad Unit Name. It exists only when the dimensions parameter contains unit_id |
| adv_id | string | Advertiser ID. It exists only when the dimensions parameter contains adv_id |
| adv_name | string ｜ null | Advertiser Name. It exists only when the dimensions parameter contains adv_id |
| pub_id | string | Publisher ID. It exists only when the dimensions parameter contains pub_id |
| pub_name | string ｜ null | Publisher Name. It exists only when the dimensions parameter contains pub_id |
| ad_format | number | Ad Format Key. Reter to <a href='#Ad Format Key'>Ad Format Key</a> |
| country | string | Country abbreviation.Refer to <a href='#Country Abbreviations'>Country Abbreviations</a> |

## ReportAPI Method

### API

---

#### Request Schema

---

- Request body see <a href='#Request Data Schema'>Request Data Schema</a>
- example:

  ```json
  {
    "start_date": "20240417",
    "end_date": "20240417",
    "dimensions": ["day", "bundle"],
    "bundle": "com.fidget.toy.pop.it.fungame,com.gofive.fashion.stylist.dress.up",
    "timezone": "UTC+0"
  }
  ```

#### Response Schema

---

| Attribute | Type | Description |
| :-: | :-: | :-- |
| status.code | integer | Server response status, when it is zero, indicates that the server responded correctly to the request. When it is not zero, you can find specific error messages in status.msg. |
| status.msg | string | Server response description. |
| timestamp | string | UTC time, which is used to indicate the time when the server responds to the current request. |
| data.data | array or null | Server response content. |
| data.total | number | Total data under current conditions |

- status code see <a href='#Status Code'>Status Code</a>
- data details see <a href='#Response Data Schema'>Response Data Schema</a>

#### Example

```shell
### POST
curl -H "x-user-id: {YOUR_USERID}"  -H "x-authorization: {YOUR_AUTHORIZATION}" -H "Content-Type: application/json" \
-d '{"start_date":"20240417","end_date":"20240417","dimensions":["day","bundle"],"bundle":"com.fidget.toy.pop.it.fungame,com.gofive.fashion.stylist.dress.up","timezone":"UTC+0"}' \
-X POST "https://iion.console.rixengine.com/backend-api/full-report/getFullReport"

```

```json
{
  "status": {
    "code": 0,
    "msg": "success"
  },
  "timestamp": "Thu Apr 18 07:17:48 +00:00 2024",
  "data": {
    "total": 2,
    "data": [
      {
        "date": "2024-04-17",
        "buyer_net_ecpm": 2.22,
        "buyer_net_revenue": 0.14,
        "total_request": 37258,
        "click": 14,
        "fill_rate": 0.4,
        "impression": 63,
        "bundle": "com.gofive.fashion.stylist.dress.up"
      },
      {
        "date": "2024-04-17",
        "buyer_net_ecpm": 0,
        "buyer_net_revenue": 0,
        "total_request": 36483,
        "click": 0,
        "fill_rate": 0.01,
        "impression": 5,
        "bundle": "com.fidget.toy.pop.it.fungame"
      }
    ]
  }
}
```

---

## Appendix

### Dimensions Key

| Dimension  |    KEY    |
| :--------: | :-------: |
|    Date    |    day    |
| Advertiser |  adv_id   |
| Publisher  |  pub_id   |
|  Unit ID   |  unit_id  |
|   Bundle   |  bundle   |
| Ad Format  | ad_format |
|  Country   |  country  |

---

### Order Key

|        Order By        |        KEY        |
| :--------------------: | :---------------: |
| Advertiser Net Revenue | buyer_net_revenue |
|        Request         |      request      |
|          Date          |       date        |
|     Advertiser ID      |      adv_id       |
|      Publisher ID      |      pub_id       |

---

### Ad Format Key

| Ad Format    | KEY |
| ------------ | --- |
| Banner       | 1   |
| Native       | 2   |
| Video        | 2   |
| Reward Video | 4   |

---

### Country Abbreviations

```json
{
  AFG: 'Afghanistan',
  ALB: 'Albania',
  DZA: 'Algeria',
  ASM: 'American Samoa',
  AND: 'Andorra',
  AGO: 'Angola',
  AIA: 'Anguilla',
  ATA: 'Antarctica',
  ATG: 'Antigua and Barbuda',
  ARG: 'Argentina',
  ARM: 'Armenia',
  ABW: 'Aruba',
  AUS: 'Australia',
  AUT: 'Austria',
  AZE: 'Azerbaijan',
  BHS: 'Bahamas',
  BHR: 'Bahrain',
  BGD: 'Bangladesh',
  BRB: 'Barbados',
  BLR: 'Belarus',
  BEL: 'Belgium',
  BLZ: 'Belize',
  BEN: 'Benin',
  BMU: 'Bermuda',
  BTN: 'Bhutan',
  BOL: 'Bolivia',
  BIH: 'Bosnia and Herzegovina',
  BWA: 'Botswana',
  BRA: 'Brazil',
  IOT: 'British Indian Ocean Territory',
  VGB: 'British Virgin Islands',
  BRN: 'Brunei',
  BGR: 'Bulgaria',
  BFA: 'Burkina Faso',
  BDI: 'Burundi',
  KHM: 'Cambodia',
  CMR: 'Cameroon',
  CAN: 'Canada',
  CPV: 'Cape Verde',
  CYM: 'Cayman Islands',
  CAF: 'Central African Republic',
  TCD: 'Chad',
  CHL: 'Chile',
  CHN: 'China',
  CXR: 'Christmas Island',
  CCK: 'Cocos Islands',
  COL: 'Colombia',
  COM: 'Comoros',
  COK: 'Cook Islands',
  CRI: 'Costa Rica',
  HRV: 'Croatia',
  CUB: 'Cuba',
  CUW: 'Curacao',
  CYP: 'Cyprus',
  CZE: 'Czech Republic',
  COD: 'Democratic Republic of the Congo',
  DNK: 'Denmark',
  DJI: 'Djibouti',
  DMA: 'Dominica',
  DOM: 'Dominican Republic',
  TLS: 'East Timor',
  ECU: 'Ecuador',
  EGY: 'Egypt',
  SLV: 'El Salvador',
  GNQ: 'Equatorial Guinea',
  ERI: 'Eritrea',
  EST: 'Estonia',
  ETH: 'Ethiopia',
  FLK: 'Falkland Islands',
  FRO: 'Faroe Islands',
  FJI: 'Fiji',
  FIN: 'Finland',
  FRA: 'France',
  PYF: 'French Polynesia',
  GAB: 'Gabon',
  GMB: 'Gambia',
  GEO: 'Georgia',
  DEU: 'Germany',
  GHA: 'Ghana',
  GIB: 'Gibraltar',
  GRC: 'Greece',
  GRL: 'Greenland',
  GRD: 'Grenada',
  GUM: 'Guam',
  GTM: 'Guatemala',
  GGY: 'Guernsey',
  GIN: 'Guinea',
  GNB: 'Guinea-Bissau',
  GUY: 'Guyana',
  HTI: 'Haiti',
  HND: 'Honduras',
  HKG: 'Hong Kong(China)',
  HUN: 'Hungary',
  ISL: 'Iceland',
  IND: 'India',
  IDN: 'Indonesia',
  IRN: 'Iran',
  IRQ: 'Iraq',
  IRL: 'Ireland',
  IMN: 'Isle of Man',
  ISR: 'Israel',
  ITA: 'Italy',
  CIV: 'Ivory Coast',
  JAM: 'Jamaica',
  JPN: 'Japan',
  JEY: 'Jersey',
  JOR: 'Jordan',
  KAZ: 'Kazakhstan',
  KEN: 'Kenya',
  KIR: 'Kiribati',
  XKX: 'Kosovo',
  KWT: 'Kuwait',
  KGZ: 'Kyrgyzstan',
  LAO: 'Laos',
  LVA: 'Latvia',
  LBN: 'Lebanon',
  LSO: 'Lesotho',
  LBR: 'Liberia',
  LBY: 'Libya',
  LIE: 'Liechtenstein',
  LTU: 'Lithuania',
  LUX: 'Luxembourg',
  MAC: 'Macau',
  MKD: 'Macedonia',
  MDG: 'Madagascar',
  MWI: 'Malawi',
  MYS: 'Malaysia',
  MDV: 'Maldives',
  MLI: 'Mali',
  MLT: 'Malta',
  MHL: 'Marshall Islands',
  MRT: 'Mauritania',
  MUS: 'Mauritius',
  MYT: 'Mayotte',
  MEX: 'Mexico',
  FSM: 'Micronesia',
  MDA: 'Moldova',
  MCO: 'Monaco',
  MNG: 'Mongolia',
  MNE: 'Montenegro',
  MSR: 'Montserrat',
  MAR: 'Morocco',
  MOZ: 'Mozambique',
  MMR: 'Myanmar',
  NAM: 'Namibia',
  NRU: 'Nauru',
  NPL: 'Nepal',
  NLD: 'Netherlands',
  ANT: 'Netherlands Antilles',
  NCL: 'New Caledonia',
  NZL: 'New Zealand',
  NIC: 'Nicaragua',
  NER: 'Niger',
  NGA: 'Nigeria',
  NIU: 'Niue',
  PRK: 'North Korea',
  MNP: 'Northern Mariana Islands',
  NOR: 'Norway',
  OMN: 'Oman',
  PAK: 'Pakistan',
  PLW: 'Palau',
  PSE: 'Palestine',
  PAN: 'Panama',
  PNG: 'Papua New Guinea',
  PRY: 'Paraguay',
  PER: 'Peru',
  PHL: 'Philippines',
  PCN: 'Pitcairn',
  POL: 'Poland',
  PRT: 'Portugal',
  PRI: 'Puerto Rico',
  QAT: 'Qatar',
  COG: 'Republic of the Congo',
  REU: 'Reunion',
  ROU: 'Romania',
  RUS: 'Russia',
  RWA: 'Rwanda',
  BLM: 'Saint Barthelemy',
  SHN: 'Saint Helena',
  KNA: 'Saint Kitts and Nevis',
  LCA: 'Saint Lucia',
  MAF: 'Saint Martin',
  SPM: 'Saint Pierre and Miquelon',
  VCT: 'Saint Vincent and the Grenadines',
  WSM: 'Samoa',
  SMR: 'San Marino',
  STP: 'Sao Tome and Principe',
  SAU: 'Saudi Arabia',
  SEN: 'Senegal',
  SRB: 'Serbia',
  SYC: 'Seychelles',
  SLE: 'Sierra Leone',
  SGP: 'Singapore',
  SXM: 'Sint Maarten',
  SVK: 'Slovakia',
  SVN: 'Slovenia',
  SLB: 'Solomon Islands',
  SOM: 'Somalia',
  ZAF: 'South Africa',
  KOR: 'South Korea',
  SSD: 'South Sudan',
  ESP: 'Spain',
  LKA: 'Sri Lanka',
  SDN: 'Sudan',
  SUR: 'Suriname',
  SJM: 'Svalbard and Jan Mayen',
  SWZ: 'Swaziland',
  SWE: 'Sweden',
  CHE: 'Switzerland',
  SYR: 'Syria',
  TWN: 'Taiwan(China)',
  TJK: 'Tajikistan',
  TZA: 'Tanzania',
  THA: 'Thailand',
  TGO: 'Togo',
  TKL: 'Tokelau',
  TON: 'Tonga',
  TTO: 'Trinidad and Tobago',
  TUN: 'Tunisia',
  TUR: 'Turkey',
  TKM: 'Turkmenistan',
  TCA: 'Turks and Caicos Islands',
  TUV: 'Tuvalu',
  VIR: 'U.S. Virgin Islands',
  UGA: 'Uganda',
  UKR: 'Ukraine',
  ARE: 'United Arab Emirates',
  GBR: 'United Kingdom',
  USA: 'United States',
  URY: 'Uruguay',
  UZB: 'Uzbekistan',
  VUT: 'Vanuatu',
  VAT: 'Vatican',
  VEN: 'Venezuela',
  VNM: 'Vietnam',
  WLF: 'Wallis and Futuna',
  ESH: 'Western Sahara',
  YEM: 'Yemen',
  ZMB: 'Zambia',
  ZWE: 'Zimbabwe'
};
```

---

### Status Code

| Error Code | Error Message                                                  |
| :--------: | :------------------------------------------------------------- |
|     0      | success                                                        |
|     -1     | system error                                                   |
|     -2     | Params invalid:[error reason]                                  |
|     -4     | request time out                                               |
|    1000    | 'x-user-id' or 'x-authorization' is invalid.                   |
|    1001    | The number of requests for the day has reached the upper limit |
