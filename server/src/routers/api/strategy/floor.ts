/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-23 17:27:13
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-07 16:54:05
 * @Description:
 */
import Router from 'koa-router';
import { floorController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/floor' });

const routers = router
  .post('/getFloorList', floorController.getFloorList)
  .post('/getAllSupplyPlacement', floorController.getAllSupplyPlacement)
  .post('/addFloor', validTesting, floorController.addFloor)
  .post('/updateFloor', floorController.updateFloor)
  .post('/deleteFloor', validTesting, floorController.deleteFloor);

export default routers;
