/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-21 15:41:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-07 14:45:32
 * @Description:
 */
import { Context } from 'koa';
import { ABTestAPI } from '@/types/ab-test';
import { abTestService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { abTestModel } from '@/models';
import { validateBody } from '@/utils/validate-params';
import { AddABTestSchema, UpdateABTestSchema } from '@/schema/strategy/ab-test';
import moment from 'moment-timezone';
import { ABTestTypeDesc } from '@/constants/strategy';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class ABTestCtrl implements ABTestAPI.ABTestCtrl {
  async getABTestList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id = 0 } = ctx.session!;
    const { cur_time_zone = 'Etc/UTC' } = ctx.request.body;
    const list = await abTestService.getABTestList(tnt_id, cur_time_zone);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`getABTestList success tnt_id=[${tnt_id}]`);
    }

    ctx.body = result;
  }

  @validateBody(AddABTestSchema)
  async addABTest(ctx: Context) {
    let result = { ...RESULT };
    const params: any = ctx.request.body;
    const { tnt_id = 0, user_id = 0 } = ctx.session!;
    params.tnt_id = tnt_id;
    params.op_id = user_id;
    let { expire_time } = params;

    params.expire_time = moment(expire_time)
      .tz('Etc/UTC')
      .format('YYYY-MM-DD HH:mm:ss');

    const [isABTestExist, isOtherGroupExist] = await Promise.all([
      abTestModel.isABTestExist(params),
      abTestModel.isOtherGroupExist(params)
    ]);
    if (isABTestExist) {
      result = getCtxResult('AB_TEST_EXISTS', '');
      ctx.body = result;
      return;
    } else if (
      Array.isArray(isOtherGroupExist) &&
      isOtherGroupExist.length > 0
    ) {
      const { type, seller_id, buyer_id } = isOtherGroupExist[0];
      const msg = `The ${ABTestTypeDesc[type]} A/B-Test of seller[${seller_id}] and buyer[${buyer_id}] is still in progress.`;
      result = getCtxResult('AB_TEST_OTHER_EXIST', '', msg);
      ctx.body = result;
      return;
    }
    const data = await abTestService.addABTest(params);
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`addABTest success tnt_id=[${tnt_id}]`);
    }
    ctx.body = result;
  }

  @validateBody(UpdateABTestSchema)
  async updateABTest(ctx: Context): Promise<void> {
    let result = { ...RESULT };
    const params: any = ctx.request.body;
    const { tnt_id = 0, user_id = 0 } = ctx.session!;
    params.tnt_id = tnt_id;
    params.op_id = user_id;
    let { expire_time } = params;

    params.expire_time = moment(expire_time)
      .tz('Etc/UTC')
      .format('YYYY-MM-DD HH:mm:ss');

    const data = await abTestService.updateABTest(params);
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`updateABTest success tnt_id=[${tnt_id}]`);
    }
    ctx.body = result;
  }
}
export const abTestCtrl = new ABTestCtrl();
