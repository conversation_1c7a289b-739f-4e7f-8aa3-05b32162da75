.btn-container {
  width: 100%;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  padding: 0 12px;
  width: 100%;
}

.drop-container {
  overflow-y: auto;
  max-height: 300px;
  > div {
    color: var(--text-color);
    border-radius: 6px;
    margin: 2px;
    display: flex;
    min-height: 32px;
    padding: 5px 12px;
    cursor: pointer;
    line-height: 22px;
    position: relative;
    font-weight: 400;
    font-size: 14px;
    &:hover {
      background-color: #e6e8eb;
      font-weight: 600;
    }
  }
  .drop-container-item-active {
    background-color: #e6e8eb;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline;
  }
}

.tag-container {
  margin-right: 3px;
  background-color: #ebeff0;
  color: var(--text-color);
  border-radius: 6px;
  height: 24px;
  line-height: 22px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  box-sizing: border-box;
  align-items: center;
  font-size: 14px;
  svg {
    color: #00000073;
  }
}
