server {
    listen              8080;
    server_name         *.console-t.rixengine.com console.*;
    underscores_in_headers on;

    set $subdomain default;
    if ($http_host ~* "^(.*)\.console-t\.rixengine\.com") {
        set $subdomain $1;
    }

    location ~* /(\.svn|CVS|Entries){
        deny all;
    }

    location ~* /((.*)\.(.*)\/(.*)\.php){
        deny all;
    }

    location ~* /\.(sql|bak|inc|old|map)$ {
        deny all;
    }

    location ~* /(js|img|css|fonts)/ {
        expires 7d;
        if ($subdomain = 'website-admin') {
            proxy_pass http://127.0.0.1:3003;
            break;
        }
        if ($subdomain = 'website') {
            proxy_pass http://127.0.0.1:3000;
            break;
        }
        root /data/htdocs/saas.rix-platform/server/webroot;
    }

    location /heartbeat {
        access_log off;
        default_type text/html;
        return 200 'ok, I am alive...';
    }

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Connection "keep-alive";

        if ($subdomain = 'website-admin') {
            proxy_pass http://127.0.0.1:3003;
            break;
        }

        if ($subdomain = 'website') {
            proxy_pass http://127.0.0.1:3000;
            break;
        }

        proxy_pass http://127.0.0.1:3001;
    }

    #location / {
    #    set $subdomain default;
    #    if ($http_host ~* "^(.*)\.rixengine\.com") {
    #        set $subdomain $1;
    #    }
    #    rewrite ^/(.*)$ /$subdomain/$1 break;
    #    proxy_pass http://localhost:3001;
    #    proxy_set_header X-Real-IP $remote_addr;
    #    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #}
}
