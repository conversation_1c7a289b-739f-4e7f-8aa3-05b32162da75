/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:52:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-18 11:52:09
 * @Description:
 */
declare namespace CommonAPI {
  type IntegrationTypeItem = {
    id: number;
    itg_name: string;
    itg_key: string;
    create_time: string;
    update_time: string;
  };
  type NotificationListItem = {
    id: number;
    title: string;
    content: string;
    unread: number;
    rule_id: number;
    create_time: string;
    update_time: string;
  };
  type UpdateNotificationParams = {
    id: number;
    unread: number;
  };
  type EditDashboardUserparams = {
    user_id: number | string;
    account_name: string;
    isSupply: boolean;
    status?: number;
    new_password?: string;
  };

  type BrandInfo = {
    brand_favicon_path: string;
    brand_logo_path: string;
    brand_name: string;
  };

  type DictItem = Record<string, any>;
}
