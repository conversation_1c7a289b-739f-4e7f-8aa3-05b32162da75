import { request } from '@umijs/max';

export async function getStgChainListV2(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/stgv2/getStgChainList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addStgChainV2(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/stgv2/addStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateStgChainV2(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/stgv2/updateStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
