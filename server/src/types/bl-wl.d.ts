/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:17:10
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-13 11:43:50
 * @Description:
 */
import { Context } from 'koa';
declare namespace BlAndWlAPI {
  type BlAndWlListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    account_name: string;
    status: number;
    create_time: string;
    update_time: string;
    account_status: number;
  };
  type AddBlAndWlParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
    op_id: number;
    tnt_id: number;
  };
  type updateBlAndWlParams = {
    id: number;
    content: string;
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type IsExistParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    id: number;
    tnt_id: number;
  };

  interface BlAndWl {
    getBlAndWlList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<BlAndWlListItem[]>;
    addBlAndWl(params: AddBlAndWlParams): Promise<boolean>;
    updateBlAndWl(params: updateBlAndWlParams): Promise<boolean>;
    isBlAndWlExists(params: IsExistParams): Promise<any[]>;
  }

  interface BlAndWlCtrlInterface {
    getBlAndWlList(ctx: Context): void;
    addBlAndWl(ctx: Context): void;
    updateBlAndWl(ctx: Context): void;
  }
}
