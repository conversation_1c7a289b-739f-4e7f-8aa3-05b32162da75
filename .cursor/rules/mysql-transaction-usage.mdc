# MySQL事务使用规范

## execTransaction 函数使用指南

在项目中，我们使用 `dbUtil.execTransaction` 函数来执行数据库事务操作。这个函数封装了事务的开始、提交和回滚逻辑，使得事务处理更加简洁和安全。

### 基本用法

```typescript
// 基本用法示例
return dbUtil.execTransaction(async tools => {
  // 使用tools中的query方法执行SQL操作
  // 所有操作都在同一事务中
  return yourModel.yourMethod(params, tools);
});
```

### 详细说明

1. `execTransaction` 函数接收一个回调函数作为参数
2. 回调函数接收 `TransactionReturnType` 类型的参数，包含以下关键方法：
   - `query`: 执行SQL查询
   - `beginTransaction`: 开始事务（已在execTransaction内部调用）
   - `commit`: 提交事务（已在execTransaction内部调用）
   - `rollback`: 回滚事务（错误时自动调用）

3. 在Model层中定义具体的事务操作方法，接收 `TransactionReturnType` 参数

### 实际案例

在 [server/src/services/strategy/geo-policy.ts](mdc:server/src/services/strategy/geo-policy.ts) 中的使用示例：

```typescript
async updateGeoPolicy(
  params: GeoPolicyAPI.UpdateGeoPolicyParams
): Promise<boolean> {
  return dbUtil.execTransaction(async tools =>
    geoPolicyModel.updateGeoPolicy(params, tools)
  );
}
```

对应的Model层实现在 [server/src/models/strategy/geo-policy.ts](mdc:server/src/models/strategy/geo-policy.ts)：

```typescript
async updateGeoPolicy(
  params: GeoPolicyAPI.UpdateGeoPolicyParams,
  tools: TransactionReturnType
): Promise<boolean> {
  const { query } = tools;
  
  // 在同一事务中执行多个SQL操作
  // 如果任何操作失败，整个事务将回滚
  // ...
}
```

### 最佳实践

1. **职责分离**：
   - Service层：调用execTransaction并传递参数给Model层
   - Model层：实现具体的业务逻辑和SQL操作

2. **错误处理**：
   - 在Model层方法中使用try/catch捕获错误
   - 抛出具体的错误信息，便于调试

3. **避免嵌套事务**：
   - 不要在一个事务内部再开启另一个事务

4. **合理使用事务**：
   - 当需要保证多个操作的原子性时使用事务
   - 简单的单一操作可以直接使用`dbUtil.query`
