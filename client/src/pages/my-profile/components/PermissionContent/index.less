.container {
  padding-right: 8px;
  background-color: #fff;
  max-height: calc(100vh - 282px);
  overflow-y: auto;
  border-bottom: 1px solid #e2eaeb;
  :global {
    .ant-tree-node-content-wrapper {
      border-radius: 4px;
    }
    .ant-tree-list-holder-inner {
      display: block !important;
      .ant-tree-treenode {
        margin-bottom: 9px;
        padding: 5px 0;
      }
    }
    .ant-tree-switcher .ant-tree-switcher-icon {
      font-size: 14px;
    }
    .ant-tree-list {
      width: 100%;
    }
    .ant-tree-checkbox-inner {
      border-radius: 4px;
    }
    .ant-tree-checkbox-indeterminate {
      .ant-tree-checkbox-inner {
        &::after {
          border-radius: 2px;
        }
      }
    }
  }
}

.root {
  display: inline-flex;
  width: 100%;
}
.last-node {
  display: inline-flex !important;
}
.root-node {
  background-color: #e6e8eb;
  border-radius: 6px;
  width: 100%;
}
.p-node {
  background-color: var(--background-color);
  min-width: 100%;
  border-radius: 6px;
}
