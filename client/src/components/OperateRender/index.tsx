/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 16:26:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-24 14:53:15
 * @Description:
 */
import EditButton from '@/components/Button/EditButton';
import styles from './index.less';
import { useAccess } from '@umijs/max';
import RixEngineFont from '@/components/RixEngineFont';
import NormalPopover from '../Popover';
import React from 'react';
import HoverToolTip from '../ToolTip/HoverTooltip';
import { judgeAuth } from '@/utils/permission';

export type OperateRenderItem = {
  label: string;
  icon?: any;
  onClick?: (params: any) => void;
  hide?: boolean | ((params: any) => boolean);
  isDelete?: boolean;
  disabled?: boolean;
  accessCode?: string;
  text?: string;
  maxTextWidth?: number;
  handleDisabled?: (params: any) => boolean;
};

type OperateRenderProps = {
  btnOptions: OperateRenderItem[];
  params: any;
  trigger?: 'hover' | 'click';
  isExpendTable?: boolean;
  moreBtnOptions?: {
    disabled?: boolean;
  };
};

export default function OperateRender({
  btnOptions,
  params,
  trigger = 'hover',
  isExpendTable = false,
  moreBtnOptions
}: OperateRenderProps): JSX.Element {
  const access = useAccess();
  const allOptions = btnOptions.filter(item => {
    return (
      (typeof item.hide === 'boolean' && !item.hide) ||
      (typeof item.hide === 'function' && !item.hide(params)) ||
      typeof item.hide === 'undefined'
    );
  });

  const flag = allOptions.length <= 2;
  const restOptions = allOptions.slice(1);

  // 阻止事件冒泡
  const handleClick = (e: any, item: OperateRenderItem) => {
    e.stopPropagation();
    item.onClick && item.onClick(params);
  };

  return (
    <div className={`${isExpendTable ? styles['flagClass'] : styles['operate-btn-container']}`}>
      {flag ? (
        allOptions.map((item, index) => (
          <React.Fragment key={index}>
            {item.text && (
              <HoverToolTip title={params[item.text]} maxWidth={(item.maxTextWidth as number) - 80}>
                <span className={styles['text']}>{params[item.text]}</span>
              </HoverToolTip>
            )}

            <EditButton
              onClick={e => item.onClick && handleClick(e, item)}
              icon={item.icon}
              danger={item.isDelete}
              disabled={item.disabled || judgeAuth(access, item.accessCode) || item.handleDisabled?.(params)}
            >
              {item.label}
            </EditButton>
          </React.Fragment>
        ))
      ) : (
        <>
          <EditButton
            onClick={e => allOptions[0].onClick && handleClick(e, allOptions[0])}
            icon={allOptions[0].icon}
            disabled={allOptions[0].disabled || judgeAuth(access, allOptions[0].accessCode)}
          >
            {allOptions[0].label}
          </EditButton>

          <NormalPopover
            content={restOptions.map((item, index) => (
              <EditButton
                onClick={e => item.onClick && handleClick(e, item)}
                key={index}
                icon={item.icon}
                danger={item.isDelete}
                disabled={item.disabled || judgeAuth(access, item.accessCode)}
              >
                {item.label}
              </EditButton>
            ))}
            trigger={trigger}
          >
            <EditButton icon={<RixEngineFont type="more" />} disabled={moreBtnOptions?.disabled}>
              More
            </EditButton>
          </NormalPopover>
        </>
      )}
    </div>
  );
}
