/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-22 15:22:22
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-10 14:10:43
 * @Description:
 */

import { useEffect, useState } from 'react';
import { Form, message } from 'antd';
import { RegionOptions } from '@/constants';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { addSampleTraceTask } from '@/services/api';
import { fetchData, handleFilterSelect, isValidBundle, isValidDomain } from '@/utils';
import Select from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import BundleTableEdit from '@/components/BundleTableEdit';
import { SampleType } from '@/constants/troubleshooting';
import NormalInput from '@/components/Input/NormalInput';
import InputNumberNormal from '@/components/Input/InputNumber';
import { CountryOptions } from '@/constants/global-mapping/country';

type AddTraceTaskProps = {
  type: number;
  visible: boolean;
  handleClose: () => void;
  reload: () => void;
  demandList: DemandAPI.DemandListItem[];
  supplyList: SupplyAPI.SupplyListItem[];
};

const AddTraceTaskDrawer: React.FC<AddTraceTaskProps> = ({
  type,
  handleClose,
  visible,
  reload,
  demandList,
  supplyList
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      form.resetFields();
    }
  }, [visible]);

  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: TroubleShootingAPI.TaskListItem) => {
    const params = {
      ...values,
      country: Array.isArray(values.country) ? values.country.sort().join(',') : '',
      bundle: Array.isArray(values.bundle) ? values.bundle.sort().join(',') : '',
      cid: Array.isArray(values.cid) ? values.cid.sort().join(',') : '',
      crid: Array.isArray(values.crid) ? values.crid.sort().join(',') : '',
      adomain: Array.isArray(values.adomain) ? values.adomain.sort().join(',') : ''
    };
    if (params.cid.length > 2000) {
      message.error('Too many Campaign IDs');
      return;
    }
    if (params.crid.length > 2000) {
      message.error('Too many Creative IDs');
      return;
    }
    handleAddTask(params);
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reload();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleAddTask = (params: TroubleShootingAPI.TaskListItem) => {
    // 新增数据
    params.type = type;
    fetchData({ setLoading, request: addSampleTraceTask, params, onSuccess });
  };

  return (
    <NormalDrawer
      blackName={'Create Sample Trace Task'}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="seller_id"
          label="Publisher:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: [SampleType.SupplyRequest, SampleType.SupplyResponse].includes(type),
              message: 'Please Select Publisher'
            }
          ]}
        >
          <Select
            placeholder="Please Select Publisher"
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            {supplyList &&
              supplyList.map((item: SupplyAPI.SupplyListItem, index: number) => {
                return (
                  <Select.Option
                    value={item.seller_id}
                    key={index}
                  >
                    {item.seller_name}({item.seller_id})
                  </Select.Option>
                );
              })}
          </Select>
        </Form.Item>
        {[SampleType.DemandResponse, SampleType.DemandRequest, SampleType.SupplyResponse].includes(type) && (
          <Form.Item
            name="buyer_id"
            label="Advertiser:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: type !== SampleType.SupplyResponse,
                message: 'Please Select Advertiser'
              }
            ]}
          >
            <Select
              placeholder="Please Select Advertiser"
              showSearch
              optionFilterProp="children"
              filterOption={handleFilterSelect}
            >
              {demandList &&
                demandList.map((item: DemandAPI.DemandListItem, index: number) => {
                  return (
                    <Select.Option value={item.buyer_id} key={index}>
                      {item.buyer_name}({item.buyer_id})
                    </Select.Option>
                  );
                })}
            </Select>
          </Form.Item>
        )}

        <Form.Item
          name="server_region"
          label="Server Region:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Server Region'
            }
          ]}
        >
          <Select
            placeholder="Please Select Server Region"
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
            options={RegionOptions}
          ></Select>
        </Form.Item>

        <Form.Item
          label="Ad Format:"
          name="ad_format"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              message: 'Please Select Ad Format'
            }
          ]}
        >
          <Select
            options={AdFormatOptions}
            placeholder="Please Select Ad Format"
            filterOption={handleFilterSelect}
            allowClear
          />
        </Form.Item>
        <Form.Item label="Country:" name="country">
          <Select
            showSearch
            mode="multiple"
            options={CountryOptions}
            placeholder="Please Select Country"
            filterOption={handleFilterSelect}
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="Number of Traces:"
          name="expected_num"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Input Number of Traces, 10-100'
            }
          ]}
          tooltip="The number of traces expected to be returned (10-100)"
        >
          <InputNumberNormal min={10} max={100} style={{ width: 150 }} />
        </Form.Item>
        <Form.Item label="Bundle" name="bundle" validateTrigger={['onChange', 'onBlur']}>
          <BundleTableEdit
            editTitle="Edit Content"
            editTips="Enter the content(one per line)"
            deleteAllTitle="Delete All Content"
            deleteAllTips="Are you sure to delete all content?"
            open={visible}
            contentMaxHeight={'calc(100vh - 430px)'}
            editSingle={true}
            validator={(val: string) => isValidBundle(val)}
          />
        </Form.Item>
        <Form.Item label="Placement ID:" name="plm_id" validateTrigger={['onChange', 'onBlur']}>
          <NormalInput placeholder="Please Input Placement ID"></NormalInput>
        </Form.Item>
        {[SampleType.SupplyRequest, SampleType.DemandRequest].includes(type) && (
          <>
            {/* <Form.Item
              label="Block Reason:"
              name="block_reason"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Select Block Reason'
                }
              ]}
            >
              <Select
                options={[
                  {
                    label: 'Success',
                    value: '0'
                  },
                  {
                    label: 'Faild',
                    value: '1'
                  }
                ]}
                placeholder="Please Select Block Reason"
                mode="multiple"
                filterOption={handleFilterSelect}
              />
            </Form.Item> */}
          </>
        )}

        {[SampleType.DemandResponse, SampleType.SupplyResponse].includes(type) && (
          <>
            {/* <Form.Item
              label="Bid Status:"
              name="bid_status"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Select Bid Status'
                }
              ]}
            >
              <Select
                options={[
                  {
                    label: 'Success',
                    value: '0'
                  },
                  {
                    label: 'Faild',
                    value: '1'
                  }
                ]}
                placeholder="Please Select Bid Status"
                mode="multiple"
                filterOption={handleFilterSelect}
              />
            </Form.Item> */}
            <Form.Item label="Campaign ID" name="cid" validateTrigger={['onChange', 'onBlur']}>
              <BundleTableEdit
                editTitle="Edit Content"
                editTips="Enter the content(one per line)"
                deleteAllTitle="Delete All Content"
                deleteAllTips="Are you sure to delete all content?"
                open={visible}
                contentMaxHeight={'calc(100vh - 430px)'}
                editSingle={true}
              />
            </Form.Item>
            <Form.Item label="Creative ID" name="crid" validateTrigger={['onChange', 'onBlur']}>
              <BundleTableEdit
                editTitle="Edit Content"
                editTips="Enter the content(one per line)"
                deleteAllTitle="Delete All Content"
                deleteAllTips="Are you sure to delete all content?"
                open={visible}
                contentMaxHeight={'calc(100vh - 430px)'}
                editSingle={true}
              />
            </Form.Item>
            <Form.Item label="Adomain" name="adomain" validateTrigger={['onChange', 'onBlur']}>
              <BundleTableEdit
                editTitle="Edit Content"
                editTips="Enter the content(one per line)"
                deleteAllTitle="Delete All Content"
                deleteAllTips="Are you sure to delete all content?"
                open={visible}
                contentMaxHeight={'calc(100vh - 430px)'}
                editSingle={true}
                validator={(val: string) => isValidDomain(val)}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddTraceTaskDrawer;
