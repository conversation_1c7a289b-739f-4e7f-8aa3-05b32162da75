/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:19:56
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-13 15:04:38
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-03-09 17:04:11
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { StatusDesc, StatusMap, StatusOptions } from '@/constants';
import { TabOption } from '@/hooks/useCurrentTabState';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { ColumnProps } from 'antd/lib/table';

export const ProfitTypeOptionsMap = {
  1: 'Pub',
  2: 'Adv',
  3: 'Adv + Pub'
};

export const ProfitBundleTypeOptionsMap = {
  4: 'Adv + Bundle',
  5: 'Adv + Pub + Bundle'
};

export const ProfitCheckListTypeOptionsMap = {
  ...ProfitTypeOptionsMap,
  ...ProfitBundleTypeOptionsMap
};

export const ProfitBundleTypeOptions = Object.entries(ProfitBundleTypeOptionsMap).map(([key, value]) => ({
  label: value,
  value: Number(key)
}));

export const ProfitAdvertiserSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    options: StatusOptions,
    mode: 'multiple',
    defaultValue: [StatusMap.Active]
  }
];

export const ProfitPublisherSearchOption: TopBarSearchItem[] = [
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    options: StatusOptions,
    mode: 'multiple'
  }
];

export const ProfitBundleSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: '',
    options: [],
    placeholder: 'Please Select Bundle',
    mode: 'multiple'
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: '',
    options: ProfitBundleTypeOptions
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    options: StatusOptions,
    mode: 'multiple'
  }
];

export const ProfitBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'Profit'
  }
];

export const ProfitColumns: ColumnProps<StrategyAPI.ProfitListItem>[] = [
  {
    title: 'Dimension',
    dataIndex: 'name',
    fixed: 'left',
    width: 200,
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverTooltip title={txt}>
        <span>
          {txt}({params.tmp_id})
        </span>
      </HoverTooltip>
    )
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'type',
    width: 100,
    render: (txt: 1 | 2 | 3) => <>{ProfitTypeOptionsMap[txt] ?? '-'}</>
  },
  {
    title: 'Profit(%)',
    dataIndex: 'profit_ratio',
    width: 80,
    render: (txt, params) => <span>{params.status === StatusMap.Active ? txt : '-'}</span>
  },

  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
          {row['account_status'] === 3 ? 'Unknown' : _}
        </span>
        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 85,
    render: _ => (
      <StatusTag type={(_ && StatusDesc[_] && StatusDesc[_].toLowerCase()) || ''}>{_ && StatusDesc[_]}</StatusTag>
    )
  }
];

export const ProfitBundleColumns: ColumnProps<StrategyAPI.PublisherAdvertiserProfitListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer',
    width: 200,
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverTooltip title={params.buyer_name}>
        <span>
          {params.buyer_name}({params.buyer_id})
        </span>
      </HoverTooltip>
    )
  },
  {
    title: 'Publisher',
    dataIndex: 'seller',
    width: 200,
    ellipsis: { showTitle: false },
    render: (_, params) => {
      if (!params.seller_id) {
        return '-';
      }
      return (
        <HoverTooltip title={params.seller_name}>
          <span>
            {params.seller_name}({params.seller_id})
          </span>
        </HoverTooltip>
      );
    }
  },
  {
    title: 'Bundle',
    dataIndex: 'bundle',
    width: 200,
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverTooltip title={txt}>
        <span>{txt}</span>
      </HoverTooltip>
    )
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'type',
    width: 180,
    render: (txt: 4 | 5) => <>{ProfitBundleTypeOptionsMap[txt] ?? '-'}</>
  },
  {
    title: 'Profit(%)',
    dataIndex: 'profit_ratio',
    width: 150,
    render: (txt, params) => <span>{params.status === StatusMap.Active ? txt : '-'}</span>
  },
  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
          {row['account_status'] === 3 ? 'Unknown' : _}
        </span>
        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 85,
    render: _ => (
      <StatusTag type={(_ && StatusDesc[_] && StatusDesc[_].toLowerCase()) || ''}>{_ && StatusDesc[_]}</StatusTag>
    )
  }
];

export const ProfitTab = {
  publisher: 'publisher',
  advertiser: 'advertiser',
  bundle: 'bundle'
} as const;

export type ProfitTabType = typeof ProfitTab[keyof typeof ProfitTab];

export const ProfitTabOptions: TabOption<ProfitTabType>[] = [
  { label: 'Advertiser Profit', value: 'advertiser' },
  { label: 'Publisher Profit', value: 'publisher' },
  { label: 'Bundle Profit', value: 'bundle' }
];

export const ProfitType = {
  Seller: 1,
  Demand: 2,
  'Seller-Demand': 3,
  DemandBundle: 4,
  DemandSellerBundle: 5
};

export const ProfitTypeOptions = [
  { label: 'Pub', value: ProfitType.Seller },
  { label: 'Adv', value: ProfitType.Demand },
  { label: 'Adv + Pub', value: ProfitType['Seller-Demand'] },
  { label: 'Adv + Bundle', value: ProfitType.DemandBundle },
  { label: 'Adv + Pub + Bundle', value: ProfitType.DemandSellerBundle }
];
