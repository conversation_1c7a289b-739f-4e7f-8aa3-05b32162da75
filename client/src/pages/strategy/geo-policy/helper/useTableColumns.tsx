import {
  GeoPolicyKeyColumns,
  GeoPolicyKeyRelationColumns,
  GeoPolicyTab,
  GeoPolicyTabType
} from '@/constants/strategy/geo-policy';
import { ColumnProps } from 'antd/lib/table';
import { useMemo } from 'react';

type UseTableColumnsProps = {
  currentTab: GeoPolicyTabType;
  operationColumn: ColumnProps<any>;
};

export const useTableColumns = ({ currentTab, operationColumn }: UseTableColumnsProps) => {
  // 获取当前标签页的表格列配置
  const currentColumns: ColumnProps<any>[] = useMemo(() => {
    const isKeyTab = currentTab === GeoPolicyTab.key;
    const columns = [...(isKeyTab ? GeoPolicyKeyColumns : GeoPolicyKeyRelationColumns)];

    // 添加操作列
    columns.push(operationColumn);

    return columns;
  }, [currentTab, operationColumn]);

  return { currentColumns };
};
