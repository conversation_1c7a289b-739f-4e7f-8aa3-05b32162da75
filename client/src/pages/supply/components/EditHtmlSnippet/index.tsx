import React, { useState } from 'react';
import { Button, Radio, Input, Form, Typography } from 'antd';
import styles from './index.less';
import { generateDFPCode, generateMaxCode, generateMWCode } from '@/utils/supply';
import Modal from '@/components/Modal/NormalModal';
import InputNumber from '@/components/Input/InputNumber';

const { Paragraph } = Typography;
const DefaultFormValues = {
  bundle_id: '',
  width: '',
  height: '',
  default_adsolt_id: '',
  dgpr: 0
};
const EditHtmlSnippet: React.FC<{ publish_type: 'DFP' | 'MW' | 'MAX'; formData: any }> = ({
  publish_type,
  formData
}) => {
  const [editVisible, setEditVisible] = useState(false);
  const [form] = Form.useForm();
  const [copyValue, setCopyValue] = useState('');
  const openEditModal = () => {
    setEditVisible(true);
  };
  const handleOk = () => {
    form.submit();
  };
  const handleCancel = () => {
    setEditVisible(false);
    setCopyValue('');
  };
  const onFinish = (values: any) => {
    const params = {
      type: publish_type,
      ...values,
      ...formData,
      domain: `${formData.domain}.svr.rixengine.com`
    };
    if (params.pv_domain) {
      params.domain = `bid.${params.pv_domain}`;
    }
    let res = '';
    if (publish_type === 'DFP') {
      res = generateDFPCode(params);
    }
    if (publish_type === 'MAX') {
      res = generateMaxCode(params);
    }
    if (publish_type === 'MW') {
      res = generateMWCode(params);
    }
    setCopyValue(res);
  };
  return (
    <React.Fragment>
      <Button type="link" onClick={() => openEditModal()}>
        HTML
      </Button>
      <Modal
        title={`Edit ${publish_type} Html Snippet${formData.adslotid ? `(${formData.adslotid})` : ''}`}
        open={editVisible}
        onCancel={handleCancel}
        onOk={handleOk}
        maskClosable={false}
        keyboard={false}
        okText="Create"
        width={500}
        style={{ top: '50px' }}
      >
        <Form
          initialValues={DefaultFormValues}
          onFinish={onFinish}
          onFinishFailed={e => console.log(e)}
          form={form}
          className={styles['html-form']}
          layout="vertical"
        >
          {publish_type === 'DFP' && (
            <Form.Item label="Bundle" name="bundle_id" rules={[{ required: true, message: 'Please Enter Bundle' }]}>
              <Input placeholder="Please Enter Bundle" allowClear />
            </Form.Item>
          )}
          <Form.Item label="Unit Width" name="width" rules={[{ required: true, message: 'Please Enter Unit Width' }]}>
            <InputNumber placeholder="Please Enter Unit Width" min={1} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            label="Unit Height"
            name="height"
            rules={[{ required: true, message: 'Please Enter Unit Height' }]}
          >
            <InputNumber placeholder="Please Enter Unit Height" min={1} style={{ width: '100%' }} />
          </Form.Item>
          {publish_type === 'DFP' && (
            <Form.Item label="Default UnitID" name="default_adsolt_id">
              <Input placeholder="Please Enter Default UnitID" allowClear />
            </Form.Item>
          )}
          {publish_type === 'DFP' && (
            <Form.Item
              label="Support GDPR"
              name="dgpr"
              rules={[{ required: true, message: 'Please Enter Default UnitID' }]}
            >
              <Radio.Group>
                <Radio value={0}>No</Radio>
                <Radio value={1}>Yes</Radio>
              </Radio.Group>
            </Form.Item>
          )}
        </Form>
        <div className={styles['item-container']}>
          <div className={styles['item-container-top']}>
            <span>Html Snippet:</span>
            {copyValue && copyValue !== 'params invalid' && (
              <Paragraph
                copyable={{
                  tooltips: ['Copy Value', 'Copy Success'],
                  text: copyValue
                }}
              ></Paragraph>
            )}
          </div>
          <div className={`${styles['item-copy']} ${styles['item-max-mw']}`}>{`${copyValue}`}</div>
        </div>
      </Modal>
    </React.Fragment>
  );
};

export default EditHtmlSnippet;
