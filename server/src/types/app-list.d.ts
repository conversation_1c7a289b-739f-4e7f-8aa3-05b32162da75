/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 11:06:54
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 11:56:06
 * @Description:
 */
import { Context } from 'koa';
declare namespace AppListAPI {
  type PlacementListItem = {
    plm_id: number;
    plm_name: string;
    app_id: number;
    ad_format: number;
    placement_type: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status: number;
    create_time: string;
    update_time: string;
  };
  type AppListItem = {
    app_id: number;
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    category: string;
    screen_orientation: number;
    status: number;
    create_time: string;
    update_time: string;
    placement_list: PlacementListItem[];
  };
  type AddAppParams = {
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    ios_bundle?: string;
    category?: string;
    screen_orientation: number;
    status?: number;
    tnt_id: number;
  };
  type AddPlacementParams = {
    plm_name: string;
    app_id: number;
    ad_format: number;
    placement_type: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status?: number;
    mute: number;
    tnt_id: number;
    seller_id: number;
    content: any;
    op_id: number;
  };
  type UpdateAppParams = {
    app_id: number;
    app_name: string;
    store_url: string;
    category?: string;
    tnt_id: number;
    platform: number;
  };
  type UpdatePlacementParams = {
    plm_id: number;
    plm_name: string;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status?: number;
    tnt_id: number;
    ad_format: number;
    mute: boolean;
    seller_id: number;
    content: any;
    op_id: number;
    isChangeFloor: number;
  };
  type IsAppExistsParams = {
    app_name: string;
    app_id?: number;
    seller_id: number;
    tnt_id: number;
    bundle: string;
    platform: number;
  };

  type IsPlacementExistsParams = {
    plm_name: string;
    tnt_id: number;
    plm_id?: number;
    app_id: number;
  };
  interface App {
    addApp(params: AddAppParams): Promise<boolean>;
    addPlacement(params: AddPlacementParams): Promise<boolean>;
    updateApp(params: UpdateAppParams): Promise<boolean>;
    updatePlacement(params: UpdatePlacementParams): Promise<boolean>;
    isAppExists(params: IsAppExistsParams): Promise<any[]>;
    isPlacementExists(params: IsPlacementExistsParams): Promise<any[]>;
    getAllPlacementList(tnt_id: number): Promise<PlacementListItem[]>;
  }

  interface AppService {
    addApp(params: AddAppParams): Promise<boolean>;
    addPlacement(params: AddPlacementParams): Promise<boolean>;
    updateApp(params: UpdateAppParams): Promise<boolean>;
    updatePlacement(params: UpdatePlacementParams): Promise<boolean>;
    isAppExists(params: IsAppExistsParams): Promise<{
      flag: boolean;
      type: 'APP_NAME_EXISTS' | 'APP_BUNDLE_EXISTS' | '';
    }>;
    isPlacementExists(params: IsPlacementExistsParams): Promise<any[]>;
    getAllPlacementList(tnt_id: number): Promise<PlacementListItem[]>;
  }

  interface AppCtrlInterface {
    addApp(ctx: Context): void;
    addPlacement(ctx: Context): void;
    updateApp(ctx: Context): void;
    updatePlacement(ctx: Context): void;
    getAllPlacementList(ctx: Context): void;
  }
}
