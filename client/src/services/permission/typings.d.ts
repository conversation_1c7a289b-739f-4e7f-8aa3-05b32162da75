/*
 * @Author: 袁跃钊 yuanyu<PERSON><PERSON>@algorix.co
 * @Date: 2023-03-10 15:24:29
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-04-28 15:58:47
 * @Description:
 */
declare namespace PermissionAPI {
  type MenuItem = {
    id: number;
    title: string;
    path: string;
    pid: number;
    is_hide: number;
    icon: string;
    component: string;
    sort: number;
    access?: string;
    menu_render: number;
    interfaces: number[];
    type: number;
    node_type: number;
  };
  type RoleListItem = {
    id: number;
    role_name: string;
    type: number;
    type_desc: string;
    status: number;
  };
  type PermissionItem = {
    id: number;
    // pms_name: string;
    type: number;
    role_id: number;
    rsc_id: number;
    tnt_id: number;
    create_time: string;
    update_time: string;
  };

  type RoleItem = {
    id: number;
    role_name: string;
    pms_list: string[];
    status: number;
  };
  type UserItem = {
    role_id: number;
    user_id: number;
    account_name: string;
    type: number;
    type_desc: string;
    permission: any[];
    status_desc: string;
    status: number;
    op_user_id: number;
  };
}
