/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-26 14:21:54
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 14:21:55
 * @Description:
 */

import { request } from '@umijs/max';

export async function getAppInfo(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<AppAdsAPI.AppInfoResult>>('/api/app-ads/getAppInfo', {
    data: body || {},
    ...(options || {})
  });
}
