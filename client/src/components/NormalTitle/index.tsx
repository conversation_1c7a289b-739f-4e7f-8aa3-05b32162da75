/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-05 14:45:51
 * @LastEditors: 袁跃钊 yuanyu<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-14 18:46:46
 * @Description:
 */
import React from 'react';
import styles from './index.less';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';

type NormalTitleProps = {
  blackName: string;
  grayName?: string;
  num?: string;
  isTitle?: boolean;
  bottom?: number;
  top?: number;
  className?: string;
  maxWidth?: number;
  toolTip?: boolean;
};

function NormalTitle({
  blackName,
  grayName,
  isTitle = false,
  bottom = 4,
  top = 0,
  className,
  maxWidth = 0,
  toolTip = true,
  num = ''
}: NormalTitleProps): JSX.Element {
  return (
    <HoverTooltip
      title={` ${grayName ? grayName : blackName || ''}`}
      maxWidth={maxWidth || undefined}
      toolTip={toolTip}
    >
      <span style={{ marginBottom: bottom, marginTop: top }} className={`${styles['title-con']} ${className || ''} `}>
        <span className={`${styles['black']} ${isTitle ? styles['title'] : ''} ${maxWidth ? styles['max-width'] : ''}`}>
          {blackName}
        </span>
        {grayName && (
          <>
            <span className={styles['line']}>|</span>
            <span className={`${styles['gray']} ${isTitle ? styles['title'] : ''}`}>{grayName}</span>
            {num && (
              <span className={styles['line']} style={{ marginRight: 0 }}>
                |
              </span>
            )}
          </>
        )}
        {num && (
          <>
            <span className={styles['span-num']}>{num}</span>
          </>
        )}
      </span>
    </HoverTooltip>
  );
}

export default NormalTitle;
