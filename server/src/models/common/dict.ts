import { DictItem } from '@/constants/dict';
import dbUtils from '@/db/mysql';

class DictModel {
  /**
   * 从 aat 数据库获取数据
   * @description 暂时用不到，先占位
   */
  getAatDict(params: DictItem) {
    // return dbUtilsAat.query(params.dict_content);
    // TODO 暂时用不到
    return [];
  }

  /**
   * 从 saas 数据库获取数据
   */
  getSaasDict(params: DictItem) {
    // false 表示跳过 tnt_id 检测
    return dbUtils.permissionQuery(params.dict_content);
  }
}

export const dictModel = new DictModel();
