/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-22 11:40:09
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 18:32:47
 * @Description:
 */

import { InternalUsers } from '@/constants';
import { sampleTraceModel } from '@/models';
import { TroubleShootingAPI } from '@/types/troubleshooting';

class SampleTraceService implements TroubleShootingAPI.SampleTraceService {
  async getSampleTraceTaskList(
    tnt_id: number,
    cur_time_zone: string,
    curUser: any
  ) {
    if (Object.keys(curUser).length === 0) {
      return [];
    }

    let list = await sampleTraceModel.getSampleTraceTaskList(
      tnt_id,
      cur_time_zone
    );

    const isInternalUser = InternalUsers.includes(curUser.type);

    return (
      list
        // 当前用户不是内部用户 过滤掉内部用户数据
        // 是 内部用户不过滤
        .filter(
          data => isInternalUser || !InternalUsers.includes(data.account_type)
        )
        .map(({ account_name, ...rest }) => {
          const newAccountName =
            InternalUsers.includes(rest.account_type) || !account_name
              ? 'System'
              : rest.account_status === 3
              ? 'UnKnow'
              : account_name;

          return {
            ...rest,
            account_name: newAccountName
          };
        }) as any[]
    );
  }

  async addSampleTraceTask(
    task: TroubleShootingAPI.TaskListItem
  ): Promise<boolean> {
    return !!(await sampleTraceModel.addSampleTraceTask(task));
  }

  async getSampleTraceList(
    params: TroubleShootingAPI.getSampleTraceTaskListParams,
    api_url: string
  ): Promise<any> {
    const data = await sampleTraceModel.getSampleTraceList(params, api_url);
    const list = data.map(
      (item: TroubleShootingAPI.TaskListItem, index: number) => ({
        ...item,
        id: index
      })
    );
    return list;
  }

  async updateSampleTraceTask(
    task: TroubleShootingAPI.TaskListItem,
    curUser: any
  ) {
    const userList = await sampleTraceModel.getUserInfoBySampleTraceTaskId(
      task.id,
      task.tnt_id
    );

    if (!userList || userList.length === 0) {
      return false;
    }

    const isInternalUser = InternalUsers.includes(curUser.type);
    const firstUserIsNotInternal = !InternalUsers.includes(userList[0].type);

    // 如果当前用户是内部用户，且task不是内部用户创建的，则不允许更新
    if (isInternalUser && firstUserIsNotInternal) {
      return false;
    }

    return !!(await sampleTraceModel.updateSampleTraceTask(task));
  }
}
export const sampleTraceService = new SampleTraceService();
