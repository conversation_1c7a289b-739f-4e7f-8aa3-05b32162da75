/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON>@algorix.co
 * @Date: 2023-03-07 14:48:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-04 00:00:52
 * @Description:
 */
import { menuModel } from '@/models';

class MenuServices {
  async getAllMenu() {
    return await menuModel.getAllMenu();
  }

  async getMenuList() {
    return await menuModel.getMenuList();
  }
}

export default new MenuServices();
