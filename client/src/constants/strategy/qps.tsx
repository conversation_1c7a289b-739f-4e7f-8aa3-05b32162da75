/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:19:56
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-14 19:38:40
 * @LastEditTime: 2023-03-06 16:14:55
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { StatusDesc, StatusOptions } from '@/constants';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { qpsList2Map, qpsForamt } from '@/utils/qps';
import { Tooltip } from 'antd';
import { Countries } from '@/constants/global-mapping/country';
import EllipsisPopover from '@/components/EllipsisPopover';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

const baseList = [...Array(9).keys()].map(val => (val + 1) * 100);
const QPSList = [
  '50',
  ...baseList,
  '1k',
  '1.5k',
  '2k',
  '2.5k',
  '3k',
  '3.5k',
  '4k',
  '4.5k',
  '5k',
  '5.5k',
  '6k',
  '7k',
  '8k',
  '9k',
  '10k',
  '15k',
  '20k',
  '25k',
  '30k',
  '35k',
  '40k',
  '45k',
  '50k',
  '60k',
  '70k',
  '80k'
];

const { arr: qps_arr, map: qpsMap } = qpsList2Map(QPSList);
export const QPSOptions = qps_arr;

export const QpsSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Advertiser'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    mode: 'multiple',
    value: '',
    options: [],
    placeholder: 'Please Select Publisher'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: '',
    options: StatusOptions,
    placeholder: 'Please Select Status'
  },
  {
    name: 'Type',
    type: 'select',
    key: 'level',
    mode: 'multiple',
    value: '',
    options: []
  }
];

export const QpsBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'QPS'
  }
];

export const QpsColumns: ColumnProps<StrategyAPI.QpsListItem>[] = [
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    dataIndex: 'level',
    width: 180,
    render: txt => <>{QpsLevelTypeToString[txt]}</>
  },
  {
    title: 'Content',
    dataIndex: 'ots_id',
    width: 300,
    ellipsis: { showTitle: false },
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      if (
        [
          QpsLevelType['demand + supply + ad_format'],
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['demand + ad_format']
        ].includes(row.level)
      ) {
        list = list.map((val: string) => QpsAdFormatToLabel[val] || val);
      }
      if (
        [
          QpsLevelType['demand + supply + country'],
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['demand + country']
        ].includes(row.level)
      ) {
        list = list.map((val: number) => Countries[val] || val);
      }
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  // 暂时去掉
  // {
  //   title: 'Unit',
  //   dataIndex: 'plm_name',
  //   width: 150,
  //   ellipsis: { showTitle: false },
  //   render: (txt) => (
  //     <HoverToolTip title={txt}>
  //       <span>{txt || '-'}</span>
  //     </HoverToolTip>
  //   ),
  // },
  {
    title: 'Server Region',
    dataIndex: 'region',
    width: 120,
    render: txt => (
      <HoverToolTip title={txt}>
        <span>{RegionLabel[txt] || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'QPS',
    width: 90,
    dataIndex: 'qps',
    render: (_, row) => {
      if (
        [
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['(unlimit)supply + bundle']
        ].includes(row.level)
      ) {
        return 'Unlimit';
      }
      return qpsForamt(_) || '-';
    }
  },

  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>{_}</span>
        {row['account_status'] === 3 && (
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160,
    render: val => <>{val}</>
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 85,
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  }
];

export const QpsColumnsForAdv: ColumnProps<StrategyAPI.QpsListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 150,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={(txt && `${txt}(${params.buyer_id})`) || '-'}>
        <span>{(txt && `${txt}(${params.buyer_id})`) || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    ellipsis: { showTitle: false },

    render: (txt, params) => (
      <HoverToolTip title={(txt && `${txt}(${params.seller_id})`) || '-'}>
        <span>{(txt && `${txt}(${params.seller_id})`) || '-'}</span>
      </HoverToolTip>
    )
  },
  ...QpsColumns
];

export const QpsColumnsForPub: ColumnProps<StrategyAPI.QpsListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    ellipsis: { showTitle: false },
    fixed: 'left',
    render: (txt, params) => (
      <HoverToolTip title={(txt && `${txt}(${params.seller_id})`) || '-'}>
        <span>{(txt && `${txt}(${params.seller_id})`) || '-'}</span>
      </HoverToolTip>
    )
  },
  ...QpsColumns
];

export const QpsColumnsForUnlimit: ColumnProps<StrategyAPI.QpsListItem>[] = [...QpsColumnsForPub].filter(
  v => v.dataIndex !== 'qps'
);

export const QpsLevelOptionsForAdv = [
  { label: 'Adv', value: 2 },
  { label: 'Adv + Pub', value: 3 },
  { label: 'Adv + Pub + Bundle', value: 4 },
  // +  { label: 'Adv + Ad Unit', value: 5 },
  { label: 'Adv + Pub + Ad Format', value: 6 },
  { label: 'Adv + Pub + Country', value: 7 },
  { label: 'Adv + Ad Format', value: 8 },
  { label: 'Adv + Country', value: 9 },
  { label: 'Adv + Bundle', value: 10 }
];
export const QpsLevelOptionsForPub = [{ label: 'Pub', value: 1 }];
export const QpsLevelOptionsForUnlimit = [
  { label: 'Pub + Bundle', value: 51 },
  { label: 'Pub + Country', value: 52 },
  { label: 'Pub + Ad Format', value: 53 }
];
export const QpsLevelType = {
  supply: 1,
  demand: 2,
  'demand + supply': 3,
  'demand + supply-bundle': 4,
  'demand + supply-unit': 5,
  'demand + supply + ad_format': 6,
  'demand + supply + country': 7,
  'demand + ad_format': 8,
  'demand + country': 9,
  'demand + bundle': 10,
  '(unlimit)supply + bundle': 51,
  '(unlimit)supply + country': 52,
  '(unlimit)supply + ad_format': 53
};

export const QpsLevelTypeToString: API.StringToStringType = {
  1: 'Pub',
  2: 'Adv',
  3: 'Adv + Pub',
  4: 'Adv + Pub + Bundle',
  5: 'Adv + Ad Unit',
  6: 'Adv + Pub + Ad Format',
  7: 'Adv + Pub + Country',
  8: 'Adv + Ad Format',
  9: 'Adv + Country',
  10: 'Adv + Bundle',
  51: 'Pub + Bundle',
  52: 'Pub + Country',
  53: 'Pub + Ad Format'
};

export const RegionListType = {
  'All Region': 0,
  USE: 1,
  APAC: 2
};
export const RegionListOptions = [
  // { label: 'All Region', value: 0 },
  { label: 'USE', value: 1 },
  { label: 'APAC', value: 2 },
  { label: 'EUW', value: 3 }
];
export const RegionLabel: API.StringToStringType = {
  // 0: 'All Region',
  1: 'USE',
  2: 'APAC',
  3: 'EUW'
};
export const RegionLabelMap = {
  // 0: 'All Region',
  USE: 'USE',
  APAC: 'APAC',
  EUW: 'EUW'
};
export const QpsAdFormatType = {
  Banner: 1,
  Native: 2,
  Video: 3
};

export const QpsAdFormatToLabel: API.StringToStringType = {
  1: 'Banner',
  2: 'Native',
  3: 'Video'
};

export const QpsAdFormatOptions = [
  { label: 'Banner', value: 1 },
  { label: 'Native', value: 2 },
  { label: 'Video', value: 3 }
];

export const QpsTabOptions = [
  { label: 'Advertiser QPS', value: '1' },
  { label: 'Publisher QPS', value: '2' }
  // { label: 'Unlimit QPS', value: '3' }
];

export const QpsTab = {
  advertiser: '1',
  publisher: '2',
  unlimit: '3'
};
