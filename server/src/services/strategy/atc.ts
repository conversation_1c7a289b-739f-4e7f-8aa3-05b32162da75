/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-07 11:41:41
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-07 11:41:42
 * @Description:
 */

import { atcModel } from '@/models';
import { AtcAPI } from '@/types/atc';

class AtcService implements AtcAPI.Atc {
  // eslint-disable-next-line max-len
  async isAtcExists(params: AtcAPI.IsExistParams): Promise<any[]> {
    return await atcModel.isAtcExists(params);
  }

  async getAtcList(tnt_id: number): Promise<AtcAPI.AtcListItem[]> {
    return await atcModel.getAtcList(tnt_id);
  }

  async updateAtc(params: AtcAPI.updateAtcParams): Promise<boolean> {
    return await atcModel.updateAtc(params);
  }
}

export const atcService = new AtcService();
