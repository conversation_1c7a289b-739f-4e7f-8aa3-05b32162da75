/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-12-14 17:07:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 10:23:08
 * @Description:
 */

import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { Pie, Plot } from '@ant-design/charts';

import type { Datum, PieConfig } from '@ant-design/charts';
import Card from '../Card';

import { PieOptions } from '@antv/g2plot';
import { getTopCountry } from '@/services/ai-board';
import { fetchData } from '@/utils';
import useResponsiveChart from '@/hooks/useResponsiveChart';

const TopCountry: React.FC = () => {
  const adfomatRef = useRef<any>(null);
  const countryRef = useRef<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<BoardAPI.TopCountryAdFormatItem[] | null>(null);
  const [currentCountry, setCurrentCountry] = useState<string>(totalData?.[0]?.country || '');
  const [profit, setProfit] = useState<string>('');
  const TopCountryConfig: PieConfig = {
    theme: 'custom-theme',
    appendPadding: [24, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'country',
    radius: 0.75,
    innerRadius: 0.55,
    autoFit: true,
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.country,
          value: `${+datum.revenue.toFixed(0)}`
        };
      }
    },

    label: {
      autoRotate: true,
      offset: '60%',
      style: {
        textAlign: 'center'
      },
      offsetY: -4,
      formatter: (datum, data) => {
        return datum?.revenue ? `${datum.country}-${(+datum.percent * 100).toFixed(0)}%` : `0`;
      }
    },
    statistic: {
      title: false,
      content: false
    },
    annotations: [
      {
        top: true,
        type: 'html',
        alignX: 'middle',
        alignY: 'middle',
        html: (container, view) => {
          const data = view.getOptions()?.data || [];
          const total = data.length === 0 ? '' : data.reduce((r, d) => r + d.revenue, 0).toFixed(0);

          const renderTooltip = () => `
            <div class="${styles['tooltip']}">
              <span class="${styles['tooltip-text']}">Total Revenue($)</span>
              <span class="${styles['tooltip-text']}">Profit($)</span>
            </div>
          `;

          const renderStatisticContainer = () => `
            <div class="${styles['country-statistic-container']}" id="country-statistic-container">
              <div class="${styles['country-total']}">
                <span>${total}</span>
              </div>
              <div class="${styles['country-profit']}" id="country-profit">
                <span style="font-size: 1.12em">${profit}</span>
              </div>
              ${renderTooltip()}
            </div>
          `;

          container.innerHTML = renderStatisticContainer();
        },
        position: ['50%', '50%']
      }
    ],
    interactions: [
      {
        type: 'element-single-selected'
      },
      {
        type: 'element-highlight-by-color'
      }
    ],
    legend: {
      position: 'right-bottom',
      layout: 'vertical'
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer'
        }
      }
    }
  };
  const TopAdFormatConfig: PieConfig = {
    theme: 'custom-theme',
    appendPadding: [24, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'ad_format',
    radius: 0.75,
    innerRadius: 0.55,
    tooltip: {
      customContent: (title, data: any) => {
        const adSizes = data[0]?.data?.top_ad_sizes;
        const adSizeHtml = adSizes
          ?.map((item: any) => {
            return `<div class="${styles['ad-size-item']}">
              <span>${item.ad_size}</span>
              <span class="${styles['rev']}">${+item.revenue?.toFixed(0)}</span>
            </div>`;
          })
          .join('');
        return `<div class="${styles['tooltip-container']}">
          <div class="${styles['tooltip-title']}">${title}</div>
          <div class="${styles['ad-size-container']}">${adSizeHtml}</div>
        </div>`;
      }
    },
    autoFit: true,
    label: {
      autoRotate: false,
      layout: {
        type: 'fixed-overlap'
      },
      offset: '50%',
      style: {
        textAlign: 'center'
      },
      offsetY: -4,
      formatter: (datum: Datum) => {
        return `${datum.ad_format}\n${datum.revenue.toFixed(0)}`;
      }
    },
    annotations: [
      {
        top: true,
        type: 'html',
        alignX: 'middle',
        alignY: 'middle',
        html: (container, view) => {
          let data = view.getOptions()?.data;

          const tooltipHtml = `<div class=${styles['tooltip']}>
            <span class="${styles['tooltip-text']}">Total Revenue($)</span>
            <span class="${styles['tooltip-text']}">Profit($)</span>
          </div>
          `;
          let curProfit = '0';
          if (currentCountry) {
            const item = totalData?.find(item => item.country === currentCountry);
            if (item) {
              curProfit = (item?.revenue - item.sl_revenue).toFixed(0);
            }
          }
          const html = currentCountry
            ? `<div class="${styles['country-statistic-container']}">
            <div class="${styles['country-total']}">
              <span>${data?.reduce((r, d) => r + d.revenue, 0).toFixed(0) || 0}</span>
            </div>
            <div class="${styles['country-profit']}">
              <span style={fontSize:'1.12em'}>${curProfit}</span>
            </div>
            ${tooltipHtml}
          </div>`
            : '';
          container.innerHTML = html;
        },
        position: ['50%', '50%']
      }
    ],
    statistic: {
      title: false,
      content: false
    },
    interactions: [
      {
        type: 'element-highlight'
      }
    ],
    legend: {
      layout: 'vertical',
      position: 'right-bottom',
      marker: {
        symbol: 'square'
      }
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer'
        }
      }
    }
  };
  useEffect(() => {
    if (!totalData) {
      fetchData({
        setLoading,
        request: getTopCountry,
        onSuccess: res => {
          if (typeof res === 'object') {
            setTotalData(res.data);
            setProfit((+res.profit).toFixed(0));
          }
        }
      });
    }
  }, []);
  useResponsiveChart([
    {
      chartRef: countryRef,
      screenWidth: 1024,
      smallScreenConfig: {
        legend: {
          position: 'bottom',
          layout: 'horizontal'
        }
      },
      largeScreenConfig: {
        legend: {
          position: 'right-bottom',
          layout: 'vertical'
        },
        animation: {
          appear: false
        }
      }
    },
    {
      chartRef: adfomatRef,
      screenWidth: 1024,
      smallScreenConfig: {
        legend: {
          position: 'bottom',
          layout: 'horizontal'
        }
      },
      largeScreenConfig: {
        legend: {
          position: 'right-bottom',
          layout: 'vertical'
        },
        animation: {
          appear: false
        }
      }
    }
  ]);
  // useEffect(() => {
  //   const countryPlot: Plot<PieConfig> = countryRef?.current?.getChart();
  //   const adfomatPlot: Plot<PieConfig> = adfomatRef?.current?.getChart();
  //   window.addEventListener('resize', () => {
  //     if (window.innerWidth < 1024) {
  //       countryPlot.update({
  //         legend: {
  //           position: 'bottom',
  //           layout: 'horizontal'
  //         }
  //       });
  //       adfomatPlot.update({
  //         legend: {
  //           position: 'bottom',
  //           layout: 'horizontal'
  //         },
  //         animation: {
  //           appear: false
  //         }
  //       });
  //     } else {
  //       countryPlot.update({
  //         legend: {
  //           position: 'right-bottom',
  //           layout: 'vertical'
  //         }
  //       });
  //       adfomatPlot.update({
  //         legend: {
  //           position: 'right-bottom',
  //           layout: 'vertical'
  //         },
  //         animation: {
  //           appear: false
  //         }
  //       });
  //     }
  //   });
  //   return () => {
  //     window.removeEventListener('resize', () => {});
  //   };
  // }, []);
  useEffect(() => {
    const countryPlot: Plot<PieConfig> = countryRef?.current?.getChart();
    const adfomatPlot: Plot<PieConfig> = adfomatRef?.current?.getChart();
    if (totalData?.length) {
      const topCountryData = totalData.map(item => {
        return {
          country: item.country,
          revenue: item.revenue
        };
      });
      const config = {
        ...TopCountryConfig,
        data: topCountryData
      };

      countryPlot.update({
        ...config,
        animation: {
          appear: false
        },
        legend: {
          position: window.innerWidth > 828 ? 'right-bottom' : 'bottom',
          layout: window.innerWidth > 828 ? 'vertical' : 'horizontal'
        }
      });
      countryPlot.setState('selected', (data: Datum) => {
        return data.country === currentCountry;
      });
      if (!currentCountry) {
        // 累加top_ad_formats 中的数据以及top_ad_sizes中的数据
        const top_ad_formats = totalData.map(item => item.top_ad_formats).flat();
        const all_ad_format = top_ad_formats.reduce((r, d) => {
          if (!r[d.ad_format]) {
            r[d.ad_format] = { ...d };
          } else {
            r[d.ad_format].revenue += +d.revenue;
            r[d.ad_format].top_ad_sizes = [...r[d.ad_format].top_ad_sizes, ...d.top_ad_sizes];
          }
          return r;
        }, {} as any);
        let adfomatData = Object.values<BoardAPI.TopCountryAdFormatItem>(all_ad_format);
        adfomatData.forEach((item: any) => {
          // ad_size相同的数据累加
          const top_ad_sizes: any = {};
          const ad_sizes = [...item.top_ad_sizes];
          ad_sizes.forEach(item => {
            if (!top_ad_sizes[item.ad_size]) {
              top_ad_sizes[item.ad_size] = { ...item };
            } else {
              top_ad_sizes[item.ad_size].revenue += +item.revenue;
            }
          });

          item.top_ad_sizes = Object.values(top_ad_sizes);
          item.top_ad_sizes = item.top_ad_sizes
            .sort((a: any, b: any) => b.revenue - a.revenue)
            .slice(0, 3)
            .map((item: any) => {
              return {
                ad_size: item.ad_size,
                revenue: +(+item.revenue).toFixed(0)
              };
            });
        });
        adfomatPlot.update({
          ...TopAdFormatConfig,
          data: adfomatData || [],
          legend: {
            position: window.innerWidth > 828 ? 'right-bottom' : 'bottom',
            layout: window.innerWidth > 828 ? 'vertical' : 'horizontal'
          }
        });
      } else {
        const adfomatData =
          totalData.find((item, index) => {
            return item.country === currentCountry;
          })?.top_ad_formats || [];

        adfomatPlot.update({
          ...TopAdFormatConfig,
          data: adfomatData || [],
          legend: {
            position: window.innerWidth > 828 ? 'right-bottom' : 'bottom',
            layout: window.innerWidth > 828 ? 'vertical' : 'horizontal'
          }
        });
      }
    }
  }, [totalData, currentCountry]);

  const topCountryOnReady = (plot: Plot<PieOptions>) => {
    plot.on('element:click', (evt: any) => {
      const states = plot.getStates();
      const isSelectedCty = states.some((item: any) => item.state === 'selected');
      if (isSelectedCty) {
        const country = evt.data?.data.country;
        setCurrentCountry(country);
      } else {
        setCurrentCountry('');
      }
    });
  };

  return (
    <div className={styles['container']}>
      <Card
        title="The table below shows yesterday statistics"
        loading={loading}
        headline="All Statistics"
        className={styles['country-top-country-card']}
      >
        <div className={styles['top-country-container']}>
          <Pie {...TopCountryConfig} ref={countryRef} onReady={topCountryOnReady} />
        </div>
      </Card>

      <Card
        title={
          <>
            Top Ad Format:{' '}
            <span style={{ color: 'var(--primary-color)', fontWeight: 700 }}>({currentCountry || 'All Country'})</span>
          </>
        }
        loading={loading}
        className={styles['country-top-tnt-card']}
      >
        <div className={styles['country-top-tnt-container']}>
          <Pie {...TopAdFormatConfig} ref={adfomatRef} />
        </div>
      </Card>
    </div>
  );
};

export default TopCountry;
