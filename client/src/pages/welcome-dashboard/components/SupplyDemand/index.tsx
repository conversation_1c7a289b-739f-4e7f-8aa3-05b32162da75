/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-01-16 17:45:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 10:24:10
 * @Description:
 */
import React, { useEffect, useState, useRef } from 'react';
import styles from './index.less';
import { G2, Pie, type Plot } from '@ant-design/charts';
import type { PieConfig } from '@ant-design/charts';
import Card from '../Card';
import { fetchData } from '@/utils';
import { getSupplyDemand } from '@/services/ai-board';
import useResponsiveChart from '@/hooks/useResponsiveChart';
const Page: React.FC = () => {
  const { registerTheme } = G2;
  registerTheme('theme', {
    colors10: ['#0CAFC7', '#2AC75F', '#FFD335', '#FFA346', '#FA6041']
  });
  const [loading, setLoading] = useState(false);
  const suppleRef = useRef<any>(null);
  const demandRef = useRef<any>(null);
  const [totalData, setTotalData] = useState({
    supplyData: [],
    demandData: []
  });
  const [supplyData, setSupplyData] = useState<BoardAPI.SupplyDemandItem[]>([]);
  const [demandData, setDemandData] = useState<BoardAPI.SupplyDemandItem[][]>([]);
  const SupplyConfig: PieConfig = {
    theme: 'theme',
    appendPadding: [24, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'seller_name',
    radius: 0.75,
    innerRadius: 0.55,
    autoFit: true,
    tooltip: {
      customContent: (title, data: any) => {
        const adFormats = data[0]?.data?.top_ad_formats;
        const rev = data[0]?.data?.revenue ? data[0]?.data?.revenue.toFixed(0) : 0;
        const adSizeHtml = adFormats
          ?.map((item: any) => {
            return `<div class="${styles['ad-size-item']}">
              <span>${item.ad_format}</span>
              <span class="${styles['rev']}">${+item.revenue.toFixed(0)}</span>
            </div>`;
          })
          .join('');
        return `<div class="${styles['tooltip-container']}">
          <div class="${styles['tooltip-title']}">
            <span>${title}</span>
            <span>${rev}</span>
          </div>
          <div class="${styles['ad-size-container']}">${adSizeHtml}</div>
        </div>`;
      }
    },
    label: {
      autoRotate: true,
      labelHeight: 30,
      formatter: (datum, data) => {
        const seller_name = datum.seller_name?.length > 12 ? `${datum.seller_name.slice(0, 12)}...` : datum.seller_name;
        return datum?.revenue
          ? `${seller_name}-${(+datum.percent * 100).toFixed(0)}%\n${+datum.revenue.toFixed(0)}`
          : `0`;
      },
      layout: {
        type: 'fixed-overlap'
      },
      offset: '40%',
      style: {
        textAlign: 'center'
      },
      offsetY: -4
    },
    statistic: {
      title: false,
      content: false
    },
    interactions: [
      {
        type: 'element-single-selected'
      },
      {
        type: 'element-highlight-by-color'
      }
    ],
    legend: {
      position: 'right-bottom',
      layout: 'vertical',

      maxItemWidth: 100
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer'
        }
      }
    }
  };
  const DemandConfig: PieConfig = {
    theme: 'theme',
    appendPadding: [24, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'buyer_name',
    radius: 0.75,
    innerRadius: 0.55,
    tooltip: {
      customContent: (title, data: any) => {
        const countries = data[0]?.data?.top_countries;
        const rev = data[0]?.data?.revenue;
        const Html = countries
          ?.map((item: any) => {
            return `<div class="${styles['ad-size-item']}">
              <span>${item.country}</span>
              <span class="${styles['rev']}">${+item.revenue.toFixed(0)}</span>
            </div>`;
          })
          .join('');
        return `<div class="${styles['tooltip-container']}">
          <div class="${styles['tooltip-title']}">
            <span>${title}</span>
            <span>${rev ? +rev.toFixed(0) : ''}</span>
          </div>
          <div class="${styles['ad-size-container']}">${Html}</div>
        </div>`;
      }
    },
    autoFit: true,
    label: {
      autoRotate: true,
      labelHeight: 30,
      formatter: (datum, data) => {
        const buyer_name = datum.buyer_name?.length > 12 ? `${datum.buyer_name.slice(0, 12)}...` : datum.buyer_name;
        return datum?.revenue
          ? `${buyer_name}-${(+datum.percent * 100).toFixed(0)}%\n${+datum.revenue.toFixed(0)}`
          : `0`;
      },
      layout: {
        type: 'fixed-overlap'
      },
      offset: '40%',
      style: {
        textOverflow: 'ellipsis',
        textAlign: 'center'
      },
      offsetY: -4
    },
    statistic: {
      title: false,
      content: false
    },
    interactions: [
      {
        type: 'element-highlight'
      },
      {
        type: 'element-selected'
      }
    ],
    legend: {
      layout: 'vertical',
      position: 'right-bottom',
      marker: {
        symbol: 'square'
      },

      maxItemWidth: 100
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer'
        }
      }
    }
  };
  useResponsiveChart([
    {
      chartRef: suppleRef,
      screenWidth: 1024,
      smallScreenConfig: {
        legend: {
          position: 'bottom',
          layout: 'horizontal'
        }
      },
      largeScreenConfig: {
        legend: {
          position: 'right-bottom',
          layout: 'vertical'
        },
        animation: {
          appear: false
        }
      }
    },
    {
      chartRef: demandRef,
      screenWidth: 1024,
      smallScreenConfig: {
        legend: {
          position: 'bottom',
          layout: 'horizontal'
        }
      },
      largeScreenConfig: {
        legend: {
          position: 'right-bottom',
          layout: 'vertical'
        },
        animation: {
          appear: false
        }
      }
    }
  ]);
  useEffect(() => {
    fetchData({
      request: getSupplyDemand,
      setLoading,
      onSuccess: res => {
        setTotalData(res);
      }
    });
  }, []);
  useEffect(() => {
    const supplyPlot: Plot<PieConfig> = suppleRef?.current?.getChart();
    const demandPlot: Plot<PieConfig> = demandRef?.current?.getChart();
    if (supplyData?.length) {
      supplyPlot.changeData(supplyData);
    }
    if (demandData?.length) {
      demandPlot.changeData(demandData);
    }
  }, [supplyData, demandData]);
  useEffect(() => {
    if (!totalData) return;
    if (Array.isArray(totalData.supplyData)) {
      setSupplyData(totalData.supplyData);
    }
    if (Array.isArray(totalData.demandData)) {
      setDemandData(totalData.demandData);
    }
  }, [totalData]);
  return (
    <div className={styles['container']}>
      <Card
        headline="Publisher"
        className={styles['supply-container-card']}
        loading={loading}
        title="The table below shows yesterday statistics"
      >
        <div className={styles['supply-container']}>
          <Pie {...SupplyConfig} ref={suppleRef} />
        </div>
      </Card>
      <Card
        headline="Advertiser"
        className={styles['demand-container-card']}
        loading={loading}
        title="The table below shows yesterday statistics"
      >
        <div className={styles['demand-container']}>
          <Pie {...DemandConfig} ref={demandRef} />
        </div>
      </Card>
    </div>
  );
};
export default Page;
