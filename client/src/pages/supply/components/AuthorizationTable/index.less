.card-container {
  border-radius: 6px;
  margin-top: 24px;
  margin-right: 16px;
  border: none;
  :global {
    .ant-card-head {
      color: var(--text-color);
      font-weight: 700;
      font-size: 16px;
      border-bottom: none;
    }
    .ant-card-body {
      padding-top: 0px;
    }
  }
}

.container {
  display: flex;
  justify-content: center;
  flex-direction: column;
  .top {
    display: flex;
    align-items: center;
    padding-bottom: 16px;
    .top-left {
      width: 500px;
      :global {
        .ant-btn {
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .top-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      > div {
        &:first-child {
          margin-right: 12px;
        }
      }
      :global {
        .ant-btn-dangerous.ant-btn-primary[disabled] {
          border: none;
        }
      }
    }
  }
}
