/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:32:59
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-09 10:40:51
 * @Description:
 */
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import {
  DashboardScheme,
  DownloadDashboardScheme
} from '@/schema/report/dashboard';
import { dashboardService } from '@/services';
import { DashboardAPI } from '@/types/dashboard';
import { getUserIp } from '@/utils';
import { sendLarkAlertCard } from '@/utils/alert';
import { getUserRole } from '@/utils/report/getBQLabel';
import { getCtxBackResult, getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class DashboardCtrl implements DashboardAPI.DashboardCtrlInterface {
  @validateBody(DashboardScheme)
  async getDashboardList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const list = await dashboardService.getNewDashboardList(formData, {
      tag: api_url,
      tenantId: formData.tnt_id,
      userRole: getUserRole(userType)
    });

    if (Array.isArray(list.data)) {
      result = getCtxBackResult('SUCCESS', list);
    } else if (typeof list === 'string') {
      result = getCtxBackResult('PARAMS_INVALID', list);
    }

    ctx.body = result;
  }

  @validateBody(DownloadDashboardScheme)
  async downloadAllReport(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const { user_id } = ctx.session!;
    // 1053 iion租户下 Data_Custom角色有自定义的列名
    if (formData.tnt_id === 1053) {
      formData.role_id = formData.cur_role_id;
    }
    try {
      const userType = ctx.state.curUser?.type;
      const data = await dashboardService.downloadAllReport(formData, user_id, {
        tag: api_url,
        tenantId: formData.tnt_id,
        userRole: getUserRole(userType)
      });
      if (data && data.code === Code.ERROR_SYS) {
        result = getCtxResult('DOWNLOAD_ERROR');
      } else if (data && data.code === Code.SUCCESS) {
        result = getCtxResult('SUCCESS', data);
      }
    } catch (err: any) {
      getLogger('error').error(`downloadAllReport error=[${err?.message}]`);
      result = getCtxResult('DOWNLOAD_ERROR');
      const { cur_log_id, curUser } = ctx.state;
      const { account_name = '', tnt_id = 0 } = curUser || {};
      const ip = getUserIp(ctx);
      sendLarkAlertCard({
        title: 'Saas Std Platform Download Report Error',
        data: {
          'Log ID': cur_log_id,
          IP: ip,
          'API URL': ctx.request.originalUrl,
          'Page URL': ctx.request.header.referer,
          Operator: `${account_name}(${user_id})`,
          'Tenant ID': tnt_id || 0,
          'Session ID': ctx.session!.session_id || 0,
          'User Agent': ctx.request.headers['user-agent'],
          'x-time-zone': ctx.request.headers['x-time-zone'],
          Params: ctx.request.body || {},
          Error: err,
          Result: result
        }
      });
    } finally {
      ctx.body = result;
    }
  }

  async getConfigQps(ctx: Context) {
    let result = { ...RESULT };
    const data = await dashboardService.getConfigQps(ctx.session!.tnt_id);
    if (Array.isArray(data)) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export default new DashboardCtrl();
