/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-20 16:58:53
 * @Description:
 */
import React, { RefObject, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { Button, Tooltip } from 'antd';
import type { TableProps, TablePaginationConfig } from 'antd';
import { ColumnProps } from 'antd/es/table';
import type { ButtonProps } from 'antd/es/button';
import type { RangePickerProps } from 'antd/es/date-picker';
import styles from './index.less';
import Table from '../OriginalTable';
import TopBar, { TopBarSearchItem, SearchResultItem, TopBarRef } from '@/components/TopBar';
import type { FormProps } from 'antd';
import Pagination from '@/components/Pagination/NormalPagination';
import { useAccess, useLocation } from '@umijs/max';
import moment from 'moment';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { FormInstance } from 'antd/es/form';
import { parse } from 'query-string';

export type ButtonType = ButtonProps & {
  label: string;
  onClick: (params: any) => void;
  accessCode?: string;
  tooltip?: string;
};

export type ColumnsType<T> = ColumnProps<T> & {
  access?: string; // 权限code
};

type NormalTableProps<T, K> = TableProps<T> &
  FormProps & {
    loading: boolean;
    columns: ColumnsType<T>[];
    dataSource: T[];
    rowKey: string;
    btnOptions?: ButtonType[];
    request?: () => void;
    pagination?: TablePaginationConfig;
    pageTitle?: React.ReactNode;
    searchOptions: TopBarSearchItem[];
    searchFormRef?: RefObject<FormInstance<any> | undefined>;
    isFold?: boolean;
    labelWidth?: number;
    isBtnTable?: boolean;
    tabOptions?: { label: string; value: string | number }[];
    defaultTab?: string | number;
    onTabChange?: (tab: string | number) => void;
    defaultFold?: boolean;
    emptyRender?: () => JSX.Element;
    defaultFormItemWidth?: number;
    noFilterParentRow?: boolean; // 兼容可展开表格父级没有数据的时的过滤情况，如Bid Floor -> Supply Floor表格
    showTopBar?: boolean;
    ExtraRangePickerOptions?: RangePickerProps;
    handleSearch?: (val: SearchResultItem[]) => void; // 搜索
    isBackSearch?: boolean; // 条件改变重新请求数据
    isExport?: boolean;
    handleExport?: (tableData: T[]) => void;
    onSearchValueChange?: (allValue: any, form: FormInstance<any>) => void;
    allowSaveSearch?: boolean;
    defaultSearchValue?: any;
  };

const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

const getCurrentData = <T, >(data: T[], page: number, pageSize: number): T[] => {
  if (!data) {
    return [];
  }
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  return data.slice(start, end);
};

function NormalTable<T extends object>({
  columns,
  dataSource,
  rowKey,
  btnOptions = [],
  request,
  pagination,
  pageTitle,
  searchOptions,
  searchFormRef,
  isFold,
  labelWidth = 80,
  loading,
  isBtnTable = true,
  tabOptions = [],
  defaultFold = true,
  defaultTab = '',
  onTabChange = () => {},
  emptyRender,
  initialValues,
  defaultFormItemWidth,
  noFilterParentRow = false,
  showTopBar = true,
  ExtraRangePickerOptions,
  isBackSearch,
  handleSearch,
  isExport,
  handleExport,
  onSearchValueChange,
  allowSaveSearch,
  defaultSearchValue,
  ...rest
}: NormalTableProps<T, keyof T>): JSX.Element {
  const access = useAccess();
  const [tableData, setTableData] = useState<T[]>(dataSource);
  const [currentPage, setCurrentPage] = useState<number>(DefCurrentPage);
  const [pageSize, setPageSize] = useState<number>(PageSize);
  const [curPageData, setCurPageData] = useState<T[]>([]);
  const [normalOptions, setNormalOptions] = useState<TopBarSearchItem[]>([]);
  const [boxOptions, setBoxOptions] = useState<TopBarSearchItem[]>([]);
  const [isBottomFold, setBottomFold] = useState<any>({}); // checkbox折叠使用
  const [searchVal, setSearchVal] = useState<SearchResultItem[]>([]);
  const [flag, setFlag] = useState<boolean>(true);
  const [currentRouteSearch, setCurrentRouteSearch] = useState<any>({});

  useEffect(() => {
    const normals = searchOptions.filter(item => item.type !== 'checkboxFold');
    const boxs = searchOptions.filter(item => item.type === 'checkboxFold');
    setNormalOptions(normals);
    setBoxOptions(boxs);
    const tmp = JSON.parse(JSON.stringify(isBottomFold));
    boxOptions.forEach(item => {
      tmp[item.key] = defaultFold;
    });
    setBottomFold(tmp);
  }, [searchOptions]);

  // 去掉无权限的
  const tableColumns = useMemo(() => {
    const tmp = columns.filter((v: any) => {
      return (!v.access || access[v.access as keyof typeof access]);
    });
    return tmp;
  }, [columns]);

  const handleBottomFold = (key: string) => {
    const tmp = JSON.parse(JSON.stringify(isBottomFold));
    tmp[key] = !isBottomFold[key];
    setBottomFold(tmp);
  };

  const handlePageChange = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
    setCurPageData(getCurrentData(tableData, page, pageSize));
  };

  const reset = () => {
    setCurrentPage(DefCurrentPage);
    setPageSize(PageSize);
  };
  const location = useLocation();
  const { search } = location;

  useEffect(() => {
    if (!dataSource && request) {
      request();
    } else {
      if (!isBackSearch) {
        if (flag && dataSource.length && initialValues) {
          setFlag(false);
          let tmpRouteSearch: any = { ...initialValues };
          if (search && allowSaveSearch) {
            const params = parse(decodeURI(search));
            tmpRouteSearch = { ...params };
            // eslint-disable-next-line guard-for-in
            for (const key in tmpRouteSearch) {
              const isMultiple = searchOptions.some(v => v.key === key && v.mode === 'multiple');
              if (isMultiple) {
                tmpRouteSearch[key] = (params[key] as string)?.split(',').map(v => {
                  return isNaN(+v) ? v : +v;
                });
              }
            }
          }
          const tmp: SearchResultItem[] = Object.keys({ ...tmpRouteSearch }).map(key => {
            return {
              key,
              value: tmpRouteSearch[key],
              mode: Array.isArray(tmpRouteSearch[key])
                ? 'multiple'
                : typeof tmpRouteSearch[key] === 'string'
                  ? 'single'
                  : undefined
            };
          });
          setCurrentRouteSearch(tmpRouteSearch);
          handleSearchChange(tmp);
        } else {
          handleSearchChange(searchVal);
        }

        reset();
      } else {
        setTableData(dataSource);
      }
    }
  }, [dataSource, isBackSearch, search]);

  useEffect(() => {
    setCurrentPage(1);
    setPageSize(PageSize);
    setCurPageData(getCurrentData(tableData, DefCurrentPage, PageSize));
  }, [tableData]);

  const handleFilterData = (item: any, searchVal: SearchResultItem[]) => {
    const hasAllChildren = !!(Array.isArray(item.allChildren) && item.allChildren.length); // 克隆数据，避免影响外层数据
    const hasChlidren = !!(Array.isArray(item.children) && item.children.length);
    let allChildrenData: any[] = [];
    if (hasAllChildren) {
      allChildrenData = item.allChildren;
      item.children = allChildrenData.filter(item => handleFilterData(item, searchVal));
    } else {
      item.allChildren = item.children;
      allChildrenData = item.children;
      if (hasChlidren) {
        item.children = allChildrenData.filter(item => handleFilterData(item, searchVal));
      }
    }
    !item.children?.length && delete (item as FormProps).children;
    const flag = searchVal.every(obj => {
      const vl = item[obj.key as keyof T] === 0 ? 0 : !item[obj.key as keyof T] ? '' : item[obj.key as keyof T];
      if (typeof obj.value === 'string' && vl) {
        // 模糊搜索
        if (obj.fuzzySearch) {
          let extraValues = '';
          // 额外的key值(待匹配的项)
          if (obj.fuzzySearch.extraKeys?.length) {
            const extraKeys = obj.fuzzySearch.extraKeys;
            extraValues = extraKeys
              .map(key => {
                return item[key as keyof T] === 0 ? 0 : !item[key as keyof T] ? '' : item[key as keyof T];
              })
              .join('');
          }
          const target = `${vl}${extraValues}`.toLowerCase();
          return target.includes(obj.value.toLowerCase());
        }
        return `${vl}`.toLowerCase().indexOf(obj.value.toLowerCase()) !== -1;
      }

      if (obj.isDate && obj.value) {
        return moment(vl).isBetween(obj.value[0], obj.value[1]);
      } else if (obj.isDate && !obj.value) {
        return true;
      }

      if (Array.isArray(obj.value) && obj.mode === 'multiple') {
        if (Array.isArray(vl)) {
          return vl.some(v => obj.value.includes(v));
        }
        return obj.value.includes(vl);
      }

      // eslint-disable-next-line eqeqeq
      return vl == obj.value;
    });

    return flag;
  };
  const handleSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
    if (isBackSearch) {
      handleSearch && handleSearch(val);
    } else {
      handleFilter(val);
    }
  };

  const handleFilter = (val: SearchResultItem[]) => {
    // 过滤空值

    const tmp = val.filter(item => {
      if (Array.isArray(item.value)) {
        return item.value.length;
      } else {
        return item.value || +item.value === 0;
      }
    });

    let data = [];
    if (noFilterParentRow) {
      data = (dataSource as any).filter((item: any) => {
        if (item.allChildren?.length) {
          item.children = item.allChildren;
        }
        const childrenData = item.children?.filter((child: any) => {
          const filterItem = handleFilterData(child, tmp);
          return filterItem;
        });
        if (childrenData?.length) {
          item.children = childrenData;
          return true;
        } else {
          return false;
        }
      });
    } else {
      data = dataSource.filter((item: any) => {
        const filterItem = handleFilterData(item, tmp);
        return filterItem;
      });
    }
    setTableData(data);
  };

  const handleReload = () => {
    request && request();
  };
  const handleSearchValueChange = (
    changeValue: any,
    allValue: any,
    isDefaultFold: boolean,
    form: FormInstance<any>
  ) => {
    const tmp: SearchResultItem[] = Object.keys(allValue).map(key => {
      return {
        key,
        value: allValue[key],
        mode: Array.isArray(allValue[key]) ? 'multiple' : typeof allValue[key] === 'string' ? 'single' : undefined
      };
    });
    setSearchVal(tmp);
    onSearchValueChange?.(allValue, form);
  };

  const handleDefaultSearchChange = (data: SearchResultItem[]) => {
    handleSearchChange(data);
  };

  const topBarRef = useRef<TopBarRef>(null);

  useImperativeHandle(
    searchFormRef,
    () => topBarRef.current?.getFormInstance(),
    [topBarRef.current],
  );

  const handleExportData = () => {
    // 需要传入数据
    handleExport && handleExport(tableData);
  };

  return (
    <div className={styles['front-table-container']}>
      {showTopBar && (
        <div className={styles['front-table-header']}>
          <TopBar
            toptitle={pageTitle}
            handleSearchChange={handleSearchChange}
            handleReload={handleReload}
            isFold={isFold}
            labelWidth={labelWidth}
            loading={loading}
            isFront={true}
            tabOptions={tabOptions}
            defaultTab={defaultTab}
            onTabChange={onTabChange}
            normalOptions={normalOptions}
            boxOptions={boxOptions}
            ref={topBarRef}
            setNormalOptions={setNormalOptions}
            setBoxOptions={setBoxOptions}
            defaultFold={defaultFold}
            isBottomFold={isBottomFold}
            handleBottomFold={handleBottomFold}
            searchOptions={searchOptions}
            initialValues={initialValues}
            defaultFormItemWidth={defaultFormItemWidth}
            ExtraRangePickerOptions={ExtraRangePickerOptions}
            isExport={isExport}
            handleExport={handleExportData}
            handleSearchValueChange={handleSearchValueChange}
            allowSaveSearch={allowSaveSearch}
            currentRouteSearch={currentRouteSearch}
            defaultValue={defaultSearchValue}
            handleDefaultSearchChange={handleDefaultSearchChange}
          />
        </div>
      )}

      <div className={`${styles['bottom-container']}`}>
        <div className={styles['bottom-top']}>
          <div className={styles['top-left']}>
            {btnOptions?.map(({ label, onClick, accessCode, tooltip, ...rest }: ButtonType, index) => {
              return (
                <div key={index}>
                  <Button {...rest} onClick={onClick} disabled={accessCode ? access.DisabledButton(accessCode) : false}>
                    {label}
                  </Button>
                  {tooltip && (
                    <Tooltip title={tooltip}>
                      <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
                    </Tooltip>
                  )}
                </div>
              );
            })}
          </div>
          <div className={styles['top-right']}>
            <Pagination
              onChange={handlePageChange}
              total={tableData?.length || 0}
              pageSize={pageSize}
              current={currentPage}
              showSizeChanger
              size="small"
              responsive
              showTotal={total => `Total ${total} items`}
              pageSizeOptions={PageSizeOptions}
              {...(pagination as TablePaginationConfig)}
            />
          </div>
        </div>
        <Table<T>
          {...rest}
          columns={tableColumns as ColumnProps<T>[]}
          dataSource={curPageData}
          rowKey={rowKey}
          loading={loading}
          isBtnTable={isBtnTable}
          scroll={rest.scroll ? rest.scroll : { y: 'calc(100vh - 220px)' }}
          emptyRender={emptyRender}
        />
      </div>
    </div>
  );
}

export default NormalTable;
