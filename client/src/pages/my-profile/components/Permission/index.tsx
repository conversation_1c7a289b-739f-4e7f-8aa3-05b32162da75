/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-22 10:39:35
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 16:15:16
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';

import { useModel, useAccess } from 'umi';

import NormalModal from '@/components/Modal/NormalModal';
import NormalInput from '@/components/Input/NormalInput';
import RixEngineFont from '@/components/RixEngineFont';
import PermissionContent from '../PermissionContent';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';

import { Layout, Menu, Button, Form, message, Modal, Spin } from 'antd';
const { Sider, Content } = Layout;

import { RoleType } from '@/constants/permission/role';

import { addRole, updateRole, editRole, deleteRole } from '@/services/permission';
import { fetchData } from '@/utils';
import { isArrSame } from '@/utils/permission';

const Permission: React.FC = () => {
  const access = useAccess();
  const [first, setFirst] = useState(true);
  const [currentRole, setCurrentRole] = useState<PermissionAPI.RoleItem | undefined>(undefined);
  const [isEdit, setIsEdit] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [form] = Form.useForm();
  const [selectedNodes, setSelectedNodes] = useState<string[]>([]);
  const [menuSelectedKeys, setMenuSelectedKeys] = useState<string[]>([`${RoleType.Administrator}`]);
  const [isChange, setIsChange] = useState(false);
  const { dataSource: RoleList, reload: reloadRoleList, loading: loadingRoleList } = useModel('useRoleList');

  useEffect(() => {
    console.log('firlst', first, RoleList);
    if (!RoleList) reloadRoleList();
    if (first && RoleList?.length) {
      setFirst(false);
    }
  }, []);
  useEffect(() => {
    if (currentRole && showModal && isEdit) {
      form.setFieldsValue({
        role_name: currentRole.role_name
      });
    } else {
      form.resetFields();
    }
  }, [showModal]);
  useEffect(() => {
    if (Array.isArray(RoleList) && RoleList.length > 0) {
      console.log(isAdd, first);
      if (first) {
        setMenuSelectedKeys([`${RoleType.Administrator}`]);
        setCurrentRole(RoleList?.find((item: PermissionAPI.RoleItem) => `${item.id}` === `${RoleType.Administrator}`));
        return;
      }
      if (isAdd) {
        setMenuSelectedKeys([`${RoleList?.[RoleList.length - 1].id}`]);
        setCurrentRole(RoleList?.[RoleList.length - 1]);
      }
    }
  }, [RoleList, isAdd]);
  const handleRoleMenuClick = (Info: any) => {
    setMenuSelectedKeys([`${Info.key}`]);
    const roleData = RoleList?.find((item: PermissionAPI.RoleItem) => `${item.id}` === Info.key);
    setCurrentRole(roleData);
  };
  const handleEditRole = (e: any, item: PermissionAPI.RoleItem) => {
    e.stopPropagation();
    setMenuSelectedKeys([`${item.id}`]);
    setCurrentRole(item);
    setShowModal(true);
    setIsEdit(true);
  };
  const handleDeleteRole = (e: any, item: PermissionAPI.RoleItem) => {
    e.stopPropagation();
    setMenuSelectedKeys([`${item.id}`]);
    setCurrentRole(item);
    Modal.confirm({
      title: `Are you sure delete the role ${item.role_name}?`,
      onOk: () => {
        const delSuccess = () => {
          message.success('Success');
          setMenuSelectedKeys([`${RoleType.Administrator}`]);
          setCurrentRole(
            RoleList?.find((item: PermissionAPI.RoleItem) => `${item.id}` === `${RoleType.Administrator}`)
          );
          reloadRoleList();
        };
        fetchData({ request: deleteRole, params: { id: item.id }, onSuccess: delSuccess });
      }
    });
  };

  const handleAddRole = () => {
    setIsEdit(false);
    setShowModal(true);
  };
  const handleConfirm = () => {
    form.submit();
  };
  const onCancel = () => {
    setShowModal(false);
    form.resetFields();
  };
  const onSuccess = (data: any) => {
    message.success('Success');
    setShowModal(false);
    form.resetFields();
    !isEdit && setIsAdd(true);
    first && setFirst(false);
    reloadRoleList();
  };
  const handleFinish = (values: any) => {
    const params = {
      ...values,
      id: currentRole?.id,
      role_name: values?.role_name?.trim()
    };
    isEdit ? fetchData({ request: updateRole, params, onSuccess }) : fetchData({ request: addRole, params, onSuccess });
  };

  const onSelectPms = (selectedNodes: any) => {
    const bofore = currentRole?.pms_list || [];
    const current = selectedNodes.map((item: any) => item.id);
    const pms_change = !isArrSame(bofore, current);
    setIsChange(pms_change);
    const selectedKeys = selectedNodes.map((item: any) => {
      return {
        type: item.type,
        rsc_id: item.id
      };
    });
    setSelectedNodes(selectedKeys);
  };
  const handleSubmitPermission = () => {
    const params = {
      permissions: selectedNodes,
      id: currentRole?.id,
      pms_change: isChange
    };
    const onSuccess = () => {
      message.success('Success');
      reloadRoleList();
      setIsAdd(false);
      first && setFirst(false);
    };
    fetchData({ request: editRole, params: params, onSuccess });
  };

  return (
    <Layout className={styles['permission-page']}>
      <Spin spinning={loadingRoleList}>
        <Sider width={192} style={{ overflowY: 'auto' }}>
          <Button
            onClick={handleAddRole}
            disabled={access.DisabledButton('addRoleAuth')}
            type="primary"
            className={styles['add-role-btn']}
            icon={<RixEngineFont type="rix-add" />}
          >
            Add Role
          </Button>
          <Menu
            mode="vertical"
            defaultSelectedKeys={['Role1']}
            onClick={handleRoleMenuClick}
            selectedKeys={menuSelectedKeys}
          >
            {RoleList?.map((item: any, index: number) => {
              return (
                <Menu.Item key={item.id} className={styles['role-menu-item']}>
                  <div className="ellipsis role-item-name">
                    <HoverToolTip title={item.role_name}>
                      <span>{item.role_name}</span>
                    </HoverToolTip>
                  </div>

                  <div className={styles['menu-btn']}>
                    {![RoleType.Administrator, RoleType.Operator, RoleType['Data Analyst']].includes(item.id) && (
                      <>
                        <RixEngineFont
                          type="edit"
                          handleClick={e => {
                            handleEditRole(e, item);
                          }}
                          className={styles['edit-btn']}
                          accessCode="editRoleAuth"
                          disabled={[RoleType.Administrator, RoleType.Operator, RoleType['Data Analyst']].includes(
                            item.id
                          )}
                        />
                        <RixEngineFont
                          type="rix-trash"
                          className={styles['delete-btn']}
                          handleClick={e => handleDeleteRole(e, item)}
                          accessCode="deleteRoleAuth"
                        />
                      </>
                    )}
                  </div>
                </Menu.Item>
              );
            })}
          </Menu>

          <NormalModal
            okText="Confirm"
            title={isEdit ? `Edit Role` : `Add Role`}
            onOk={handleConfirm}
            open={showModal}
            onCancel={onCancel}
            width={494}
            confirmLoading={false}
            maskClosable={true}
            style={{ top: 200 }}
            className={styles.addUserModal}
          >
            <Form
              form={form}
              layout="horizontal"
              labelCol={{ style: { width: 142 }}}
              wrapperCol={{ span: 24 }}
              labelAlign="left"
              onFinish={handleFinish}
              // onValuesChange={handleValueChange}
              validateTrigger={['onBlur', 'onSubmit']}
            >
              <Form.Item
                name="role_name"
                label="Role Name"
                rules={[
                  {
                    required: true,
                    message: 'Please Enter the Role Name'
                  }
                ]}
              >
                <NormalInput></NormalInput>
              </Form.Item>
            </Form>
          </NormalModal>
        </Sider>
      </Spin>
      <Content>
        <div className={styles['permission-container']}>
          <div className={styles['permission-top']}>
            {/* 暂时不做数据权限，使用文本即可 */}
            {/* <NormalRadio value={currentTab} onChange={handleTabChange}>
              <Radio>Functional Permission</Radio>
            </NormalRadio> */}
            Functional Permission
          </div>
          <PermissionContent
            currentData={currentRole}
            onSelectPms={onSelectPms}
            accessCode="editRoleAuth"
          ></PermissionContent>
          <Button
            type="primary"
            onClick={handleSubmitPermission}
            disabled={
              access.DisabledButton('editRoleAuth') ||
              [RoleType['Data Analyst'], RoleType.Operator, RoleType.Administrator].includes(currentRole?.id as number)
            }
          >
            Submit
          </Button>
        </div>
      </Content>
    </Layout>
  );
};
export default Permission;
