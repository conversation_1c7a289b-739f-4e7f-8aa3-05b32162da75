/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-22 18:45:08
 * @LastEditTime: 2023-02-13 18:03:20
 * @Description:
 */
import { Parser } from 'json2csv';
import type { Options } from 'json2csv';
import moment from 'moment';
import { SpecialSuccessCode, isErrorCode } from '@/constants';
// 修改为世界时UTC0
export const formatTime = function(val: string | Date, pattern = 'yyyy-MM-dd hh:mm:ss') {
  let time;

  if (typeof val === 'string' && val.length === 8) {
    // 将字符串转换为标准格式的日期字符串
    const year = val.slice(0, 4);
    const month = val.slice(4, 6);
    const day = val.slice(6, 8);
    val = `${year}-${month}-${day}`;
    time = new Date(val);
  } else if (val instanceof Date) {
    time = val;
  } else {
    time = new Date(val);
  }

  if (isNaN(time.getTime())) {
    return 'Invalid Date';
  }
  // const time = new Date(val);
  const y = time.getUTCFullYear() + '';
  const mm = (time.getUTCMonth() + 1 + '').padStart(2, '0');
  const d = (time.getUTCDate() + '').padStart(2, '0');
  const h = (time.getUTCHours() + '').padStart(2, '0');
  const m = (time.getUTCMinutes() + '').padStart(2, '0');
  const s = (time.getUTCSeconds() + '').padStart(2, '0');
  return pattern
    .replace('yyyy', y)
    .replace('MM', mm)
    .replace('dd', d)
    .replace('hh', h)
    .replace('mm', m)
    .replace('ss', s);
};
export const formatUTC8Time = function(val: string | Date, pattern = 'yyyy-MM-dd hh:mm:ss') {
  const time = new Date(val);
  const y = time.getFullYear() + '';
  const mm = (time.getMonth() + 1 + '').padStart(2, '0');
  const d = (time.getDate() + '').padStart(2, '0');
  const h = (time.getHours() + '').padStart(2, '0');
  const m = (time.getMinutes() + '').padStart(2, '0');
  const s = (time.getSeconds() + '').padStart(2, '0');
  return pattern
    .replace('yyyy', y)
    .replace('MM', mm)
    .replace('dd', d)
    .replace('hh', h)
    .replace('mm', m)
    .replace('ss', s);
};
export function objectFlip(obj: { [key: string]: string | number }): { [key: string | number]: string } {
  const ret: any = {};
  Object.keys(obj).forEach((key: string) => {
    ret[obj[key]] = key;
  });
  return ret;
}

// 验证输入
export function isValidBundle(bundle: string) {
  // 仅支持字母数字下划线
  const reg = /^[a-zA-Z0-9._-]+$/;
  const iosReg = /^[0-9]+$/;
  const androidReg = /[a-zA-Z]+[0-9a-zA-Z_](.[a-zA-Z]+[0-9a-zA-Z_])*/;
  return reg.test(bundle) && (iosReg.test(bundle) || androidReg.test(bundle));
}

// 接口请求
export const fetchData = ({ setLoading, request, params, onSuccess, onError, onFinally }: API.PropsType) => {
  setLoading && setLoading(true);
  request(params)
    .then(res => {
      // 增加特殊的成功code判断
      const isSpecialCode = Object.values(SpecialSuccessCode).includes(res.code);
      const isSpecialErrorCode = Object.values(isErrorCode).includes(res.code);
      if (Array.isArray(res)) {
        onSuccess && onSuccess(res);
      } else {
        if (res && res.code === 0) {
          onSuccess && onSuccess(res.data);
        } else if (isSpecialErrorCode) {
          onError && onError(res.message);
        } else if (isSpecialCode) {
          onSuccess && onSuccess(res.message);
        }
      }
    })
    .catch(e => {
      console.log('e', e);
      onError && onError(e);
    })
    .finally(() => {
      setLoading && setLoading(false);
      onFinally && onFinally();
    });
};
// 验证数字
export const validateNumber = (rule: any, value: any) => {
  const reg = /^\d+(\.\d+)?$/;
  if ((!value && +value !== 0) || value === '') {
    return Promise.reject(new Error(``));
  } else if (!reg.test(value)) {
    return Promise.reject(new Error('Please Input Number'));
  }
  return Promise.resolve();
};

export function isNumber(val: string | number): boolean {
  return Object.prototype.toString.call(val).slice(8, -1) === 'Number';
}

export const handleFilterSelect = (input: any, option: any) => {
  const tmp = option.children || option.label || '';
  const str = tmp && Array.isArray(tmp) ? tmp.join('') : `${tmp}`;
  return (str && str.toLowerCase().indexOf(input.toLowerCase()) >= 0) || false;
};

export const validUrl = (rule: any, value: any) => {
  value = removeZWSpace(value);
  const reg = /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
  if (!value) {
    return Promise.resolve();
  } else if (!reg.test(value)) {
    return Promise.reject(new Error('Please enter the correct URL'));
  }
  return Promise.resolve();
};

// 去除字符串中的零宽空格
export const removeZWSpace = (str: string) => {
  const whitespaceSet = new Set([
    '\n',
    '\r',
    '\t',
    '\f',
    '\u00A0',
    '\u2000',
    '\u2001',
    '\u2002',
    '\u2003',
    '\u2004',
    '\u2005',
    '\u2006',
    '\u2007',
    '\u2008',
    '\u2009',
    '\u200A',
    '\u200B',
    '\u2028',
    '\u2029',
    '\u3000'
  ]);

  let result = '';

  for (let i = 0; i < str.length; i++) {
    if (!whitespaceSet.has(str[i])) {
      result += str[i];
    }
  }
  return result;
};

export const getLastWeekDate = () => {
  // 暂时改为三天
  const date = new Date();
  date.setDate(date.getDate() - 2);
  return date;
};

export const downloadCsv = async function(fileName: string, data: object[], option: Options<any>) {
  const json2csvParser = new Parser(option);
  const csv = json2csvParser.parse(data);
  console.log('downloadCsv', csv);
  const blob = new Blob(['\ufeff' + csv], { type: 'text/csv' });
  const a = document.createElement('a');
  a.setAttribute('href', URL.createObjectURL(blob));
  a.setAttribute('download', `${fileName}.csv`);
  a.click();
};

/**
 * @param obj
 * @description 简易深拷贝
 */
export const deepClone = (obj: any) => {
  // 判断是否需要递归
  if (typeof obj !== 'object' || obj == null) {
    return obj;
  }
  const result: any = Array.isArray(obj) ? [] : {};
  // 函数直接返回
  if (typeof obj === 'function' || obj instanceof moment) {
    return obj;
  }

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = deepClone(obj[key]);
    }
  }
  return result;
};

export const formatMoney = (num: number) => {
  const data = num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return data;
};

export function capitalizeFirstLetter(str: string) {
  const [firstChar, ...rest] = str;
  return `${firstChar.toUpperCase()}${rest.join('')}`;
}

/**
 * description: 数字格式化加上单位B,M,K
 * @param {number} originFixed 小于一千时保留的位数，默认2
 * @param {number} formatFixed 格式化之后保留的位数，默认2
 */
export const formatNumberToUnit = (num: number, originFixed = 2, formatFixed = 2) => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(formatFixed)}B`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(formatFixed)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(formatFixed)}K`;
  }
  return num.toFixed(originFixed);
};

// 验证网站域名
export const isValidDomain = (value: any) => {
  const reg = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
  return reg.test(value);
};

// 验证名称是否合法 目前就上下游名称规范使用 只允许字母数字空格中文_-
export const isValidName = (value: string) => {
  const reg = /^[\u4e00-\u9fa5a-zA-Z0-9_-\s]+$/;
  return reg.test(value);
};
