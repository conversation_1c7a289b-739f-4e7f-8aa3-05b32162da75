/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-03 21:34:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-12 20:11:12
 * @Description:
 */

class PubReportService {
  // 下载全部报表
  // async downloadAllReport(options: any, api_url: string) {
  //   const params = dashboardService.getReportParams(options, false, true);
  //   return await pubReportModel.downloadAllReport(params, api_url);
  // }
}

export default new PubReportService();
