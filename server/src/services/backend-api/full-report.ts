import { BackendAPI } from '@/types/backend-api';
import { DashboardAPI } from '@/types/dashboard';
import { dashboardService } from '@/services';
import { appModel, demandModel, supplyModel } from '@/models';
import {
  BeDashboardDimension,
  BeDashboardMetrics
} from '@/constants/backend-api/full-report';
import { generateMap } from '@/utils';
import { AppListAPI } from '@/types/app-list';
class FullReportServices implements BackendAPI.FullReportServices {
  async getFullReport(
    params: DashboardAPI.GetListParams,
    api_url: string
  ): Promise<{ total: number; data: DashboardAPI.DashboardListItem[] }> {
    const { tnt_id, cur_time_zone } = params;
    const [result, supplyList, demandList, appList] = await Promise.all([
      dashboardService.getNewDashboardList(
        params,
        { tag: api_url, tenantId: tnt_id?.toString() },
        {}
      ),
      supplyModel.getSupplyList(tnt_id, cur_time_zone, undefined, true),
      demandModel.getDemandList(tnt_id, cur_time_zone, true),
      appModel.getAllPlacementList(tnt_id)
    ]);
    const supplyMap = generateMap(supplyList, 'seller_id', 'seller_name');
    const demandMap = generateMap(demandList, 'buyer_id', 'buyer_name');
    const appMap = generateMap(
      appList as AppListAPI.PlacementListItem[],
      'plm_id',
      'plm_name'
    );
    const { data, total } = result as {
      total: number;
      data: DashboardAPI.FullReportListItem[];
    };
    const list = data.map(item => {
      const newItem = { ...item } as any;
      const allItemKeys = [
        ...BeDashboardMetrics,
        ...BeDashboardDimension,
        'date'
      ];
      Object.keys(item).forEach(key => {
        if (!allItemKeys.includes(key)) {
          delete newItem[key as keyof typeof newItem];
        } else {
          if (key === 'seller_id') {
            newItem.pub_id = newItem[key];
            newItem.pub_name = supplyMap[newItem[key]] || null;
            delete newItem.seller_id;
          }
          if (key === 'buyer_id') {
            newItem.adv_id = newItem[key];
            newItem.adv_name = demandMap[newItem[key]] || null;
            delete newItem.buyer_id;
          }
          if (key === 'app_bundle_id') {
            newItem.bundle = newItem[key];
            delete newItem.app_bundle_id;
          }
          if (key === 'placement_id') {
            newItem.unit_id = newItem[key];
            newItem.unit_name = appMap[newItem[key]] || null;
            delete newItem.placement_id;
          }
          if (key === 'total_seller_bid_floor') {
            newItem.bid_floor = newItem[key];
            delete newItem.total_seller_bid_floor;
          }
        }
      });
      return newItem;
    });

    return { total, data: list };
  }
}

export const beFullReportServices = new FullReportServices();
