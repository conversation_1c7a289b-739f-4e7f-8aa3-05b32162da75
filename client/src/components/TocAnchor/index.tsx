import { Anchor, AnchorProps } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import styles from './styles.less';

const { Link } = Anchor;

interface Toc {
  href: string;
  target?: string;
  title: React.ReactNode;
  children?: Toc[];
}

interface TocAnchorProps extends AnchorProps {
  tocOptions: Toc[];
}

// 递归渲染 toc
const renderTocItems = (tocItems: Toc[]) => {
  return tocItems.map(({ children, ...itemProps }) => {
    if (children) {
      const nestedItems = renderTocItems(children);
      return (
        <Link key={itemProps.href} {...itemProps}>
          {nestedItems}
        </Link>
      );
    }
    return <Link key={itemProps.href} {...itemProps} />;
  });
};

function TocAnchor({ tocOptions, ...props }: TocAnchorProps) {
  const [targetOffset, setTargetOffset] = useState<number | undefined>(undefined);

  useEffect(() => {
    setTargetOffset(window.innerHeight / 2);
  }, []);

  const tocItems = useMemo(() => {
    return renderTocItems(tocOptions);
  }, [tocOptions]);

  return (
    <Anchor targetOffset={targetOffset} {...props} className={styles['toc-anchor']}>
      {tocItems}
    </Anchor>
  );
}

export default TocAnchor;
