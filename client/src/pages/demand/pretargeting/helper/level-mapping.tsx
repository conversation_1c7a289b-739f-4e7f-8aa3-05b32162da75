// 预定位条件映射配置，用于转换前端显示与后端存储格式
export enum TargetingStatus {
  ALLOW = 'Allow',
  BLOCK = 'Block'
}

export enum ValueType {
  STRING = 'string',
  NUMBER = 'number',
  STRING_ARRAY = 'string[]',
  NUMBER_ARRAY = 'number[]'
}

export interface PretargetingConfig {
  level: number;
  levelName: string;
  name: string;
  label: string;
  /**
   * 从获取结构转换成存储结构的数据类型
   */
  valueType: ValueType;
  status: TargetingStatus;
  statusName: string;
}

// 预定位条件映射配置表（索引 + 1 对应 level）
export const LevelConfigMap: PretargetingConfig[] = [
  {
    level: 1,
    levelName: 'country-level',
    name: 'country',
    label: 'Country',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'country-status'
  },
  {
    level: 2,
    levelName: 'country-level',
    name: 'country',
    label: 'Country',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'country-status'
  },
  {
    level: 3,
    levelName: 'platform-level',
    name: 'platform',
    label: 'Device',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'platform-status'
  },
  {
    level: 4,
    levelName: 'mobile_os-level',
    name: 'mobile_os',
    label: 'OS',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'mobile_os-status'
  },
  {
    level: 5,
    levelName: 'mobile_inventory-level',
    name: 'mobile_inventory',
    label: 'Inventory',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'mobile_inventory-status'
  },
  {
    level: 6,
    levelName: 'category-level',
    name: 'category',
    label: 'Category',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'category-status'
  },
  {
    level: 7,
    levelName: 'category-level',
    name: 'category',
    label: 'Category',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'category-status'
  },
  {
    level: 8,
    levelName: 'ad_platform-level',
    name: 'ad_platform',
    label: 'Ad Format',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'ad_platform-status'
  },
  // level 9 暂时不支持
  {
    level: 9,
    levelName: 'site-level',
    name: 'site',
    label: 'Site',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'site-status'
  },
  {
    level: 10,
    levelName: 'mobile_app-level',
    name: 'mobile_app',
    label: 'Bundle(Domain)',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'mobile_app-status'
  },
  {
    level: 11,
    levelName: 'mobile_app-level',
    name: 'mobile_app',
    label: 'Bundle(Domain)',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'mobile_app-status'
  },
  {
    level: 12,
    levelName: 'ad_size-level',
    name: 'ad_size',
    label: 'Ad Size',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'ad_size-status'
  },
  {
    level: 13,
    levelName: 'ad_size-level',
    name: 'ad_size',
    label: 'Ad Size',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'ad_size-status'
  },
  {
    level: 14,
    levelName: 'max_price-level',
    name: 'max_price',
    label: 'Maximum Floor Price',
    valueType: ValueType.NUMBER,
    status: TargetingStatus.ALLOW,
    statusName: 'max_price-status'
  },
  {
    level: 15,
    levelName: 'server_region-level',
    name: 'server_region',
    label: 'Server Region',
    valueType: ValueType.STRING,
    status: TargetingStatus.ALLOW,
    statusName: 'server_region-status'
  },
  {
    level: 16,
    levelName: 'network-level',
    name: 'network',
    label: 'Network',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'network-status'
  },
  {
    level: 17,
    levelName: 'network-level',
    name: 'network',
    label: 'Network',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'network-status'
  },
  {
    level: 18,
    levelName: 'seller-level',
    name: 'seller',
    label: 'Publisher',
    valueType: ValueType.NUMBER_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'seller-status'
  },
  {
    level: 19,
    levelName: 'seller-level',
    name: 'seller',
    label: 'Publisher',
    valueType: ValueType.NUMBER_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'seller-status'
  },
  {
    level: 20,
    levelName: 'time_slot-level',
    name: 'time_slot',
    label: 'Delivery Time Slot',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'time_slot-status'
  },
  {
    level: 21,
    levelName: 'brand-level',
    name: 'brand',
    label: 'Device Brand',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.ALLOW,
    statusName: 'brand-status'
  },
  {
    level: 22,
    levelName: 'brand-level',
    name: 'brand',
    label: 'Device Brand',
    valueType: ValueType.STRING_ARRAY,
    status: TargetingStatus.BLOCK,
    statusName: 'brand-status'
  },
  {
    level: 23,
    levelName: 'min_price-level',
    name: 'min_price',
    label: 'Minimum Floor Price',
    valueType: ValueType.NUMBER,
    status: TargetingStatus.ALLOW,
    statusName: 'min_price-status'
  }
];

// 根据 LevelConfigMap 的 name 做聚合
export interface LevelConfigGroup {
  levelName: string;
  levelOptions: { label: string; value: number }[];
  name: string;
  key: string;
  label: string;
  valueType: ValueType;
  status: TargetingStatus;
}

/**
 * 根据 LevelConfigMap 生成 LevelConfigMapGroup
 */
export const LevelConfigMapGroup: Record<string, LevelConfigGroup> = LevelConfigMap.reduce((acc, config) => {
  const { name, level, levelName, label, valueType, status } = config;

  if (!acc[name]) {
    acc[name] = {
      levelName,
      levelOptions: [],
      name,
      key: name,
      label,
      valueType,
      status
    };
  }

  acc[name].levelOptions.push({
    label: status,
    value: level
  });

  return acc;
}, {} as Record<string, LevelConfigGroup>);
