/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-24 12:17:50
 * @Description:
 */
import { Badge } from 'antd';

import type { TagProps } from 'antd/lib/tag';

const TagTypes = ['active', 'paused'];
type TagType = typeof TagTypes[number];
type StatusTagProps = TagProps & {
  type?: TagType;
  textColor?: string;
};

const ColorMap = {
  orange: '#FFB114',
  blue: '#476DF5',
  green: '#1CD880',
  red: '#FA5A42'
} as const;

const StatusMap: { [key: TagType]: any } = {
  new: ColorMap.orange, //
  'live test': ColorMap.blue,
  active: ColorMap.green,
  testing: ColorMap.blue,
  allowed: ColorMap.green,
  blocked: ColorMap.red,
  enabled: ColorMap.green,
  paused: ColorMap.red,
  initial: ColorMap.orange,
  issued: ColorMap.blue,
  success: ColorMap.green,
  creating: ColorMap.orange,
  created: ColorMap.green,
  failed: ColorMap.red,
  'passed due': ColorMap.red,
  'invoice creating': ColorMap.orange,
  'invoice created': ColorMap.green,
  'invoice issuing': ColorMap.orange,
  'invoice issued': ColorMap.green,
  'dunning issuing': ColorMap.orange,
  'dunning issued': ColorMap.green,
  'failed to create': ColorMap.red,
  'failed to issue': ColorMap.red,
  'dunning failed': ColorMap.red,
  closed: ColorMap.blue,
  overdue: ColorMap.red,
  open: ColorMap.orange,
  running: ColorMap.green,
  stop: ColorMap.red,
  login: ColorMap.green,
  logout: ColorMap.red
};

function StatusTag({ type = 'active', children, textColor = '#606266', ...rest }: StatusTagProps): JSX.Element {
  return <Badge {...rest} color={StatusMap[type] || 'rgba(0,0,0,0)'} text={children} style={{ color: textColor }} />;
}

export default StatusTag;
