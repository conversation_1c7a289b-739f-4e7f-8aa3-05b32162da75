/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:45:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-20 16:45:38
 * @Description:
 */

import { Context } from 'koa';
import { boardService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { BoardAPI } from '@/types/ai-board';
import { validateBody } from '@/utils/validate-params';
import { AddTrafficRequest } from '@/schema/main-board';
import { boardModel } from '@/models';
import { getUserRole } from '@/utils/report/getBQLabel';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

// !注释都不删除，后续可能会用到
class BoardCtrl implements BoardAPI.BoardCtrl {
  async getOverview(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const data = await boardService.getOverview(
      formData,
      {
        tag: api_url,
        tenantId: formData.tnt_id,
        userRole: getUserRole(userType)
      }
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTopCountry(ctx: Context) {
    let result = { ...RESULT };
    const { cur_hour, cache_cur_hour, cur_time_zone } = ctx.request.body;
    const { tnt_id = 0 } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const data = await boardService.getTopCountry(
      cur_hour || cache_cur_hour,
      cur_time_zone,
      tnt_id,
      {
        tag: api_url,
        tenantId: tnt_id,
        userRole: getUserRole(userType)
      }
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getSupplyDemand(ctx: Context) {
    let result = { ...RESULT };
    const { cur_hour, cache_cur_hour, cur_time_zone } = ctx.request.body;
    const { tnt_id = 0 } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const data = await boardService.getSupplyDemand(
      cur_hour || cache_cur_hour,
      cur_time_zone,
      tnt_id,
      {
        tag: api_url,
        tenantId: tnt_id,
        userRole: getUserRole(userType)
      }
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTopAdFormatEcpmAndEcpr(ctx: Context) {
    let result = { ...RESULT };
    const { cur_hour, cache_cur_hour, cur_time_zone } = ctx.request.body;
    const { tnt_id = 0 } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const data = await boardService.getTopAdFormatEcpmAndEcpr(
      cur_hour || cache_cur_hour,
      cur_time_zone,
      tnt_id,
      {
        tag: api_url,
        tenantId: tnt_id,
        userRole: getUserRole(userType)
      }
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getBudgetAndTraffic(ctx: Context) {
    let result = { ...RESULT };
    const { cur_hour, cache_cur_hour, cur_time_zone } = ctx.request.body;
    const { tnt_id = 0 } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const data = await boardService.getBudgetAndTraffic(
      cur_hour || cache_cur_hour,
      tnt_id,
      cur_time_zone,
      {
        tag: api_url,
        tenantId: tnt_id,
        userRole: getUserRole(userType)
      }
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(AddTrafficRequest)
  async trafficRequest(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id = 0, user_id = 0 } = ctx.session!;
    const formData = ctx.request.body;
    formData.tnt_id = tnt_id;
    formData.user_id = user_id;

    const isExistLog = await boardModel.isExistLog(formData);
    if (isExistLog) {
      result = getCtxResult('TRAFFIC_REQUESTED');
      ctx.body = result;
      return;
    }
    const data = await boardService.trafficRequest(formData);

    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTffReqLog(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id = 0 } = ctx.session!;
    const formData = ctx.request.body;
    const data = await boardService.getTffReqLog(
      tnt_id,
      formData.cur_time_zone
    );
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const boardController = new BoardCtrl();
