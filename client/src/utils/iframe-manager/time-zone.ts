import moment from 'moment-timezone';

/**
 * 时区设置选项
 */
export interface TimeZoneOptions {
  /** 是否在设置后自动刷新页面 */
  autoReload?: boolean;
  /** 是否保留页面状态 */
  preserveState?: boolean;
  /** 用户ID，用于localStorage存储键名 */
  userId?: string;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 时区设置结果
 */
export interface TimeZoneResult {
  /** 设置是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 设置的时区 */
  timeZone: string;
  /** 是否已刷新页面 */
  reloaded?: boolean;
}

/**
 * 时区管理工具类
 */
export class TimeZoneManager {
  private static readonly STORAGE_PREFIX = 'time-';
  private static readonly DEFAULT_TIMEZONE = 'Etc/UTC';
  private static debug: boolean = false;

  /**
   * 设置时区
   * @param timeZone 时区字符串 (如 'Asia/Shanghai', 'America/New_York')
   * @param options 设置选项
   * @returns 设置结果
   */
  static setTimeZone(timeZone: string, options: TimeZoneOptions = {}): TimeZoneResult {
    const { autoReload = true, preserveState = false, userId = '', debug = false } = options;
    this.debug = debug;

    try {
      // 验证时区是否有效
      if (!moment.tz.zone(timeZone)) {
        return {
          success: false,
          error: `Invalid time zone: ${timeZone}`,
          timeZone
        };
      }

      // 保存到localStorage
      const storageKey = `${this.STORAGE_PREFIX}${userId}`;
      localStorage.setItem(storageKey, timeZone);

      // 设置全局时区变量
      if (typeof window !== 'undefined') {
        // @ts-ignore
        window.cur_time_zone = timeZone;
      }

      // 设置moment默认时区
      moment.tz.setDefault(timeZone);

      if (this.debug) {
        console.log(`[TimeZoneManager] Time zone set to: ${timeZone}`);
      }

      // 如果需要自动刷新页面
      if (autoReload) {
        const reloadResult = this.reloadPage(preserveState);
        return {
          success: true,
          timeZone,
          reloaded: reloadResult.success,
          error: reloadResult.error
        };
      }

      return {
        success: true,
        timeZone
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (this.debug) {
        console.error('[TimeZoneManager] Failed to set time zone:', error);
      }
      return {
        success: false,
        error: errorMessage,
        timeZone
      };
    }
  }

  /**
   * 获取当前时区
   * @param userId 用户ID
   * @returns 当前时区
   */
  static getCurrentTimeZone(userId: string = ''): string {
    try {
      const storageKey = `${this.STORAGE_PREFIX}${userId}`;
      const storedTimeZone = localStorage.getItem(storageKey);

      if (storedTimeZone && moment.tz.zone(storedTimeZone)) {
        return storedTimeZone;
      }

      // 如果localStorage中没有或无效，尝试从window对象获取
      if (typeof window !== 'undefined' && (window as any).cur_time_zone) {
        const windowTimeZone = (window as any).cur_time_zone;
        if (moment.tz.zone(windowTimeZone)) {
          return windowTimeZone;
        }
      }

      // 返回默认时区
      return this.DEFAULT_TIMEZONE;
    } catch (error) {
      if (this.debug) {
        console.error('[TimeZoneManager] Failed to get time zone:', error);
      }
      return this.DEFAULT_TIMEZONE;
    }
  }

  /**
   * 刷新页面
   * @param preserveState 是否保留页面状态
   * @returns 刷新结果
   */
  static reloadPage(preserveState: boolean = false): { success: boolean; error?: string } {
    try {
      if (typeof window === 'undefined') {
        return {
          success: false,
          error: 'window object is not available'
        };
      }

      // 刷新页面
      window.location.reload();

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reload page';
      if (this.debug) {
        console.error('[TimeZoneManager] Failed to reload page:', error);
      }
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * 初始化时区设置
   * @param userId 用户ID
   * @param debug 是否启用调试模式
   * @returns 初始化结果
   */
  static initializeTimeZone(userId: string = '', debug: boolean = false): TimeZoneResult {
    this.debug = debug;
    try {
      const currentTimeZone = this.getCurrentTimeZone(userId);

      // 设置全局时区变量
      if (typeof window !== 'undefined') {
        // @ts-ignore
        window.cur_time_zone = currentTimeZone;
      }

      // 设置moment默认时区
      moment.tz.setDefault(currentTimeZone);

      if (this.debug) {
        console.log(`[TimeZoneManager] Time zone initialized: ${currentTimeZone}`);
      }

      return {
        success: true,
        timeZone: currentTimeZone
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize time zone';
      if (this.debug) {
        console.error('[TimeZoneManager] Failed to initialize time zone:', error);
      }
      return {
        success: false,
        error: errorMessage,
        timeZone: this.DEFAULT_TIMEZONE
      };
    }
  }

  /**
   * 验证时区是否有效
   * @param timeZone 时区字符串
   * @returns 是否有效
   */
  static isValidTimeZone(timeZone: string): boolean {
    return moment.tz.zone(timeZone) !== null;
  }

  /**
   * 获取所有可用时区
   * @returns 时区列表
   */
  static getAvailableTimeZones(): string[] {
    return moment.tz.names();
  }
}

/**
 * 便捷函数：设置时区并刷新页面
 * @param timeZone 时区字符串
 * @param options 设置选项
 * @returns 设置结果
 */
export function setTimeZoneAndReload(timeZone: string, options: TimeZoneOptions = {}): TimeZoneResult {
  return TimeZoneManager.setTimeZone(timeZone, {
    ...options,
    autoReload: true
  });
}

/**
 * 便捷函数：仅设置时区（不刷新页面）
 * @param timeZone 时区字符串
 * @param options 设置选项
 * @returns 设置结果
 */
export function setTimeZoneOnly(timeZone: string, options: TimeZoneOptions = {}): TimeZoneResult {
  return TimeZoneManager.setTimeZone(timeZone, {
    ...options,
    autoReload: false
  });
}

export default TimeZoneManager;
