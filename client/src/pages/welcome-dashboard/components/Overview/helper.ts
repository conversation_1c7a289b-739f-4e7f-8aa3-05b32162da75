import { OverviewColorMap, OverviewFillColorMap, OverviewOptions } from '@/constants/ree-ai/main-board';
import { formatNumberToUnit } from '@/utils';
import { AreaConfig } from '@ant-design/charts';

// 注意index, 0: revenue, 1: profit, 2: ecpr, 3: request
export const getConfig = (index: number, data?: BoardAPI.OverviewItem): AreaConfig => {
  const hoursData = data?.hours_data;
  const currentData = hoursData?.map(item => {
    return {
      date: item.date,
      value: item[OverviewOptions[index].number as keyof BoardAPI.OverviewItem['hours_data']]
    };
  });
  const config: AreaConfig = {
    autoFit: true,
    data: currentData || [],
    xField: 'date',
    yField: 'value',
    xAxis: false,
    yAxis: false,
    smooth: true,
    areaStyle: {
      fillOpacity: 1,
      fill: OverviewColorMap[index] as string
    },
    line: {
      color: OverviewFillColorMap[index] as string,
      style: {
        lineWidth: 3
      }
    },
    tooltip: {
      position: 'top',
      customContent: (title, data) => {
        const value = formatNumberToUnit(data?.[0]?.data.value || 0, 0, 2);
        return `<div >
            <div style="margin-bottom: 8px">${title}</div>
            <div>${value}</div>
          </div>`;
      },
      domStyles: {
        'g2-tooltip': {
          backgroundColor: '#fff',
          boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)',
          borderRadius: '4px',
          padding: ' 8px'
        }
      }
    },
    padding: 4
  };
  return config;
};
