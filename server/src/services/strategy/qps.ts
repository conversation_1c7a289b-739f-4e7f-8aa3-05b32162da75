/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:05:52
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:31:56
 * @Description:
 */
import { StatusMap } from '@/constants/strategy';
import { demandModel, qpsModel } from '@/models';
import { QpsAPI } from '@/types/qps';

class QpsService implements QpsAPI.Qps {
  async isQpsExists(params: QpsAPI.IsExistsQps): Promise<any> {
    return await qpsModel.isQpsExists(params);
  }

  async isBundleExists(params: QpsAPI.IsExistsQps) {
    return await qpsModel.isBundleExists(params);
  }

  async getQpsList(tnt_id: number, cur_time_zone: string) {
    const [list, buyer] = await Promise.all([
      qpsModel.getQpsList(tnt_id, cur_time_zone),
      demandModel.getTestingDemand(tnt_id)
    ]);
    const buyer_ids = buyer?.map((v: any) => v.buyer_id) || [];
    const data = list?.filter(v => !buyer_ids.includes(v.buyer_id));
    console.log('xx压抑', list);
    return data;
  }

  async addQps(params: QpsAPI.AddQpsParams): Promise<boolean> {
    return await qpsModel.addQps(params);
  }

  async updateQps(params: QpsAPI.UpdateQpsParams): Promise<boolean> {
    return await qpsModel.updateQps(params);
  }

  async updateDuplicatedQps(
    params: QpsAPI.AddQpsParams & QpsAPI.UpdateQpsParams
  ): Promise<boolean> {
    return await qpsModel.updateDuplicatedQps(params);
  }

  /**
   * @description 这个接口没有用到
   */
  async updateQpsStatus(params: QpsAPI.UpdateQpsParams): Promise<boolean> {
    return await qpsModel.updateQpsStatus(params);
  }

  async addQpsWithTransaction(params: any): Promise<boolean> {
    try {
      const { count, data } = await qpsModel.isQpsExists({ ...params, id: 0 });

      let result;
      if (count !== 0 && data?.length && data[0].status === StatusMap.Paused) {
        params.id = data[0].id;
        result = await qpsModel.updateDuplicatedQps(params);
        if (result) {
          throw { code: 'QPS_DUPLICATED_WHEN_PAUSED', data: true };
        } else {
          throw { code: 'QPS_DUPLICATED_WHEN_PAUSED', data: false };
        }
      } else if (count === 0) {
        result = await qpsModel.addQps(params);
        if (result) {
          return result;
        }
      } else {
        throw { code: 'QPS_EXISTS' };
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  async updateQpsWithTransaction(params: any): Promise<boolean> {
    try {
      const { count } = await qpsModel.isBundleExists({ ...params });
      if (count !== 0) {
        throw { code: 'QPS_EXISTS' };
      }

      const result = await qpsModel.updateQps(params);
      return result;
    } catch (error) {
      throw error;
    }
  }
}

export default new QpsService();
