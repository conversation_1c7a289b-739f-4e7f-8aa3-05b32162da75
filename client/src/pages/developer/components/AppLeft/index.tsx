/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-15 10:29:23
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-09 16:26:44
 * @Description:
 */
import { <PERSON><PERSON>, Spin } from 'antd';
import { useAccess } from 'umi';
import styles from './index.less';
import Select from '@/components/Select/NormalSelect';
import AppItem from '../AppItem';
import Input from '@/components/Input/NormalInput';
import InputSearch from '@/components/Input/InputSearch';
import RixEngineFont from '@/components/RixEngineFont';
import { AppItemColor } from '@/constants';
type AppLeftProps = {
  supplyList?: SupplyAPI.SupplyListItem[];
  supplyLoading?: boolean;
  supplyIndex?: number;
  handleSupplyChange?: (val: number) => void;
  handleOpenModel?: () => void;
  handleSearch: (val: string) => void;
  dataLoading: boolean;
  filterList: AppAPI.AppListItem[];
  handleChangeCurrentApp: (
    current: AppAPI.AppListItem | SupplyAPI.SellerAppItem | undefined,
    itemColor: string
  ) => void;
  currentApp: AppAPI.AppListItem | SupplyAPI.SellerAppItem | undefined;
  supply?: SupplyAPI.SupplyListItem | undefined;
  isDeveloper: boolean;
  maxHeight: string;
  auth?: string;
  isSite?: boolean;
};

function AppLeft({
  supplyIndex,
  supplyList,
  supplyLoading,
  handleChangeCurrentApp,
  handleOpenModel,
  isDeveloper,
  handleSearch,
  handleSupplyChange,
  dataLoading,
  maxHeight,
  filterList,
  currentApp,
  supply,
  auth,
  isSite
}: AppLeftProps): JSX.Element {
  const access = useAccess();
  return (
    <div className={styles['app-left']}>
      {isDeveloper && (
        <div className={styles['app-top']}>
          <Select
            options={
              supplyList &&
              supplyList
                .filter((item: SupplyAPI.SupplyListItem) => item.cus_status !== 2)
                .map((item: SupplyAPI.SupplyListItem) => {
                  return {
                    label: `${item.seller_name}(${item.seller_id})`,
                    value: item.seller_id
                  };
                })
            }
            loading={supplyLoading}
            value={supplyIndex}
            onChange={handleSupplyChange}
            style={{ width: 168 }}
            showSearch
            optionFilterProp="children"
            filterOption={(input, option: any) =>
              option && option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
          <Button type="primary" onClick={handleOpenModel} disabled={access.DisabledButton(auth) || !supply}>
            {isSite ? 'Create Site' : 'Create App'}
          </Button>
        </div>
      )}
      <div className={styles['app-search']}>
        {/* 防抖处理 */}
        <InputSearch placeholder="Please input app name, app id or app key" handleSearch={handleSearch} />
      </div>
      <div
        style={{ maxHeight: maxHeight, overflowY: filterList.length > 0 ? 'auto' : 'hidden' }}
        className={styles['app-scroll']}
      >
        <Spin spinning={dataLoading}>
          {filterList.map((item, index) => {
            return (
              <AppItem
                item={item}
                key={index}
                ellipsis={true}
                background={item.app_id === currentApp?.app_id ? '#DAF3F7' : '#fff'}
                handleClick={handleChangeCurrentApp}
                itemColor={
                  index % 2 === 0 ? `linear-gradient(163deg,${AppItemColor['0']})` : (AppItemColor['1'] as string)
                }
                isSite={isSite}
              />
            );
          })}
          {!filterList.length && (
            <div className={styles['content-empty']}>
              <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
              <span>No Data</span>
            </div>
          )}
        </Spin>
      </div>
    </div>
  );
}

export default AppLeft;
