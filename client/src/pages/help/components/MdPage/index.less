@import (reference) '~antd/es/style/themes/index';
.container {
  width: 100%;
  max-height: 100vh;
  .top-container {
    display: flex;
    z-index: 11;
    height: 64px;
    width: 100%;
    background-color: #fff;
  }

  .top-container-hidden {
    display: none;
  }

  .get-started {
    padding: 24px 16px;
    // padding-left: 0px;
    // padding-top: 80px;
    background-color: var(--background-color);
    max-height: calc(100% - 64px);
    display: flex;
    gap: 16px;
    width: 100%;

    position: relative;
    flex-wrap: wrap;

    img {
      max-width: 100%;
    }
    .nav-container {
      max-width: 320px;
      // margin: 0 16px 0 24px;
      background-color: #fff;
      border-radius: 6px;
      border: 1px solid #eee;
      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
      transform: translate(0, 0);

      height: calc(100vh - 48px);
      overflow: auto;
      position: sticky;
      top: 24px;

      @media (max-width: 768px) {
        position: relative;
        top: 0;
        // margin-bottom: 16px;
        max-width: 100%;
        height: auto;
        width: 100% !important;
      }

      :global {
        .ant-anchor-wrapper {
          background-color: #fff;
          .ant-anchor-ink:first-child::before {
            background-color: #eee;
          }

          .ant-anchor-ink-ball {
            border-color: var(--primary-color);
          }

          .ant-anchor-link-active > .ant-anchor-link-title {
            color: var(--primary-color);
          }
        }

        // div aria-hidden=true, display: none;
        div[aria-hidden='true'] {
          display: none;
          width: 100% !important;
        }

        @media (max-width: 768px) {
          .ant-affix {
            position: relative !important;
            top: 0 !important;
            width: 100% !important;
          }
        }
      }
    }

    .article-container {
      flex: 1;
      max-width: 100%;
      margin: 0;
      padding: 0 32px 20px;
      background: #fff;
      box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
      overflow: auto;
      border-radius: 6px;

      @media (max-width: 768px) {
        // min-width: 448px;
        padding: 0 16px 20px;
      }

      th {
        background-color: var(--background-color);
      }
      ul {
        list-style-type: disc;
      }
    }

    .article-container.min {
      width: calc(100% - 320px);
    }

    :global {
      .img-w-full-center {
        width: 100%;
        display: block;
        margin: 0 auto 10px;
      }
    }
  }
}
