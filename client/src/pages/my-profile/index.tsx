import React, { useEffect, useState, useRef, useMemo } from 'react';
import styles from './index.less';
import { useModel, history, useAccess, useSearchParams } from '@umijs/max';
import { Form, Radio, Menu, Dropdown, Button } from 'antd';

import NormalInput from '@/components/Input/NormalInput';
import PageContainer from '@/components/RightPageContainer';
import NormalRadio from '@/components/Radio/NormalRadio';
import ManageUser from './components/ManageUser';
import Permission from './components/Permission';
import RixEngineFont from '@/components/RixEngineFont';

import { ProfileBreadOptions, ProfileMenuKey, TimeZone } from '@/constants/base/my-profile';
import { ProfileTab, ProfileMenuOptions } from '@/constants/base/my-profile';

const Page: React.FC = () => {
  const access = useAccess();
  const [searchParams, setSearchParams] = useSearchParams();
  const { initialState } = useModel('@@initialState');
  const { previousUrl, backUrl } = useModel('usePreviousUrl');
  const [form] = Form.useForm();
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    if (initialState?.currentUser) {
      // 管理员
      setIsAdmin(!access.DisabledButton('AccountsAndPermissionsAuth'));
      form.setFieldsValue(initialState?.currentUser);
    }
  }, [initialState?.currentUser]);

  const handleGoBack = () => {
    if (backUrl) {
      history.push(backUrl);
    } else if (previousUrl) {
      history.go(-1);
    } else {
      history.push('/');
    }
  };

  const [currentTab, setCurrentTab] = useState(searchParams.get('tab') || ProfileTab.Info);
  const handleTabChange = (e: any) => {
    if (e.target.value === ProfileTab.Info) {
      setCurrentMenu('');
      setSearchParams({ tab: e.target.value, menuKey: '' });
    } else {
      if (!access.DisabledButton('ManageUsersAuth')) {
        setCurrentMenu(ProfileMenuKey.ManageUsers);
        setSearchParams({ tab: e.target.value, menuKey: ProfileMenuKey.ManageUsers });
        setSelectedKeys([ProfileMenuKey.ManageUsers]);
      } else if (!access.DisabledButton('RolePermissionsAuth')) {
        setCurrentMenu(ProfileMenuKey.Permissions);
        setSearchParams({ tab: e.target.value, menuKey: ProfileMenuKey.Permissions });
        setSelectedKeys([ProfileMenuKey.Permissions]);
      } else if (!access.DisabledButton('CustomPermissionsAuth')) {
        setCurrentMenu(ProfileMenuKey.CusPermissions);
        setSearchParams({ tab: e.target.value, menuKey: ProfileMenuKey.CusPermissions });
        setSelectedKeys([ProfileMenuKey.CusPermissions]);
      }
    }
    setCurrentTab(e.target.value);
  };

  const [currentMenu, setCurrentMenu] = useState(searchParams.get('menuKey') || ProfileMenuKey.ManageUsers);
  const [selectedKeys, setSelectedKeys] = useState([searchParams.get('menuKey') || ProfileMenuKey.ManageUsers]);
  const handleMenuClick = (value: string) => {
    setCurrentMenu(value);
    setSelectedKeys([value]);
    setCurrentTab(ProfileTab.Management);
    setSearchParams({ tab: ProfileTab.Management, menuKey: value });
  };
  const menu = (
    <Menu className={styles['menu']} selectedKeys={selectedKeys}>
      {ProfileMenuOptions.map((item, index) => {
        return (
          <Menu.Item
            key={item.key}
            onClick={() => handleMenuClick(item.key)}
            disabled={access.DisabledButton(item.accessCode)}
          >
            {item.label}
          </Menu.Item>
        );
      })}
    </Menu>
  );

  const ChildRef = useRef<any>();
  const handleAddUser = () => {
    ChildRef.current!.handleAddUser();
  };

  return (
    <PageContainer
      isBack
      hideMenu={true}
      handleGoBack={handleGoBack}
      options={ProfileBreadOptions}
      className={styles['profile-container']}
    >
      <div className={styles['container']}>
        <div className={styles['top']}>
          <div className={styles['top-left']}>
            <div className={styles['tab']}>
              <NormalRadio value={currentTab} onChange={handleTabChange}>
                <Radio key={ProfileTab.Info} value={ProfileTab.Info}>
                  General Info
                </Radio>
              </NormalRadio>
            </div>

            {!access.DisabledButton('AccountsAndPermissionsAuth') && (
              <Dropdown
                overlay={menu}
                overlayStyle={{ height: '100%' }}
                trigger={['hover']}
                overlayClassName={styles['top-dropdown']}
              >
                <NormalRadio value={currentTab} onChange={handleTabChange}>
                  <Radio
                    key={ProfileTab.Management}
                    value={ProfileTab.Management}
                    disabled={access.DisabledButton([
                      'ManageUsersAuth',
                      'RolePermissionsAuth',
                      'CustomPermissionsAuth'
                    ])}
                  >
                    Accounts&Permissions
                  </Radio>
                </NormalRadio>
              </Dropdown>
            )}
          </div>
          {isAdmin && currentTab === ProfileTab.Management && currentMenu === ProfileMenuKey.ManageUsers && (
            <Button
              type="primary"
              onClick={handleAddUser}
              disabled={access.DisabledButton('addUserAuth')}
              icon={<RixEngineFont type="rix-add" />}
            >
              Add User
            </Button>
          )}
        </div>
        {currentTab === ProfileTab.Info && (
          <Form form={form} layout="horizontal" labelCol={{ style: { width: 150 }}} wrapperCol={{ span: 24 }}>
            <Form.Item
              name="tnt_name"
              label="Company Name:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true
                }
              ]}
            >
              <NormalInput style={{ maxWidth: 418 }} disabled />
            </Form.Item>
            <Form.Item
              name="email"
              label="Email:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true
                }
              ]}
            >
              <NormalInput style={{ maxWidth: 418 }} disabled />
            </Form.Item>
            <Form.Item
              name="timezone"
              label="Time Zone:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true
                }
              ]}
            >
              <NormalInput style={{ maxWidth: 418 }} disabled placeholder={TimeZone} />
            </Form.Item>
            <Form.Item
              name="currency"
              label="Currency:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true
                }
              ]}
            >
              <NormalInput style={{ maxWidth: 418 }} disabled placeholder={'USD'} />
            </Form.Item>
          </Form>
        )}
        {useMemo(() => {
          return (
            isAdmin &&
            currentTab === ProfileTab.Management &&
            currentMenu && (
              <div className="manage-container">
                {currentMenu === ProfileMenuKey.ManageUsers && <ManageUser onRef={ChildRef}></ManageUser>}
                {currentMenu === ProfileMenuKey.Permissions && <Permission></Permission>}
              </div>
            )
          );
        }, [currentTab, currentMenu, isAdmin])}
      </div>
    </PageContainer>
  );
};

export default Page;
