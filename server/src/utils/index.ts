/* *
 * @Author: <EMAIL>
 * @file:
 * @Date: 2023-01-01 21:08:00
 * @Last Modified by: chen<PERSON><EMAIL>
 * @Last Modified time: 2022-11-23 10:27:45
 */

import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { Context } from 'koa';
import nodemailer from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';
import devConfig from '../config/dev.env.config';
import prodConfig from '../config/prod.env.config';
import testConfig from '../config/test.env.config';
import { getLogger  } from '@/config/log4js';
import hotfixConfig from '../config/hotfix.env.config';
import { EmailParams } from '@/constants';

export const getConfig = function () {
  if (!process.env.NODE_ENV) {
    throw Error(`environment variable 'NODE_ENV' is required.`);
  }
  let result = prodConfig;
  switch (process.env.NODE_ENV) {
    case 'prod':
      result = prodConfig;
      break;
    case 'test':
      result = testConfig;
      break;
    case 'hotfix':
      result = hotfixConfig;
      break;
    default:
      result = devConfig;
      break;
  }
  return result;
};

export const genEnCode = function (
  pasLen: number,
  isSpecialCode: boolean = true
) {
  const specialCode = ['_', '-', '$', '%', '&', '@', '+', '!'];
  const codeArr = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9'
  ];
  const pasArr = [...codeArr];
  if (isSpecialCode) {
    pasArr.push(...specialCode);
  }
  let password = '';
  const pasArrLen = pasArr.length;
  for (let i = 0; i < pasLen; i++) {
    password += pasArr[Math.floor(Math.random() * pasArrLen)];
  }
  return password;
};

export const md5 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('md5');
  return hash.update(content).digest('hex');
};

export const sha256 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('sha256');
  return hash.update(content).digest('hex');
};

export const formatDateTime = function (value: Date, format: string) {
  const year = value.getFullYear();
  const month = value.getMonth() + 1;
  const day = value.getDate();
  const hour = value.getHours();
  const min = value.getMinutes();
  const secs = value.getSeconds();
  const str = format
    .replace(/dd/, `0${day}`.slice(-2))
    .replace(/yyyy/, `${year}`)
    .replace(/MM/, `0${month}`.slice(-2))
    .replace(/hh/, `0${hour}`.slice(-2))
    .replace(/mm/, `0${min}`.slice(-2))
    .replace(/ss/, `0${secs}`.slice(-2));
  return str;
};

export const readFileByPromise = function (path: string) {
  return new Promise((resolve, reject) => {
    fs.readFile(path, 'utf-8', (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data);
      }
    });
  });
};

export function formatMoney(money: number) {
  const str = money.toFixed(2); // 只取2位小数
  const l = str.split('.')[0]; // 获取整数位
  const r = str.split('.')[1]; // 获取小数位
  const arr = []; // 用于保存结果
  const len = Math.ceil(l.length / 3); // 3位数一个 `,`
  for (let i = 0; i < len; i++) {
    // 如果传(-3,0)获取不到参数，将0换成undefined相当于没传
    arr.unshift(l.slice(-3 * (i + 1), -3 * i || undefined));
    if (i !== len - 1) {
      // 最后一次截取不加 `,`了
      arr.unshift(',');
    }
  }
  return `${arr.join('')}.${r}`;
}

export const trim = function (str: any) {
  return Boolean(str) === true ? `${str}`.replace(/(^\s*)|(\s*$)/g, '') : '';
};
// 是否空白字符串
export const isNotEmptyStr = function (obj: any) {
  return !/^\s*$/.test(trim(obj));
};

const { emailConfig } = getConfig();
const mailParams: SMTPTransport | SMTPTransport.Options = {
  host: emailConfig.host,
  port: emailConfig.port,
  secure: false,
  debug: true,
  requireTLS: true,
  auth: {
    user: emailConfig.user,
    pass: emailConfig.pass
  },
  tls: {
    ciphers: 'SSLv3'
  }
};

const transporter = nodemailer.createTransport(mailParams);
export const sendEmail = async function sendEmail(obj: any) {
  // send mail with defined transport object
  const { from, bcc } = EmailParams;
  const params = {
    from,
    ...obj,
    bcc
  };
  const DevEnv = ['dev', 'development'];
  if (DevEnv.includes(process.env.NODE_ENV || 'prod')) {
    delete params.bcc;
  }
  await transporter.sendMail(params, (error, info) => {
    if (error) {
      getLogger('error').error(`error: ${error.message}`);
    } else {
      getLogger('app').info(`Message sent: ${info.messageId}`);
    }
  });
};

export const sleep = (ms: number) => new Promise((resolved) => {
  setTimeout(() => {
    resolved(true);
  }, ms);
});

export const objKeySort = (obj: any) => {
  const newObj: any = {};
  Object.keys(obj)
    .sort()
    .forEach((key) => {
      if (Object.hasOwn(obj, key)) {
        newObj[key] = obj[key];
      }
    });
  return newObj;
};

export const floorArrSort = (arr: any[]): (string | number)[][] => arr.sort((a, b) => {
  if (a[0] !== b[0]) {
    return a[0] - b[0];
  }
  return a[2] - b[2];
});

export const str2num = <T>(str: T): number => {
  if (!str) return 0;
  return Number.isNaN(+str) ? 0 : +str;
};

export async function createDirectory(filePath: string) {
  const directory = path.dirname(filePath);

  try {
    await fs.promises.mkdir(directory, { recursive: true });
  } catch (err) {
    console.log('createDirectory error', err);
    getLogger('error').error(`createDirectory error: ${err}`);
    throw err;
  }
}

export const getCsDomain = (pv_domain: string, host_prefix: string) => {
  const domain =
    process.env.NODE_ENV === 'prod'
      ? 'console.rixengine.com'
      : 'console-t.rixengine.com';
  let cs_domain = '';
  if (process.env.NODE_ENV === 'development') {
    cs_domain = 'allowed.console-t.rixengine.com';
  } else if (pv_domain) {
    cs_domain =
      process.env.NODE_ENV === 'prod'
        ? `console.${pv_domain}`
        : `console-t.${pv_domain}`;
  } else {
    cs_domain = `${host_prefix}.${domain}`;
  }
  console.log('cs_domain', cs_domain);
  return cs_domain;
};

/**
 * description: 数字格式化加上单位B,M,K
 * @param {number} originFixed 小于一千时保留的位数，默认2
 * @param {number} formatFixed 格式化之后保留的位数，默认2
 */
export const formatNumberToUnit = (
  num: number,
  originFixed = 2,
  formatFixed = 2
) => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(formatFixed)}B`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(formatFixed)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(formatFixed)}K`;
  }
  return num.toFixed(originFixed);
};

/**
 * @description: 获取用户ip
 */
export const getUserIp = (ctx: Context) => {
  const { req } = ctx;
  const ip: any =
    req.headers['x-forwarded-for'] ||
    req.headers['x-real-ip'] ||
    req.socket.remoteAddress ||
    '';
  return ip.replaceAll('::ffff:', '');
};

/**
 * @description: 获取真实用户ip（可能被伪造）
 */
export const getRealUserIp = (ctx: Context) => {
  const { req } = ctx;

  if (req.headers['x-forwarded-for']) {
    return req.headers['x-forwarded-for'].toString().split(',')[0].trim();
  }

  if (req.headers['x-real-ip']) {
    return req.headers['x-real-ip'].toString().trim();
  }

  if (req.socket.remoteAddress) {
    return req.socket.remoteAddress.replaceAll('::ffff:', '');
  }

  return '';
};

/**
 * @description: 解析JSON字符串，兼容解析失败的情况
 * @returns {object} 返回解析结果和解析错误结果,解析正确时parseError返回undefined
 */
export const parseJson = (
  str: string
): {
  parseResult: any;
  parseError?: string;
} => {
  let parseResult;
  let parseError;
  try {
    parseResult = JSON.parse(str);
  } catch (error: any) {
    parseError = str;
  }
  return { parseResult, parseError };
};

/**
 * @description: Generate key value map
 * @param {array} data - An array of objects
 * @param {keyof T} key - A key of an object within the data array item as the return map key
 * @param {keyof T} value - A key of an object within the data array item as the return map value
 * @returns {object} - An object with the value of key as the key and the value of value as the value of the
 *                     corresponding object.eg: { data.item[key]: data.item[value] }
 */
export const generateMap = <T, K extends keyof T, P extends keyof T>(
  data: T[],
  key: K,
  value: P
) => {
  if (!Array.isArray(data)) throw new Error('data must be an array');
  const result = {} as Record<string, T[P]>;
  data.forEach((item) => {
    result[item[key] as string] = item[value];
  });
  return result;
};

export const array2Object = function (arr: any[], key: string) {
  const obj: any = {};
  if (!Array.isArray(arr)) {
    return obj;
  }
  arr.forEach((it) => {
    obj[it[key]] = it;
  });
  return obj;
};

// 验证输入
export function isValidBundle(bundle: string) {
  const iosReg = /^[0-9]+$/;
  const androidReg = /[a-zA-Z]+[0-9a-zA-Z_](.[a-zA-Z]+[0-9a-zA-Z_])*/;
  return iosReg.test(bundle) || androidReg.test(bundle);
}
