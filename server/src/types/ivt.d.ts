/*
 * @Author: 袁跃钊 yuanyu<PERSON><PERSON>@algorix.co
 * @Date: 2024-01-09 19:39:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-09 20:00:00
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-19 14:45:16
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-28 16:45:03
 * @Description:
 */
import { Context } from 'koa';

export declare namespace IVTAPI {
  type AddIvtParams = {
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    op_id: number;
    status: number;
    buyer_id_str?: string;
    seller_id_str?: string;
    type: number;
    ratio: number;
    bundle?: string;
  };

  type UpdateIvtParams = {
    id: number;
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    op_id: number;
    status: number;
    buyer_id_str?: string;
    seller_id_str?: string;
    type: number;
    ratio: number;
    bundle?: string;
  };

  type CheckIvtParams = {
    id: number;
    tnt_id: number;
    seller_id?: number[];
    buyer_id?: string[];
    bundle?: string;
    type: number;
  };

  interface IvtController {
    getIvtList(ctx: Context): Promise<any>;
    addIvt(ctx: Context): Promise<any>;
    updateIvt(ctx: Context): Promise<any>;
  }

  interface IvtServices {
    isIvtExist(params: any): Promise<any>;
    getIvtList(tnt_id: number): Promise<any>;
    addIvt(params: any): Promise<Boolean>;
    updateIvt(params: any): Promise<Boolean>;
  }

  interface IvtModel {
    isIvtExist(params: any): Promise<any>;
    getIvtList(tnt_id: number): Promise<any>;
    addIvt(params: any, shouldUpdateOnDuplicate?: boolean): Promise<Boolean>;
    updateIvt(params: any): Promise<Boolean>;
  }
}
