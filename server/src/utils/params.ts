/* *
 * @Author: yuan<PERSON>@algorix.co
 * @file:
 * @Date: 2019-08-01 11:29:50
 * @Last Modified by: <EMAIL>
 * @Last Modified time: 2019-08-23 17:29:42
 */

import { escape } from 'mysql';
import { trim, isNotEmptyStr } from '@/utils';

type JoinQueryKey = {
  exactQueries?: string[];
  fuzzyQueries?: string[];
  fuzzyQueriesForMysql?: string[];
  arrayQueries?: string[];
  extra?: string;
  numberCols?: string[];
  prefix?: { [key: string]: string[] } // 多表查询字段前缀映射关系 要带. key是前缀
}

// 转义对象中的所有字符串键
export const getEscapeParams = function <T extends { [key: string]: any }>(
  obj: T
): T {
  const keys = Object.keys(obj);
  const newObj: T = { ...obj };
  keys.forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const val = newObj[key];
      if (typeof val === 'string') {
        // @ts-ignore 下面不能去除哈
        newObj[key] = escape(val).replace(/^\'|\'$/g, '');
      } else if (Array.isArray(val) && val.length) {
        // 数组类型
        const arr = val.map((item) => {
          if (typeof item === 'string') {
            // @ts-ignore
            return escape(item).replace(/^\'|\'$/g, '');
          } if (
            Object.prototype.toString.call(item) === '[object Object]'
          ) {
            return getEscapeParams(item);
          } return item;
        });
        // @ts-ignore
        newObj[key] = arr;
      } else if (Object.prototype.toString.call(val) === '[object Object]') {
        // 对象类型
        // @ts-ignore
        newObj[key] = getEscapeParams(val);
      }
    }
  });
  return newObj;
};
// 转义字符串
export const getEscapeString = function (str: string): string {
  return escape(str).replace(/^\'|\'$/g, '');
};

/**
 *
 * @param key 表字段
 * @param value 值 字符串格式化为key = "value"
 * @param isNum 是否数字
 * @returns
 */
export const exactQueries = function (key: string, value: any, isNum: boolean, prefixMap?: { [key: string]: string }) {
  if (typeof value !== 'undefined' && !!trim(value)) {
    const pre = prefixMap ? prefixMap[key] || '' : '';
    const str = getEscapeString(`${value}`);
    return isNum ? `${pre}${key} = ${value}` : `${pre}${key} = '${str}'`;
  }
  return '';
};

export const fuzzyQueries = function (key: string, value: any, prefixMap?: { [key: string]: string }) {
  if (typeof value !== 'undefined' && !!trim(value)) {
    const pre = prefixMap ? prefixMap[key] || '' : '';
    const str = getEscapeString(`${value}`);
    return `${pre}${key} ~* '${str}'`;
  }
  return '';
};

/**
 * @desc mysql模糊查询
 */
export const fuzzyQueriesForMysql = function (key: string, value: any, prefixMap?: { [key: string]: string }) {
  if (typeof value !== 'undefined' && !!trim(value)) {
    const pre = prefixMap ? prefixMap[key] || '' : '';
    const str = getEscapeString(`${value}`.toLowerCase());
    return `${pre}${key} like '%${str}%'`;
  }
  return '';
};

/**
 *
 * @param key 表字段
 * @param value 值 式化为 in (a,b,c) 格式
 * @param isNum 是否数字 结果 in (1,2,3) 或 in ('a','b','c')
 * @returns
 */
export const arrayQueries = function (key: string, value: any[], isNum: boolean, prefixMap?: { [key: string]: string }) {
  if (Array.isArray(value) && value.length > 0) {
    const pre = prefixMap ? prefixMap[key] || '' : '';
    const content = value.map((item) => (isNum ? `${item}` : `"${getEscapeString(item)}"`)).join(',');
    return `${pre}${key} in (${content})`;
  }
  return '';
};

/**
 *
 * @param keys 字段数组
 * @description keys = {exactQueries: 用于精确条件的字段, arrayQueries:用于多个查询如 in (a,b,c)得字段，numberCols: 数字类型的字段}
 * @param obj 数据对象
 * @returns
 */
export const joinQueries = function (keys: JoinQueryKey, obj: any) {
  let exactQueriesStr = '';
  let fuzzyQueriesStr = '';
  let arrayQueriesStr = '';
  const queries = keys.extra ? [keys.extra] : [];
  const numberCols = keys.numberCols ? keys.numberCols : [];
  // 查询前缀，多表查询使用， 比如adx.admin cd.xxx
  const prefix = keys.prefix ? keys.prefix : {};
  const prefixMap: { [key: string]: string } = {};
  const pre_key = Object.keys(prefix);
  pre_key.forEach((v) => {
    prefix[v].forEach((e) => {
      prefixMap[e] = v;
    });
  });

  if (keys.exactQueries) {
    exactQueriesStr = trim(keys.exactQueries
      .map((item: string) => exactQueries(item, obj[item], numberCols.some((col: string | number) => col === item), prefixMap))
      .filter(isNotEmptyStr).join(' and '));
  }
  if (keys.fuzzyQueries) {
    fuzzyQueriesStr = trim(
      keys.fuzzyQueries.map((item: string) => fuzzyQueries(item, obj[item], prefixMap)).filter(isNotEmptyStr).join(' and ')
    );
  }
  if (keys.fuzzyQueriesForMysql) {
    fuzzyQueriesStr = trim(
      keys.fuzzyQueriesForMysql.map((item: string) => fuzzyQueriesForMysql(item, obj[item], prefixMap)).filter(isNotEmptyStr).join(' and ')
    );
  }
  if (keys.arrayQueries) {
    arrayQueriesStr = trim(
      keys.arrayQueries
        .map((item: string) => arrayQueries(item, obj[item], numberCols.some((col: string | number) => col === item), prefixMap))
        .filter(isNotEmptyStr)
        .join(' and ')
    );
  }
  !!exactQueriesStr && queries.push(exactQueriesStr);
  !!fuzzyQueriesStr && queries.push(fuzzyQueriesStr);
  !!arrayQueriesStr && queries.push(arrayQueriesStr);
  return trim(queries.join(' and '));
};

/**
 * @description 拼接字符串
 * @param strList 字符串数组
 * @param connector 连接符
 * @returns
 */
export const joinStr = function (strList: any[], connector: any) {
  return trim(strList.filter(isNotEmptyStr).join(connector));
};
