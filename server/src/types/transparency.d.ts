import { Context } from 'koa';

export declare namespace TransparencyAPI {
  type AddStgParams = {
    tnt_id: number;
    type: number;
    publisher_id: number;
    developer_website_domain: string;
    bundle: string;
    op_id: number;
    status: number;
  };

  type CheckStgParams = {
    isEdit?: boolean;
    id: number;
    tnt_id: number;
    developer_website_domain: string;
    bundle: string;
    type: number;
  };

  type UpdateStgParams = {
    id: number;
    tnt_id: number;
    publisher_id: number;
    developer_website_domain: string;
    bundle: string;
    op_id: number;
    status: number;
    type: number;
  };

  interface StgChainController {
    getStgChainList(ctx: Context): Promise<any>;
    addStgChain(ctx: Context): Promise<any>;
    updateStgChain(ctx: Context): Promise<any>;
  }

  interface StgChainServices {
    isStgChainExist(params: CheckStgParams): Promise<any>;
    getStgChainList(tnt_id: number): Promise<any>;
    addStgChain(params: AddStgParams): Promise<Boolean>;
    updateStgChain(params: UpdateStgParams): Promise<Boolean>;
  }

  interface StgChainModel {
    isStgChainExist(params: CheckStgParams): Promise<any>;
    getStgChainList(tnt_id: number): Promise<any>;
    addStgChain(params: AddStgParams): Promise<Boolean>;
    updateStgChain(params: UpdateStgParams): Promise<Boolean>;
  }
}
