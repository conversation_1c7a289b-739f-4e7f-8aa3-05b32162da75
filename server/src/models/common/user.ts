/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:09:49
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 17:35:48
 * @Description:
 */
import { UserType } from '@/constants';
import { StatusType } from '@/constants/permission';
import dbUtils from '@/db/mysql';
import { UserAPI } from '@/types/user';
import { getCsDomain, sendEmail } from '@/utils';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class UserModel {
  async getUserLinkList(linked_user_ids: number[]) {
    const sql = `
      select user_id, account_name, user.tnt_id, tnt_name from user left join tenant on user.tnt_id = tenant.tnt_id where user.status = 1 and tenant.status = 1 and user_id in (?)
    `;
    return dbUtils.query(sql, [linked_user_ids]);
  }

  async getUserInfoByUserId(user_id: number) {
    const sql = `
      select
        u.user_id,
        u.account_name,
        t.cs_domain,
        u.password,
        u.tnt_id,
        sa.special_user_id,
        sa.linked_user_ids,
        sa.status as sa_status
      from user as u
      left join tenant as t on u.tnt_id = t.tnt_id
      left join switch_account as sa on u.user_id = sa.special_user_id
      where u.user_id = ? and u.status=?
      limit 1
    `;
    return dbUtils.query(sql, [user_id, StatusType.Active]);
  }

  async getOneUser(options: UserAPI.LoginParams) {
    const sql = `
      select
        u.user_id,
        u.account_name,
        u.tnt_id,
        u.role,
        u.type,
        u.email as user_email,
        u.token as user_token,
        t.tnt_name,
        t.tnt_type,
        t.email,
        t.token,
        t.host_prefix as domain,
        t.cs_domain,
        t.pv_domain,
        t.host_prefix,
        t.pl_status,
        t.hm_status,
        p.brand_name as brand,
        sa.status as sa_status,
        coalesce(ur.role_id, 0) as role_id
      from (select * from user where password=? and account_name = ? and status = ?) as u
      left join user_role_rl as ur on ur.user_id=u.user_id
      left join privatization as p on p.tnt_id = u.tnt_id
      left join switch_account as sa on sa.special_user_id = u.user_id
      inner join tenant as t on t.tnt_id = u.tnt_id and t.cs_domain = ? and t.status = ? 
      limit 1
    `;
    return await dbUtils.query(sql, [
      options.password,
      options.account_name,
      StatusType.Active,
      options.cs_domain,
      StatusType.Active
    ]);
  }

  async getOneUserByUserIdAndTntId(user_id: number, tnt_id: number) {
    const sql = `
    select
      u.user_id,
      u.account_name,
      u.tnt_id,
      u.role,
      u.type,
      u.email as user_email,
      u.token as user_token,
      t.tnt_name,
      t.email,
      t.token,
      t.host_prefix as domain,
      t.cs_domain,
      t.pv_domain,
      t.host_prefix,
      t.pl_status,
      t.hm_status,
      p.brand_name as brand,
      sa.status as sa_status,
      coalesce(ur.role_id, 0) as role_id
    from (select * from user where user_id = ? and status = ?) as u
    left join user_role_rl as ur on ur.user_id=u.user_id
    left join privatization as p on p.tnt_id = u.tnt_id
    left join switch_account as sa on sa.special_user_id = u.user_id
    inner join tenant as t on t.tnt_id = u.tnt_id and t.tnt_id = ? and t.status = ? 
    limit 1
  `;
    return await dbUtils.query(sql, [
      user_id,
      StatusType.Active,
      tnt_id,
      StatusType.Active
    ]);
  }

  async resetPasswordByUserId(options: UserAPI.ResetPasswordParams) {
    return !!(await dbUtils.query(
      'update user set password = ? where user_id = ? and tnt_id = ?',
      [options.new_password, options.user_id, options.tnt_id]
    ));
  }

  async isAccountNameExists(
    params: UserAPI.validAccountNameExistsParams,
    user_id?: number
  ) {
    const { clause, params: values } = buildSQLWhereClause([
      ['account_name = ?', params.account_name],
      ['tnt_id = ?', params.tnt_id],
      ['user_id != ?', user_id ? user_id : null]
    ]);

    return dbUtils.query(`select account_name from user ${clause}`, values);
  }

  async confirmPassword(params: UserAPI.ConfirmPasswordParams) {
    const { user_id, tnt_id, old_password } = params;
    return await dbUtils.query(
      'select user_id from user where user_id = ? and tnt_id = ? and status = 1 and password = ? limit 1',
      [user_id, tnt_id, old_password]
    );
  }

  async sendEmailToUser(params: any) {
    const { account_name, password, isResetPwd, email, tnt_id } = params;
    let { brand, host_prefix, pv_domain } = params;
    if (!brand || !host_prefix || !pv_domain) {
      const sql = `select 
        privatization.brand_name as brand,
        tenant.host_prefix,
        tenant.pv_domain 
        from tenant 
        left join privatization on tenant.tnt_id = privatization.tnt_id
        where tenant.tnt_id = ?`;

      const data = await dbUtils.query(sql, [tnt_id]);
      if (Array.isArray(data) && data.length) {
        host_prefix = data[0]?.host_prefix || '';
        brand = data[0]?.brand || 'RixEngine';
        pv_domain = data[0]?.pv_domain || '';
      }
    }
    const subject_str = isResetPwd ? 'Reset Password' : 'New Account';
    const cs_domain = getCsDomain(pv_domain, host_prefix);
    const domain =
      process.env.NODE_ENV === 'prod'
        ? `https://${cs_domain}`
        : `http://${cs_domain}`;
    sendEmail({
      from: `${brand}<<EMAIL>>`,
      to: `${email || ''}`,
      subject: `${brand} ${subject_str} (${account_name})`,
      html: `
                        <p>Congratulations,${brand} Account had been successfully ${
        isResetPwd ? 'reset password!' : password ? 'registered!' : 'changed!'
      }</p>
                        <p>Platform: ${domain}</p>
                        <p>Username: ${account_name}</p>
                        ${password ? `<p>Password: ${password}</p>` : ''}
                        ${
                          process.env.NODE_ENV !== 'prod'
                            ? '<p>Development</p>'
                            : ''
                        }
                    `
    });
    return true;
  }

  async getDashboardUser(params: any) {
    const { isSupply, user_id, tnt_id } = params;
    const type = isSupply ? UserType.Supply : UserType.Demand;
    const sql = `
    select
      u.account_name as account_name,
      u.user_id as user_id,
      u.status as status,
      u.password as password,
      u.api_status as api_status,
      ${isSupply ? 'i.seller_id as seller_id' : 'i.buyer_id as buyer_id'},
      i.token as token
    from (select account_name,user_id,status,password,api_status from user where user_id=? and type = ? and tnt_id=? limit 1) as u
    left join (select token,user_id,${
      isSupply ? 'seller_id' : 'buyer_id'
    } from ${isSupply ? 'seller' : 'buyer'}) as i on i.user_id = u.user_id`;

    return await dbUtils.query(sql, [user_id, type, tnt_id]);
  }

  async editDashboardUser(params: UserAPI.EditUserParams) {
    const {
      user_id,
      status,
      api_status,
      tnt_id,
      new_password,
      email = '',
      account_name,
      isSupply
    } = params;

    const userObj = buildSQLSetClause([
      ['status', status],
      ['api_status', api_status],
      ['password', new_password],
      [
        'account_name',
        account_name
          ? isSupply
            ? `Pub_${account_name}`
            : `Adv_${account_name}`
          : undefined
      ],
      ['email', email]
    ]);

    if (!userObj) {
      return false;
    }

    const sql = `update user set ? where user_id=? and tnt_id=?`;
    return !!(await dbUtils.query(sql, [userObj, user_id, tnt_id]));
  }

  async vaildPassword(params: any) {
    const { user_id, password, tnt_id } = params;
    return dbUtils.query(
      'select user_id from user where user_id=? and password=? and tnt_id=? limit 1',
      [user_id, password, tnt_id]
    );
  }

  // 更改账号
  async updateAccountName(params: UserAPI.EditUserAccountParams) {
    const { user_id, account_name, tnt_id, type } = params;
    const sql = `update user set account_name=?, status=? where user_id=? and tnt_id=? and type=?`;
    const values = [account_name, StatusType.Active, user_id, tnt_id, type];
    return !!(await dbUtils.query(sql, values));
  }

  // 重置用户密码
  async resetPassword(params: UserAPI.ResetUserPwdParams) {
    const { user_id, tnt_id, type, password, account_name } = params;

    const userObj = buildSQLSetClause([
      ['password', password],
      ['status', StatusType.Active],
      ['account_name', account_name],
      ['user_id', user_id],
      ['tnt_id', tnt_id],
      ['type', type]
    ]);

    const sql = `update user set ? where account_name=? and user_id=? and tnt_id=? and type=?`;

    return !!(await dbUtils.query(sql, [
      userObj,
      account_name,
      user_id,
      tnt_id,
      type
    ]));
  }
}

export const userModel = new UserModel();
