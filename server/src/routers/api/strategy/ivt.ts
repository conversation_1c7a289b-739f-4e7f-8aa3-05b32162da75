/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-09 19:49:20
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-09 19:49:21
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-16 18:39:45
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-27 15:15:03
 * @Description:
 */
import Router from 'koa-router';
import { ivtCtrl } from '@/controllers';

const { addIvt, updateIvt, getIvtList } = ivtCtrl;
const router = new Router({ prefix: '/ivt' });

const routers = router
  .post('/addIvt', addIvt)
  .post('/updateIvt', updateIvt)
  .post('/getIvtConfigList', getIvtList);

export default routers;
