/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-21 15:28:09
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-06 18:44:36
 * @Description:
 */
import { UserType } from '@/constants';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { ABTestAPI } from '@/types/ab-test';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

class ABTestModel implements ABTestAPI.ABTestModel {
  async isABTestExist(params: ABTestAPI.CheckABTestParams): Promise<boolean> {
    const {
      seller_id = 0,
      buyer_id = 0,
      type,
      id,
      tnt_id,
      country = '',
      ad_format = 0,
      ad_size = ''
    } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', seller_id],
      ['buyer_id = ?', buyer_id],
      ['type = ?', type],
      ['tnt_id = ?', tnt_id],
      ['country = ?', country],
      ['ad_format = ?', +ad_format],
      ['ad_size = ?', ad_size],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select id from stg_ab_test ${clause} limit 1`;
    const result = await dbUtil.query(sql, values);
    return result.length > 0;
  }

  async isOtherGroupExist(params: ABTestAPI.CheckABTestParams) {
    const { seller_id = 0, buyer_id = 0, type, tnt_id } = params;
    const now = moment().format('YYYY-MM-DD HH:mm:ss');

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', seller_id],
      ['buyer_id = ?', buyer_id],
      ['type != ?', type],
      ['tnt_id = ?', tnt_id],
      ['expire_time >= ?', now]
    ]);

    const sql = `select id,type,seller_id,buyer_id from stg_ab_test ${clause} limit 1`;
    const result = await dbUtil.query(sql, values);
    return result;
  }
  async getABTestList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<ABTestAPI.ABTestListItem[]> {
    const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];
    const sql = `
    select 
      a.id, 
      a.type, 
      a.seller_id, 
      a.buyer_id, 
      a.country,
      a.ad_format,
      a.ad_size,
      a.content, 
      a.op_id,
      ${getTzTimeSql('a.create_time', cur_time_zone)} as create_time,
      ${getTzTimeSql('a.update_time', cur_time_zone)} as update_time,
      a.status,
      (case when u.status = 3 then "Unknown"
            when a.op_id = 0 then "System"
            when u.type in (?) then "System"
            else u.account_name end) as op_name,
      ${getTzTimeSql('a.expire_time', cur_time_zone)} as expire_time,
      u.status account_status,
      s.seller_name, 
      b.buyer_name
    from stg_ab_test a 
    left join user u on u.user_id = a.op_id
    left join seller s on a.seller_id = s.seller_id
    left join buyer b on a.buyer_id = b.buyer_id
    where a.tnt_id = ?
    order by a.update_time desc`;

    return await dbUtil.query(sql, [SystemType, tnt_id]);
  }

  async addABTest(params: ABTestAPI.AddABTestParams) {
    const {
      seller_id,
      buyer_id,
      type,
      content,
      tnt_id,
      op_id,
      expire_time,
      country = '',
      ad_format = 0,
      ad_size = ''
    } = params;
    const sql = `insert into stg_ab_test 
      (seller_id, buyer_id, type, content,tnt_id,op_id,expire_time,country,ad_format,ad_size) values ?`;
    return !!(await dbUtil.query(sql, [
      [
        [
          seller_id,
          buyer_id,
          type,
          content,
          tnt_id,
          op_id,
          expire_time,
          country,
          +ad_format,
          ad_size
        ]
      ]
    ]));
  }

  async updateABTest(params: ABTestAPI.UpdateABTestParams) {
    const {
      id,
      content,
      tnt_id,
      op_id,
      status,
      expire_time,
      country = '',
      ad_format = 0,
      ad_size = ''
    } = params;

    const abTestObj = buildSQLSetClause([
      ['content', content],
      ['op_id', op_id],
      ['expire_time', expire_time],
      ['status', status],
      ['country', country],
      ['ad_format', +ad_format],
      ['ad_size', ad_size]
    ]);

    if (!abTestObj) {
      return false;
    }

    const sql = `update stg_ab_test set ? where id = ? and tnt_id = ?`;
    return !!(await dbUtil.query(sql, [abTestObj, id, tnt_id]));
  }
}

export const abTestModel = new ABTestModel();
