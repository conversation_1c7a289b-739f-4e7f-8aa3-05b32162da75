@import (reference) '~antd/es/style/themes/index';

@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.menu {
  box-shadow: none;
  margin-bottom: 12px;
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    width: 200px;
    margin: 0 16px;
    border-radius: 6px;
  }
  :global {
    .ant-dropdown-menu-title-content {
      color: var(--text-color);
    }
  }
}

.right {
  display: flex;
  gap: 12px;
  // height: 48px;
  margin-left: auto;
  overflow: hidden;
  .action {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s;
    > span {
      vertical-align: middle;
      color: var(--text-color);
    }
  }
  .search {
    padding: 0 12px;
    &:hover {
      background: transparent;
    }
  }
  .account {
    display: flex;
    align-items: center;
    gap: 8px;
    .avatar {
      color: var(--primary-color);
      vertical-align: top;
      background: rgba(255, 255, 255, 0.85);
    }
  }
}

// .dropdown-container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;
//   .top {
//     display: flex;
//     flex-direction: column;
//     align-items: center;
//     padding-bottom: 2px;
//     width: 100%;
//     .name {
//       padding-top: 4px;
//       color: #5E6466;
//       width: 196px;
//       text-align: center;
//       border-bottom: 1px solid #E2EAEB;
//       padding-bottom: 8px;
//     }
//   }
// }
.dropdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 12px;
  min-width: 160px;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  .top {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 2px;
    width: 100%;
    .name {
      padding-top: 4px;
      color: #5e6466;
      width: 196px;
      text-align: center;
      border-bottom: 1px solid #e2eaeb;
      padding-bottom: 8px;
    }
  }
  :global {
    .ant-dropdown-menu {
      box-shadow: none;
      width: 100%;
      li {
        border-radius: 6px;
      }
    }
  }
}

.name {
  width: max-content;
}

.divider {
  width: 1px;
  background: #d7d7d7;
  height: 2em;
  align-self: center;
}

@media (max-width: 768px) {
  .right {
    gap: 8px;
    .name {
      display: none;
    }
    .avatar {
      display: block !important;
    }

    .divider {
      display: none;
    }
  }
}
