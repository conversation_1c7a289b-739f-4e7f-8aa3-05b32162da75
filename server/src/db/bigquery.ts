/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-11 09:02:29
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-08 11:44:08
 * @Description:
 */

import { getLogger } from '@/config/log4js';
import { CommonAPI } from '@/types/common';
import { getConfig, md5 } from '@/utils';
import { getBQLabels } from '@/utils/report/getBQLabel';
import { BigQuery, Query } from '@google-cloud/bigquery';
import { ResourceStream } from '@google-cloud/paginator';
import { Storage } from '@google-cloud/storage';
import { LabelGenerationParams } from '@rixfe/rix-tools';
import fs from 'fs';
import { getRedisByKey, setRedisByKey } from './redis';

const { redisConfig } = getConfig();
const { platform_key } = redisConfig;
const bigqueryClient = new BigQuery();
const storage = new Storage();

/**
 *
 * @param sqlQuery 查询sql
 * @param allowCache 是否允许缓存,默认不允许,如果允许，传入配置对象，参数如下，允许空对象
 * 「cacheTime」缓存时间,单位秒
 * tag是模块名称/api路径 必传 用于区分是那块调用了
 * @returns
 */
export async function queryStackOverflow(
  sql: string,
  opt: LabelGenerationParams & { cacheTime?: number }
) {
  try {
    const { cacheTime, ...rest } = opt;
    const labels = getBQLabels(rest, sql);
    const stringifyLabels = JSON.stringify(labels);

    const options: Query = {
      query: sql,
      location: 'us-east4',
      labels
    };
    const cacheKey = md5(`${platform_key}_${sql}`);
    if (cacheTime) {
      const cacheData: any = await getRedisByKey(cacheKey);
      if (cacheData) {
        getLogger('sql').info(
          `get gcp data from cache, query_sql: [${sql}], cache_key: [${cacheKey}], tag:${stringifyLabels}`
        );
        return (cacheData as any[]) || [];
      }
    }
    getLogger('sql').info(`queryStackOverflow--BigQuery sql=[${sql}] tag=[${stringifyLabels}]`);
    const [rows] = await bigqueryClient.query(options);
    if (cacheTime && !!rows && rows.length > 0) {
      await setRedisByKey(cacheKey, rows, cacheTime);
    }
    return rows || [];
  } catch (error: any) {
    console.log('queryStackOverflow(BigQuery) error', error?.message || error);
    getLogger('sql').error(
      `queryStackOverflow(BigQuery) error: ${
        error?.message || error
      },sqlQuery: ${sql}`
    );
    return Promise.reject(error);
  }
}

export async function bigqueryStream(sql: string, opt: LabelGenerationParams) {
  let stream: ResourceStream<any>;
  try {
    const labels = getBQLabels(opt, sql);

    const options: Query = {
      query: sql,
      location: 'us-east4',
      labels
    };

    stream = await bigqueryClient.createQueryStream(options);
    getLogger('sql').info(`bigqueryStream--BigQuery sql=[${sql}]`);
    return stream;
  } catch (error: any) {
    console.log('bigqueryStream error', error?.message || error);
    getLogger('sql').error(`bigqueryStream error: ${error?.message || error}`);
    return Promise.reject(error?.message || '');
  }
}

/**
 * 上传文件到 Google Cloud Storage。
 * @param params - 包含上传参数的对象。
 * @param params.filename - 要上传的文件的名称。eg: 'local/path/to/file.txt'
 * @param params.bucket - 要上传到的 GCS 存储桶的名称。
 * @param params.filePath - 在 GCS 存储桶中的目标路径。eg: 'remote/path/to'
 * @param params.clearLocal - 是否在上传成功后删除本地文件。默认删除。
 * @returns 上传成功返回 gcs文件的路径(不含桶路径)，失败返回 错误信息。
 */
export async function uploadFile(params: CommonAPI.UploadParams) {
  const { clearLocal = true } = params;
  const bucket = params.bucket || 'console-rixengine-com';
  const options = {
    destination: params.fileName
  };
  try {
    await storage.bucket(bucket).upload(params.filePath, options);
    getLogger('app').info(
      `gcp uploadFile success, bucket: [${bucket}], fileName: [${params.fileName}]`
    );
    if (clearLocal) {
      // 删除本地文件
      fs.unlink(params.filePath, err => {
        if (err) throw err;
        getLogger('app').info(
          `gcp uploadFile success, localPath: [${params.filePath}], bucket: [${bucket}], fileName: [${params.fileName}]`
        );
      });
    }
    return Promise.resolve(params.fileName);
  } catch (error: any) {
    getLogger('app').error(`gcp uploadFile failed err=[${error.message}]`);
    console.log('xx报错', error);
    return Promise.reject(error);
  }
}

/**
 * @description 从 Google Cloud Storage 下载文件流。
 * @param params - 包含下载参数的对象。
 * @param params.bucket - 要下载的 GCS 存储桶的名称。
 * @param params.fileName - 要下载的文件名。
 */
export async function downloadFileStream(params: CommonAPI.DownloadParams) {
  try {
    const bucketName = params.bucket || 'console-rixengine-com';
    const { fileName } = params;
    return await storage
      .bucket(bucketName)
      .file(fileName)
      .createReadStream() // stream is created
      .on('finish', () => {
        getLogger('app').info(
          `gcp downloadFileStream success, bucket: [${bucketName}], fileName: [${fileName}]`
        );
      });
  } catch (error: any) {
    getLogger('app').error(`gcp downloadFileStream failed err=[${error.message}]`);
    return Promise.reject(error);
  }
}
