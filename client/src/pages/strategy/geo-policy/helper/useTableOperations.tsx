import OperateRender, { OperateRenderItem } from '@/components/OperateRender';
import { GeoPolicyTab, GeoPolicyTabType } from '@/constants/strategy/geo-policy';
import { PlusOutlined } from '@ant-design/icons';
import { type ColumnProps } from 'antd/lib/table';
import { useCallback } from 'react';
import { type KeyDrawerToolRef } from '../AddGeoPolicyKeyDrawer';
import { type KeyRelationDrawerToolRef } from '../AddGeoPolicyKeyRelationDrawer';

type ButtonType = {
  label: string;
  type: 'primary' | 'default';
  size: 'small' | 'middle' | 'large';
  onClick: (params: any) => void;
  icon?: React.ReactNode;
  accessCode?: string;
};

type UseTableOperationsProps = {
  currentTab: GeoPolicyTabType;
  keyDrawerToolRef: React.RefObject<KeyDrawerToolRef>;
  relationDrawerToolRef: React.RefObject<KeyRelationDrawerToolRef>;
};

export const useTableOperations = ({
  currentTab,
  keyDrawerToolRef,
  relationDrawerToolRef
}: UseTableOperationsProps) => {
  const onEdit = useCallback(
    (params: any) => {
      if (currentTab === GeoPolicyTab.key) {
        keyDrawerToolRef.current?.openDrawer(params);
      } else {
        relationDrawerToolRef.current?.openDrawer(params);
      }
    },
    [currentTab, keyDrawerToolRef, relationDrawerToolRef]
  );

  const onCreate = useCallback(
    () => {
      if (currentTab === GeoPolicyTab.key) {
        keyDrawerToolRef.current?.openDrawer();
      } else {
        relationDrawerToolRef.current?.openDrawer();
      }
    },
    [currentTab, keyDrawerToolRef, relationDrawerToolRef]
  );

  // 获取操作按钮配置
  const getOperationButtons = useCallback((): OperateRenderItem[] => {
    return [
      {
        label: 'Edit',
        onClick: onEdit,
        accessCode: 'updateGeoPolicyAuth'
      }
    ];
  }, [onEdit]);

  // 获取操作列配置
  const getOperationColumn = useCallback(() => {
    const btnOptions = getOperationButtons();

    return {
      title: 'Operation',
      dataIndex: 'operate',
      width: 80,
      fixed: 'right',
      render: (txt: any, params: any) => <OperateRender btnOptions={btnOptions} params={params} isExpendTable={true} />
    } as ColumnProps<any>;
  }, [getOperationButtons]);

  // 获取表格扩展按钮
  const getExpandButtons = useCallback((): ButtonType[] => {
    if (currentTab === GeoPolicyTab.key) {
      return [
        {
          label: 'Add Policy Key',
          type: 'primary',
          size: 'small',
          onClick: () => onCreate(),
          icon: <PlusOutlined />,
          accessCode: 'addGeoPolicyAuth'
        }
      ];
    }
    return [
      {
        label: 'Add Policy Authorization',
        type: 'primary',
        size: 'small',
        onClick: onCreate,
        icon: <PlusOutlined />,
        accessCode: 'addGeoPolicyAuth'
      }
    ];
  }, [currentTab, onCreate]);

  return {
    getOperationButtons,
    getOperationColumn,
    getExpandButtons
  };
};
