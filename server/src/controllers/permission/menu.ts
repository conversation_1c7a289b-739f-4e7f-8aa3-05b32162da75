/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2023-03-07 14:21:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-05-06 19:02:15
 * @Description:
 */

import { Context } from 'koa';
import { menuService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class MenuController {
  async getAllMenu(ctx: Context) {
    let result = { ...RESULT };
    const data = await menuService.getAllMenu();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  // 组装菜单使用
  async getMenuList(ctx: Context) {
    let result = { ...RESULT };
    const data = await menuService.getMenuList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export default new MenuController();
