/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-18 10:57:08
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-18 11:25:35
 * @Description:
 */
export const FaviconPathMap: { [key: string]: string } = {
  default: '/favicon.ico'
  // 'i.console-t.rixengine.com': '/favicon/UAstation_favicon.png',
  // 'console.bidtox.com': '/favicon/UAstation_favicon.png',
  // 'lechance.console.rixengine.com': '/favicon/LeChance_favicon.png'
};

export const LogoPathMap: { [key: string]: string } = {
  default: '/img/logo.png'
};

export const BrandBucketName = {
  default: 'console-rix-engine',
  prod: 'static-rix-engine'
};
export const UploadLogoPath = {
  default: 'saas_others/test/',
  prod: 'logos/'
};

export const UploadFaviconPath = {
  default: 'saas_others/test/',
  prod: 'favicons/'
};
