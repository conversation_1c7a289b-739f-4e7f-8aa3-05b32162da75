/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-01 19:46:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-17 19:19:55
 * @Description:
 */
import { defineConfig } from '@umijs/max';
import routes from './config/routes';
import proxy from './config/proxy';
import child_process from 'child_process';

const isDev = process.env.NODE_ENV === 'development';
const version_hash = child_process.execSync('git log -1 --format=%h').toString().trim();

const secretKey = child_process.execSync('git log -1 --format=%H').toString().trim();

export default defineConfig({
  hash: true,
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  routes,
  layout: {
    // local: true,
  },
  styleLoader: {}, // 不拆分css
  locale: {
    // default zh-CN
    default: 'en-US',
    // title: true,
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false
  },
  define: {
    // 这里定义的会覆盖 .env 文件中的定义
    'process.env': {
      NODE_ENV: 'dev',
      UMI_ENV: 'dev',
      SECRET_KEY: secretKey,
      VERSION_HASH: version_hash
    }
  },
  // 开发环境使用HTTPS
  ...(isDev
    ? {
        https: {
          key: './ssl/key.pem',
          cert: './ssl/cert.pem',
          hosts: ['localhost', '127.0.0.1', '***************']
        }
      }
    : {}),
  proxy: proxy['dev'],
  theme: {
    'root-entry-name': 'variable',
    '@primary-color': '#0CAFC7',
    '@layout-header-background': '#fff',
    '@layout-sider-background': '#f3f5f5',
    '@primary-color-hover': '#0CAFC7',
    'primary-1': '#DAF3F7',
    'primary-5': '#0CAFC7'
  },
  // TODO 暂时不删除console
  // extraBabelPlugins: isProd ? ['transform-remove-console'] : [],
  // @ts-ignore
  chainWebpack: function (config: any) {
    const CompressionPlugin = require('compression-webpack-plugin');
    if (process.env.NODE_ENV === 'production') {
      //gzip压缩
      config.plugin('compression-webpack-plugin').use(CompressionPlugin, [
        {
          test: /\.js$|\.html$|\.css$/,
          threshold: 10240,
          deleteOriginalAssets: false
        }
      ]);
    }
  },
  clickToComponent: { editor: 'cursor' },
  mfsu: false,
  favicons: ['/api/common/file/favicon.ico']
});
