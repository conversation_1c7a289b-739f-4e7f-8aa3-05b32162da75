import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { CheckboxUniqueKeyOptionsType, TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/es/table';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { formatMoney } from '@/utils';

export const HumanSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    tooltip: '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported',
    timeLimit: 93
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Domain',
    type: 'input',
    key: 'domain',
    value: '',
    placeholder: 'Support multiple domains',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Dimension',
    key: 'dimension',
    type: 'checkboxFold',
    value: [],
    options: [
      { label: 'Date', value: 'day' },
      { label: 'Month', value: 'month' },
      { label: 'Advertiser', value: 'buyer_id' },
      { label: 'Publisher', value: 'seller_id' },
      { label: 'Bundle', value: 'bundle' },
      { label: 'Domain', value: 'domain' },
      { label: 'Seat', value: 'seat' },
      { label: 'Country', value: 'country' },
      { label: 'Sub Publisher ID', value: 'publisher_id' }
    ]
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Total Events', value: 'total_events' },
      { label: 'Ivt(%)', value: 'ivt_rate' },
      { label: 'SIVT', value: 'sivt' },
      { label: 'Sivt(%)', value: 'sivt_rate' },
      { label: 'GIVT', value: 'givt' },
      { label: 'Givt(%)', value: 'givt_rate' },
      { label: 'Valid Traffic', value: 'valid_traffic' },
      { label: 'Valid Traffic(%)', value: 'valid_traffic_rate' },
      { label: 'SIVT Automated Browsing(%)', value: 'sivt_automated_browsing' },
      { label: 'SIVT False Representation(%)', value: 'sivt_false_representation' },
      { label: 'SIVT Manipulated Behavior(%)', value: 'sivt_manipulated_behavior' },
      { label: 'SIVT Misleading User Interface(%)', value: 'sivt_misleading_user_interface' },
      { label: 'SIVT Undisclosed Classification(%)', value: 'sivt_undisclosed_classification' },
      { label: 'GIVT Data Center(%)', value: 'givt_data_center' },
      { label: 'GIVT Irregular Pattern(%)', value: 'givt_irregular_pattern' },
      { label: 'GIVT Known Crawler(%)', value: 'givt_known_crawler' },
      { label: 'GIVT False Representation(%)', value: 'givt_false_representation' },
      { label: 'GIVT Misleading User Interface(%)', value: 'givt_misleading_user_interface' }
    ]
  }
];

export const HumanCheckboxUniqueKeyOptions: CheckboxUniqueKeyOptionsType[] = [
  {
    key: 'dimension',
    value: ['day', 'month'], // 这两个值二选一
  },
];

export const HumanChangeableColumns: ColumnProps<DashboardAPI.HumanReportItem>[] = [
  {
    title: 'Seat',
    width: 200,
    dataIndex: 'seat',
    ellipsis: { showTitle: false },
    // sorter: (a, b) => +a.seat - +b.seat,
    render: (_, params) => (
      <HoverToolTip title={params.seat}>
        <span>{params.seat}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Total Events',
    width: 200,
    dataIndex: 'total_events',
    ellipsis: { showTitle: false },
    sorter: (a, b) => +a.total_events - +b.total_events,
    render: (_, params) => (
      <HoverToolTip title={params.total_events}>
        <span>{formatMoney(+params.total_events)}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Ivt(%)',
    width: 200,
    dataIndex: 'ivt_rate',
    ellipsis: { showTitle: false },
    sorter: true
  },
  {
    title: 'Sivt(%)',
    width: 200,
    dataIndex: 'sivt_rate',
    ellipsis: { showTitle: false },
    sorter: true
  },
  {
    title: 'Givt(%)',
    width: 200,
    dataIndex: 'givt_rate',
    ellipsis: { showTitle: false },
    sorter: true
  },
  {
    title: 'Sub Publisher ID',
    width: 200,
    dataIndex: 'publisher_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.publisher_id}>
        <span>{params.publisher_id}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Country',
    width: 200,
    dataIndex: 'country',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.country}>
        <span>{params.country}</span>
      </HoverToolTip>
    )
  },

  {
    title: 'SIVT',
    width: 200,
    dataIndex: 'sivt',
    ellipsis: { showTitle: false },
    sorter: true,
    render: (_, params) => <>{formatMoney(+params.sivt)}</>,
  },

  {
    title: 'GIVT',
    width: 200,
    dataIndex: 'givt',
    ellipsis: { showTitle: false },
    sorter: true,
    render: (_, params) => <>{formatMoney(+params.givt)}</>
  },

  {
    title: 'Valid Traffic',
    width: 200,
    dataIndex: 'valid_traffic',
    ellipsis: { showTitle: false },
    sorter: true,
    render: (_, params) => <>{formatMoney(+params.valid_traffic)}</>
  },
  {
    title: 'Valid Traffic(%)',
    width: 200,
    dataIndex: 'valid_traffic_rate',
    sorter: true
  },
  {
    title: 'SIVT Automated Browsing(%)',
    width: 230,
    dataIndex: 'sivt_automated_browsing'
  },
  {
    title: 'SIVT False Representation(%)',
    width: 230,
    dataIndex: 'sivt_false_representation'
  },
  {
    title: 'SIVT Manipulated Behavior(%)',
    width: 240,
    dataIndex: 'sivt_manipulated_behavior'
  },
  {
    title: 'SIVT Misleading User Interface(%)',
    width: 260,
    dataIndex: 'sivt_misleading_user_interface'
  },
  {
    title: 'SIVT Undisclosed Classification(%)',
    width: 260,
    dataIndex: 'sivt_undisclosed_classification'
  },
  {
    title: 'GIVT Data Center(%)',
    width: 200,
    dataIndex: 'givt_data_center'
  },
  {
    title: 'GIVT Irregular Pattern(%)',
    width: 200,
    dataIndex: 'givt_irregular_pattern'
  },
  {
    title: 'GIVT Known Crawler(%)',
    width: 200,
    dataIndex: 'givt_known_crawler'
  },
  {
    title: 'GIVT False Representation(%)',
    width: 230,
    dataIndex: 'givt_false_representation'
  },
  {
    title: 'GIVT Misleading User Interface(%)',
    width: 260,
    dataIndex: 'givt_misleading_user_interface'
  }
];

export const HumanAllColumns: ColumnProps<DashboardAPI.HumanReportItem>[] = [
  {
    title: 'Date',
    dataIndex: 'day',
    width: 130,
    sorter: (a, b) => {
      // a.day_hour - b.day_hour,
      const a1 = new Date(a.day).getTime();
      const b1 = new Date(b.day).getTime();
      return a1 - b1;
    }
  },
  {
    title: 'Month',
    dataIndex: 'month',
    width: 130,
    sorter: (a, b) => {
      const a1 = new Date(a.month).getTime();
      const b1 = new Date(b.month).getTime();
      return a1 - b1;
    }
  },
  {
    title: 'Advertiser',
    width: 200,
    dataIndex: 'buyer_id',
    sorter: (a, b) => +a.buyer_id - +b.buyer_id,
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.buyer}>
        <span>{params.buyer}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher',
    width: 200,
    dataIndex: 'seller_id',
    ellipsis: { showTitle: false },
    sorter: (a, b) => +a.seller_id - +b.seller_id,
    render: (_, params) => (
      <HoverToolTip title={params.seller}>
        <span>{params.seller}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Bundle',
    width: 200,
    dataIndex: 'bundle',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.bundle}>
        <span>{params.bundle}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Domain',
    width: 200,
    dataIndex: 'domain',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.domain}>
        <span>{params.domain}</span>
      </HoverToolTip>
    )
  },
  ...HumanChangeableColumns
];

export const DefaultDimension = ['day'];

export const DefaultColumnKeys = ['day'];

export const DefaultMetrics = [
  // 'seat',
  'total_events',
  'ivt_rate',
  // 'publisher_id',
  // 'country',
  'valid_traffic',
  'sivt',
  'givt',
  'sivt_rate',
  'givt_rate',
  'valid_traffic_rate'
  // 'sivt_automated_browsing',
  // 'sivt_false_representation',
  // 'sivt_manipulated_behavior',
  // 'sivt_misleading_user_interface',
  // 'sivt_undisclosed_classification',
  // 'givt_data_center',
  // 'givt_irregular_pattern',
  // 'givt_known_crawler',
  // 'givt_false_representation',
  // 'givt_misleading_user_interface'
];

export const HumanBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'Tool2 Reporting'
  }
];
