/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-09-30 11:31:37
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-02 20:51:07
 * @Description:
 */
import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  openKeys?: boolean;
} = {
  navTheme: 'light',
  headerTheme: 'light',
  // 拂晓蓝
  primaryColor: '#0CAFC7',
  layout: 'mix',
  contentWidth: 'Fluid',
  splitMenus: false,
  fixedHeader: true,
  fixSiderbar: true,
  colorWeak: false,
  title: '',
  headerHeight: 64,
  pwa: false,
  logo: '/img/logo.png',
  menu: {
    locale: false,
  },
  openKeys: false
};

export default Settings;
