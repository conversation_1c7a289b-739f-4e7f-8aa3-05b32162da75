/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:05:48
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-20 16:43:50
 * @Description:
 */
import { UserType } from '@/constants';
import { capModel } from '@/models';
import { CapAPI } from '@/types/cap';

class CapService implements CapAPI.Cap {
  async isCapExists(params: CapAPI.IsExistParams): Promise<any[]> {
    return await capModel.isCapExists(params);
  }

  async getCapList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<CapAPI.CapListItem[]> {
    let list = await await capModel.getCapList(tnt_id, cur_time_zone);
    list = list.map((item: any) => {
      [UserType.Rix_Admin, UserType.Rix_Data_Analyst].includes(
        item.account_type
      ) && (item.account_name = 'System');
      item.account_status === 3 && (item.account_name = 'UnKnow');
      delete item.account_type;
      return item;
    });
    return list;
  }

  async addCap(params: CapAPI.AddCapParams): Promise<boolean> {
    return await capModel.addCap(params);
  }

  async updateCap(params: CapAPI.updateCapParams): Promise<boolean> {
    return await capModel.updateCap(params);
  }
}

export default new CapService();
