import { PolicyKeyOption } from '@/pages/strategy/geo-policy/helper/useSearchOptions';
import type { SelectProps } from 'antd';
import { Select, Tooltip } from 'antd';
import styles from './index.less';

export interface StatusSelectProps extends SelectProps<any> {
  options?: PolicyKeyOption[];
}

/**
 * 带状态样式的Select组件
 * status: 1 - 启用(绿色)，2 - 禁用(灰色)
 */
function StatusSelect(props: StatusSelectProps): JSX.Element {
  const { options, ...restProps } = props;
  const flag = props.mode === 'multiple' || props.mode === 'tags';

  return (
    <Select
      maxTagPlaceholder={omittedValues => (
        <Tooltip
          overlayClassName={styles['tooltip-container']}
          placement="topRight"
          title={
            <div className={styles['placeholder-container']}>
              {omittedValues.map(({ label }, index) => (
                <span key={index}>{label}</span>
              ))}
            </div>
          }
        >
          <span style={{ cursor: 'pointer' }}>+{omittedValues.length}...</span>
        </Tooltip>
      )}
      maxTagCount="responsive"
      optionFilterProp="children"
      filterOption={(input, option: any) =>
        option && option.label && option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      placeholder="Please Select"
      {...restProps}
      allowClear={!!flag || props.allowClear}
      className={`${styles['status-select-container']} ${flag ? styles['status-select-multiple-container'] : ''} ${
        props.className
      }`}
      popupClassName={`${styles['status-select-dropdown']} ${props.popupClassName ? props.popupClassName : ''}`}
      optionLabelProp="label"
    >
      {options?.map(option => (
        <Select.Option key={option.value} value={option.value} label={option.label}>
          <div className={styles['option-item']}>
            <span
              className={`${styles['status-dot']} ${
                option.status === 1 ? styles['status-active'] : styles['status-inactive']
              }`}
            />
            <span>{option.label}</span>
          </div>
        </Select.Option>
      ))}
    </Select>
  );
}

export default StatusSelect;
