/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-03-05 10:52:39
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:57:48
 * @Description:
 */

// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';

// ?components
import HoverToolTip from '@/components/ToolTip/HoverTooltip';

// ?utils
import { formatMoney } from '@/utils';
import { RegionOptions } from '..';
import { FormatExportValueMapType } from '@/utils/export-file';

export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'A/B Test Reporting'
  }
];

const ABTestTypeMap: { [key: string]: string } = {
  op_profit_ratio: 'Profit',
  op_bidfloor: 'Bid Floor',
  op_banner_transfer_format: 'Transfer Format'
};
export const ABTestTypeOptions = [
  { label: 'op_profit_ratio', value: 1 },
  { label: 'op_bidfloor', value: 2 },
  { label: 'op_banner_transfer_format', value: 3 }
];

const DimensionsOptions = [
  { label: 'Date', value: 'day' },
];
export const SearchOptions: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 15
  },
  {
    name: 'Type',
    type: 'select',
    key: 'test_tag_a',
    value: [],
    rules: [{ required: true, message: 'Please select Type' }],
    options: [
      {
        label: 'Profit',
        value: 'op_profit_ratio'
      },
      {
        label: 'Bid Floor',
        value: 'op_bidfloor'
      },
      {
        label: 'Transfer Format',
        value: 'op_banner_transfer_format'
      }
    ]
  },
  {
    name: 'Test Group',
    type: 'select',
    key: 'group',
    rules: [{ required: true, message: 'Please select Group' }],
    value: [],
    options: []
  },
  {
    name: 'Server Region',
    key: 'region',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: RegionOptions.map(item => {
      return {
        value: item.label,
        label: item.label
      };
    })
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: DimensionsOptions
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Request', value: 'request' },
      { label: 'Response', value: 'response' },
      { label: 'Impression(ADM)', value: 'impression' },
      {
        label: 'Fill Rate',
        value: 'fill_rate',
        tooltip: { showTitle: true, title: '(sum(Response) * 100) / sum(Request)' }
      },
      { label: 'Win Rate', value: 'win_rate', tooltip: { showTitle: true, title: '(sum(Impression(ADM)) *100 / sum(response))' } },

      { label: 'Advertiser Net Revenue', value: 'buyer_net_revenue' },
      {
        label: 'eCPR',
        value: 'ecpr',
        tooltip: {
          showTitle: true,
          title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Publisher Request)'
        }
      },
      {
        label: 'Profit eCPR',
        value: 'profit_ecpr'
        // tooltip: {
        //   showTitle: true,
        //   title: 'sum(Advertiser Net Revenue) * 1000000 / sum(Publisher Request)'
        // }
      },
      {
        label: 'QPS (Real)',
        value: 'real_qps',
        tooltip: { showTitle: true, title: 'Total Request / Secs' }
      },
      {
        label: 'Profit',
        value: 'profit',
        tooltip: { showTitle: true, title: 'sum(Advertiser Net Revenue) - sum(Publisher Net Revenue)' }
      },
      {
        label: 'Profit Rate',
        value: 'profit_rate',
        tooltip: {
          showTitle: true,
          title: '(sum(Advertiser Net Revenue) - sum(Publisher Net Revenue))) / sum(Advertiser Net Revenue)'
        }
      },
      { label: 'Average Bid Floor', value: 'avg_bid_floor' },
      { label: 'Average Bid Price', value: 'avg_bid_price' }
    ],
    placeholder: 'Please select Content'
  }
];

export const DefaultDimension = ['day'];
export const DefaultMetrics = [
  'request',
  'response',
  'impression',
  'fill_rate',
  'win_rate',
  'buyer_net_revenue',
  'ecpr',
  'profit_ecpr',
  'real_qps',
  'profit',
  'profit_rate',
  'avg_bid_floor',
  'avg_bid_price'
];

export const AllColumns: ColumnProps<DashboardAPI.ABtestReportItem>[] = [
  {
    title: 'Date',
    key: 'day',
    fixed: 'left',
    width: 160,
    dataIndex: 'date',
    render: (txt, params) => <>{params.date}</>,
    sorter: true
  },
  {
    title: 'Type',
    width: 160,
    dataIndex: 'test_tag_a',
    key: 'test_tag_a',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={ABTestTypeMap[params.test_tag_a] || '-'}>
        <span>{ABTestTypeMap[params.test_tag_a] || '-'}</span>
      </HoverToolTip>
    )
  },

  {
    title: 'Content',
    width: 200,
    dataIndex: 'test_tag_b',
    key: 'test_tag_b',
    ellipsis: { showTitle: false }
  },
  {
    title: 'Request',
    width: 160,
    dataIndex: 'request',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Response',
    key: 'response',
    dataIndex: 'response',
    width: 150,

    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Impression(ADM)',
    key: 'impression',
    dataIndex: 'impression',
    width: 180,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Fill Rate(%)',
    key: 'fill_rate',
    dataIndex: 'fill_rate',
    width: 150
  },
  {
    title: 'Win Rate(%)',
    key: 'win_rate',
    dataIndex: 'win_rate',
    width: 150
  },
  {
    title: 'Advertiser Net Revenue',
    width: 220,
    dataIndex: 'buyer_net_revenue',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'eCPR',
    key: 'ecpr',
    dataIndex: 'ecpr',
    sorter: true,
    width: 150
  },
  {
    title: 'Profit eCPR',
    key: 'profit_ecpr',
    dataIndex: 'profit_ecpr',
    sorter: true,
    width: 150
  },
  {
    title: 'QPS (Real)',
    key: 'real_qps',
    dataIndex: 'real_qps',

    width: 120
  },
  {
    title: 'Profit',
    key: 'profit',
    dataIndex: 'profit',
    width: 150,
    // sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Profit Rate(%)',
    key: 'profit_rate',
    dataIndex: 'profit_rate',
    // sorter: true,
    width: 150
  },
  {
    title: 'Average Bid Floor',
    key: 'avg_bid_floor',
    dataIndex: 'avg_bid_floor',
    // sorter: true,
    width: 150
  },
  {
    title: 'Average Bid Price',
    key: 'avg_bid_price',
    dataIndex: 'avg_bid_price',
    // sorter: true,
    width: 150
  }
];

// 固定维度（不可通过筛选项选择）
export const FixedDimension = [
  'test_tag_a',
  'test_tag_b',
]

export const DefaultColumnKeys = [
  // 'buyer_gross_revenue',
  ...FixedDimension,
  'request',
  'response',
  'impression',
  'fill_rate',
  'win_rate',
  'buyer_net_revenue',
  'ecpr',
  'profit_ecpr',
  'real_qps',
  'profit',
  'profit_rate',
  'avg_bid_floor',
  'avg_bid_price'
];


// 处理本地导出的数据映射问题
export const FormatExportValueMap: FormatExportValueMapType = {
  test_tag_a: (test_tag_a: any) => ABTestTypeMap[test_tag_a],
};
