/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:44:06
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:24:15
 * @FilePath: /saas.rix-platform/server-ts/src/services/supply/supply.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEmpo
 */
import { AuthLevel } from '@/constants/demand';
import { QueryParamType } from '@/db/mysql';
import { delRedisByKey } from '@/db/redis';
import { supplyModel, userModel } from '@/models';
import { SupplyAPI } from '@/types/supply';
import { getConfig, md5 } from '@/utils';
import { buildSQLSetClause } from '@rixfe/rix-tools';
const { redisConfig } = getConfig();

class SupplyService implements SupplyAPI.Supply {
  async getSupplyList(
    tnt_id: number,
    cur_time_zone: string,
    id?: number,
    isTesting?: boolean
  ) {
    return await supplyModel.getSupplyList(
      tnt_id,
      cur_time_zone,
      id,
      isTesting
    );
  }

  async addSupply(params: SupplyAPI.AddSupplyParams): Promise<boolean> {
    return supplyModel.addSupply(params);
  }

  async updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean> {
    const {
      adomain_filter = 1,
      channel_type,
      native_strict_validation = 2,
      banner_multi_size,
      seller_id,
      seller_name,
      status,
      relationship,
      tnt_id,
      profit_model,
      profit_ratio = 0,
      rev_share_ratio = 100,
      profit_id,
      op_id,
      cus_status = 2,
      integration_type,
      tagid_status = 2,
      pass_burl = 2,
      pass_nurl = 2,
      pass_lurl = 2,
      rev_track_type = 1,
      user_id,
      profitRatioChange = false,
      sp_id = 0,
      publisher_id = '',
      support_omid = 2
    } = params;

    const sellerUpdateObj = buildSQLSetClause([
      ['seller_name', seller_name],
      ['status', status],
      ['channel_type', channel_type],
      ['integration_type', integration_type],
      ['relationship', relationship],
      ['profit_model', profit_model],
      ['rev_share_ratio', rev_share_ratio],
      ['cus_status', cus_status],
      ['tagid_status', tagid_status],
      ['rev_track_type', rev_track_type],
      ['support_omid', support_omid],
      ['pass_burl', pass_burl],
      ['pass_nurl', pass_nurl],
      ['pass_lurl', pass_lurl],
      ['sp_id', sp_id],
      ['publisher_id', publisher_id],
      ['adomain_filter', adomain_filter],
      ['native_strict_validation', native_strict_validation],
      ['banner_multi_size', banner_multi_size]
    ]);

    const profitUpdateObj = buildSQLSetClause([
      ['profit_ratio', profit_ratio],
      ['op_id', profitRatioChange ? op_id : null],
      ['status', 1]
    ]);

    const sellerUpdateSql = `update seller set ? where seller_id=? and tnt_id=?`;
    const profitUpdateSql = `update profit set ? where id=? and tnt_id=? and seller_id=?`;

    const sqls = [
      {
        sql: sellerUpdateSql,
        values: [sellerUpdateObj, seller_id, tnt_id]
      },
      {
        sql: profitUpdateSql,
        values: [profitUpdateObj, profit_id, tnt_id, seller_id]
      }
    ];
    const res = await supplyModel.updateSupply(sqls);
    if (res) {
      const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
      await delRedisByKey(key);
    }
    return res;
  }

  async isSupplyNameExists(
    seller_name: string,
    tnt_id: number,
    seller_id?: number
  ): Promise<any[]> {
    return await supplyModel.isSupplyNameExists(seller_name, tnt_id, seller_id);
  }

  async getSupplyAuth(seller_id: number, tnt_id: number) {
    return await supplyModel.getSupplyAuth(seller_id, tnt_id);
  }

  async isSupplyUserNameExists(
    account_name: string,
    tnt_id: number,
    user_id?: number
  ) {
    const params: any = {
      account_name,
      tnt_id
    };
    return await userModel.isAccountNameExists(params, user_id);
  }

  // 获取对应的
  async getSupplyAppPlacement(
    seller_id: number,
    flag: boolean,
    tnt_id: number
  ) {
    try {
      // 获取app
      const apps: SupplyAPI.SellerAppItem[] = await supplyModel.getSupplyApp(
        seller_id,
        tnt_id
      );
      const appids = apps.map(item => item.app_id);
      let authids = [...appids];
      let placements: SupplyAPI.SellerPlacement[] = [];
      if (appids.length) {
        // 获取placement
        placements = await supplyModel.getAppPlacement(appids, tnt_id);
        const plids = placements.map(item => item.plm_id);
        authids = [...authids, ...plids];
      }
      let dsps: any[] = [];
      // 获取授权的dsp
      if (authids.length && !flag) {
        dsps = await supplyModel.getAuthDsp(authids, tnt_id);
      }
      const plats = placements.map(item => {
        const pls_dsp = dsps.filter(
          val =>
            val.pub_id === `${item.plm_id}` &&
            val.level === AuthLevel['Supply Placement']
        );
        let obj: any = {
          ...item,
          demand_list: pls_dsp
        };
        if (flag) {
          delete obj.status;
        } else {
          obj = {
            plm_id: item.plm_id,
            plm_name: item.plm_name,
            app_id: item.app_id,
            ad_format: item.ad_format,
            status: item.status,
            demand_list: pls_dsp
          };
        }
        return obj;
      });
      const data = apps.map(item => {
        const pls = plats.filter(val => val.app_id === item.app_id);
        const app_dsp = dsps.filter(
          (val: any) =>
            val.pub_id === `${item.app_id}` &&
            val.level === AuthLevel['Supply App']
        );
        return {
          ...item,
          adslots: pls,
          demand_list: app_dsp
        };
      });
      return Promise.resolve(data);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  async setSupplyAuth(params: SupplyAPI.SetSupplyAuthParams, op_id: number) {
    const { level, buyer_ids, pub_id, status, app_id, plm_id, tnt_id } = params;

    const sqls: QueryParamType[] = [
      {
        sql: 'delete from config_auth where level = ? and pub_id = ? and tnt_id=?',
        values: [level, pub_id, tnt_id]
      }
    ];

    if (buyer_ids && buyer_ids.length) {
      const values = buyer_ids.map(buyer_id => [
        level,
        pub_id,
        buyer_id,
        op_id,
        tnt_id
      ]);
      sqls.push({
        sql: 'insert into config_auth (level, pub_id, buyer_id, op_id, tnt_id) values ?',
        values: [values]
      });
    }

    // 更改status
    if (+level === AuthLevel['Supply App'] && app_id) {
      sqls.push({
        sql: 'update seller_app set status = ? where app_id = ? and tnt_id = ?',
        values: [status, app_id, tnt_id]
      });
    }

    if (+level === AuthLevel['Supply Placement'] && plm_id) {
      sqls.push({
        sql: 'update seller_placement set status = ? where plm_id = ? and tnt_id = ?',
        values: [status, plm_id, tnt_id]
      });
    }

    return supplyModel.setSupplyAuth(sqls);
  }

  async getSupplyEndpoint(tnt_id: number, seller_id?: number) {
    return await supplyModel.getSupplyEndpoint(tnt_id, seller_id);
  }

  async getDashboardSupplyList(params: {
    tnt_id: number;
    seller_id: number[];
  }) {
    return await supplyModel.getDashboardSupplyList(params);
  }
}

export default new SupplyService();
