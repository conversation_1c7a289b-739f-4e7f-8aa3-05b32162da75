/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-28 20:08:21
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-21 11:53:52
 * @Description:
 */
declare namespace BoardAPI {
  type OverviewItem = {
    revenue: number;
    revenue_increase: number;
    profit: number;
    profit_increase: number;
    request: number;
    request_increase: number;
    impression: number;
    impression_increase: number;
    ecpr: number;
    ecpr_increase: number;
    hours_data?: {
      date: string;
      revenue: number;
      profit: number;
      request: number;
      impression: number;
    }[];
    update_time: string;
  };

  type TopCountryItem = {
    country: string;
    revenue: number;
    top_tnts: {
      tnt: number;
      revenue: number;
    }[];
  };
  type TopCountryAdFormatItem = {
    country: string;
    revenue: number;
    sl_revenue: number;
    top_ad_formats: {
      top_ad_sizes: {
        ad_size: string;
        revenue: number;
      }[];
      country: string;
      revenue: number;
      ad_format: string;
    }[];
  };
  type SupplyDemandItem = {
    seller_id: number;
    seller_name: string;
    buyer_id: number;
    buyer_name: string;
    revenue: number;
    sl_revenue: number;
    ad_format: string;
    country: string;
    top_ad_sizes?: {
      ad_size: string;
      revenue: number;
    }[];
    top_countries?: {
      country: string;
      revenue: number;
    }[];
  };

  type TopAdFormatItem = {
    ad_format: string;
    revenue: number;
    top_ad_sizes: {
      ad_size: string;
      revenue: number;
    }[];
  };
  type CTVData = {
    date: string;
    revenue: number;
  };
  type TenantRevenueItem = {
    tnt: string;
    revenue: number;
  };

  type EcpmAndEcprItem = {
    date: string;
    ecpm: number;
    ecpr: number;
    revenue: number;
    ad_format: string;
  };

  type BudgetAndTrafficItem = {
    ad_format: number;
    revenue: number;
    country: string;
    isRevenue: boolean;
    ecpr: number;
    key: string;
    isReuqested: boolean;
    buyer_options: {
      buyer_id: number;
      revenue: number;
      buyer: string;
    }[];
  };

  type TrafficRequestLog = {
    id: number;
    content: string;
    ext_1: string;
    mixed_key: string;
    op_id: number;
    op_name: string;
    create_time: string;
  };
}
