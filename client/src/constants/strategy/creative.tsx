/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-20 11:41:14
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-13 15:42:10
 * @Description:
 */
// ?types
import { ColumnProps } from 'antd/lib/table';
import { TopBarSearchItem } from '@/components/TopBar';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import EllipsisPopover from '@/components/EllipsisPopover';

// ?components
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import StatusTag from '@/components/Tag/StatusTag';

// ?constants
import { StatusDesc, StatusOptions } from '@/constants';

// ?utils
export const CreativeColumns: ColumnProps<StrategyAPI.CreativeListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 150,
    ellipsis: { showTitle: false },
    fixed: 'left',
    render: (txt, params) => (
      <HoverToolTip title={txt ? `${txt}(${params.buyer_id})` : params.buyer_id === 0 ? 'All Advertisers' : '-'}>
        <span>{txt ? `${txt}(${params.buyer_id})` : params.buyer_id === 0 ? 'All Advertisers' : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    ellipsis: { showTitle: false },
    // render: (txt: string, params) => {
    //   let list = (txt && txt.split(',').filter(v => v)) || [];
    //   return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    // }
    render: (txt, params) => (
      <HoverToolTip title={txt ? `${txt}(${params.seller_id})` : 'All Publishers'}>
        <span>{txt ? `${txt}(${params.seller_id})` : params.seller_id === 0 ? 'All Publishers' : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Type',
    dataIndex: 'type',
    width: 100,
    render: (txt: number, params) => {
      return txt ? BlockTypeMap[txt] : '-';
    }
  },
  {
    title: 'Content',
    dataIndex: 'content',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt: string, params) => {
      let list = (txt && txt.split(',').filter(v => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  {
    title: 'Remark',
    dataIndex: 'remark',
    width: 150,
    render: (txt: string) => {
      return txt || '-';
    }
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['op_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status_desc',
    width: 100,
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  }
];

export const CreativeSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const CreativeBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'Creative'
  }
];

export const BlockTypeMap: { [key: number]: string } = {
  1: 'Crid',
  2: 'Adomain',
  3: 'Bundle'
};

export const BlockTypeOptions = [
  {
    label: 'Crid',
    value: 1
  },
  {
    label: 'Adomain',
    value: 2
  },
  {
    label: 'Bundle',
    value: 3
  }
];

export const IsAllMap = {
  YES: 1,
  NO: 0,
  DEFAULT: -1
};
