{"private": true, "scripts": {"dev": "cross-env UMI_ENV=dev max dev", "build": "cross-env UMI_ENV=prod max build", "builddeploy": "cross-env UMI_ENV=prod max build && rm -rf ../server/webroot/* && cp -Rf dist/. ../server/webroot && rm -rf dist*", "format": "prettier --cache --write .", "postinstall": "max setup", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "setup": "max setup", "start": "npm run dev", "lint": "eslint --fix --ext .ts,.tsx src/"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.7.0", "@ant-design/pro-card": "^1.20.17", "@ant-design/pro-components": "^1.1.3", "@ant-design/pro-layout": "^6.38.18", "@umijs/max": "^4.0.13", "ahooks": "^3.7.0", "antd": "^4.22.7", "crypto-js": "^4.2.0", "github-markdown-css": "^5.2.0", "highlight.js": "^11.8.0", "immutable": "^5.0.0-beta.4", "json2csv": "^5.0.7", "mammoth": "^1.6.0", "marked": "^7.0.1", "marked-highlight": "^2.0.6", "marked-smartypants": "^1.1.0", "md5": "^2.3.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "node-html-parser": "^6.1.5", "query-string": "^7.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-json-tree": "^0.18.0", "react-svg": "^15.1.9", "valtio": "^2.1.5"}, "devDependencies": {"@json2csv/plainjs": "^7.0.1", "@types/crypto-js": "^4.2.2", "@types/json2csv": "^5.0.3", "@types/md5": "^2.3.5", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "prettier-plugin-organize-imports": "^2", "prettier-plugin-packagejson": "^2", "typescript": "^4.1.2"}}