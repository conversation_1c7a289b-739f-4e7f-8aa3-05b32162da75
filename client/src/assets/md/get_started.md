# 「RixEngine」🌟 管理平台使用手册 🌟

# **一、入门指南**

![img](https://static.rixengine.com/a/platform/help/manual/1.png)

# **二、产品功能**

**SaaS**平台为广告生态提供一站式服务，包括：

- **Dashboard** :为广告上下游提供关键指标动态变化及昨日统计数据，提高运营效率；
- **Ree AI** :依托大数据和 AI 技术，为用户提供一键式智能线上流量交易服务，助力全平台业务增长；
- **Data Report** :提供数据分析报表、结算报表、流量质量等报表。便于全方位数据分析；
- **Partner** :通过 Partner 统一管理同一客户的多个上下游账号，可按 Partner 维度查看客户的数据，便于结算；
- **Supply** :对下游（供应方）进行统一管理，便于快速接入下游；
- **Developer** :针对有差异性需求的下游，可在 RixEngine SaaS 平台进行 App 的添加以及对应广告位的管理；
- **Demand** :对上游（需求方）进行统一管理，便于快速接入上游；
- **Strategy Config** :对上下游流量转发、利润策略等进行统一管理配置，例如利润、QPS 等策略；

# **三、产品使用指南**

## **「数据看板」**

### **1. Dashboard**

#### 1.1 Overview

​ **实时查看关键指标（eg: Revenue、Profit）今日增长率，以及 Top Country、Top Ad Format、Top Publisher、Top Advertiser 等昨日关键的统计数据**。

![image-20240129153742991](https://static.rixengine.com/a/platform/help/manual/3.1-1-1.1.png)

#### 1.2 Compare

​ **实时数据曲线对比图（环比昨日），支持上下游维度和不同数据指标（eg: Revenue、Profit）的筛选。**

![image-20230915173941233](https://static.rixengine.com/a/platform/help/manual/3.1-1-1.2.png)

### **2. Ree AI**

​ **依托大数据和 AI 技术，为用户提供一键式智能线上流量交易服务。**

#### 2.1 Al Board

​ **实时收集和分析账号数据，利用 AI 精准算法，帮助客户识别优质预算和流量，推荐适合加量投放的国家/地区和广告格式，提升广告运营效率。客户可全平台范围内"求流量"或"求预算"，助力业务增长。**

![image-20240416164755824](https://static.rixengine.com/a/platform/help/manual/ree-ai-11.png)

#### 2.2 Al Suggestions

​ **针对 Revenue、IVT、QPS、Cap 四项指标提供监控预警和优化建议，助力运营人员迅速获取全局数据洞察，及时调整广告策略。更多相关问题可查看<a href="/help/ai-guide">Al Suggestions 文档</a>。**

![image-20240416170212609](https://static.rixengine.com/a/platform/help/manual/ree-ai-2.png)

![image-20240416165245704](https://static.rixengine.com/a/platform/help/manual/ree-ai-3.png)

### **3. Data Report**

#### 3.1 Full Reporting

​ **根据数据维度和数据指标维度的筛选，进行业务指标数据分析。**

![image-20230918101209293](https://static.rixengine.com/a/platform/help/manual/3.1-2-1.png)

#### 3.2 Billing Reporting

​ **该模块主要用于月度结算，可根据需求查看不同时间区间的汇总数据报告（包括周报、月报、季度报告等）。**

![image-20230915184012276](https://static.rixengine.com/a/platform/help/manual/3.1-2-2.png)

<!-- #### 3.3 Pixalate Reporting

​ **Pixalate 是用于检测流量质量的工具，该模块为增值服务，额外收费，需联系相关对接人开启相应的扫描功能才会有数据。**

![image-20240109144224176](https://static.rixengine.com/a/platform/help/manual/3.1-2-2.3.png)

#### 3.4 Human Reporting

​ **Human 可用于检测流量质量（常用于 CTV 流量），该模块为增值服务，额外收费，需联系相关对接人开启相应的扫描功能才会有数据。**

![image-20240109144254641](https://static.rixengine.com/a/platform/help/manual/3.1-2-2.4.png) -->

#### 3.3 Export Log

​ **数据异步下载模块，主要用于提升数据量级较大时的下载效率。**

![image-20240109144329618](https://static.rixengine.com/a/platform/help/manual/3.1-2.5-1.png)

![image-20240109144403203](https://static.rixengine.com/a/platform/help/manual/3.1-2.5-2.png)

### **4. Partner**

​ **同一个客户同时存在多个 Advertiser/Publisher 账号，可通过 Partner 统一管理该客户账号，并按 Partner 维度查看该客户的数据。**

- **创建 Partner**

  ![image-20240115105645355](https://static.rixengine.com/a/platform/help/manual/Partner-1.png)

- **Advertiser 指定 Partner**

  ![image-20240115110421733](https://static.rixengine.com/a/platform/help/manual/Partner-2.png)

![image-20240115110520411](https://static.rixengine.com/a/platform/help/manual/Partner-3.png)

- **Publisher 指定 Partner**

![image-20240115111015241](https://static.rixengine.com/a/platform/help/manual/Partner-4.png)

![image-20240115111117709](https://static.rixengine.com/a/platform/help/manual/Partner-5.png)

## **「供应方接入」**

### **1. Supply**

#### 1.1 创建 Publisher

​ 下游开发者接入，需在此模块创建 Publisher 相关信息，为后续的身份认证做准备。

**Step1** :登入进入模块

- **登陆进入模块步骤：**【Supply】-->【Publisher List】

- **登陆进入模块图示：**

  ![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.1-1.png)

**Step2** :新建 Publisher：

- **新建 Publisher 入口：【+ Create Publisher 】**

- **新建 Publisher 入口图示：**

![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.1-2.png)

- **新建 Publisher 步骤：**

  - **第一步** :

    填写 Publisher 名称，选择接入方式（后续技术对接方式）、媒体的类型以及接入媒体的关系是直客还是代理，最后选择设备的类型。

  - **第二步** :

    1. 选择是否自定义广告位（【Custom Placement】）

       - 若下游开发者平台的广告位等信息已完善，不需要再在 RixEngine 管理平台进行广告位管理，节省运营成本，则选择【Paused】状态
       - 若开发者平台接入 SaaS 平台，还需涉及到广告位的创建和运营管理，则选择【Active】状态

       **Tips** :需注意，若设置为 Active 状态，但未在 RixEngine 平台添加**App**和**广告位**，流量分发时会被 RixEgine 平台**拒收**）

       **注意** :当选择 SDK 方式接入时，自定义广告位不可以关闭（Paused），需在 RixEngine SaaS 平台上进行 App 的添加以及广告位的创建。

    2. Publisher TagID 的作用是在 RixEngine SaaS 平台上进行广告位数据指标的监控，在 DashBoard 页面进行 tagid 维度的数据展示。（前置条件：当 Custom Placement 处于 Paused 状态时显示此字段）

       **a. 使用场景** :若开发者未在 RixEngine SaaS 平台进行自定义广告位，又想要在 SaaS 平台上查看广告位数据指标，则选择【Active】状态，否则选【Paused】状态

  - **第三步** :利润配置

    - a. Profit：针对该下游流量获得的广告收入（上游结算的金额），平台预留设置 Profit Ratio（%）为平台自身的利润。

- **新建 Publisher 步骤图示：**![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.1-3.png)

#### 1.2 流量分发授权

​ 下游（Publisher）需对上游（Advertiser）进行流量分发授权。

**Step1** :对上游（Demand）授权

- **详情页面分别展示的是 Publisher 基本信息、对上游的授权以及应用和广告位的授权**

  1. 展示的是 Publisher 的基本信息

  2. Publisher 维度授权。针对 Publisher 维度进行上游的授权

  3. 应用层和广告位层维度授权。可分别对应用和广告位进行上游的授权（**注意** :应用层和广告位层的授权都必须是在 RixEngine SaaS 平台创建的）

- **注意** :流量授权优先级是 Ad Placement > App > Publisher，说明：

  1. 若广告位（Ad Placement）授权。则以广告位授权为主，对应广告位的流量仅分发给已授权方（Demand）
  2. 若广告位未授权。而对应 App 授权，则以 App 授权为主，所有未授权广告位流量仅分发给 App 的已授权方（Demand）
  3. 若广告位和 App 都未授权。则所有广告位流量仅分发给 Publisher 的已授权方（Demand）

  - **举例**：如下图所示![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.2-1.png)

* **对上游(Demand)授权图示：**

  **对上游授权入口图示:**![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.2-2.png)

  **对上游(需求方)授权图例:**![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.2-3.png)

#### 1.3 流量接收节点

​ 下游（Publisher）将流量分发到 SaaS 平台的流量接收节点（Endpoint）。

**Step1** :获取流量接收节点 Endpoint，进行相关技术配置，完成接入

- **第一步** :获取下游流量接收节点 Endpoint（系统自动生成）。

- **第二步：下游**完成相关节点配置，进行技术对接。（具体双方研发同学联调测试）

- **界面图示**：

  **查看流量接收节点入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.3-1.png)

  **图例** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.3-2-1.png)

#### 1.4 Publisher 账号管理

​ 该模块的目的是解决数据核对问题，总共有两种方案：1、提供上游账号进行登陆校对。2、提供 API Reporting 报表进行校对

**Step1**：进行下游特定 Publisher 账号密码的创建以及账号信息的邮件发送

- **下游账号密码的创建以及账号登录用户名信息的更改**：

  - **第一步** :首次进入页面时需创建密码，创建完成后可给特定下游看 Report Data 以及 Develoer 模块
    1. **New Password**：新密码
    2. **Repeat Password**：确认密码
    3. **Sent to email**：将登录地址和账号密码信息发送到邮箱（To Yourself：是否发送此平台登录账号所绑定的邮箱）
  - **第二步**：根据个人需求，可更改登录用户名的信息

    1. **User Name**：用户名默认前缀为「 Pub\_ 」开头
    2. **Sent to email**：同上

  - **第三步** :若想停止账号的登录和使用可点击页面的 Status 按钮

    （**备注** :登录之前需确认账号的 Status 是否为开启状态）

- **Publisher 账号管理图示**： **下游账号信息管理入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-1.png) **图例** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-2.png) **创建密码图例** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-3.png) **用户名信息图例** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-4.png)

**Step2**：Token 信息、账号 ID 信息以及 Report API 的获取

![image-20231017162435120](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-5.png)

![image-20231017162756699](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-6.png)

### **2. Developer**

​ 适用场景：开发者未能利用原有平台创建广告位，需使用 RixEngine SaaS 平台进行广告位基本信息创建和管理的情况

#### 2.1 添加应用

**Step1** :登入 App List 页以及选择对应的 Publisher

**对开发者的应用以及广告位的管理**

- 开发者需要在 RixEngine SaaS 平台自定义广告位等需求场景下使用（前置条件：需在 Publisher List 页面将字段【Custom Placement】设置为【Active】状态使用）

* **Step1 图示**：

  **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-1.png)

  **选择 Publisher（可选权限：仅限于【Custom Placement】状态为【Active】)**![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-2.png)

**Step2** :添加应用填写应用信息

- **添加应用对应用信息的添加以及编辑**
  1. **App Name** :必填，需输入 App 的真实名称
  2. **Bundle** :必填，需输入 App 真实的软件包名称
  3. **StoreURL** :必填，需输入 App 所在对应的应用商店 URL
  4. **Category** :选填，选择 App 所在的分类
  5. **Platform** :选择 APP 所对应的平台
  6. **Screen Orientation**：选择应用所适配的屏幕，竖版还是横版
- **Step 2 图示**：

  **添加应用入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-3.png)

  **填写基本的应用信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-4.png)

  **编辑应用信息入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-5.png)

#### 2.2 添加广告位

**Step1** :添加广告位信息

- **对应的广告位的添加以及编辑**

  以下是每一种广告形式（Ad Format）对应的字段说明：

1. **Banner：横幅广告。**
   - Ad Size：必填，选择广告位的尺寸
   - Bid Floor：必填，竞价底价的设置
   - Ad Position：选填，选择广告位的位置
2. **Medium Rectangle：一种中矩形广告，海外常用的一种广告形式。**
   - Ad Size：必填，选择广告位的尺寸
   - Bid Floor：必填，竞价底价的设置
   - Ad Position：选填，选择广告位的位置
3. **Interstitial：一种全屏显示的广告形式，通常在用户完成某些操作后出现，例如加载应用程序、完成游戏关卡等**
   - Ad Size：必填，选择广告位的尺寸
   - Support MIME Types：必填，若勾选 Video，则是视频类型，否则就是 Banner 广告类型
   - Allow Skip：是否允许跳过视频广告
   - Mute：是否静音播放广告
   - Bid Floor：必填，竞价底价的设置
   - Show Skip Button After：必填，多长时间后显示广告【跳过】按钮
   - Max Duration：广告视频的最大时长
4. **Rewarded Video：激励视频广告**
   - Ad Size：必填，选择广告位的尺寸
   - Bid Floor：必填，竞价底价的设置
   - Min Duration：广告视频的最小时长
   - Max Duration：广告视频的最大时长
5. **Native：原生广告**
   - Assets：必填，Native 广告内容的形式
   - Bid Floor：必填，竞价底价的设置

（**备注：竞价采取的是第一竞价模式 。 Bid Floor 为 0，则表示不设置底价**）

- **添加广告位图示**：

  **添加广告位入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-1.png) **填写广告位的基本信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-2.png) **编辑广告位入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-3.png) **编辑页面（广告形式不可修改）** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-4.png)

## **「需求方接入」**

### **1. Demand**

#### **1.1 创建 Advertiser**

​ 上游（Advertiser）接入的基础配置功能

**Step1** :创建 Advertiser（上游）步骤

- **对上游（Advertiser）基本信息的添加** :

  1. **Advertiser Name** :必填，输入上游（Advertiser）的名称
  2. **Integration Type** :对接协议适配器的类型，默认为 RTB, Online 即为其他协议，需技术同学线下沟通对接
  3. **Auction Type** :竞价类型，一价竞价和二价竞价（Second Price），默认采用一价（First Price）
  4. **Profit：平台从该上游（advertiser）获得的**广告收入（上游结算的金额），平台预留设置 Profit Ratio（%）为平台自身的利润
  5. **Profit Model** :默认为 net （与上游无收入分成）；
  6. **Pass Supply Chain**:如上游未要求上传 Supply Chain，默认为 Paused。
  7. **Mraid Traffic Filter**：默认为 Paused, Active 表示支持 Mraid 的流量才会发给该上游。
  8. **Pixalate IVT Block**:当上游对 Pixalate IVT 无要求时，默认为 Unlimited；当上游对 Pixalate IVT 有要求时，可设置为 Limited，并设置 Max Pixalate IVT，超过 Max Pixalate IVT 的量将不会送给该上游。**该功能需要开启 Pixalate 扫描。**
  9. **Human IVT Block**:当上游对 Human IVT 无要求时，默认为 Unlimited；当上游对 Human IVT 有要求时，可设置为 Limited，并设置 Max Human IVT，超过 Max Human IVT 的量将不会送给该上游。**该功能需要开启 Human 扫描。**
  10. **IFA Required**:当上游对 IDFA 字段内容无要求时，默认为 Paused；当上游要求 IDFA 字段为非空值时，可设置为 Active，IDFA 字段为空值的请求不会发给该上游。

- **创建上游图示**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-1.png) **填写 Advertiser（上游）的基本信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-2.png) **编辑上游（Advertiser）入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-3.png)

#### **1.2 流量接收节点配置**

​ 完成基础信息的填写后，需对种广告形式（Native/Banner/Video/Reward Video）的流量接收节点进行配置

**Step1** :针对下游流量接收节点设置

- **针对下游流量分发接收节点的相关设置，相关配置可以到具体美东（USE）站点的 和 新加坡（APSE）站点。**
  1. **Endpoint** :下游分发流量到上游的网络节点
  2. **Socket Timeout** :用于控制服务器之间的连接时间，当服务器在设定的时间内没有收到响应，连接就会中断
  3. **Gzip** :数据转发是否启用压缩算法，启用压缩可以降低服务器带宽成本
- **Step 1 截图**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.2-1.png) **添加节点的基本信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.2-2.png)

#### **1.3 流量接收授权**

​ 上游（Advertiser）针对某些下游（Publisher）的流量分发授权

**Step1** :对下游（开发者）流量的授权

- **说明**：上游（Advertiser）对下游流量的授权管理页面。即上游挑选符合要求的下游（Publisher）进行流量分发授权

- **对下游（开发者）流量的授权 图示**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.3-1.png) **授权开发者或者取消授权信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.3-2.png)

#### **1.4 流量筛选规则配置**

​ 为了更好的满足需求方（Advertiser）的需求，提供了针对流量进行筛选规则的配置

**Step1**: 对流量进行筛选规则配置

- **为了满足需求方（Advertiser）的需求，可对 Publisher 的流量进行筛选操作。**

- 说明：

  1. **Campaign Name** :筛选流量规则的名称
  2. **Publisher** :筛选流量对应的 Publisher 的黑白名单，可多选，默认白名单
     - 通用需求：选择的 Publisher 必须是在「Advertiser List -> More -> Authorization」目录下已授权的 Publisher，若没有选择 Publisher，则规则对已授权的所有 Publisher 生效
     - 差异化需求：为了更好的满足您的差异化需求，您可针对每一个 Publisher 分别配置流量筛选规则，也可针对多个 Publisher 配置相同的流量筛选规则
  3. **Country** :筛选流量对应的国家的黑白名单，可多选，默认白名单
  4. **Category** :筛选流量对应的 App 类别的黑白名单，可多选，默认白名单
  5. **Network**：筛选流量对应设备网络的黑白名单，可多选，默认白名单
  6. **Delivery Time Slot**：自定义交易时间段
  7. **OS** :筛选流量对应的设备操作系统
  8. **Device** :筛选流量的设备类型
  9. **Inventory** :筛选媒体的库存来源于哪里
  10. **Ad Format** :筛选流量的广告形式，可多选
  11. **Ad Size** ：筛选流量对应的广告尺寸的黑白名单，可多选，默认白名单
  12. **Bundle(Domain)**：添加筛选 Bundle(Domain)的黑白名单，默认白名单
  13. **Maximum Floor Price** :筛选流量对应广告位的底价，若底价超过配置值，则过滤掉流量
  14. **Server Region** :选择流量分发接收服务器的节点，APAC（亚太地区）、USE（北美地区）
      - 选择 Default 会根据系统配置的相同区域节点进行转发， 即流量分发为 APAC 区域流量分发上游 APAC 节点，USE 区救域流量分发上游 USE 节点。

  （**备注：若以上无配置字段，则不对流量分发进行限制，否则按已配置规则进行流量过滤 . 但是每个上游必须有一条 pretargetting 配置内容，否则无法对该上游进行流量转发。**）

  <font color=red>**流量筛选规则配置注意事项：**</font>

  Pretargeting 支持配置多条， 流量只要满足其中任何一条 Pretargeting 即可以分发给该 Advertiser。

- **流量筛选规则配置图示**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.4-1.png) **创建流量筛选规则配置入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.4-2.png) **添加规则配置信息** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.4-3.png) **配置完成点击「Save」按钮进行保存**。

#### 1.5 Advertiser 账号管理

​ 该模块的目的是解决数据核对问题，总共有两种方案：1、提供上游账号进行登陆校对。2、提供 API Reporting 报表进行校对。

**Step1**：进行上游特定 Advertiser 账号密码的创建以及账号信息的邮件发送

- 上游账号密码的创建以及账号登录用户名信息的更改
- **第一步** :首次进入页面时需创建密码，创建完成后可给特定上游看 Data Report
  1. **New Password**：新密码
  2. **Repeat Password**：确认密码
  3. **Sent to email**：将登录地址和账号密码信息发送到邮箱（To Yourself：是否发送此平台登录账号所绑定的邮箱）
- **第二步**：根据个人需求，可更改登录用户名的信息

  1. **User Name**：用户名默认前缀为「 Adv\_ 」开头
  2. **Sent to email**：同上 **第三步** :若想停止账号的登录和使用可点击页面的 Status 按钮

  （**备注** :登录之前需确认账号的 Status 是否为开启状态）

- **截图**：

**上游账号信息管理入口** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-1.png)

**图例** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-2.png)

**创建密码图例** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-3.png)

**用户名信息图例** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-4.png)

**Step2**：Token 信息、账号 ID 信息以及 Report API 的获取

![image-20231018112456412](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-5.png)

![image-20231017163210875](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-6.png)

## **「运营策略配置」**

### **1. Strategy Config**

​ 关于上下游接入相关策略的配置，例如上下游收入、展示量的限制以及黑白名单的添加。

#### **1.1 BL&WL**

​ 为了更好的满足您的需求，我们提供 Advertiser 以及对应 Publisher 多维度的黑白名单服务。

- **操作步骤：Strategy Config —> BL&WL —>「+Create BL&WL」**，可进行需求方（Advertiser）针对 Publisher 多维度的黑白名单设置，维度包括了 Bundle、Country 以及 Ad Format

- **添加配置信息** :

  1. **Publisher** :选择想要加入黑白名单的 Publisher

  2. **Advertiser** :选择想要加入黑白名单的 Advertiser

  3. **Type** :根据不同维度的黑白名单进行选择

  4. **Content**：根据选择的 Type 进行任务

     - 若选择的是 Bundle，则需添加对应 Bundle Name

     - 其余都可以下拉框选择对应枚举值，可多选

- **添加配置信息图示**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-1.png) **添加黑白名单配置信息页面** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-2.png) **编辑页面入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-3.png)

- **注意事项** :

白名单配置之后只允许配置白名单的内容进行转发。

![image-20230921141037747](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-4.png)

#### **1.2 PMP**

为了提高广告投放的**控制力、精准度**，并保障品牌安全与**高质量展示环境**，PMP 交易模式是程序化广告中的理想选择。它通过**私有化购买**和**优先交易权**，为广告主提供更可靠的广告展示，同时避免了公开竞价中存在的虚假流量和不稳定性，确保广告资源的优化利用。

- **Create PMP Inventory**：在 PMP 交易市场中创建专属的流量资源，为特定广告主提供定制化的展示机会。通过这种私有化的方式，广告主不仅能够确保获取到优质的媒体资源，还可以预先锁定展示位置和价格，进一步优化投放效果。

![image-20241115110242075](https://static.rixengine.com/a/platform/help/manual/pmp3.png?t=20241119)

- **Create PMP Deal** :创建 PMP 交易

![image-20241115110242075](https://static.rixengine.com/a/platform/help/manual/pmp4.png?t=20241119)

#### **1.3 Cap**

​ 为了更好的满足您的需求，您可针对 Advertiser 以及对应的 Publisher 进行日收入限制或者日广告展示量的限制。

- **操作步骤 :Strategy Config --> Cap**，可针对 Advertiser 与对应的 Publisher 进行日收入和日展示量的限制

- **Create Cap** :
  1. **Daily Revenue Cap** :与特定 Advertiser 对应 Publisher 的日收入的最高上限
- **Create Cap 图示**： **入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-1.png) **创建 Cap 配置相关页面** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-2.png) **编辑 Cap 配置入口** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-3.png)

#### **1.4 Profit**

​ 为了能让您收益可控，可以根据需求进行相关利润率配置，我们会根据您配置的利用动态模型算法进行流量分配，提前保证了您的收益。

- **利润相关配置 :**

- **Profit 配置位置：Strategy Config-->Profit**

- **说明**：

  1. **Advertiser Profit :可单独添加与某个下游（Publisher）的利润率映射关系**

     - 使用场景：若上游（Advertiser）对特定下游（Publisher）有差异化的利润需求，即可配置。

     - 利润率优先级关系从高到低：adv+pub > adv > pub。

  1. **Publisher Profit：可单独添加与某个上游（Advertiser）的利润率映射关系**
     - 利润率优先级关系从高到低：adv+pub > adv > pub。
     - 使用场景：若开发者（Publisher）对特定上游（Advertiser）有差异化的利润率需求，即可配置。
  1. **Bundle Profit :可单独添加与某个（下游的）Bundle 的利润率映射关系**
     - 使用场景：若上游（Advertiser）对特定（下游的）Bundle 有差异化的利润需求，即可配置。
     - 利润率优先级关系从高到低： adv+pub+bundle > adv + bundle > adv+pub > adv > pub。

- **优先级举例说明**

  **维度越细优先级越高**：上游 Test-adv-0(32064)对所有下游设定的利润率为 30%，对下游 Test-pup-0(37475)设定利润率为 33%，对所有下游从 Bundle：123456 送来的量设定利润率为 35%，对从下游 Test-pup-0(37475)从 Bundle：123456 送来的量设定利润率为 40%，那么上游 Test-adv-0(32064)对下游 Test-pup-0(37475)从 Bundle：123456 送来的量的利润率即为 40%，对下游 Test-pup-0(37475)从其他 Bundle（除 123456）送来的量的利润率即为 33%，上游 Test-adv-0(32064)对其他下游「除 Test-pup-0(37475)」从 Bundle：123456 送来的量的利润率即为 35%，上游 Test-adv-0(32064)对其他下游「除 Test-pup-0(37475)」从其他 Bundle（除 123456）送来的量的利润率即为 30%。

  ![image-20241115104548768](https://static.rixengine.com/a/platform/help/manual/profit1.png)

  ![image-20241115104635108](https://static.rixengine.com/a/platform/help/manual/profit2.png)

  **一一对应的利润率的优先级高于非一一对应**：上游 test-adv(30604)对所有下游设定的利润率为 33%，对下游 test-pub(36048)设定利润率为 50%，那么上游 test-adv(30604)对下游 test-pub(36048)的利润率即为 50%，其他未被上游 test-adv(30604)设定一一对应利润率的下游利润率为 33%；同时，下游 test-pub(36048)对上游 test-adv(30604)的利润率也为 50%，其他未被下游 test-pub(36048)设定一一对应利润率的上游（**假设该上游未设置利润率**）利润率为 35%。

  ![image-20241115111552649](https://static.rixengine.com/a/platform/help/manual/profit3.png)

  ![image-20241115111605906](https://static.rixengine.com/a/platform/help/manual/profit4.png)

  **上游的 profit 配置优先于下游的 profit 配置**：上游 test1-adv(30608)对应所有下游设定的利润率为 30%，且没有对下游 test1-pub(36053)设特定利润率。同时，下游 test1-pub(36053)对应所有上游设定的利润率为 50%，且没有对上游 test1-adv(30608)设特定利润率，则上游 test1-adv(30608)与下游 test1-pub(36053)的利润率以上游 test1-adv(30608)设定的利润率为准，取 30%。

  ![image-20231219110258039](https://static.rixengine.com/a/platform/help/manual/profit5.png)

  ![image-20231219110316593](https://static.rixengine.com/a/platform/help/manual/profit6.png)

- **Profit 配置操作截图**

  **添加上游与某个下游（Publisher）的利润率映射关系入口** :![image-20241115112606693](https://static.rixengine.com/a/platform/help/manual/profit7.png)

  **添加下游与某个上游（Advertiser）的利润率映射关系入口** :![image-20241115112310654](https://static.rixengine.com/a/platform/help/manual/profit8.png)

  **添加上游与某个（下游的）Bundle 的利润率映射关系入口** :![image-20241115113711136](https://static.rixengine.com/a/platform/help/manual/profit9.png)

  **添加利润率信息：**

  1. **添加 Advertiser Profit 信息**![image-20241115112828362](https://static.rixengine.com/a/platform/help/manual/profit10.png)
  2. **添加 Publisher Profit 信息**![image-20241115112931036](https://static.rixengine.com/a/platform/help/manual/profit11.png)
  3. **添加 Bundle Profit 信息**![image-20241115113152995](https://static.rixengine.com/a/platform/help/manual/profit12.png)

      **添加后的图例** :![image-20241115113356808](https://static.rixengine.com/a/platform/help/manual/profit13.png)

#### **1.5 QPS**

为了让您的机器成本、带宽成本可控，我们支持动态调整 QPS，为您减少 IDC 成本。

**注意：**

- QPS 数值设置需要结合 eCPR 和分区域 QPS (Real)来调整：
- 在没有特殊要求的前提下，上游 QPS 默认为 100/Region，下游 QPS 默认为 1000/Region。
- eCPR 在 1 及以下，QPS 配置不高于分区域 QPS (Real)。
- eCPR 在 1-2 之间，QPS 配置为分区域 QPS (Real)的 1.5 倍左右。
- eCPR 在 2 及以上，QPS 配置为分区域 QPS (Real)的 2 倍。
- 对于 eCPR 特别大的下游，QPS 配置需要适当放宽，以防止下游突然加量。

**QPS 配置操作截图** ：

![image-20231130152610621](https://static.rixengine.com/a/platform/help/manual/3.4-1.4-1.png)

![image-20231130151106438](https://static.rixengine.com/a/platform/help/manual/3.4-1.4-2.png)

#### **1.6 Creative**

该模块是为了满足 Publisher 对 Advertiser 不合规素材的屏蔽需求，支持按「Adomain」/「Crid」维度屏蔽相关素材。

![image-20240112113452975](https://static.rixengine.com/a/platform/help/manual/3.4-1.5-1.png)

![image-20240112113524490](https://static.rixengine.com/a/platform/help/manual/3.4-1.5-2.png)

## **「流量授权渠道查询」**

### 1. App-ads.txt

​ 该模块是为了快速查询广告流量资源与授权销售渠道的关系， 让广告主可以更安全放心地进行广告交易，减少从未经授权的流量方购买广告的问题。主要是查询指定 Bundle 列表是否添加了 App-ads.txt 的 lines。lines 支持输入“rixengine.com”或“rixengine.com,36069, RESELLER”格式。

![image-20240109144435654](https://static.rixengine.com/a/platform/help/manual/App-ads.png)

## **「权限配置」**

### 1. My Profile

​ 为了确保超级管理员对系统的角色控制和用户管理，可通过权限配置模块实现。

- **权限配置入口 :**

![image-20230918143444002](https://static.rixengine.com/a/platform/help/manual/3.5-1-1.png)

#### **1.1 Role Permission**

​ 该模块主要是实现创建角色，以及针对不同的角色配置不同的具体权限，系统默认配置了 Administator 角色，管理员可根据需要创建角色。

![image-20230918143504682](https://static.rixengine.com/a/platform/help/manual/3.5-1.1-1.png)

#### **1.2 Manage Users**

​ 该模块主要是实现新用户及已有用户的管理。

- **管理已有用户**

  ![image-20230918143521769](https://static.rixengine.com/a/platform/help/manual/3.5-1.2-1.png)

- **新增用户**

  ![image-20230918143557813](https://static.rixengine.com/a/platform/help/manual/3.5-1.2-2.png)
