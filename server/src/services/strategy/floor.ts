/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-23 17:09:00
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-20 16:36:58
 * @Description:
 */
import { FloorAPI } from '@/types/floor';
import { floorModel } from '@/models';
import { UserType } from '@/constants';

class FloorService implements FloorAPI.FloorService {
  async getFloorList(
    tnt_id: number,
    cur_time_zone: string,
    plm_id?: number,
    type?: number
  ): Promise<any[]> {
    return floorModel.getFloorList(tnt_id, cur_time_zone, plm_id, type);
  }
  async getAllSupplyPlacement(tnt_id: number) {
    return await floorModel.getAllSupplyPlacement(tnt_id);
  }
  async addFloor(params: any): Promise<boolean> {
    return await floorModel.addFloor(params);
  }
  async isExistedFloor(
    params: FloorAPI.ExistedFloor
  ): Promise<{ count: number }> {
    return await floorModel.isExistedFloor(params);
  }
  async updateFloor(params: FloorAPI.UpdatFloorParams): Promise<boolean> {
    return await floorModel.updateFloor(params);
  }
  async deleteFloor(params: FloorAPI.UpdatFloorParams): Promise<boolean> {
    return await floorModel.deleteFloor(params);
  }
}

export default new FloorService();
