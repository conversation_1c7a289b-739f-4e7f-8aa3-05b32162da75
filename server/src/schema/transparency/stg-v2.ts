import Joi from 'joi';

const CommonStgSchema = {
  developer_website_domain: Joi.string(),
  bundle: Joi.string(),
  tnt_id: Joi.number().integer().required(),
  publisher_id: Joi.string().required()
};

export const AddStgSchema = Joi.object({
  ...CommonStgSchema
});

export const UpdateStgSchema = Joi.object({
  ...CommonStgSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
