.dates-container {
  position: relative;
  min-height: 32px;
  .dates-picker {
    position: absolute;
  }
  .hide-picker {
    z-index: -1;
  }
  .select-container {
    position: absolute;
  }
}

:global {
  .ant-select.ant-select-in-form-item.ant-select-show-search:not(.ant-select-customize-input) {
    .ant-select-selector {
      .ant-select-selection-overflow {
        cursor: pointer !important;
      }
    }
  }
}

.site-calendar-customize-header-wrapper {
  width: 300px;
  border: 1px solid #f0f0f0;
  border-radius: 2px;
}
.tag-container {
  margin-right: 3px;
  background-color: #ebeff0;
  color: var(--text-color);
  border-radius: 6px;
  height: 24px;
  line-height: 22px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  display: flex;
  box-sizing: border-box;
  align-items: center;
  font-size: 14px;
  svg {
    color: #00000073;
  }
}

.dates-picker-container {
  :global {
    .ant-picker-header {
      .ant-picker-header-prev-btn,
      .ant-picker-header-super-prev-btn,
      .ant-picker-header-next-btn,
      .ant-picker-header-super-next-btn {
        color: #5e6466;
      }
      .ant-picker-header-view {
        color: var(--text-color);
      }
    }
    .ant-picker-cell-today {
      .ant-picker-cell-inner {
        &:before {
          cursor: pointer;
          border: none;
          top: 24px;
          left: 50%;
          width: 4px;
          height: 4px;
          border-radius: 4px;
          background: var(--primary-1);
          transform: translateX(-50%);
        }
      }
    }
    .ant-picker-cell.ant-picker-cell-in-view {
      .selected-date {
        cursor: pointer;
        border: none;
        border-radius: 4px;
        background: var(--primary-color);
        color: #fff;
      }
      .no-selected-date {
        cursor: pointer;
        border: none;
        border-radius: 4px;
        background: #fff;
        color: var(--text-color);
      }
      .ant-picker-cell-inner {
        border-radius: 6px;
      }
    }

    .ant-picker-content {
      th {
        color: #8d9799;
      }
      tr {
        height: 36px;
      }
      .ant-picker-cell-disabled {
        color: #d7dadb;
        &::before {
          height: 32px;
        }
        .no-selected-date {
          color: #d7dadb;
          background: #f5f7f8;
          cursor: not-allowed;
          border: none;
          border-radius: 4px;
        }
      }
    }
  }
}
