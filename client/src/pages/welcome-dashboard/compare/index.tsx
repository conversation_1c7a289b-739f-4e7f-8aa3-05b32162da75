/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-28 20:02:09
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 18:08:19
 * @Description:
 */

import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { SorterResult } from 'antd/lib/table/interface';
import { useModel } from 'umi';
import moment from 'moment-timezone';

import { DateType } from '@/constants/data-report';
import { TopBarSearchItem, SearchResultItem } from '@/components/TopBar';

import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';
import BrokenLine from '../components/BrokenLIne';
import NormalSelect from '@/components/Select/NormalSelect';

import { Dimensions, WelcomeDashboardColumns, MetricsOptions, WelcomeDashboardSearchOption } from '@/constants/welcome';
import { fetchData } from '@/utils';
import { getWelcomeDashboardList } from '@/services/common';

const Page: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandListWithTesting');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const [dataSource, setDataSource] = useState<API.BackResult<DashboardAPI.DashboardListItem>>({ data: [], total: 0 });
  const [sortedInfo, setSortedInfo] = useState<SorterResult<DashboardAPI.DashboardListItem>>({
    columnKey: 'date',
    order: 'descend'
  });
  const [currentMetric, setCurrentMetric] = useState<string>(MetricsOptions[0].value);
  const [searchOptions, setSearchOptions] = useState(WelcomeDashboardSearchOption);
  const [appearAnimation, setAppearAnimation] = useState<boolean>(false);
  useEffect(() => {
    getTableData();
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = [...WelcomeDashboardSearchOption];
    if (Array.isArray(demandList) && Array.isArray(supplyList)) {
      const dOptions: any[] = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });

      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }

    const datesIndex = options.findIndex(item => item.key === 'dates');
    if (datesIndex !== -1) {
      options[datesIndex].value = [moment().subtract(1, 'days').format('YYYYMMDD'), moment().format('YYYYMMDD')];
      const tmp = {
        dates: [moment().subtract(1, 'days').format('YYYYMMDD'), moment().format('YYYYMMDD')]
      };
      options[datesIndex].defaultValue = tmp.dates;
    }
    setSearchOptions(options);
  };

  const getTableData = (options?: any) => {
    let params = handleGetSearchParams(0, 72, searchOptions);
    if (options) {
      params = options;
    }
    fetchData({
      request: getWelcomeDashboardList,
      setLoading,
      params,
      onSuccess: (res: any) => {
        if (res) {
          const data = res?.data || [];
          // res.total === -1 ? (res.total = totalCount) : setTotalCount(res.total);
          res.total === -1 ? (res.total = dataSource.total) : res.total;
          const result = { data: data, total: res.total };
          setDataSource(result);
          setAppearAnimation(true);
        }
      }
    });
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key
    };
  };

  const handleSortChange = (page: number, size: number, search: SearchResultItem[], sorter: any) => {
    if (!dataSource || !Array.isArray(dataSource.data)) return;
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });
    const sortData = dataSource.data.toSorted((a: any, b: any) => {
      if (field === 'date') {
        return order === 'descend'
          ? moment(b['date']).valueOf() - moment(a['date']).valueOf()
          : moment(a['date']).valueOf() - moment(b['date']).valueOf();
      }
      return order === 'descend' ? +b[field] - +a[field] : +a[field] - +b[field];
    });
    setDataSource({ data: sortData, total: dataSource.total });
    setAppearAnimation(false);
  };
  const handleSearchChange = (start: number, end: number, val: SearchResultItem[], isPaing?: boolean) => {
    if (isPaing) {
      const data = dataSource.data.slice(start, end);
      setDataSource({ data: data, total: dataSource?.total || 0 });
      return;
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData(params);
  };
  const handleGetSearchParams = (start: number, end: number, val: SearchResultItem[]) => {
    const tmp = val.filter(
      item => (Array.isArray(item.value) && item.value.length > 0) || (!Array.isArray(item.value) && item.value)
    );
    const { order, order_key } = getOrderAndKey();
    const endDate = moment().format('YYYYMMDD');
    const startDate = moment().subtract(1, 'days').format('YYYYMMDD');
    const dates = [startDate, endDate];
    const params: any = {
      split_time: DateType.Hour,
      start: 0,
      end: 72,
      dates,
      columns: [...Dimensions],
      metrics: MetricsOptions.map(item => item.value),
      order_key,
      order
    };

    tmp.forEach(item => {
      params[item.key] = item.value;
    });

    return params;
  };
  const handleSelect = (val: string) => {
    setCurrentMetric(val);
    setAppearAnimation(true);
  };

  return (
    <PageContainer >
      <div className={styles['dashboard-container']}>
        <BackTable<DashboardAPI.DashboardListItem>
          pageTitle="Dashboard"
          searchOptions={searchOptions}
          columns={[]}
          rowKey={'id'}
          handleSearch={handleSearchChange}
          tableData={[] as any}
          loading={loading}
          getTableData={() => {}}
          defaultParams={{}}
          isExportAll={false}
          handleDownloadAll={() => {}}
          showTable={false}
          isFold={true}
          defaultFold={false}
        />
        <div className={styles['borken-line-container']}>
          <div className={styles['select-container']}>
            <NormalSelect
              options={MetricsOptions}
              onSelect={handleSelect}
              defaultValue={MetricsOptions[0].value}
              style={{ minWidth: '200px', margin: '0 16px 8px auto' }}
            ></NormalSelect>
          </div>
          <BrokenLine
            dataSource={dataSource}
            loading={loading}
            metric={currentMetric}
            appearAnimation={appearAnimation}
          ></BrokenLine>
        </div>
        <BackTable<DashboardAPI.DashboardListItem>
          pageTitle="Dashboard"
          searchOptions={[]}
          columns={WelcomeDashboardColumns}
          rowKey={'id'}
          scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
          handleSearch={handleSearchChange}
          tableData={dataSource}
          loading={loading}
          getTableData={getTableData}
          defaultParams={{}}
          isExportAll={false}
          handleDownloadAll={() => {}}
          sortDirections={['descend', 'ascend', 'descend']}
          handleSortChange={handleSortChange}
          showTopBar={false}
        />
      </div>
    </PageContainer>
  );
};

export default Page;
