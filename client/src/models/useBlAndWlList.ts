/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-19 16:17:08
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:54:04
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getBlAndWlList } from '@/services/api';

export default function useBlAndWlList() {
  const { data, run, loading, error } = useCustomRequest(getBlAndWlList, {
    cacheKey: 'wl-bl-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
