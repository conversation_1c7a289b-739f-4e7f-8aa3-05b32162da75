/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-05 10:19:29
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-05 10:40:48
 * @Description:
 */

import Router from 'koa-router';
import { Context, Next } from 'koa';
import { setInterfaceTimeOut } from '@/utils/timeout';
import { parseDateParams } from '@/utils/report';
import { abTestReportCtrl } from '@/controllers/data-report/abtest-report';
const router = new Router({ prefix: '/abtest-report' });
const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(60 * 60 * 1000);
  await next();
};
const routers = router.post(
  '/getABTestReportList',
  setTimeOutMiddler,
  setInterfaceTimeOut(15 * 60 * 1000),
  parseDateParams,
  abTestReportCtrl.getABTestReportList
);
export default routers;
