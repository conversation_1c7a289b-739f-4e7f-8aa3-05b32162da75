/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:08:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-05 14:44:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-10 16:54:18
 * @Description:
 */
import { request } from '@umijs/max';

export async function getDashboardList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.DashboardListItem>>('/api/dashboard/getDashboardList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getAdvReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.DashboardListItem>>('/api/dashboard/getAdvReportList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getPubReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.DashboardListItem>>('/api/dashboard/getPubReportList', {
    data: body || {},
    ...(options || {})
  });
}

// 下载全部报表
export async function downloadDashboardList(body?: any, options?: { [key: string]: any }) {
  return request<any>('/api/dashboard/downloadDashboardReport', {
    data: body || {},
    ...(options || {})
  });
}

// 下载全部报表
export async function downloadAdvList(body?: any, options?: { [key: string]: any }) {
  return request<any>('/api/dashboard/downloadAdvReport', {
    data: body || {},
    ...(options || {})
  });
}

// 下载全部报表
export async function downloadPubList(body?: any, options?: { [key: string]: any }) {
  return request<any>('/api/dashboard/downloadPubReport', {
    data: body || {},
    ...(options || {})
  });
}

export async function getAdvertiserBillingList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.BillingListItem>>('/api/billing/getAdvertiserBillingList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getPublisherBillingList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.BillingListItem>>('/api/billing/getPublisherBillingList', {
    data: body || {},
    ...(options || {})
  });
}

export async function downloadAdvertiserBillingList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.BillingListItem>>('/api/billing/downloadAdvertiserBillingList', {
    data: body || {},
    ...(options || {})
  });
}

export async function downloadPublisherBillingList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.BillingListItem>>('/api/billing/downloadPublisherBillingList', {
    data: body || {},
    ...(options || {})
  });
}
export async function getConfigQps(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<StrategyAPI.QpsListItem>>('/api/dashboard/getConfigQps', {
    data: body || {},
    ...(options || {})
  });
}
export async function getPixalateReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.PixalateReportItem>>('/api/dashboard/getPixalateReport', {
    data: body || {},
    ...(options || {})
  });
}

export async function downloadPixalateReport(body?: any) {
  return request<any>('/api/dashboard/downloadPixalateReport', {
    data: body || {}
  });
}
export async function getHumanReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.HumanReportItem>>('/api/dashboard/getHumanReport', {
    data: body || {},
    ...(options || {})
  });
}

export async function downloadHumanReport(body?: any) {
  return request<any>('/api/dashboard/downloadHumanReport', {
    data: body || {}
  });
}

export async function getExportedReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.ExportedReportItem>>(
    '/api/exported-report/getExportedReportList',
    {
      data: body || {},
      ...(options || {})
    }
  );
}

export async function getExportedTaskStatus(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<DashboardAPI.ExportedReportItem>>(
    '/api/exported-report/getExportedTaskStatus',
    {
      data: body || {},
      ...(options || {})
    }
  );
}

export async function getABTestReportList(body?: any, options?: { [key: string]: any }) {
  return request<API.BackResponseResult<StrategyAPI.ABTestListItem>>('/api/abtest-report/getABTestReportList', {
    data: body || {},
    ...(options || {})
  });
}
