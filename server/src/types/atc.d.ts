/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-07 11:31:26
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-07 14:35:55
 * @Description:
 */
import { Context } from 'koa';
declare namespace AtcAPI {
  type AtcListItem = {
    id: number;
    model: number;
    op_id: number;
    create_time: string;
    update_time: string;
  };

  type updateAtcParams = {
    id: number;
    model: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type IsExistParams = {
    model: number;
    id: number;
    tnt_id: number;
  };

  interface Atc {
    updateAtc(params: updateAtcParams): Promise<boolean>;
  }

  interface AtcCtrlInterface {
    updateAtc(ctx: Context): void;
  }
}
