/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-10 15:24:14
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-04-07 21:14:52
 * @Description:
 */

import { request } from 'umi';

export async function getAllMenu(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/menu/getAllMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/addRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function deleteRole(body: { ids: (number | string)[] }, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/deleteRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/updateRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
export async function editRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/editRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
export async function getAllRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/getAllRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addOneUser(body: { account_name: string; password: string }, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/addOneUser', {
    data: body,
    ...(options || {})
  });
}

export async function editUser(body: UserAPI.EditUserParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/editUser', {
    data: body,
    ...(options || {})
  });
}

export async function deleteUser(body: { user_id: number }, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/deleteUser', {
    data: body,
    ...(options || {})
  });
}

export async function getUserList(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/getUserList', {
    data: body || {},
    ...(options || {})
  });
}
