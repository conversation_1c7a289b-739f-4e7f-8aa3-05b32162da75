/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-22 10:38:32
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-23 16:57:34
 * @Description:
 */

import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';

import { LangType } from '@/constants/base/lang';
import { request, useModel } from '@umijs/max';
import { Anchor } from 'antd';
import 'github-markdown-css/github-markdown-light.css';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
import { marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import { markedSmartypants } from 'marked-smartypants';
import { LinkItem, getLinkItem } from '../../utils';
import LangDropdown from '../LangDropdown';
import './index.global.less';
import { iframeManager } from '@/utils/iframe-manager';
import classNames from 'classnames';

const { Link } = Anchor;

function renderLink(items: LinkItem[]) {
  // 递归 render
  return items.map(item => (
    <Link key={item.anchor} href={`#${item.anchor}`} title={item.text}>
      {item.children && renderLink(item.children)}
    </Link>
  ));
}

let anchors: string[] = [];
let linkItems: LinkItem[] = [];

marked.use(
  markedSmartypants(),
  markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code, lang) {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, { language }).value;
    }
  })
);
const renderer = new marked.Renderer();
renderer.heading = function (text, level, raw) {
  // 匹配标签正则
  const regex = /(<([^>]+)>)/gi;
  const str = text.replace(regex, '').trim();
  const anchor = getLinkItem(str, level, anchors, linkItems);

  return `<h${level} id="${anchor}" ${level === 1 && 'class="cus-h1"'}>${text}</h${level}>\n`;
};
renderer.list = function (body, ordered, start) {
  const isOrder = ordered ? 'ol' : 'ul';
  return `<${isOrder} class="cus-list">${body}</${isOrder}>`;
};
marked.setOptions({
  renderer,
  gfm: true, // 启动Github样式的Markdown
  pedantic: false, // 只解析符合markdown.pl定义的，不修正markdown的错误
  // sanitize: false, // 原始输出，忽略HTML标签
  breaks: true // 支持Github换行符，必须打开gfm选项
  // smartLists: true // 优化列表输出
  // smartypants: true
});
type MdPathOrEnMdPath = { mdPath: string; enMdPath?: never } | { mdPath?: never; enMdPath: string };
type MdPageProps = MdPathOrEnMdPath & {
  isApi?: boolean;
};

const MdPage: React.FC<MdPageProps> = ({ mdPath, isApi, enMdPath = '' }) => {
  const { initialState } = useModel('@@initialState');
  const [content, setContent] = useState('');
  const [lang, setLang] = useState('');
  const [mdHtml, setMdHtml] = useState<{ [key: string]: string }>({ en: '', zh: '' });
  const [anchor, setAnchor] = useState<JSX.Element[]>();
  const [disabled, setDisabled] = useState(false);
  const shouldHideTopContainer = useMemo(() => {
    return iframeManager.isToponTenant();
  }, [location.href]);

  useEffect(() => {
    if (!linkItems.length) return;
    const links = renderLink(linkItems);
    setAnchor(links);
  }, [content]);

  useEffect(() => {
    if (!lang) return;
    const hostname = initialState?.currentUser?.cs_domain || location.hostname;
    const host_prefix = initialState?.currentUser?.host_prefix || hostname.split('.')[0] || '{host}';

    let host = `${host_prefix}.`;
    let domain = 'rixengine.com';
    const pv_domain = initialState?.currentUser?.pv_domain;
    if (pv_domain) {
      host = '';
      domain = pv_domain;
    } else if (!hostname.includes('console.rixengine.com')) {
      host = '';
      domain = hostname.replace('console.', '');
    }
    // 重置数据
    anchors = [];
    linkItems = [];
    const path = lang === LangType.en ? enMdPath : mdPath || enMdPath;
    if (mdHtml[lang]) {
      let html = marked(mdHtml[lang]);
      html = isApi ? html.replaceAll(`{host}\.`, host).replaceAll(`{domain}`, domain) : html;
      setContent(html);
      return;
    }
    request(path!).then(md => {
      setMdHtml({ ...mdHtml, [lang]: md });
      let html = marked(md);
      html = isApi ? html.replaceAll(`{host}\.`, host).replaceAll(`{domain}`, domain) : html;
      setContent(html);
    });
  }, [lang]);

  const handleLangChange = (value: string) => {
    if (!enMdPath && value === LangType.en) {
      setDisabled(true);
    }
    if (!mdPath && value === LangType.zh) {
      setDisabled(true);
    }
    setLang(value);
  };
  return (
    <div className={styles['container']}>
      <div
        className={classNames(styles['top-container'], { [styles['top-container-hidden']]: shouldHideTopContainer })}
      >
        <LangDropdown onLangChange={handleLangChange} disabled={disabled}></LangDropdown>
      </div>
      <div className={styles['get-started']}>
        <div className={`${styles['nav-container']} `}>
          <Anchor
            style={{
              padding: '24px 24px 24px 6px',
              maxHeight: '100%'
            }}
            showInkInFixed
            affix
            getContainer={() => {
              return document.getElementById('anchor-container') || window;
            }}
          >
            {anchor}
          </Anchor>
        </div>
        <div className={`${styles['article-container']} `} id="anchor-container">
          <div dangerouslySetInnerHTML={{ __html: content }} className="markdown-body"></div>
        </div>
      </div>
    </div>
  );
};

export default MdPage;
