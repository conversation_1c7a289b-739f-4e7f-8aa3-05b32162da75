/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:36:48
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-13 14:41:54
 * @Description:
 */
import { Context } from 'koa';
declare namespace CapAPI {
  type CapListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
    seller_name: string;
    buyer_name: string;
    account_name: string;
    account_status: number;
    op_id: number;
    create_time: string;
    update_time: string;
  };
  type AddCapParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    op_id: number;
    tnt_id: number;
    bundle?: string;
    sys_update_time?: string;
  };
  type updateCapParams = {
    id: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
    op_id: number;
    bundle?: string;
    tnt_id: number;
    type: number
  };

  type IsExistParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    id: number;
    tnt_id: number;
    bundle?: string;
  };

  interface Cap {
    getCapList(tnt_id: number, cur_time_zone: string): Promise<CapListItem[]>;
    addCap(params: AddCapParams): Promise<boolean>;
    updateCap(params: updateCapParams): Promise<boolean>;
    isCapExists(params: IsExistParams): Promise<any[]>;
  }

  interface CapCtrlInterface {
    getCapList(ctx: Context): void;
    addCap(ctx: Context): void;
    updateCap(ctx: Context): void;
  }
}
