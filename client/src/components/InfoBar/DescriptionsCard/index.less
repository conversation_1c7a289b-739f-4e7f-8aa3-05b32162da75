.descriptions-card-collapse {
  :global {
    .ant-collapse-item {
      .ant-collapse-header {
        padding-left: 0px;
        padding-bottom: 3px;
        padding-top: 5px;
      }
      .ant-collapse-content {
        .ant-collapse-content-box {
          padding-top: 0px;
          padding-bottom: 0px;
          > .ant-descriptions {
            border-top: 1px solid #e9e9e9;
            padding-top: 8px;
          }
        }
      }
      .ant-descriptions-item {
        .ant-descriptions-item-container {
          width: 100%;
        }
        .ant-descriptions-item-content {
          width: 100%;
        }
      }
    }
  }

  .collapse-line-header {
    display: flex;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline;
}
