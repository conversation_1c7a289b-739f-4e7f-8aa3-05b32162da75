/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-04 15:02:49
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-04 16:57:57
 * @Description:
 */

import Joi from 'joi';
import { Country } from '@/constants/country';

const countries = Object.keys(Country);

export const AddTrafficRequest = Joi.object({
  ad_format: Joi.valid(1, 2, 3, 4).required(),
  buyer_ids: Joi.array().items(Joi.number().integer()).min(1).required(),
  country: Joi.string().valid(...countries).required(),
  ext_1: Joi.number().valid(1, 2).required()
});
