/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-02-15 16:35:37
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-03-01 11:51:55
 * @Description:
 */
import Icon from '@ant-design/icons';
import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Space } from 'antd';
import styles from './index.less';

const LoadingIcon = () => {
  return (
    // <div className={styles['la-ball-scale-pulse']}>
    //   <div></div>
    //   <div></div>
    // </div>
    <div className={styles['la-ball-fussion']}>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  );
};
const SpinIcon = (props: Partial<CustomIconComponentProps>) => <Icon component={LoadingIcon} {...props} />;
const CusLoadingIcon = (): any => (
  <Space>
    <SpinIcon />
  </Space>
);

export default CusLoadingIcon;
