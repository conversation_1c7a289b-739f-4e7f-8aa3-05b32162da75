/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-16 17:28:14
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 11:45:58
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-19 10:16:41
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:49:29
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { fetchData, isValidBundle, removeZWSpace } from '@/utils';
import { addIvtConfig, updateIvtConfig } from '@/services/api';
import { DefaultFormData, IvtTypeOptions } from '@/constants/strategy/ivt';
import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import InputNumberNormal from '@/components/Input/InputNumber';
import BundleTableEdit from '@/components/BundleTableEdit';

type AddIvtConfigProps = {
  isEdit: boolean;
  visible: boolean;
  item?: StrategyAPI.IvtConfigItem;
  onClose: () => void;
  onSave: () => void;
  demandList: DemandAPI.DemandListItem[];
  supplyList: SupplyAPI.SupplyListItem[];
};
const AddIvtConfigModel: React.FC<AddIvtConfigProps> = ({
  visible,
  isEdit,
  item,
  onClose,
  onSave,

  demandList,
  supplyList
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    form.resetFields();
    if (visible && isEdit && item) {
      const tmp = {
        ...item,
        tnt_id: item.tnt_id,
        seller_id: item.seller_id || undefined,
        buyer_id: item.buyer_id || undefined,
        status: item.status
      };
      const { bundle } = item;
      const bundleList = bundle?.split(',') || [];
      form.setFieldsValue({ ...tmp, bundle: bundleList });
    }
  }, [item, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  const handleAddIvtConfig = (params: any) => {
    const bundle_tmp = params.bundle?.join(',') || '';
    const bundle = removeZWSpace(bundle_tmp);
    params.bundle = bundle;
    fetchData({ request: addIvtConfig, params, onSuccess, setLoading });
  };

  const handleUpdateIvtConfig = (params: any) => {
    const bundle_tmp = params.bundle?.join(',') || '';
    const bundle = removeZWSpace(bundle_tmp);
    params.bundle = bundle;
    const { seller_id, buyer_id } = params;
    if (seller_id?.length && seller_id[0] === 0) {
      delete params.seller_id;
    }
    if (buyer_id?.length && buyer_id[0] === 0) {
      delete params.buyer_id;
    }
    fetchData({ request: updateIvtConfig, params, onSuccess, setLoading });
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      const params = {
        ...values,
        id: item?.id,
        buyer_id: Array.isArray(values.buyer_id) ? [...values.buyer_id] : [values.buyer_id || 0],
        seller_id: Array.isArray(values.seller_id) ? [...values.seller_id] : [values.seller_id || 0],
        ori_data: item || {}
      };
      handleUpdateIvtConfig(params);
    } else {
      handleAddIvtConfig({ ...values });
    }
  };

  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit IVT Rule' : 'Create IVT Rule'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        initialValues={{ ...DefaultFormData }}
      >
        <Form.Item label="Publisher" name="seller_id">
          <NormalSelect
            placeholder="Please Select Publisher"
            allowClear
            mode={isEdit ? undefined : 'multiple'}
            filterOption
          >
            {supplyList &&
              supplyList.map((item: SupplyAPI.SupplyListItem, index: number) => {
                return (
                  <NormalSelect.Option
                    value={item.seller_id}
                    key={index}
                    disabled={item.status === DemandAndSupplyStatusMap.Testing}
                  >
                    {item.seller_name}({item.seller_id})
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>

        <Form.Item label="Advertiser" name="buyer_id">
          <NormalSelect
            placeholder="Please Select Advertiser"
            allowClear
            mode={isEdit ? undefined : 'multiple'}
            filterOption
          >
            {
              demandList?.map((item, index) => {
                return (
                  <NormalSelect.Option value={item.buyer_id} key={index}>
                    {item.buyer_name}({item.buyer_id})
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>
        <Form.Item
          name="ratio"
          label="Traffic Ratio:"
          validateTrigger={['onChange', 'onBlur']}
          validateFirst
          rules={[{ required: true, message: 'Please Input Traffic Ratio' }]}
        >
          <InputNumberNormal style={{ width: '150px' }} min={1} max={100} />
        </Form.Item>
        <Form.Item label="Bundle" name="bundle">
          <BundleTableEdit
            editTitle={'Edit Bundle List'}
            editTips={`Enter the bundle(one per line)`}
            deleteAllTitle="Delete All"
            deleteAllTips={`Are you sure to delete all bundle?`}
            contentMaxHeight={isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'}
            editSingle={true}
            validator={(val: string) => isValidBundle(val)}
          />
        </Form.Item>
        <Form.Item label="Type" name="type" rules={[{ required: true }]}>
          <NormalRadio>
            {IvtTypeOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddIvtConfigModel;
