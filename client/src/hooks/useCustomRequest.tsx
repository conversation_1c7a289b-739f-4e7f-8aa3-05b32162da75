/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-01-06 19:43:22
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-09 09:30:45
 * @Description:
 */
import { useRequest, useModel } from 'umi';
import { type BaseResult } from '@ahooksjs/use-request/lib/types';

const useCustomRequest = <R = any, P extends any[] = any, U = any, UU extends U = any>(
  services: any,
  options?: object
): BaseResult<U, P> => {
  const defaultModel = useModel('@@initialState');
  return useRequest(services, {
    loadingDelay: 200,
    manual: true,
    ready: !!defaultModel?.initialState?.currentUser,
    formatResult: (result: any) => {
      if (result.code === 0) {
        return result.data;
      }
    },
    ...options
  });
};

export default useCustomRequest;
