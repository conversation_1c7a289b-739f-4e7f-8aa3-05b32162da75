/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-21 20:28:04
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 18:27:32
 * @Description:
 */
declare namespace TroubleShootingAPI {
  type TaskListItem = {
    id: number;
    status: number;
    type: number;
    server_region: string;
    seller_id: number;
    bundle: string;
    block_status: string;
    ad_format: number;
    plm_id: number;
    adomain: string;
    crid: string;
    country: string;
    cid: string;
    bid_status: string;
    buyer_id: number;
    tag_id: string;
    expected_num: number;
    tnt_id: number;
    created_time: string;
    account_status: number;
    buyer_name: string;
    seller_name: string;
  };

  type TraceListItem = {
    id: number;
    type: number;
    seller_id: number;
    seller: string;
    tnt_id: number;
    buyer_id: number;
    buyer: string;
    region: number;
    bundle: string;
    cid: string;
    crid: string;
    adomain: string;
    ad_format: number;
    bid_status: number;
    block_reason: number;
    placement_id: number;
    tag_id: string;
    data: string;
  };

  type getTraceListParams = {
    tag_id: string;
  };
}
