.operate-btn-container {
  width: 100%;
  display: flex;
  overflow: hidden;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: center;
  button {
    padding-left: 0;
    &:nth-child(2) {
      margin-right: 8px;
    }
  }
  .text {
    white-space: nowrap;
    margin-right: 4px;
    max-width: calc(100% - 40px);
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.flagClass {
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: flex-end;
}
.operate-btn-popover {
  :global {
    .ant-popover-inner {
      border-radius: 6px;
    }
    .ant-popover-inner-content {
      padding: 12px;
      min-width: 130px;
      button {
        width: 100%;
        justify-content: flex-start;
        &:hover {
          background: #edeff0;
        }
      }
    }
  }
}

.first-btn {
  display: flex;
  align-items: center;
}
