/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 11:44:06
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 19:43:18
 * @FilePath: /saas.rix-platform/server-ts/src/services/demand/demand.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEmpo
 */
import { StatusMap } from '@/constants';
import { AuthLevel } from '@/constants/demand';
import { QueryParamType } from '@/db/mysql';
import { delRedisByKey } from '@/db/redis';
import { demandModel, userModel } from '@/models';
import { DemandAPI } from '@/types/demand';
import { getConfig, md5 } from '@/utils';
import { getEscapeParams } from '@/utils/params';
import { buildSQLSetClause } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

const { redisConfig } = getConfig();
class DemandService implements DemandAPI.Demand {
  async getDemandList(
    tnt_id: number,
    cur_time_zone: string,
    isTesting: boolean
  ) {
    return await demandModel.getDemandList(tnt_id, cur_time_zone, isTesting);
  }

  async addDemand(params: DemandAPI.AddDemandParams): Promise<boolean> {
    return demandModel.addDemand(params);
  }

  async updateDemand(params: DemandAPI.UpdateDemandParams): Promise<boolean> {
    const {
      buyer_id,
      buyer_name,
      status,
      tnt_id,
      profit_model,
      profit_ratio = 0,
      rev_share_ratio = 100,
      profit_id,
      profit_status,
      op_id,
      auction_type,
      imp_track_type,
      schain_required,
      schain_hops,
      schain_complete,
      pass_display_manager,
      display_manager_filter = 2,
      idfa_required = 2,
      filter_mraid = 2,
      user_id,
      max_hm_ivt_ratio = -1,
      max_pxl_ivt_ratio = -1,
      dp_id = 0,
      omid_track = 2,
      multi_format = 2,
      native_format = 1,
      native_version = 2,
      banner_multi_size
    } = params;

    const buyerUpdateObj = buildSQLSetClause([
      // 字符串类型需要加引号
      ['buyer_name', buyer_name],
      ['status', status],
      ['schain_required', schain_required],
      ['schain_hops', schain_hops],
      ['schain_complete', schain_complete],
      ['pass_display_manager', pass_display_manager],
      // 当 pass_display_manager 为 pause 时，display_manager_filter 需要改成 pause
      [
        'display_manager_filter',
        pass_display_manager === StatusMap.Paused
          ? StatusMap.Paused
          : display_manager_filter
      ],
      ['filter_mraid', filter_mraid],
      ['max_hm_ivt_ratio', max_hm_ivt_ratio],
      ['max_pxl_ivt_ratio', max_pxl_ivt_ratio],
      ['idfa_required', idfa_required],
      ['multi_format', multi_format],
      ['banner_multi_size', banner_multi_size],
      ['profit_model', profit_model],
      ['rev_share_ratio', rev_share_ratio],
      ['imp_track_type', imp_track_type],
      ['omid_track', omid_track],
      ['auction_type', auction_type],
      ['dp_id', dp_id],
      ['native_format', native_format],
      ['native_version', native_version]
    ]);

    const profitUpdateObj = buildSQLSetClause([
      ['profit_ratio', profit_ratio || 0],
      ['op_id', op_id],
      ['status', profit_status]
    ]);

    const buyerUpdateSql = `update buyer set ? where buyer_id=? and tnt_id=?`;
    const profitUpdateSql = `update profit set ? where id=? and tnt_id=? and buyer_id=?`;

    const sqls = [
      {
        sql: buyerUpdateSql,
        values: [buyerUpdateObj, buyer_id, tnt_id]
      },
      {
        sql: profitUpdateSql,
        values: [profitUpdateObj, profit_id, tnt_id, buyer_id]
      }
    ];
    const res = await demandModel.updateDemand(sqls);
    if (res) {
      const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
      await delRedisByKey(key);
    }
    return res;
  }

  async isDemandNameExists(
    buyer_name: string,
    tnt_id: number,
    buyer_id?: number
  ): Promise<any[]> {
    return await demandModel.isDemandNameExists(buyer_name, tnt_id, buyer_id);
  }

  async isDemandAccountExists(
    account_name: string,
    tnt_id: number,
    user_id?: number
  ) {
    const params: any = {
      account_name,
      tnt_id
    };
    return await userModel.isAccountNameExists(params, user_id);
  }

  async isTestingDemand(buyer_id: number, tnt_id: number): Promise<any[]> {
    return await demandModel.isTestingDemand(buyer_id, tnt_id);
  }

  async getDemandEndpoint(tnt_id: number, buyer_id: number) {
    return await demandModel.getDemandEndpoint(tnt_id, buyer_id);
  }

  async setDemandEndpoint(
    endpoint: DemandAPI.AddEndpointParams[],
    buyer_id: number,
    tnt_id: number
  ) {
    const sqls: QueryParamType[] = [
      {
        sql: 'delete from buyer_endpoint where buyer_id = ? and tnt_id = ?',
        values: [buyer_id, tnt_id]
      }
    ];

    if (endpoint.length) {
      const rows = endpoint.map(item => [
        buyer_id,
        item.server_region,
        item.ad_format,
        item.url,
        item.connect_timeout,
        item.socket_timeout,
        item.gzip,
        tnt_id
      ]);

      sqls.push({
        sql: 'insert into buyer_endpoint(buyer_id, server_region, ad_format, url, connect_timeout, socket_timeout, gzip, tnt_id) values ?',
        values: [rows]
      });
    }

    return !!(await demandModel.setDemandEndpoint(sqls));
  }

  async getPretargetCampaign(
    buyer_id: number,
    campaign_id: number,
    tnt_id: number,
    cur_time_zone: string
  ) {
    const campaign = await demandModel.getPretargetCampaign(
      buyer_id,
      tnt_id,
      campaign_id
    );
    const list = await demandModel.getPretargetCampaignItem(
      buyer_id,
      tnt_id,
      campaign_id
    );
    const data = campaign.map((item: DemandAPI.PretargetCampaignItem) => {
      // eslint-disable-next-line max-len
      const items = list.filter(
        (val: DemandAPI.PretargetContentItem) =>
          val.campaign_id === item.campaign_id
      );
      const update_time = items?.[0]?.update_time || item.update_time;
      const times = Math.max(
        new Date(update_time).getTime(),
        new Date(item.update_time).getTime()
      );
      return {
        ...item,
        items,
        update_time: moment(times)
          .tz(cur_time_zone)
          .format('YYYY-MM-DD HH:mm:ss')
      };
    });
    return data;
  }

  async updatePretargetCampaign(formData: DemandAPI.PretargetUpdateItem) {
    const { campaign_id, campaign_name, buyer_id, items, tnt_id, op_id } =
      getEscapeParams(formData);

    const contents: any[][] = [];
    const pt_flag = Array.from({ length: 20 }, () => 0);
    for (const { level, content } of items) {
      pt_flag[level - 1] = 1;
      contents.push([campaign_id, buyer_id, level, content, tnt_id, op_id]);
    }

    const sqls: QueryParamType[] = [
      {
        sql: 'update pt_campaign set campaign_name = ?, pt_flag = ?, op_id=? where buyer_id = ? and campaign_id = ? and tnt_id=?',
        values: [
          campaign_name,
          pt_flag.join('|'),
          op_id,
          buyer_id,
          campaign_id,
          tnt_id
        ]
      },
      {
        sql: 'delete from pt_item where buyer_id = ? and campaign_id = ? and tnt_id=?',
        values: [buyer_id, campaign_id, tnt_id]
      }
    ];

    if (contents.length) {
      sqls.push({
        sql: 'insert into pt_item(campaign_id, buyer_id, level, content, tnt_id, op_id) values ?',
        values: [contents]
      });
    }

    return !!(await demandModel.updatePretargetCampaign(sqls));
  }

  async updatePretargetStatus(formData: DemandAPI.PretargetUpdateStatusItem) {
    const { campaign_id, buyer_id, status, tnt_id, op_id } = formData;

    return !!(await demandModel.updatePretargetCampaign([
      {
        sql: 'update pt_campaign set status = ?, op_id=? where buyer_id = ? and campaign_id = ? and tnt_id=?',
        values: [status, op_id, buyer_id, campaign_id, tnt_id]
      },
      {
        sql: 'update pt_item set op_id=? where buyer_id = ? and campaign_id = ? and tnt_id=?',
        values: [op_id, buyer_id, campaign_id, tnt_id]
      }
    ]));
  }

  async addPretargetCampaign(formData: DemandAPI.PretargetUpdateItem) {
    return demandModel.addPretargetCampaign(formData);
  }

  async getDemandAuth(buyer_id: number, tnt_id: number) {
    return await demandModel.getDemandAuth(buyer_id, tnt_id);
  }

  async setDemandAuth(
    buyer_id: number,
    authIds: string[],
    op_id: number,
    tnt_id: number
  ) {
    const sqls: QueryParamType[] = [
      {
        sql: 'delete from config_auth where buyer_id = ? and level = ? and tnt_id = ?',
        values: [buyer_id, AuthLevel.Supply, tnt_id]
      }
    ];

    if (authIds?.length) {
      const rows = authIds.map(item => [
        buyer_id,
        AuthLevel.Supply,
        item,
        op_id,
        tnt_id
      ]);

      sqls.push({
        sql: 'insert into config_auth(buyer_id, level, pub_id, op_id, tnt_id) values ?',
        values: [rows]
      });
    }

    return !!(await demandModel.setDemandAuth(sqls));
  }
}

export default new DemandService();
