import { createScopeHooks } from '@/stores/scope';
import { PretargetingDataItem } from './helper/transformPretargetListData';

interface PretargetingState {
  /**
   * 当前选中的广告主
   */
  currentDemand: DemandAPI.DemandListItem | null;
  /**
   * 1: 新增 2: 编辑
   */
  pretargetModalOp: 1 | 2;
  /**
   * 弹窗是否显示
   */
  pretargetModalVisible: boolean;
  /**
   * 弹窗的数据
   */
  onePretargetingData: PretargetingDataItem | null;
  /**
   * 复制弹窗是否显示
   */
  pretargetCopyModalVisible: boolean;
  /**
   * 复制弹窗的数据
   */
  pretargetCopyModalData: PretargetingDataItem | null;
}

const initialPretargetingState: PretargetingState = {
  currentDemand: null,
  pretargetModalOp: 1,
  pretargetModalVisible: false,
  onePretargetingData: null,
  pretargetCopyModalVisible: false,
  pretargetCopyModalData: null,
};

const { useScopeState: usePretargetingState, useScopeStore: usePretargetingStore } =
  createScopeHooks<PretargetingState>();

export { usePretargetingState, usePretargetingStore, initialPretargetingState };
