export namespace GeoPolicyAPI {
  /**
   * 后端接口参数类型
   */
  type AddGeoPolicyKeyRelationSchema = {
    policy_key_id: number;
    seller_id: number;
  }

  type UpdateGeoPolicyKeyRelationSchema = {
    id: number;
    status: number;
    policy_key_id: number;
    seller_id: number;
  }

  type AddGeoPolicyKeyRelationParams = AddGeoPolicyKeyRelationSchema & {
    tnt_id: number;
    op_id: number;
  }

  type UpdateGeoPolicyKeyRelationParams = UpdateGeoPolicyKeyRelationSchema & {
    tnt_id: number;
    op_id: number;
  }

  /**
   * 添加 policy key
   */
  type AddGeoPolicyKeySchema = {
    policy_key: string;
    remark: string;
    is_default: number;
  }

  type AddGeoPolicyKeyParams = AddGeoPolicyKeySchema & {
    tnt_id: number;
    op_id: number;
  }

  /**
   * 更新 policy key
   */
  type UpdateGeoPolicyKeySchema = {
    id: number;
    policy_key: string;
    remark: string;
    status: number;
    is_default: number;
  }

  type UpdateGeoPolicyKeyParams = UpdateGeoPolicyKeySchema & {
    tnt_id: number;
    op_id: number;
  }

  type DefaultPolicyKeyParams = {
    tnt_id: number;
    policy_key_id?: number;
  }

  /**
   * 后端接口返回类型：policy key list
   */
  type GeoPolicyKeyListItem = {
    id: number;
    unique_id: string;
    policy_key: string;
    remark: string;
    op_id: number;
    op_name: string;
    op_status: number;
    status: number;
    is_default: number;
    update_time: number;
  };

  /**
   * 后端接口返回类型：policy key relation list
   */
  type GeoPolicyKeyRelationListItem = {
    id: number;
    unique_id: string;
    policy_key_id: number;
    policy_key: string;
    policy_key_status: number;
    seller_id: number;
    seller_name: string;
    op_id: number;
    op_name: string;
    op_status: number;
    status: number;
    update_time: number;
  };
}
