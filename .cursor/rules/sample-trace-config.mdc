---
description: 
globs: 
alwaysApply: false
---
# SampleTrace（Troubleshooting）租户配置规则

## 概述
SampleTrace是Troubleshooting模块的核心功能，用于追踪和调试广告请求。该功能仅对特定租户开放，通过租户ID白名单机制进行权限控制。

## 开放原理
SampleTrace功能采用**白名单机制**进行权限控制：
- 在 [access.ts](mdc:client/src/access.ts) 中定义授权租户列表
- 通过 `isSampleTraceAuthTnt` 变量控制权限
- 结合 `isRixAdmin`（超级管理员）权限进行双重验证

## 当前开放租户列表
```javascript
const isSampleTraceAuthTnt = [1052, 1047, 1053, 1054, 1075, 1056, 1081, 1067, 1079, 1065, 1077].includes(tnt_id);
```

### 租户说明
- **1052**: Rix
- **1047**: Demo  
- **1053**: IION
- **1054**: RixAD
- **1075**: 特殊租户
- **1056, 1081, 1067, 1079, 1065, 1077**: 其他授权租户

## 权限控制逻辑

### 1. 前端权限控制
在 [access.ts](mdc:client/src/access.ts) 第38-65行：
```javascript
// troubleshooting模块权限判断
const isTroubleUser = isRixAdmin || isSampleTraceAuthTnt;

// 具体权限分配
TroubleshootingPermission: accessObj.TroubleshootingPermission && isTroubleUser,
TroubleshootingSampleTracePermission: accessObj.TroubleshootingSampleTracePermission && isTroubleUser,
TroubleshootingSampleTraceTaskPermission: accessObj.TroubleshootingSampleTraceTaskPermission && isTroubleUser,
TroubleshootingSampleTraceTracePermission: accessObj.TroubleshootingSampleTraceTracePermission && isTroubleUser,
```

### 2. 后端数据过滤
在 [sample-trace.ts](mdc:server/src/services/troubleshooting/sample-trace.ts) 中：
- 内部用户可以查看所有数据
- 外部用户只能查看非内部用户创建的数据
- 内部用户不能操作外部用户创建的任务

### 3. 菜单权限控制
在 [PermissionContent/index.tsx](mdc:client/src/pages/my-profile/components/PermissionContent/index.tsx) 第78行：
```javascript
(v.access.includes('Troubleshooting') && (access.isRixAdmin || access.isSampleTraceAuthTnt))
```

## 如何添加/删除租户

### 添加新租户
1. 在 [access.ts](mdc:client/src/access.ts) 第38行的数组中添加新的租户ID：
```javascript
const isSampleTraceAuthTnt = [1052, 1047, 1053, 1054, 1075, 1056, 1081, 1067, 1079, 1065, 1077, NEW_TENANT_ID].includes(tnt_id);
```

### 删除租户
1. 从 [access.ts](mdc:client/src/access.ts) 第38行的数组中移除对应的租户ID

### 注意事项
- 修改后需要重新部署前端应用
- 建议在测试环境先验证权限配置
- 删除租户权限前需确认该租户没有正在运行的SampleTrace任务

## 相关文件
- **权限配置**: [client/src/access.ts](mdc:client/src/access.ts)
- **后端服务**: [server/src/services/troubleshooting/sample-trace.ts](mdc:server/src/services/troubleshooting/sample-trace.ts)
- **前端页面**: [client/src/pages/troubleshooting/sample-trace/](mdc:client/src/pages/troubleshooting/sample-trace)
- **权限常量**: [server/src/constants/index.ts](mdc:server/src/constants/index.ts)
- **权限工具**: [client/src/utils/permission.ts](mdc:client/src/utils/permission.ts)

## 权限验证流程
1. 用户登录后，系统获取用户的 `tnt_id`（租户ID）
2. 前端 `access.ts` 检查 `tnt_id` 是否在 `isSampleTraceAuthTnt` 白名单中
3. 如果在白名单中或用户是超级管理员，则授予SampleTrace相关权限
4. 后端服务根据用户类型过滤数据，确保数据安全
5. 前端根据权限控制菜单显示和功能访问
