/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-03 10:42:17
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-28 20:34:10
 * @Description: 数据库配置
 */
const config = Object.seal({
  port: 3001,
  database: {
    host: '*********',
    port: 3306,
    user: 'saas_user_w',
    password: 'afa28f72470948dc901aa1d48384d21c',
    database: 'db_saas',
    timezone: 'UTC'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  encryptionStr: '8rL5Mbp5-', // 加密密钥
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  /**
   * 前端自身的 redis 数据配置
   */
  redisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQD',
    RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
    optArray: [
      {
        port: 6379,
        host: '**********',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '**********',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    BE_API_DAILY_LIMIT: 50
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 6379,
        host: '*********',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '*********',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
  },
  UpdateUserInfo_KEY: 'needUpdateInfo',
  NotifyFeishuUrl:
    'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc',
  BoardCaCheTime: 60 * 10
});

export default config;
