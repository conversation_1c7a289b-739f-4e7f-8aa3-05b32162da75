import FrontTable, { ButtonType } from '@/components/Table/FrontTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { PmpTabOptions } from '@/constants/strategy/pmp';
import { ColumnProps } from 'antd/lib/table';

type Props<T> = {
  dataSource: T[];
  loading: boolean;
  columns: ColumnProps<T>[];
  searchOptions: TopBarSearchItem[];
  request: any;
  onTabChange: (tab: string | number) => void;
  btnOptions?: ButtonType[] | undefined;
  currentTab: number;
  defaultSearchValue?: any;
};

function PmpItem<T extends object>({
  dataSource,
  loading,
  columns,
  searchOptions,
  request,
  onTabChange,
  btnOptions,
  currentTab,
  defaultSearchValue
}: Props<T>): JSX.Element {
  const normalEmptyRender = () => <span> </span>;

  return (
    <FrontTable<T>
      searchOptions={searchOptions}
      loading={loading}
      columns={columns}
      dataSource={dataSource}
      rowKey={'id'}
      request={request}
      tabOptions={PmpTabOptions}
      defaultTab={currentTab}
      onTabChange={onTabChange}
      btnOptions={btnOptions}
      scroll={{ y: 'calc(100vh - 220px)' }}
      labelWidth={130}
      emptyRender={dataSource.length ? normalEmptyRender : undefined}
      initialValues={defaultSearchValue}
      isFold
    />
  );
}

export default PmpItem;
