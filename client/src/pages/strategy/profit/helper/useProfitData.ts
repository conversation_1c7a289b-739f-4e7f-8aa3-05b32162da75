import { useModel } from '@umijs/max';
import { useCallback, useEffect, useRef } from 'react';
import { ProfitTabType } from '@/constants/strategy/profit';

type UseProfitDataProps = {
  currentTab: ProfitTabType;
};

export const useProfitData = ({ currentTab }: UseProfitDataProps) => {
  // 使用全局Model获取数据和方法
  const { dataSource, reload, loading } = useModel('useProfitList');
  
  // 存储上一次的标签页，用于判断是否切换了标签页
  const prevTabRef = useRef(currentTab);
  
  // 标签页切换时重新加载数据
  useEffect(() => {
    reload({ type: currentTab });
  }, [currentTab, reload]);

  // 处理数据，当标签页切换或加载中时返回空数组
  const processedData = useCallback(() => {
    if (prevTabRef.current !== currentTab || loading) {
      prevTabRef.current = currentTab;
      return [];
    }
    return dataSource;
  }, [dataSource, loading, currentTab]);

  // 重新加载数据
  const reloadData = useCallback(() => {
    reload({ type: currentTab });
  }, [reload, currentTab]);

  return {
    profitList: dataSource,
    processedData: processedData(),
    loading,
    reloadData
  };
}; 
