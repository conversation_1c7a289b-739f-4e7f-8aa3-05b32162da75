import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Form, Radio, Button, message, Input, Typography } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useAccess, useModel } from 'umi';
import { useRequest } from 'ahooks';

import NormalInput from '@/components/Input/NormalInput';
import { ProfileListItem } from '@/constants/base';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import NormalModal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';

import { StatusOptions, ResetPwdOptions, ResetPwdType } from '@/constants';
import {
  SendEmailTyleOptions,
  PmsOverrideType
} from '@/constants/base/my-profile';
import { RoleTypeOptions } from '@/constants/permission/role';
import { addOneUser, editUser, sendEmail, isAccountNameExists } from '@/services/api';

import { fetchData, downloadCsv } from '@/utils';

const { Paragraph } = Typography;
type AddUserProps = {
  isEdit: boolean;
  visible: boolean;
  setVisible: (params: boolean) => void;
  rowUserInfo?: ProfileListItem;
  reloadUserList: () => void;
};
const AddUser: React.FC<AddUserProps> = ({ isEdit, visible, setVisible, rowUserInfo, reloadUserList }) => {
  const access = useAccess();
  const [form] = Form.useForm();
  const [attentionForm] = Form.useForm();
  const [isResetPwd, setIsResetPwd] = useState(false);
  const [showAttention, setShowAttention] = useState(false);
  const [showEmail, setShowEmail] = useState(false);
  const [email, setEmail] = useState('');
  const [roleChange, setRoleChange] = useState(false);

  const { dataSource: roleList, reload: reloadRoleList, loading: roleLoading } = useModel('useRoleList');

  const [RolesOptopns, setRolesOptopns] = useState<any[]>([]);

  const { runAsync: checkedName } = useRequest(isAccountNameExists, {
    debounceWait: 300,
    manual: true
  });
  useEffect(() => {
    if (isEdit) {
      form.setFieldsValue({
        ...rowUserInfo,
        reset_status: ResetPwdType.No,
        ov_type: PmsOverrideType.Yes
      });
    } else {
      form.resetFields();
    }
  }, [isEdit, visible]);
  useEffect(() => {
    if (!roleList) {
      reloadRoleList();
    }
  }, []);
  useEffect(() => {
    if (roleList && roleList.length) {
      setRolesOptopns(
        roleList.map((item: any) => ({
          label: item.role_name,
          value: item.id
        }))
      );
    } else if (roleList?.length === 0) {
      setRolesOptopns(RoleTypeOptions);
    }
  }, [roleList]);

  const handleValueChange = (changedValues: any) => {
    changedValues.role_id && setRoleChange(changedValues.role_id !== rowUserInfo?.role_id);
    changedValues.reset_status && setIsResetPwd(changedValues.reset_status === ResetPwdType.Yes);
  };

  const handleConfirm = () => {
    const formValues = form.getFieldsValue();
    const { reset_status, email, account_name, new_password, password, role_id } = formValues;
    setIsResetPwd(reset_status === ResetPwdType.Yes);
    setEmail(email);
    attentionForm.setFieldsValue({
      account_name: account_name,
      password: new_password || password,
      send_email: 1
    });
    form.submit();
  };
  const handleAttentionConfirm = () => {
    if (attentionForm.getFieldValue('send_email') === 1) {
      setShowEmail(true);
    }
    setShowAttention(false);
  };

  const sendSuccess = (data: any) => {
    message.success('Send Success');
    attentionForm.resetFields();
    setIsResetPwd(false);
    setEmail('');
  };
  const handleEmailConfirm = () => {
    setShowEmail(false);
    const params = {
      ...attentionForm.getFieldsValue(),
      email,
      isResetPwd
    };
    fetchData({ request: sendEmail, params: params, onSuccess: sendSuccess });
  };

  const onSuccess = (data: any) => {
    message.success('Success');
    reloadUserList();
    setVisible(false);
    setRoleChange(false);
    setShowAttention(isResetPwd || !isEdit);
    form.resetFields();
  };
  const handleFinish = (values: any) => {
    setVisible(false);
    const obj = {
      ...values,
      account_name: values.account_name.trim(),
      roleChange: roleChange ? 1 : 0,
      user_id: rowUserInfo?.user_id,
      op_user_id: rowUserInfo?.op_user_id
    };

    isEdit
      ? fetchData({ request: editUser, params: obj, onSuccess })
      : fetchData({ request: addOneUser, params: obj, onSuccess });
  };

  const handleCancel = () => {
    setVisible(false);
    setShowAttention(false);
    setIsResetPwd(false);
    setRoleChange(false);
    form.resetFields();
    attentionForm.resetFields();
  };
  const handleDownload = () => {
    const fileName = `userInfo_${new Date().getTime()}`;
    const fields: any = [
      { label: 'User Name', value: 'account_name' },
      { label: 'Password', value: 'password' }
    ];
    downloadCsv(fileName, [attentionForm.getFieldsValue()] || [], { fields });
  };

  return (
    <>
      <NormalModal
        okText="Confirm"
        title={isEdit ? `Edit User` : `Add User`}
        onOk={handleConfirm}
        open={visible}
        onCancel={handleCancel}
        width={494}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 50 }}
        className={styles.addUserModal}
      >
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 142 }}}
          wrapperCol={{ span: 24 }}
          labelAlign="left"
          onValuesChange={handleValueChange}
          onFinish={handleFinish}
          validateTrigger={['onBlur', 'onChange']}
        >
          {isEdit && (
            <>
              <Form.Item label="User Name" name="account_name" rules={[{ required: true }]}>
                <NormalInput disabled style={{ maxWidth: 281 }} />
              </Form.Item>
              <Form.Item label="Status" name="status" rules={[{ required: true }]}>
                <NormalRadio>
                  {StatusOptions.map((item, index) => (
                    <Radio key={index} value={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </NormalRadio>
              </Form.Item>
              <Form.Item label="Reset Password" name="reset_status" rules={[{ required: true }]}>
                <NormalRadio>
                  {ResetPwdOptions.map((item, index) => (
                    <Radio key={index} value={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </NormalRadio>
              </Form.Item>
              {isResetPwd && (
                <>
                  <Form.Item
                    name="new_password"
                    label="New Password:"
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Input New Password!'
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters'
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          const chineseReg = /[\u4e00-\u9fa5]/;
                          if (!value || (reg.test(value) && !chineseReg.test(value))) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(new Error('Password must contain number and capitals'));
                          }
                          return Promise.reject(new Error('Password cannot contain Chinese'));
                        }
                      })
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
                  </Form.Item>
                  <Form.Item
                    name="repeat_password"
                    label="Repeat Password:"
                    validateTrigger={['onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Repeat Password'
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters'
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          if (!value || (reg.test(value) && getFieldValue('new_password') === value)) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(new Error('Password must contain number and capitals'));
                          }
                          return Promise.reject(new Error('Please enter the same password'));
                        }
                      })
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
                  </Form.Item>
                </>
              )}
            </>
          )}
          {!isEdit && (
            <>
              {/* 防止表单自动填充 */}
              <Input
                style={{
                  height: '0px',
                  width: '0px',
                  overflow: 'hidden',
                  padding: '0px',
                  border: 'none',
                  position: 'absolute'
                }}
                name="user_name"
                maxLength={1}
              ></Input>
              <Form.Item
                label="User Name"
                name="account_name"
                rules={[
                  { required: true, message: 'Please Input User Name!', validateTrigger: ['onBlur'] },
                  {
                    validateTrigger: ['onChange', 'onBlur'],
                    validator: (_, value, callback) => {
                      value &&
                        checkedName({ account_name: value }).then((res: any) => {
                          if (res.code === 0) {
                            callback();
                          } else {
                            callback(res.message);
                          }
                        });
                    }
                  }
                ]}
              >
                <NormalInput style={{ maxWidth: 281 }} autoComplete="off" />
              </Form.Item>
              <Input.Password
                style={{
                  height: '0px',
                  width: '0px',
                  overflow: 'hidden',
                  padding: '0px',
                  border: 'none',
                  position: 'absolute'
                }}
                maxLength={11}
              />
              <Form.Item
                label="Password"
                name="password"
                rules={[
                  {
                    required: true,
                    message: 'Please Input New Password!'
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters'
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      const chineseReg = /[\u4e00-\u9fa5]/;
                      if (
                        !value ||
                        (reg.test(value) && getFieldValue('old_password') !== value && !chineseReg.test(value))
                      ) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Password must contain number and capitals'));
                      }
                      return Promise.reject(new Error('Password cannot contain Chinese'));
                    }
                  })
                ]}
              >
                <NormalInput.Password style={{ maxWidth: 281 }} allowClear autoComplete="off" />
              </Form.Item>
            </>
          )}
          <Form.Item label="Role" name="role_id" rules={[{ required: true, message: 'Please Select Role!' }]}>
            <Select placeholder="Please Select Role">
              {RolesOptopns.map((item, index) => {
                return (
                  <Select.Option key={index} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>

          {/* 代码不删，后期可能会恢复使用 */}
          {/* {isEdit && roleChange && (
            <Form.Item
              label="Permission Override"
              name="ov_type"
              rules={[{ required: true, message: 'Please Select Permission Override!' }]}
            >
              <NormalRadio>
                {PmsOverrideTypeOptions.map((item, index) => (
                  <Radio key={index} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          )} */}

          <Form.Item
            label="E-mail"
            name="email"
            rules={[
              { required: true, message: 'Please Input E-mail!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                  if (!value || reg.test(value)) {
                    return Promise.resolve();
                  }
                  if (!reg.test(value)) {
                    return Promise.reject(new Error('Please enter the correct email!'));
                  }
                  return Promise.reject(new Error('Please enter the email!'));
                }
              })
            ]}
          >
            <NormalInput placeholder="Please Input E-mail"></NormalInput>
          </Form.Item>
        </Form>
      </NormalModal>
      <NormalModal
        okText="Confirm"
        title={`Attention`}
        onOk={handleAttentionConfirm}
        open={showAttention}
        onCancel={handleCancel}
        width={494}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 50 }}
        className={styles.addUserModal}
      >
        <div className={styles['tips']}>
          <ExclamationCircleOutlined className={styles['tips-icon']} />
          <div className={styles['tips-info']}>
            Please be sure to remember your password, our system will encrypt and save the password.
          </div>
        </div>
        <Form
          form={attentionForm}
          layout="horizontal"
          labelCol={{ style: { width: 142 }}}
          wrapperCol={{ span: 24 }}
          labelAlign="left"
          onValuesChange={handleValueChange}
          validateTrigger={['onBlur', 'onSubmit']}
        >
          <Form.Item label="User Name" name="account_name" rules={[{ required: true }]}>
            <div className={styles['copy-container']}>
              <span>{attentionForm.getFieldValue('account_name')}</span>
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: `${attentionForm.getFieldValue('account_name')}`,
                  icon: 'Copy'
                }}
              ></Paragraph>
            </div>
          </Form.Item>
          <Form.Item label="Password" name="password" rules={[{ required: true }]} style={{ position: 'relative' }}>
            <div className={styles['copy-container']}>
              <span>{attentionForm.getFieldValue('password')}</span>
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: `${attentionForm.getFieldValue('password')}`,
                  icon: 'Copy'
                }}
              ></Paragraph>
            </div>
          </Form.Item>
          <div className={styles['btn']}>
            <Button icon={<RixEngineFont type="rix-download" />} onClick={handleDownload}>
              Download
            </Button>
          </div>
          <Form.Item
            name="send_email"
            label="Sent to email"
            rules={[
              {
                required: true,
                message: 'Please Select Relationship'
              }
            ]}
          >
            <NormalRadio>
              {SendEmailTyleOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        </Form>
      </NormalModal>

      <NormalModal
        okText="Confirm"
        title={`Attention`}
        onOk={handleEmailConfirm}
        open={showEmail}
        onCancel={() => setShowEmail(false)}
        width={494}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 50 }}
        className={styles.addUserModal}
        okButtonProps={{ disabled: access.DisabledButton('editUserAuth') }}
      >
        <div className={styles['tips']}>
          <ExclamationCircleOutlined className={styles['tips-icon']} />
          <div className={styles['tips-info']}>
            {`Attention Please confirm that your account password will be sent to the email: ${email}`}
          </div>
        </div>
      </NormalModal>
    </>
  );
};

export default AddUser;
