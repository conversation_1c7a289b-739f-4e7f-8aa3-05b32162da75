import { DefaultRequestOptions } from '@/constants/request';
import { GeoDefaultKeyMap, GeoPolicyTab, GeoPolicyTabType } from '@/constants/strategy/geo-policy';
import { getGeoPolicyKeyList, getGeoPolicyKeyRelationList } from '@/services/api';
import { useRequest } from '@umijs/max';
import { useCallback, useEffect, useState } from 'react';
import { PolicyKeyOption } from './useSearchOptions';

type UseGeoPolicyDataProps = {
  currentTab: GeoPolicyTabType;
};

export const useGeoPolicyData = ({ currentTab }: UseGeoPolicyDataProps) => {
  const {
    data: keyDataSource,
    run: reloadGeoPolicyList,
    loading: loadingGeoPolicyList
  } = useRequest(getGeoPolicyKeyList, {
    ...DefaultRequestOptions,
    cacheKey: 'geo-policy-key-list'
  });

  const {
    data: relationDataSource,
    run: reloadGeoPolicyKeyRelationList,
    loading: loadingGeoPolicyKeyRelationList
  } = useRequest(getGeoPolicyKeyRelationList, {
    ...DefaultRequestOptions,
    cacheKey: 'geo-policy-key-relation-list'
  });

  const loading = loadingGeoPolicyList || loadingGeoPolicyKeyRelationList;

  const reload = useCallback(
    (tab: GeoPolicyTabType) => {
      if (tab === GeoPolicyTab.key) {
        reloadGeoPolicyList();
      } else {
        reloadGeoPolicyList();
        reloadGeoPolicyKeyRelationList();
      }
    },
    [reloadGeoPolicyList, reloadGeoPolicyKeyRelationList]
  );

  // 标签页切换时重新加载数据
  useEffect(() => {
    reload(currentTab);
  }, [currentTab, reload]);

  // 处理数据，当标签页切换或加载中时返回空数组
  const processedData = useCallback(() => {
    return currentTab === GeoPolicyTab.key ? keyDataSource : relationDataSource;
  }, [keyDataSource, currentTab, relationDataSource]);

  // 重新加载数据
  const reloadData = useCallback(() => {
    reload(currentTab);
  }, [reload, currentTab]);

  // 生成 policyKeyOptions
  const [policyKeyOptions, setPolicyKeyOptions] = useState<PolicyKeyOption[]>([]);

  useEffect(() => {
    if (keyDataSource) {
      const newOptions = keyDataSource
        .map(item => ({
          label: item.policy_key,
          value: item.id,
          status: item.status,
          is_default: item.is_default
        }));
      setPolicyKeyOptions(newOptions);
    }
  }, [keyDataSource]);

  return {
    keyDataSource: keyDataSource || [],
    relationDataSource: relationDataSource || [],
    policyKeyOptions,
    processedData: processedData(),
    loading,
    reloadData
  };
};
