/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-04-24 15:24:46
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 20:02:10
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { useLocation, useSearchParams } from 'umi';
import { Spin, Form, Input, Checkbox, message, Switch } from 'antd';
import { useRequest } from 'ahooks';

import { AccountBreadOptions } from '@/constants/demand';
import { forceLogOut, editDashboardUser, sendEmail, isAccountNameExists } from '@/services/api';
import { fetchData } from '@/utils';
import { useModel } from '@umijs/max';

import PageContainer from '@/components/RightPageContainer';
import NormalTitle from '@/components/NormalTitle';
import NormalModal from '@/components/Modal/NormalModal';
import NormalInput from '@/components/Input/NormalInput';
import OriginalTable from '@/components/Table/OriginalTable';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';

import type { OperateRenderItem } from '@/components/OperateRender';
import { ColumnProps } from 'antd/es/table';

import { StatusMap } from '@/constants';
import { DemandColumns } from '@/constants/demand/account';
const Page: React.FC = () => {
  const { state }: any = useLocation();
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  const [demand, setDemand] = useState<DemandAPI.DemandListItem>();
  const [tableData, setTableData] = useState<any>([]);

  const [visible, setVisible] = useState(false);
  const [isCreate, setIsCreate] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [toSelf, setToSelf] = useState(false);
  const [createStatus, setCreateStatus] = useState(false); // 控制create password和reset password的显示
  const [statusLoading, setStatusLoading] = useState<{ index: number; type: 'status' | 'api_status' } | undefined>();
  const [user, setUser] = useState<DemandAPI.DemandUser>();
  const { dataSource: UserList, reload: reloadUser, loading: userLoading } = useModel('useDashboardUser');
  const { initialState } = useModel('@@initialState');
  const { runAsync: checkedName } = useRequest(isAccountNameExists, {
    debounceWait: 300,
    manual: true
  });
  const handleCreate = () => {
    setVisible(true);
    setIsCreate(true);
  };
  const handleReset = () => {
    setVisible(true);
    setIsCreate(false);
  };
  const handleEidt = (params: DemandAPI.DemandUser) => {
    form.setFieldsValue({ buyer_name: user?.account_name.slice(4) });
    setVisible(true);
    setIsEdit(true);
  };
  const handleSwitch = (e: boolean, row: DemandAPI.DemandUser, type: 'status' | 'api_status') => {
    setStatusLoading({
      index: row?.user_id,
      type
    });
    const params = {
      user_id: row?.user_id,
      [type]: e ? StatusMap.Active : StatusMap.Paused,
      ori_data: { [type]: row[type], user_id: row?.user_id }
    };

    fetchData({
      request: editDashboardUser,
      params,
      onSuccess: data => {
        if (type === 'status' && !e) {
          fetchData({
            request: forceLogOut,
            params,
            onSuccess: () => {
              setStatusLoading(undefined);
              // 刷新用户列表
              reloadUser({ user_id: demand?.user_id || searchParams.get('u_id'), isSupply: false });
            }
          });
        } else {
          setStatusLoading(undefined);
          reloadUser({ user_id: demand?.user_id || searchParams.get('u_id'), isSupply: false });
        }
      }
    });
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Create Password',
      onClick: handleCreate,
      icon: <RixEngineFont type="rix-createPwd" />,
      hide: userLoading || createStatus
    },
    {
      label: 'Reset Password',
      onClick: handleReset,
      icon: <RixEngineFont type="rix-resetPwd" />,
      hide: userLoading || !createStatus
    }
  ];
  const editNameOptions: OperateRenderItem[] = [
    {
      label: '',
      onClick: handleEidt,
      icon: <RixEngineFont type="edit" className={styles['account-edit']} />,
      text: 'account_name'
    }
  ];
  const tmpColumns: ColumnProps<DemandAPI.DemandUser>[] = [
    {
      title: 'User Name',
      dataIndex: 'buyer_name',
      width: 180,
      render: (txt, params) => <OperateRender btnOptions={editNameOptions} params={params} />
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 100,
      render: (_, row) => (
        <Switch
          checked={row.status === StatusMap.Active}
          onChange={e => handleSwitch(e, row, 'status')}
          loading={statusLoading?.index === row?.user_id && statusLoading?.type === 'status'}
          checkedChildren="Active"
          unCheckedChildren="Paused"
        />
      )
    },
    {
      title: 'API Status',
      dataIndex: 'api_status',
      width: 100,
      render: (_, row) => (
        <Switch
          checked={row.api_status === StatusMap.Active}
          onChange={e => handleSwitch(e, row, 'api_status')}
          loading={statusLoading?.index === row?.user_id && statusLoading?.type === 'api_status'}
          checkedChildren="Active"
          unCheckedChildren="Paused"
        />
      )
    },
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 150,
      render: (txt, params) => <OperateRender btnOptions={OperateOptions} params={params} />
    }
  ];

  const columns: ColumnProps<DemandAPI.DemandUser>[] = [...tmpColumns, ...DemandColumns];
  useEffect(() => {
    if (state?.row) {
      setDemand(state.row);
      const { user_id } = state.row;
      reloadUser({ user_id: user_id || searchParams.get('u_id'), isSupply: false });
    }
  }, [state]);

  useEffect(() => {
    if (UserList?.length && demand) {
      const demandUser = UserList.find((v: DemandAPI.DemandUser) => v.user_id === demand?.user_id);
      const createStatus = demandUser?.isCreate;
      setCreateStatus(createStatus);
      setUser(demandUser);
      setTableData(UserList);
    }
  }, [UserList, demand]);

  const handleConfirm = () => {
    form.submit();
  };
  const handleSuccess = (data: any, values: any) => {
    isCreate ? message.success('Create Success') : message.success('Reset Success');
    const { send_email, new_password } = values;
    let email = send_email
      ? send_email
          .split('\n')
          .filter((v: string) => v)
          .map((v: string) => v.trim())
          .join(',')
      : '';
    if (toSelf && initialState?.currentUser?.user_email) {
      email = initialState?.currentUser?.user_email + `${email ? ',' : ''}${email}`;
    }
    if (email) {
      toSelf ? (email = email + ',' + initialState?.currentUser?.user_email) || '' : email;
      const params = {
        account_name: isEdit ? `Adv_${form.getFieldValue('buyer_name')}` : user?.account_name,
        email,
        password: new_password,
        isResetPwd: !isCreate && !isEdit,
        pv_domain: initialState?.currentUser?.pv_domain
      };
      fetchData({ request: sendEmail, params: params, onSuccess: () => message.success('Send Success') });
    }
    reloadUser({ user_id: demand?.user_id || searchParams.get('u_id'), isSupply: false });
    form.resetFields();
    setVisible(false);
    setIsEdit(false);
    setIsCreate(false);
    setToSelf(false);
  };
  const handleFinish = (values: any) => {
    const { buyer_name } = values;
    const params: CommonAPI.EditDashboardUserparams = {
      account_name: buyer_name?.trim(),
      new_password: values?.new_password,
      user_id: user?.user_id || searchParams.get('u_id') || 0,
      isSupply: false
    };

    if (!isEdit) {
      params.status = StatusMap.Active;
    }

    fetchData({
      request: editDashboardUser,
      params: { ...params, ori_data: { account_name: user?.account_name, user_id: user?.user_id, isSupply: false } },
      onSuccess: data => handleSuccess(data, values)
    });
  };
  const handleCancel = () => {
    form.resetFields();
    setVisible(false);
    setIsEdit(false);
    setToSelf(false);
  };

  const onChange = (e: any) => {
    setToSelf(e.target.checked);
  };

  return (
    <PageContainer
      options={AccountBreadOptions}
      isBack
      handleGoBack={() => history.go(-1)}
      className={styles['page-container-account']}
    >
      <Spin spinning={userLoading}>
        <div className={styles['account-container']}>
          <div className={styles['top']}>
            <NormalTitle
              blackName={`Demand Account`}
              grayName={`${demand?.buyer_name || searchParams.get('name')}(${demand?.buyer_id})`}
              isTitle={true}
              bottom={28}
            />
          </div>
          <OriginalTable
            loading={false}
            columns={columns}
            dataSource={tableData}
            rowKey="user_id"
            scroll={{ y: 'calc(100vh - 220px)' }}
          />
          <NormalModal
            okText="Confirm"
            title={isEdit ? 'Edit User Name' : isCreate ? 'Create Password' : 'Reset Password'}
            onOk={handleConfirm}
            open={visible}
            onCancel={handleCancel}
            width={494}
            confirmLoading={false}
            maskClosable={true}
            style={{ top: 150 }}
            className={styles.addUserModal}
          >
            <Form
              form={form}
              layout="horizontal"
              labelCol={{ style: { width: 142 } }}
              wrapperCol={{ span: 24 }}
              labelAlign="left"
              validateTrigger={['onBlur', 'onSubmit']}
              onFinish={handleFinish}
            >
              {!isEdit && (
                <>
                  <Form.Item
                    name="new_password"
                    label="New Password:"
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Input New Password!'
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters'
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          const chineseReg = /[\u4e00-\u9fa5]/;
                          if (
                            !value ||
                            (reg.test(value) && getFieldValue('old_password') !== value && !chineseReg.test(value))
                          ) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(new Error('Password must contain number and capitals'));
                          }
                          return Promise.reject(new Error('Password cannot contain Chinese'));
                        }
                      })
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
                  </Form.Item>
                  <Form.Item
                    name="repeat_password"
                    label="Repeat Password:"
                    validateTrigger={['onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Repeat Password'
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters'
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          if (!value || (reg.test(value) && getFieldValue('new_password') === value)) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(new Error('Password must contain number and capitals'));
                          }
                          return Promise.reject(new Error('Please enter the same password'));
                        }
                      })
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
                  </Form.Item>
                </>
              )}
              {isEdit && (
                <Form.Item
                  label="User Name"
                  name="buyer_name"
                  validateTrigger={['onChange', 'onBlur']}
                  rules={[
                    { required: true, message: 'Please Input User Name!', validateTrigger: ['onBlur'] },
                    {
                      validator: async (_, value, callback) => {
                        if (value) {
                          if (`Adv_${value}` === user?.account_name) {
                            return Promise.reject('New user name cannot be the same as the old user name!');
                          } else {
                            const res = await checkedName({ account_name: `Adv_${value?.trim()}` });

                            if (res.code === 0) {
                              return Promise.resolve();
                            } else {
                              return Promise.reject(res.message);
                            }
                          }
                        }
                      }
                    }
                  ]}
                >
                  <NormalInput style={{ maxWidth: 418 }} autoComplete="off" addonBefore="Adv_" />
                </Form.Item>
              )}
              <Form.Item
                name="send_email"
                label="Sent to email"
                rules={[
                  {
                    required: (isCreate || (!isCreate && !isEdit)) && !toSelf && !isEdit,
                    message: 'Please Input email!'
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const emails =
                        value &&
                        value
                          .split('\n')
                          .filter((v: string) => v)
                          .map((v: string) => v.trim());
                      const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                      const isValidate = emails?.every((v: string) => reg.test(v));
                      if (!value || isValidate) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Please enter the correct email!'));
                      }
                      return Promise.reject(new Error('Please enter the email!'));
                    }
                  })
                ]}
              >
                <Input.TextArea
                  placeholder={`Please Input email (one per line)`}
                  allowClear
                  // rows={1}
                  autoSize={{ maxRows: 10, minRows: 5 }}
                />
              </Form.Item>
              <Form.Item name="to_self" className={styles['self-email']}>
                <Checkbox checked={toSelf} onChange={onChange}>
                  To Yourself
                </Checkbox>
              </Form.Item>
            </Form>
          </NormalModal>
        </div>
      </Spin>
    </PageContainer>
  );
};

export default Page;
