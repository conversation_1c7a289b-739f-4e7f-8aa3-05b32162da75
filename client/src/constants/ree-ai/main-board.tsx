/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-12 16:55:06
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-21 11:53:42
 * @Description:
 */
// import { BreadcrumbItem } from '@/components/Breadcrumb';
// import type { TopBarSearchItem } from '@/components/TopBar';

// import StatusTag from '@/components/Tag/StatusTag';
// import { StatusDesc, StatusOptions } from '@/constants';
// import EllipsisPopover from '@/components/EllipsisPopover';
// import moment from 'moment';

export const OverviewColorMap: API.NumberType = {
  0: 'rgba(18, 107, 240, 0.05)',
  1: 'rgba(28, 175, 52, 0.05)',
  2: 'rgba(255, 144, 18, 0.05)',
  3: 'rgba(108, 44, 235, 0.05)'
};

export const OverviewFillColorMap: API.NumberType = {
  0: '#126bf0',
  1: '#1caf34',
  2: '#ff9012',
  3: '#6c2ceb'
};

export type TopCardsItem = {
  title: string;
  number: keyof BoardAPI.OverviewItem;
  percentage: keyof BoardAPI.OverviewItem;
  isDollar?: boolean;
};

export const OverviewOptions: TopCardsItem[] = [
  {
    title: 'Revenue',
    number: 'revenue',
    percentage: 'revenue_increase',
    isDollar: true
  },
  {
    title: 'Profit',
    number: 'profit',
    percentage: 'profit_increase',
    isDollar: true
  },
  {
    title: 'Ecpr',
    number: 'ecpr',
    percentage: 'ecpr_increase',
    isDollar: true
  },
  // {
  //   title: 'Impression',
  //   number: 'impression',
  //   percentage: 'impression_increase'
  // },
  { title: 'Request', number: 'request', percentage: 'request_increase' }
];
