/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-26 14:39:05
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 14:39:06
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { AppAdsAPI } from '@/types/app-ads';
import { post } from '@/utils/request';

class AppInfo {
  async getAppInfo(
    options: AppAdsAPI.GetAppInfoParams
  ): Promise<AppAdsAPI.GetAppInfoResult> {
    const { params } = options;

    getLogger('app').info(`getAppInfo searchParams=[${JSON.stringify(params)}]`);

    const result = await post({
      url: 'http://**********:8080/bundle/search/bundle',
      data: params
    });

    return result.data as AppAdsAPI.GetAppInfoResult;
  }
}

export const appInfoModel = new AppInfo();
