/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-21 14:40:21
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-08 15:49:07
 * @Description:
 */

import { Tooltip } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/NewStatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { Countries } from '@/constants/global-mapping/country';
import { StatusOptions } from '@/constants';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import moment from 'moment-timezone';

export const IsAllMap = {
  YES: 1,
  NO: 0,
  DEFAULT: -1
};

export const ABTestSearchOption: TopBarSearchItem[] = [
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Publisher'
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Advertiser'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const ABTestBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'A/B Test'
  }
];

export const ABTestColumns: ColumnProps<StrategyAPI.ABTestListItem>[] = [
  {
    title: 'Publisher',
    width: 220,
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      return _ ? (
        <HoverToolTip title={`${_}(${row['seller_id']})`}>
          <span>{_ ? `${_}(${row['seller_id']})` : `-(${row['seller_id']})`}</span>
        </HoverToolTip>
      ) : (
        '-'
      );
    }
  },
  {
    title: 'Advertiser',
    width: 220,
    dataIndex: 'buyer_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      return (
        <HoverToolTip title={`${_}(${row['buyer_id']})`}>
          <span>{_ ? `${_}(${row['buyer_id']})` : `-(${row['buyer_id']})`}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Country',
    width: 100,
    dataIndex: 'country',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      console.log(_);
      return (
        <HoverToolTip title={`${Countries[_] || _}`}>
          <span>{_ ? Countries[_] || _ : '-'}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Ad Format',
    width: 140,
    dataIndex: 'ad_format',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      return (
        <HoverToolTip title={`${AdFormatToLabel[_] || '-'}`}>
          <span>{AdFormatToLabel[_] || '-'}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Ad Size',
    width: 140,
    dataIndex: 'ad_size',
    ellipsis: { showTitle: false },
  },
  {
    title: 'Content',
    dataIndex: 'content',
    width: 110
  },
  {
    title: 'Expiration Time',
    dataIndex: 'expire_time',
    width: 180,
    render: _ => {
      const isExpired = moment().isAfter(moment(_));
      return (
        <>
          <span>{_}</span>
          {isExpired && (
            <Tooltip title="Expired">
              <ExclamationCircleOutlined
                style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
              />
            </Tooltip>
          )}
        </>
      );
    }
  },
  {
    title: 'Modified by',
    width: 160,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <HoverToolTip title={_}>
          <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
            {row['account_status'] === 3 ? 'Unknown' : row['account_status'] === 0 ? '-' : _}
          </span>
        </HoverToolTip>

        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Created At',
    dataIndex: 'create_time',
    width: 160
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100,
    render: _ => <StatusTag value={_} statusDescMap={{ 1: 'Active', 2: 'Paused' }} />
  }
];

export const ABTestTabOptions = [
  { label: 'Profit', value: 1 },
  { label: 'Bid Floor', value: 2 },
  { label: 'Transfer Format', value: 3 }
];

export const ABTestTab = {
  Profit: 1,
  BidFloor: 2,
  TransferFormat: 3
};

export const ABTestTabDesc: { [key: number]: string } = {
  1: 'Profit',
  2: 'Bid Floor',
  3: 'Transfer Format'
};

export const ContentKeyMap: { [key: number]: string } = {
  1: 'profit',
  2: 'bid_floor'
};

export const ABTestBtnDesc: { [key: number]: string } = {
  1: 'Profit A/B Test',
  2: 'Bid Floor A/B Test',
  3: 'Transfer Format A/B Test'
};

export const ABTestColumnsKeyMap: { [key: number]: string[] } = {
  1: ['seller_name', 'buyer_name', 'content', 'expire_time', 'op_name', 'update_time', 'status'],
  2: [
    'seller_name',
    'buyer_name',
    'country',
    'ad_format',
    'ad_size',
    'content',
    'expire_time',
    'op_name',
    'update_time',
    'status'
  ],
  3: ['buyer_name', 'content', 'expire_time', 'op_name', 'update_time', 'status']
};

export const ContentLabelDesc: { [key: number]: string } = {
  1: 'Profit Ratio',
  2: 'Bid Floor',
  3: 'Transfer Format'
};
