import React, { useState, useEffect } from 'react';
import styles from './index.less';
import { useLocation } from 'umi';
import { Spin, Tooltip, Typography } from 'antd';
import PageContainer from '@/components/RightPageContainer';

import { parse } from 'query-string';
import { EndpointBreadOptions } from '@/constants/supply';
import { getSupplyEndpoint } from '@/services/api';
import { fetchData } from '@/utils';
import { useModel } from '@umijs/max';
import NormalTitle from '@/components/NormalTitle';
import { QuestionCircleOutlined } from '@ant-design/icons';

const { Paragraph } = Typography;
type Endpoints = {
  use: string;
  apac: string;
  global: string;
  euw: string;
};
const Page: React.FC = () => {
  const location: any = useLocation();
  const [loading, setLoading] = useState(false);
  const [seller_id, setSellerId] = useState<number | string>('');

  const [endpoints, setEndpoints] = useState<{ use: string; apac: string; euw: string; global: string }>({
    use: '',
    apac: '',
    euw: '',
    global: ''
  });
  const [supply, setSupply] = useState<SupplyAPI.SupplyListItem>();
  const { dataSource, reload, loading: supplyLoading } = useModel('useSupplyList');

  useEffect(() => {
    const { id } = parse(location.search);
    if (id) {
      setSellerId(id as string);
      fetchEndpointData(id as string);
    }
    if (!dataSource) {
      reload();
    }
    handleSupply();
  }, []);

  useEffect(() => {
    handleSupply();
  }, [dataSource, seller_id]);

  const handleSupply = () => {
    if (seller_id && Array.isArray(dataSource)) {
      const item = dataSource.find((v: SupplyAPI.SupplyListItem) => v.seller_id === +seller_id);
      if (item) {
        setSupply(item);
      }
    }
  };

  const fetchEndpointData = (id: number | string) => {
    const onSuccess = (data: any) => {
      const { host_prefix, pv_domain, token } = data;
      const endpoints = {
        use: `http://${host_prefix}.use.svr.rixengine.com/rtb?sid=${id}&token=${token}`,
        apac: `http://${host_prefix}.apse.svr.rixengine.com/rtb?sid=${id}&token=${token}`,
        euw: `http://${host_prefix}.euw.svr.rixengine.com/rtb?sid=${id}&token=${token}`,
        global: `https://${host_prefix}.svr.rixengine.com/rtb?sid=${id}&token=${token}`
      } as Endpoints;
      if (pv_domain) {
        endpoints.use = `http://us.bid.${pv_domain}/rtb?sid=${id}&token=${token}`;
        endpoints.apac = `http://ap.bid.${pv_domain}/rtb?sid=${id}&token=${token}`;
        endpoints.euw = `http://eu.bid.${pv_domain}/rtb?sid=${id}&token=${token}`;
        endpoints.global = `https://bid.${pv_domain}/rtb?sid=${id}&token=${token}`;
      }
      setEndpoints(endpoints);
    };
    fetchData({ setLoading, request: getSupplyEndpoint, params: { seller_id: id }, onSuccess });
  };
  const handleGoBack = () => {
    history.go(-1);
  };
  return (
    <PageContainer
      options={EndpointBreadOptions}
      isBack
      handleGoBack={handleGoBack}
      className={styles['page-container-endpoint']}
    >
      <Spin spinning={loading || supplyLoading}>
        <div className={styles['endpoint-container']}>
          <div className={styles['top']}>
            <NormalTitle
              blackName={`Supply Endpoint`}
              grayName={`${supply?.seller_name}(${supply?.seller_id})`}
              isTitle={true}
              bottom={28}
            />
            <div className={styles['info-con']}>
              <span className={styles['title']}>USE:</span>
              <span className={styles['copy-container']}>
                <span className={styles['info']}>{endpoints.use}</span>
                <Paragraph
                  copyable={{
                    tooltips: false,
                    text: endpoints.use,
                    icon: 'Copy'
                  }}
                ></Paragraph>
              </span>
            </div>
            <div className={styles['info-con']}>
              <span className={styles['title']}>APAC:</span>
              <div className={styles['copy-container']}>
                <span className={styles['info']}>{endpoints.apac}</span>
                <Paragraph
                  copyable={{
                    tooltips: false,
                    text: endpoints.apac,
                    icon: 'Copy'
                  }}
                ></Paragraph>
              </div>
            </div>
            <div className={styles['info-con']}>
              <span className={styles['title']}>EUW:</span>
              <div className={styles['copy-container']}>
                <span className={styles['info']}>{endpoints.euw}</span>
                <Paragraph
                  copyable={{
                    tooltips: false,
                    text: endpoints.euw,
                    icon: 'Copy'
                  }}
                ></Paragraph>
              </div>
            </div>
            <div className={styles['global-con']}>
              <div className={styles['info-con']}>
                <span className={styles['title']}>Global:</span>
                <div className={styles['copy-container']}>
                  <span className={styles['info']}>{endpoints.global}</span>
                  <Paragraph
                    copyable={{
                      tooltips: false,
                      text: endpoints.global,
                      icon: 'Copy'
                    }}
                  ></Paragraph>
                </div>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tooltip title="Global Endpoint is used by developers with SDK Integration">
                  <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </PageContainer>
  );
};

export default Page;
