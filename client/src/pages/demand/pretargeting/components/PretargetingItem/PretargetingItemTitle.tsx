import RixEngineFont from '@/components/RixEngineFont';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import { PretargetingDataItem } from '@/pages/demand/pretargeting/helper/transformPretargetListData';
import { usePretargetingState } from '@/pages/demand/pretargeting/store';
import { Tooltip } from 'antd';
import classnames from 'classnames';
import moment from 'moment-timezone';
import styles from './index.less';

interface PretargetingItemTitleProps {
  handlePauseActive: (status: number) => void;
  currentItem: PretargetingDataItem;
}

const renderStatusTags = (status: number, min_price: number, max_price: number, serverRegion: string) => {
  return (
    <div className={styles['status-container']}>
      <div className={styles['status-tags']}>
        <span
          className={classnames(styles['status-tag'], status === 1 ? styles['active-status'] : styles['paused-status'])}
        >
          {status === 1 ? 'Active' : 'Paused'}
        </span>
        <span className={classnames(styles['status-tag'], styles['active-status'])}>Min: $ {min_price || '-'}</span>
        <span className={classnames(styles['status-tag'], styles['active-status'])}>Max: $ {max_price || '-'}</span>
        <span className={classnames(styles['status-tag'], styles['active-status'])}>{serverRegion}</span>
      </div>
    </div>
  );
};

const renderInfoItems = (op_name: string, update_time: string) => {
  return (
    <div className={styles['info-container']}>
      <div className={`${styles['info-item']}`}>
        Last Modify:
        <Tooltip title={op_name}>
          <span className={styles['operator-name']}>{op_name}</span>
        </Tooltip>
      </div>
      <div className={`${styles['info-item']}`}>
        Last Modify Time:
        <span className={styles['modify-time']}>{moment(update_time).format('YYYY-MM-DD HH:mm:ss')}</span>
      </div>
    </div>
  );
};

const PretargetingItemTitle = ({ handlePauseActive, currentItem }: PretargetingItemTitleProps) => {
  const { campaign_name, status, min_price, max_price, server_region, op_name, update_time } = currentItem;

  const state = usePretargetingState();

  const handleCopy = () => {
    state.pretargetCopyModalVisible = true;
    state.pretargetCopyModalData = { ...currentItem };
  };

  const handleEdit = () => {
    state.pretargetModalOp = 2;
    state.pretargetModalVisible = true;
    state.onePretargetingData = { ...currentItem };
  };

  return (
    <div className={styles['pretargeting-item-title']}>
      {/* Name section */}
      <HoverTooltip title={campaign_name} maxWidth={180}>
        <div className={styles['campaign-name']}>{campaign_name}</div>
      </HoverTooltip>
      {/* Status section */}
      {renderStatusTags(status, min_price, max_price, server_region)}
      {/* Info section */}
      {op_name && update_time && renderInfoItems(op_name, update_time)}
      {/* Action buttons section */}
      <div className={styles['actions-toolbar']}>
        <RixEngineFont type="rix-copy" handleClick={handleCopy} />
        <RixEngineFont type="rix-edit" handleClick={handleEdit} accessCode="updatePretargetingAuth" />
        {status === 1 ? (
          <RixEngineFont type="stop" handleClick={() => handlePauseActive(2)} accessCode="updatePretargetStatusAuth" />
        ) : (
          <RixEngineFont handleClick={() => handlePauseActive(1)} type="start" accessCode="updatePretargetStatusAuth" />
        )}
        <RixEngineFont type="rix-trash" handleClick={() => handlePauseActive(3)} accessCode="updatePretargetingAuth" />
      </div>
    </div>
  );
};

export default PretargetingItemTitle;
