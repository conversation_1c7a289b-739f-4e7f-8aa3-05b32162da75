/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:39:45
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 12:05:10
 * @Description:
 */
declare namespace DashboardAPI {
  type DashboardListItem = {
    id: number;
    buyer_gross_ecpm: number;
    buyer_gross_revenue: number;
    buyer_net_ecpm: number;
    buyer_net_revenue: number;
    buyer_id: string;
    seller_id: string;
    buyer: string;
    seller: string;
    date: string & { value: string };
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    total_request: string;
    win: string;
    win_rate: string;
    ad_width: string;
    ad_height: string;
    profit: number | string;
    profit_rate: number;
    country: string;
    ad_format: string;
    ad_size: string;
    platform: string;
    click: string;
    ctr: string;
    ecpr: string;
    day: string;
    day_hour: string;
    adv_ecpr: string;
    app_name: string;
    app_bundle_id: string;
    adv_config_qps: number;
    pub_config_qps: number;
    region: string;
    colKey?: string;
    placement_id: number | string;
    seller_schain_hop: number;
    seller_schain_complete: any;
  };
  type BillingListItem = {
    id: number;
    buyer_gross_ecpm: number;
    buyer_gross_revenue: number;
    buyer_net_ecpm: number;
    buyer_net_revenue: number;
    buyer_id: string;
    seller_id: string;
    buyer: string;
    seller: string;
    date: string;
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    total_request: string;
    win: string;
    win_rate: string;
    ad_width: string;
    ad_height: string;
    profit: number | string;
    profit_rate: number;
    country: string;
    ad_format: string;
    ad_size: string;
    platform: string;
    click: string;
    ctr: string;
    ecpr: string;
    day: string;
    day_hour: string;
    adv_ecpr: string;
    partner_id: string;
    partner_name: string;
  };

  type PixalateReportItem = {
    day: string;
    month: string;
    seller_id: string;
    buyer_id: string;
    app_bundle_id: string;
    fraud_type: string;
    gross_tracked_ads: string;
    sivt_imp: string;
    givt_imp: string;
    sivt_imp_rate: string;
    givt_imp_rate: string;
    measured_imp: string;
    views: string;
    events: string;
    buyer: string;
    seller: string;
    date: string;
    country: string;
    publisher_id: string;
  };
  type HumanReportItem = {
    seller_id: number;
    seller: string;
    buyer_id: number;
    buyer: string;
    bundle: string;
    seat: string;
    domain: string;
    publisher_id: string;
    country: string;
    total_events: number;
    valid_traffic: number;
    sivt: number;
    givt: number;
    sivt_automated_browsing: number;
    sivt_false_representation: number;
    sivt_manipulated_behavior: number;
    sivt_misleading_user_interface: number;
    sivt_undisclosed_classification: number;
    givt_data_center: number;
    givt_irregular_pattern: number;
    givt_known_crawler: number;
    givt_false_representation: number;
    givt_misleading_user_interface: number;
    tnt: number;
    day: string;
    month: string;
  };

  type ExportedReportItem = {
    id: number;
    name: string;
    type: string;
    status: number;
    create_time: string;
    query_condition: string;
    url: string;
    type_desc: string;
    status_desc: string;
    err_msg: string;
  };

  type ABtestReportItem = {
    id: number;
    date: string;
    seller_id: number;
    buyer_id: number;
    test_tag_a: string;
    test_tag_b: string;
    seller_name: string;
    buyer_name: string;
    request: number;
    response: number;
    impression: number;
    fill_rate: number;
    win_rate: number;
    buyer_net_revenue: number;
    ecpr: number;
    profit_ecpr: number;
    real_qps: number;
    profit: number;
    profit_rate: number;
    avg_bid_floor: number;
    avg_bid_price: number;
    create_time: string;
    update_time: string;
  };
}
