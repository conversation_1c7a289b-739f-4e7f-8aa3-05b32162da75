/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:36:48
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-13 14:38:29
 * @Description:
 */
import { Context } from 'koa';

declare namespace QpsAPI {
  type QpsListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    app_name: string;
    plm_name: string;
    create_time: string;
    update_time: string;
    account_name: string;
    account_status: number;
    region: number;
    bundle: string;
  };
  type AddQpsParams = {
    pub_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
    tnt_id: number;
    ots_id: string;
    region: number;
  };
  type UpdateQpsParams = {
    id: number;
    status: number;
    qps: number;
    op_id: number;
    tnt_id: number;
    ots_id: number;
  };

  type IsExistsQps = {
    level: number;
    buyer_id: number;
    pub_id: number;
    id: number;
    tnt_id: number;
    region: number;
    ots_id: string;
  };

  interface Qps {
    getQpsList(tnt_id: number, cur_time_zone: string): Promise<QpsListItem[]>;
    addQps(params: AddQpsParams): Promise<boolean>;
    updateQps(params: UpdateQpsParams): Promise<boolean>;
    isQpsExists(params: IsExistsQps): Promise<any[]>;
  }

  interface QpsCtrlInterface {
    getQpsList(ctx: Context): void;
    addQps(ctx: Context): void;
    updateQps(ctx: Context): void;
  }
}
