@import (reference) '~antd/es/style/themes/index';

.lang {
  margin-left: auto;
  margin-right: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
  cursor: pointer;
  span {
    padding: 6px;
  }
}
.lang-disabled {
  color: #8d9799;
  cursor: not-allowed;
}
.lang-container {
  width: 180px;
  padding: 12px;
  border-radius: 6px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  background-color: #fff;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

  > div {
    height: 32px;
    line-height: 32px;
    padding: 0px 12px;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &:last-child {
      margin-bottom: 0px;
    }

    &.active {
      background-color: #e6e8eb;
      font-weight: 600;
    }

    &:hover {
      background-color: #e6e8eb;
      font-weight: 600;
    }
  }

  .ellipsis {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 6px;
  }
}
