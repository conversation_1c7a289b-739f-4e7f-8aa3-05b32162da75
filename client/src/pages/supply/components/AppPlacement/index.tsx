import React, { useEffect, useState } from 'react';
import { ColumnProps } from 'antd/es/table';
import { Spin } from 'antd';
import styles from './index.less';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusDesc } from '@/constants';
import AppListEdit from '../AppListEdit';
import { AppItemColor, AuthLevel as Level } from '@/constants';
import { PlatformTypeToLabel, PublisherType } from '@/constants/app-list';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import OriginalTable from '@/components/Table/OriginalTable';
import DemandPopover from '../DemandAuthPopover';
import EditHtmlSnippet from '../EditHtmlSnippet';
import { Alphabets } from '@/constants';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import NormalTitle from '@/components/NormalTitle';
import RixEngineFont from '@/components/RixEngineFont';
import InputSearch from '@/components/Input/InputSearch';

type AppPlacementProps = {
  currentApp: SupplyAPI.SellerAppItem | undefined;
  loading: boolean;
  demandList: DemandAPI.DemandListItem[];
  handleGetAppList: () => void;
  isMax: boolean;
  token: string;
  domain: string;
  pvDomain: string; // 租户自定义域名
  publisherType: number;
};

const AppPlacement: React.FC<AppPlacementProps> = ({
  currentApp,
  demandList,
  loading,
  handleGetAppList,
  isMax,
  token,
  domain,
  pvDomain,
  publisherType
}) => {
  const AppColumns: ColumnProps<SupplyAPI.SellerAppItem>[] = [
    {
      title: 'Bundle',
      dataIndex: 'bundle',
      key: 'bundle',
      width: 200,
      fixed: 'left',
      ellipsis: { showTitle: false },
      render: _ => (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      )
    },
    {
      title: 'Authorized Advertisers',
      dataIndex: 'demand_list',
      key: 'demand_list',
      width: 260,
      ellipsis: { showTitle: false },
      render: () => (
        <div style={{ position: 'relative', height: '22px' }}>
          <DemandPopover demand_list={currentApp?.demand_list || []} />
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 85,
      render: text => (
        <StatusTag type={StatusDesc[text] && StatusDesc[text].toString().toLocaleLowerCase()} textColor="#606666">
          {StatusDesc[text]}
        </StatusTag>
      )
    },
    {
      title: 'Operation',
      dataIndex: 'operate',
      key: 'operate',
      width: 150,
      fixed: 'right',
      render: (_, params) => (
        <div className={styles['btn-container']}>
          <AppListEdit
            status={params.status}
            demand_list={params.demand_list}
            appname={currentApp?.app_name || ''}
            params={{ level: Level['Supply App'], app_id: params.app_id, status: params.status, pub_id: params.app_id }}
            loading={loading}
            allDemandList={demandList}
            handleGetAppList={handleGetAppList}
          />
          {isMax && PublisherType[publisherType] === 'MAX' && (
            <EditHtmlSnippet
              publish_type="MAX"
              formData={{
                seller_id: params.seller_id,
                app_id: params.app_id,
                token: token,
                domain: domain,
                pv_domain: pvDomain
              }}
            />
          )}
        </div>
      )
    }
  ];

  const PlacementColumns: ColumnProps<SupplyAPI.SellerPlacement>[] = [
    {
      title: 'Unit',
      dataIndex: 'plm_name',
      fixed: 'left',
      ellipsis: { showTitle: false },
      render: _ => (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      ),
      width: 150
    },
    {
      title: 'Unit ID',
      dataIndex: 'plm_id',
      width: 120
    },
    {
      title: 'Ad Format',
      dataIndex: 'ad_format',
      width: 130,
      render: text => <>{AdFormatToLabel[text]}</>
    },
    {
      title: 'Authorized Advertisers',
      width: 260,
      dataIndex: 'demand_list',
      render: list => (
        <div style={{ position: 'relative', height: '22px' }}>
          <DemandPopover demand_list={list || []} />
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 85,
      render: text => (
        <StatusTag type={StatusDesc[text] && StatusDesc[text].toLocaleLowerCase()} textColor="#606666">
          {StatusDesc[text]}
        </StatusTag>
      )
    },
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 180,
      fixed: 'right',
      render: (_, params) => (
        <div className={styles['btn-container']}>
          <AppListEdit
            status={params.status}
            demand_list={params.demand_list}
            appname={params.plm_name}
            params={{
              level: Level['Supply Placement'],
              status: params.status,
              pub_id: params.plm_id,
              plm_id: params.plm_id
            }}
            loading={params.loading || false}
            allDemandList={demandList}
            handleGetAppList={handleGetAppList}
            auth={'SupplyAuthorizeAuth'}
          />
          {isMax && PublisherType[publisherType] === 'MW' && (
            <EditHtmlSnippet
              publish_type="MW"
              formData={{
                seller_id: currentApp?.seller_id,
                app_id: params.app_id,
                token: token,
                plm_id: params.plm_id,
                bundle: currentApp?.bundle,
                app_name: currentApp?.app_name,
                domain: domain,
                pv_domain: pvDomain
              }}
            />
          )}
        </div>
      )
    }
  ];
  const [filterList, setFilterList] = useState<SupplyAPI.SellerPlacement[]>([]);

  const str = currentApp?.app_name.replace(/[^a-zA-Z]/g, '') || '';
  const alphabet = str.length ? str[0].toLowerCase() : '';
  const index = Alphabets.findIndex(val => val === alphabet.toLowerCase());
  useEffect(() => {
    if (currentApp && currentApp.adslots) {
      setFilterList(currentApp.adslots || []);
    } else {
      setFilterList([]);
    }
  }, [currentApp]);

  const handleSearch = (val: string) => {
    if (val) {
      const list =
        currentApp?.adslots.filter((item: SupplyAPI.SellerPlacement) => {
          return (
            item.plm_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 || item.plm_id.toString().indexOf(val) !== -1
          );
        }) || [];
      setFilterList(list);
    } else {
      setFilterList(currentApp?.adslots || []);
    }
  };

  return (
    <div className={styles['container']}>
      <Spin spinning={loading}>
        {alphabet && (
          <div className={styles['app-item-container']}>
            <div className={styles['item-left']}>
              <div
                className={styles['profile-container']}
                style={{ backgroundColor: AppItemColor[index % 6] as string }}
              >
                <span>
                  <RixEngineFont type={`${alphabet}`} style={{ fontSize: 36 }} />
                </span>
                {currentApp?.platform ? (
                  <div>
                    <RixEngineFont
                      type={`${PlatformTypeToLabel[currentApp?.platform].toLocaleLowerCase()}`}
                      style={{ fontSize: 14 }}
                    />
                  </div>
                ) : null}
              </div>
            </div>
            <div className={styles['item-right']}>
              <NormalTitle blackName={`${currentApp?.app_name}`} grayName={`${currentApp?.app_id}`} toolTip={false} />
              <span className={styles['gray']}>{currentApp?.bundle}</span>
            </div>
          </div>
        )}
        <div className={styles['app-center']}>
          <h3>Basic Information</h3>
          <OriginalTable<SupplyAPI.SellerAppItem>
            columns={AppColumns}
            dataSource={currentApp ? [currentApp] : []}
            pagination={false}
            rowKey="app_id"
            loading={false}
            size="small"
            scroll={{ x: 500, y: 'auto' }}
          />
        </div>
        <div className={styles['app-bottom']}>
          <NormalTitle blackName="Ad Placement" num={`${filterList.length || 0}`} isTitle toolTip={false} />
          <InputSearch placeholder="Please input unit name or unit id" handleSearch={handleSearch} />
          <OriginalTable
            columns={PlacementColumns}
            dataSource={filterList}
            pagination={false}
            rowKey="plm_id"
            loading={false}
            size="small"
            scroll={{ x: 300, y: 'calc(100vh - 455px)' }}
          />
        </div>
      </Spin>
    </div>
  );
};

export default AppPlacement;
