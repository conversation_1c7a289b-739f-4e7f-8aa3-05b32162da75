/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-03 14:13:16
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-20 11:37:35
 * @Description:
 */
import { BudgetAndTrafficBreadOptions } from '@/constants/ree-ai/ai-board';
import Breadcrumb from '@/components/Breadcrumb';
import RixEngineFont from '@/components/RixEngineFont';
import ListItem from './components/ListItem';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Button, Spin } from 'antd';
import { getBudgetAndTraffic } from '@/services/ai-board';
import { fetchData } from '@/utils';
import { useModel } from '@umijs/max';
import RequestModal from './components/RequestModel';
import FormModal from './components/FormModal';
import Card from '@/pages/welcome-dashboard/components/Card';

const Page: React.FC = () => {
  const { dataSource: demandList, reload } = useModel('useDemandListWithTesting');
  const { dataSource: logList, reload: reloadLog } = useModel('useTrafficReqLog');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<BoardAPI.BudgetAndTrafficItem[]>();
  const [visible, setVisible] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [item, setItem] = useState<{
    ad_format: number;
    country: string;
    buyer_options: BoardAPI.BudgetAndTrafficItem['buyer_options'];
  }>();
  const [totalData, setTotalData] = useState<{
    data: BoardAPI.BudgetAndTrafficItem[];
    update_time: string;
  }>({ data: [], update_time: '' });
  const getTrafficRequestList = () => {
    fetchData({
      request: getBudgetAndTraffic,
      setLoading,
      onSuccess: res => {
        setTotalData(res);
      }
    });
  };
  useEffect(() => {
    reload();
    reloadLog();
    getTrafficRequestList();
  }, []);

  useEffect(() => {
    const { data: dataSource } = totalData;
    if (demandList?.length && dataSource?.length) {
      // 生成 buyer_id 和 buyer_name(buyer_name) 的映射
      const buyerMap = demandList.reduce((pre: any, cur: any) => {
        pre[cur.buyer_id] = `${cur.buyer_name}`;
        return pre;
      }, {});
      dataSource?.forEach(item => {
        const buyer_options = item.buyer_options?.map(item => ({
          ...item,
          buyer: buyerMap[item.buyer_id] || `${item.buyer_id}`
        }));
        item.buyer_options = buyer_options;
      });
      setData(dataSource);
    }
  }, [demandList, totalData]);

  const handleRequest = (
    ad_format: number,
    country: string,
    buyer_options: BoardAPI.BudgetAndTrafficItem['buyer_options']
  ) => {
    setItem({ ad_format, country, buyer_options });
    setVisible(true);
  };
  const handleCancel = () => {
    setVisible(false);
  };

  return (
    <Spin spinning={loading}>
      <div className={styles['container']}>
        <div className={styles['top']}>
          <Breadcrumb options={BudgetAndTrafficBreadOptions} separator=">" />
          <Button icon={<RixEngineFont type="rix-list" />} type="primary" onClick={e => setFormVisible(true)}>
            Request Form
          </Button>
        </div>
        <Card title={`Current Opportunities Based on ${totalData.update_time} Data`} className={styles['title']}></Card>

        <div className={styles['list-container']}>
          {data?.map((item, index) => (
            <ListItem
              key={index}
              isRevenue={item.isRevenue}
              country={item.country}
              buyer_options={item.buyer_options || []}
              ad_format={item.ad_format}
              revenue={item.revenue}
              onClickRequest={handleRequest}
              disabled={item.isReuqested}
            />
          ))}
          {!data?.length && (
            <div className={styles['content-empty']}>
              <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
              <span>No Data</span>
            </div>
          )}
        </div>
        <RequestModal
          visible={visible}
          onCancel={handleCancel}
          item={item}
          onSave={() => {
            handleCancel();
            getTrafficRequestList();
            reloadLog();
          }}
        />
        <FormModal visible={formVisible} onCancel={() => setFormVisible(false)} data={logList} />
      </div>
    </Spin>
  );
};
export default Page;
