import React, { useEffect, useState } from 'react';
import { Input, Button, notification, Row, Select } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { isValidBundle, handleFilterSelect } from '@/utils';
import Modal from '@/components/Modal/NormalModal';
import InputNumber from '@/components/Input/InputNumber';
import styles from './index.less';
import { Countries } from '@/constants/global-mapping/country';
type BundleProps = {
  visible: boolean;
  handleOk: (val: any) => void;
  handleCancel: () => void;
  defaultList: string[];
  validator?: (val: string) => boolean;
  title?: string;
  tips?: string;
  maskClosable?: boolean;
  keyboard?: boolean;
  isReverse?: boolean; // 值是否是反过来的
  isEdit?: boolean; //
  currentEditBundle?: string;
  isBidFloor?: boolean; // 是否是bid floor
  onBidFloorChange?: (FloorMap: { [key: string]: number }) => void;
  floorMap?: { [key: string]: number };
  setFloorMap?: (FloorMap: { [key: string]: number }) => void;
};

function BundleInput({
  defaultList,
  visible,
  handleCancel,
  handleOk,
  validator,
  title,
  tips,
  maskClosable = false,
  keyboard = false,
  isReverse = false,
  isEdit,
  currentEditBundle,
  isBidFloor,
  onBidFloorChange,
  floorMap,
  setFloorMap
}: BundleProps): JSX.Element {
  const [value, setValue] = useState('');
  const [editBundleList, setEditBundleList] = useState<string[]>([]);
  const [singleBundle, setSingleBundle] = useState<string>('');
  useEffect(() => {
    if (isEdit) setSingleBundle(currentEditBundle as string);
  }, [isEdit, currentEditBundle]);
  const [countryValue, setCountryValue] = useState<string[]>([]);
  const [disabledList, setDisabledList] = useState<string[]>([]);
  const [bidFloor, setBidFloor] = useState<number>(0);
  // const [floorMap, setFloorMap] = useState<Map<string, number>>(new Map());
  useEffect(() => {
    if (isEdit && !isBidFloor) {
      setSingleBundle(currentEditBundle as string);
    }
    if (isEdit && isBidFloor) {
      setCountryValue([currentEditBundle as string]);
      setBidFloor(floorMap![currentEditBundle as string]);
    }
    if (!isEdit && isBidFloor) {
      setCountryValue([]);
      setBidFloor(0);
    }
  }, [isEdit, currentEditBundle]);
  useEffect(() => {
    if (isBidFloor) {
      // setCountryValue(defaultList);
      setDisabledList(defaultList);
    }
  }, [defaultList]);
  // 校验是否合规跟重复
  const handleAdd = () => {
    const data = isReverse ? [...editBundleList, ...defaultList] : [...defaultList, ...editBundleList];
    const newBundleList = [...new Set(data)].filter(v => v);
    // 查找重复
    const repeat = defaultList.filter(bundle => {
      return [...new Set(editBundleList)].includes(bundle);
    });
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];

    newBundleList.forEach(bundle => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });

    const diff = validBundle.filter(bundle => {
      return !defaultList.includes(bundle);
    });
    handleOk(validBundle);
    // bid floor 用
    if (isBidFloor) {
      const tmpMap: { [key: string]: number } = {};
      data?.map((item: string) => {
        if (floorMap && !floorMap[item]) {
          tmpMap[item] = bidFloor;
        }
      });
      onBidFloorChange?.({ ...tmpMap, ...floorMap });
    }
    setCountryValue([]);
    // 关闭弹窗
    handleCancel();
    if (invalidBundle.length > 0 || repeat.length > 0 || validBundle.length > 0) {
      const args = {
        message: 'Add',
        description: (
          <>
            {diff.length > 0 && (
              <div>
                <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {diff.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {repeat.length > 0 && (
              <div>
                <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`repeated: `}</span>
                <div className={styles['tips-content']}>
                  {repeat.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {invalidBundle.length > 0 && (
              <div>
                <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`invalid: `}</span>
                <div className={styles['tips-content']}>
                  {invalidBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5
      };
      notification.open(args);
    }
    setValue('');
    setEditBundleList([]);
    setBidFloor(0);
  };
  const handleDelete = () => {
    let newBundleList = [...new Set(defaultList)].filter(v => v);
    const notFound = editBundleList.filter(bundle => {
      return !newBundleList.includes(bundle);
    });
    newBundleList = newBundleList.filter(bundle => {
      return !editBundleList.includes(bundle);
    });
    const diff = defaultList.filter(bundle => {
      return !newBundleList.includes(bundle);
    });
    handleOk(newBundleList);

    // 关闭弹窗
    handleCancel();
    if (diff.length > 0 || notFound.length > 0) {
      const args = {
        message: 'Delete',
        description: (
          <>
            {diff.length > 0 && (
              <div>
                <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {diff.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {notFound.length > 0 && (
              <div>
                <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`not found failure: `}</span>
                <div className={styles['tips-content']}>
                  {notFound.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5
      };
      notification.open(args);
    }
    setValue('');
    setEditBundleList([]);
  };
  const handleOverWrite = () => {
    const newBundleList = [...new Set(editBundleList)].filter(v => v);
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];
    newBundleList.forEach(bundle => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });
    // 翻转数据
    const data = validBundle;
    handleOk(data);
    // 关闭弹窗
    handleCancel();
    if (invalidBundle.length > 0) {
      const args = {
        message: 'Overwrite',
        description: (
          <>
            {validBundle.length > 0 && (
              <div>
                <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {validBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {invalidBundle.length > 0 && (
              <div>
                <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`invalid: `}</span>
                <div className={styles['tips-content']}>
                  {invalidBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5
      };
      notification.open(args);
    }
    setValue('');
    setEditBundleList([]);
  };

  const handleSaveSingle = () => {
    if (isBidFloor) {
      const tmpMap = { ...floorMap };
      tmpMap![singleBundle] = bidFloor;
      setFloorMap!(tmpMap);
      handleOk(defaultList);
      onBidFloorChange!(tmpMap);
    } else {
      const newBundleList = [...new Set(defaultList)].filter(v => v);
      const findIndex = newBundleList.findIndex(bundle => bundle === currentEditBundle);
      if (findIndex > -1) {
        newBundleList[findIndex] = singleBundle;
      }
      handleOk(newBundleList);
    }
    handleCancel();
    setValue('');
    setEditBundleList([]);
  };

  const handleChangeValue = (val: { target: { value: string } }) => {
    if (isBidFloor) {
      console.log(val);
      setCountryValue(val as any);
      setEditBundleList(val as any);
    } else {
      if (isEdit) {
        setSingleBundle(val.target.value);
      } else {
        const tmp = val.target.value.split('\n').map(bundle => {
          const val = bundle.trim();
          return val.replaceAll(',', '');
        });
        const res = tmp.filter((item, index) => {
          return item !== '' || (!item && index === tmp.length - 1);
        });
        // 解决空值问题
        setValue(res.join('\n'));
        setEditBundleList(tmp.filter(bundle => bundle !== ''));
      }
    }
  };
  const handleBidChange = (num: any) => {
    setBidFloor(num);
  };

  return (
    <Modal
      title={isBidFloor ? 'Add Specific Country or Area' : title || 'Edit app ads List'}
      open={visible || isEdit}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={maskClosable}
      keyboard={keyboard}
      footer={
        <>
          {!isEdit && (
            <>
              <Button type="primary" onClick={handleAdd}>
                Add
              </Button>
              {!isBidFloor && (
                <>
                  <Button type="primary" onClick={handleDelete}>
                    Delete
                  </Button>
                  <Button type="primary" onClick={handleOverWrite}>
                    OverWrite
                  </Button>
                </>
              )}
            </>
          )}
          {isEdit && (
            <Button type="primary" onClick={handleSaveSingle}>
              Save
            </Button>
          )}
        </>
      }
    >
      {!isEdit && !isBidFloor && (
        <Input.TextArea
          rows={4}
          placeholder={tips || 'Enter the app ads (ad_domain,pub_acc_id,name)'}
          value={value}
          onChange={handleChangeValue}
        ></Input.TextArea>
      )}
      {isEdit && !isBidFloor && <Input value={singleBundle} onChange={handleChangeValue}></Input>}

      {isBidFloor && (
        <>
          <Select
            placeholder="Please Select Country"
            mode="multiple"
            filterOption={handleFilterSelect}
            onChange={handleChangeValue}
            value={countryValue as any}
            style={{ width: '100%' }}
            showArrow
            maxTagCount={20}
            allowClear
            popupClassName={styles['country-select']}
            disabled={isEdit}
          >
            {Object.keys(Countries).map((item: string, index: number) => {
              return (
                <Select.Option value={item} key={index} disabled={disabledList.includes(item)}>
                  {Countries[item]}({item})
                </Select.Option>
              );
            })}
          </Select>
          <Row style={{ marginTop: '16px' }}>
            <span style={{ lineHeight: '32px', paddingRight: '8px', color: '#5E6466' }}>Bid Floor:</span>
            <InputNumber
              style={{ width: '150px' }}
              addonAfter="$"
              min={0}
              precision={4}
              max={10000}
              value={bidFloor}
              onChange={handleBidChange}
            />
          </Row>
        </>
      )}
    </Modal>
  );
}

export default BundleInput;
