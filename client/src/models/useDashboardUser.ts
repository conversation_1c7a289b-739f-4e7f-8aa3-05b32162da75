/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-04 16:40:49
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-05-04 16:41:47
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getDashboardUser } from '@/services/api';

export default function useDashboardUser() {
  const { data, run, loading, error } = useCustomRequest(getDashboardUser, {
    cacheKey: 'dashboardUser-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
