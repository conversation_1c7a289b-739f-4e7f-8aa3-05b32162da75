

# 1上游FAQ

## 1.1技术对接问题

#### **Q1:上游对接方式有哪些？**

A1:ORTB 2.5协议。其他对接方式，请联系Rixengine商务同学。

#### **Q2:服务器节点分布区域？**

A2:USE（美东地区）、APSE（亚太地区-新加坡）、EUW（欧洲-荷兰）。

## 1.2业务问题

#### **Q1:广告出价是Net Price还是Gross Price？**

A1:默认Net Price。

#### Q2:支持的竞价类型（1 = 第一竞价，2 = 第二竞价）？

A2:都支持，可在平台配置，默认第一竞价。

#### Q3：流量是否支持过滤Max Bid Floor？

A3:支持。

#### Q4:广告的计费方式？

A4:默认采用ADM（JS）计费方式，也支持ADM（IMG-Pixel）、bURL计费。

#### Q5:是否支持lurl上报？

A5: 支持。

#### Q6:是否支持Supply Chain？

A6:支持，可在平台进行配置，默认关闭。

#### Q7:是否支持流量过滤功能？

A7:支持，可在Pretargeting中进行流量过滤配置。

#### Q8:是否支持针对流量的黑白名单操作？

A8:支持，可在平台进行配置。

#### Q9:最大可接受的响应时间是多少？

A9:默认为400ms，最大可接受的响应时间由下游决定，Tmax可在平台进行配置。

#### Q10:是否支持Deep Link/Univeral Link？

A10:支持。

#### Q11:是否支持报表API功能？

A11:支持，相关文档信息可在平台Advertiser账号管理【Account】页面中获取。

# 2下游FAQ

## 2.1技术对接问题

#### Q1:支持的对接方式有哪些？

A1:支持RTB、Prebid、SDK、Vast Tag，具体对接细节请查看技术对接文档。

#### Q2:EndPoint的分布区域？

A2:USE（美东）和APSE（亚太）。

#### Q3:是否支持测试？测试期间收益是否结算？

A3:支持，将publisher的status设置为 Testing，即为测试状态。 测试期间的收益不结算。

## 2.2业务问题

#### Q1:广告计费方式是什么？

A1:支持ADM和bURL，默认使用ADM（JS）计费方式。

#### Q2: 广告展示的有效时间是？支持修改吗？

A2:一小时，暂时不能修改，因为会影响平台统计数据归因。

#### Q3:是否支持报表API拉取数据？

A3:支持，相关文档信息可在平台Publisher账号管理【Account】页面中获取。

<!-- # 3客户FAQ

#### Q1:第三方流量检测工具与第三方素材检测工具的成本是怎么算的？

A1:请与Rixengine商务同学咨询。

#### Q2:服务费中的月度总百万广告请求数是什么？

A2:请求数指的是向Demand端发送的广告请求数，即Out Request或者数据看板勾选Advertiser维度的Request数量。

#### Q3:流量检测工具是？

A3：Pixalate和Human，该功能为增值服务，可联系Rixengine商务同学。

#### Q4:素材检测工具是？

A4:GeoEdge，该功能为增值服务，可联系Rixengine商务同学。 -->

# 3其他运营FAQ

#### Q1:策略配置中设置cap和QPS的区别是什么？

A1:QPS是转发给上游的每秒最大请求量，分region维度；cap的设置一般是为了规避结算风险。

#### Q2:支持一个请求返回多个广告吗？

A1:暂时不支持。

------

**更多运营技巧，请参照<a href='/help/operational-guide'>Operational Guide</a>**









