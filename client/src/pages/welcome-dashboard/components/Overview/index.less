.container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  min-height: 146px;
  gap: 12px;
  flex-wrap: wrap;
  .item-container {
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // max-width: 276px;
    flex: 1;
    padding: 16px 12px;
    border-radius: 4px;
    .left {
      .title {
        color: #5e6466;
      }
      .number {
        max-width: 100%;
        font-weight: bold;
        span:first-child {
          margin-right: 4px;
        }
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Satoshi Variable';
        font-size: 24px;
        color: var(--text-color);
        margin: 12px 0 8px 0;
      }
      .percentage {
        font-family: 'Satoshi Variable';
        line-height: normal;
        display: flex;
        font-weight: bold;
        align-items: center;
      }
    }

    .right {
      width: 108px;
      height: 62px;
    }
  }
}
