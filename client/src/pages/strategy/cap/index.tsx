/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:10:51
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-12 15:29:41
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { useModel, useAccess } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import { CapSearchOption, CapBreadOptions, CapColumns } from '@/constants/strategy/cap';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import EditButton from '@/components/Button/EditButton';
import { PlusOutlined } from '@ant-design/icons';
import AddCapModel from '../components/AddCapModel';
import { TopBarSearchItem } from '@/components/TopBar';
import { StatusMap } from '@/constants';
const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource, reload, loading } = useModel('useCapList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const tmpColumns: ColumnProps<StrategyAPI.CapListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      fixed: 'right',
      width: 100,
      render: (txt, params) => (
        <>
          <EditButton onClick={() => handleEditCap(params)} disabled={access.DisabledButton('updateCapAuth')}>
            Edit
          </EditButton>
        </>
      )
    }
  ];
  const columns = [...CapColumns, ...tmpColumns];
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [cap, setCurrentCap] = useState<StrategyAPI.CapListItem | undefined>(undefined);
  const [searchOptions, setSearchOptions] = useState(CapSearchOption);

  useEffect(() => {
    if (dataSource) {
      reload();
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    handleSearchOptions();
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    if (demandList) {
      const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      dOptions.unshift({ label: `All Advertisers`, value: 0 });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      sOptions.unshift({ label: `All Publishers`, value: 0 });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  };

  const handleEditCap = (params: StrategyAPI.CapListItem) => {
    setCurrentCap(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentCap(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={CapBreadOptions}>
      <FrontTable<StrategyAPI.CapListItem>
        pageTitle="Cap"
        isFold
        searchOptions={searchOptions}
        loading={dataSource && !dataSource.length && loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create Cap',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
            accessCode: 'addCapAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={80}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        initialValues={{ status: [StatusMap.Active] }}
      />
      <AddCapModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadCap={reload}
        cap={cap}
        demandList={demandList || []}
        supplyList={supplyList || []}
      />
    </PageContainer>
  );
};

export default Page;
