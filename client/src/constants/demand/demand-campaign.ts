import { Countries } from '../global-mapping/country';
import { <PERSON><PERSON><PERSON><PERSON> } from '../global-mapping/device-brand';
import { Levels } from '../global-mapping/level';

export const demandCampaign = Object.seal({
  Level: Levels,
  Country: Countries,
  Platform: {
    '1': 'Mobile/Tablet',
    '2': 'Personal Computer',
    '3': 'Connected TV',
    '4': 'Phone',
    '5': 'Tablet',
    '6': 'Connected Device',
    '7': 'Set Top Box',
    '0': 'Unknown'
  },
  MoblieOS: {
    '0': 'Undefined',
    '1': 'iOS',
    '2': 'Android',
    '3': 'Other',
    '4': 'Linux',
    '5': 'MacOS',
    '6': 'Windows',
    // OTT/CTV 预留11~29
    '11': 'tvOS',
    '12': 'Roku',
    '13': 'Amazon',
    '14': 'Microsoft',
    '15': 'Samsung Smart TV',
    '16': 'LG Smart TV',
    '17': 'Sony Playstation',
    '18': 'Vizio',
    '19': 'Philips Smart TV',
    '50': 'Tizen',
    '51': 'KaiOS'
  },

  // !这里更改过 之前是1 -> website , 2 -> inapp
  MoblieInventory: {
    '2': 'Website',
    '1': 'Inapp'
  },
  Category: {
    IAB1: 'Arts & Entertainment',
    IAB2: 'Automotive',
    IAB3: 'Business',
    IAB4: 'Careers',
    IAB5: 'Education',
    IAB6: 'Family & Parenting',
    IAB7: 'Health & Fitness',
    IAB8: 'Food & Drink',
    IAB9: 'Hobbies & Interests',
    IAB10: 'Home & Garden',
    IAB11: 'Law,Gov‘t & Politics',
    IAB12: 'News',
    IAB13: 'Personal Finance',
    IAB14: 'Society',
    IAB15: 'Science',
    IAB16: 'Pets',
    IAB17: 'Sports',
    IAB18: 'Style & Fashion',
    IAB19: 'Technology & Computing',
    IAB20: 'Travel',
    IAB21: 'Real Estate',
    IAB22: 'Shopping',
    IAB23: 'Religion & Spirituality',
    IAB24: 'Uncategorized',
    IAB25: 'Non-Standard Content',
    IAB26: 'Illegal Content'
  },
  AdPlatform: {
    // '1': 'Text Ads',
    '2': 'Image Ads',
    '3': 'Native Ads',
    '4': 'Video',
    // '5': 'Instream Midroll Video',
    // '6': 'Instream Postroll Video',
    // '7': 'Outstream Video',
    '8': 'Interstital Video',
    '9': 'Rewarded Video',
    '10': 'Interstital Banner'
  },
  Network: {
    0: 'Unknown',
    1: 'Ethernet',
    2: 'WIFI',
    3: 'Cellular Network – Unknown Generation',
    4: 'Cellular Network – 2G',
    5: 'Cellular Network – 3G',
    6: 'Cellular Network – 4G',
    7: 'Cellular Network – 5G'
  },
  Adformat: ['', 'Banner', 'Native', 'Video', 'Rewarded Video'],
  DeviceBrand,
  Type: {
    '0': 'Open Auction'
  },
  ServerRegion: ['', 'USE', 'APAC'],
  TimeSlot: {
    '0': '00-01',
    '1': '01-02',
    '2': '02-03',
    '3': '03-04',
    '4': '04-05',
    '5': '05-06',
    '6': '06-07',
    '7': '07-08',
    '8': '08-09',
    '9': '09-10',
    '10': '10-11',
    '11': '11-12',
    '12': '12-13',
    '13': '13-14',
    '14': '14-15',
    '15': '15-16',
    '16': '16-17',
    '17': '17-18',
    '18': '18-19',
    '19': '19-20',
    '20': '20-21',
    '21': '21-22',
    '22': '22-23',
    '23': '23-00'
  }
});
