/*
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 14:30:12
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-05 12:34:54
 * @Description: 数据库配置
 */

const config = Object.seal({
  port: 4090,
  database: {
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    // database: 'db_saas_prod',
    database: 'db_saas',
    timezone: 'UTC'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  encryptionStr: '8rL5Mbp5-', // 加密密钥
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  redisConfig: {
    expire_time: 48 * 60 * 60, // 48h
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
    is_cluster: false,
    optArray: [
      {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    BE_API_DAILY_LIMIT: 50
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  UpdateUserInfo_KEY: 'needUpdateInfo',
  NotifyFeishuUrl:
    'https://open.feishu.cn/open-apis/bot/v2/hook/60030935-5897-491d-bd6f-9f39145b9787',
  BoardCaCheTime: 60 * 10
});

export default config;
