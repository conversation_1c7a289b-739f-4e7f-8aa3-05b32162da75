
import useCustomRequest from '@/hooks/useCustomRequest';
import { getPmpInventoryList } from '@/services/api';

interface PmpInternalListProps {
  dataSource: StrategyAPI.PmpInternalListItem[];
  reload: () => void;
  loading: boolean;
}

export default function usePmpInternalList(): PmpInternalListProps {
  const { data, run, loading, error } = useCustomRequest(getPmpInventoryList, {
    cacheKey: 'pmp-internal-all-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data || [],
    reload: run,
    loading
  };
}
