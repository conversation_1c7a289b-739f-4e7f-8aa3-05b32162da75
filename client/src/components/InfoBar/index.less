.info-bar-tab {
  :global {
    .ant-tabs-nav {
      margin-bottom: 12px;
    }
    .ant-tabs-nav-list {
      .ant-tabs-tab {
        padding-top: 3px;

        &:hover {
          color: var(--primary-color);
        }

        &.ant-tabs-tab-active .ant-tabs-tab-btn {
          color: var(--primary-color);
        }
      }
      .ant-tabs-ink-bar {
        background: var(--primary-color);
      }
    }
    .ant-table {
      margin-top: 8px;
    }
    .ant-descriptions-small .ant-descriptions-row > th {
      padding-bottom: 0px;
    }
    .ant-descriptions-small .ant-descriptions-row > td {
      padding-bottom: 12px;
    }
  }

  .tab-tile {
    display: flex;
    align-items: center;
    gap: 6px;
    .title-icon {
      color: var(--primary-color);
      &:hover {
        cursor: pointer;
        color: var(--primary-color-hover);
      }
    }
  }
}
