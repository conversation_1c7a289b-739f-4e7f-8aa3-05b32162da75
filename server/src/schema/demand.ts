/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-19 15:50:43
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:05:59
 * @Description:
 */
import { SpecialProfitMinValue } from '@/constants';
import { ProfitModel } from '@/constants/supply';
import { isValidBundle } from '@/utils';
import Joi from 'joi';

const scheme = {
  buyer_name: Joi.string().required(),
  integration_type: Joi.number().integer().required(),
  profit_status: Joi.number().integer().required(),
  profit_ratio: Joi.when('profit_status', {
    is: 1,
    then: Joi.number().integer().min(SpecialProfitMinValue).max(100).required()
  }),
  rev_share_ratio: Joi.when('profit_model', {
    is: 2,
    then: Joi.number().integer().min(0).max(100).required()
  }),
  profit_model: Joi.number()
    .integer()
    .valid(...ProfitModel)
    .required(),
  idfa_required: Joi.number().integer().valid(1, 2).required(),
  filter_mraid: Joi.number().integer().valid(1, 2).required(),
  dp_id: Joi.number().integer(),
  omid_track: Joi.number().integer().required(),
  multi_format: Joi.number().integer().valid(1, 2).required()
};

export const AddDemandScheme = Joi.object(scheme);

export const UpdateDemandScheme = Joi.object({
  ...scheme,
  status: Joi.number().integer().valid(1, 2).required(),
  buyer_id: Joi.number().integer().required()
});

export const GetDemandEndpointScheme = Joi.object({
  buyer_id: Joi.number().integer()
});

export const GetPretargetCampaignScheme = Joi.object({
  buyer_id: Joi.number().integer()
});

export const SetDemandEndpointScheme = Joi.object({
  buyer_id: Joi.number().integer().required(),
  endpoint: Joi.string()
});

export const OpeartePretargetCampaignScheme = Joi.object({
  op: Joi.number().integer().valid(1, 2, 3).required(),
  buyer_id: Joi.number().integer().required(),
  campaign_id: Joi.number().integer().when('op', {
    // 编辑操作
    is: 2,
    then: Joi.number().integer().required()
  }),
  campaign_name: Joi.string()
    .when('op', {
      // 添加操作
      is: 1,
      then: Joi.string().required()
    })
    .when('op', {
      // 如果是删除操作
      is: 3,
      then: Joi.string()
    }),
  items: Joi.string().custom((value, helpers) => {
    try {
      const data: any[] = JSON.parse(value);
      // 验证bundle
      const bundle = data.find(v => +v.level === 10);
      let flag = true;
      if (bundle && bundle.length) {
        flag = bundle.every((v: string) => isValidBundle(v));
      }
      if (!flag) {
        return helpers.error('any.invalid');
      }
      return value;
    } catch (error) {
      return helpers.error('any.invalid');
    }
  }),
  status: Joi.number().integer().valid(1, 2, 3)
});

export const SetDemandAuthScheme = Joi.object({
  authIds: Joi.array().items(Joi.number().integer()),
  buyer_id: Joi.number().integer().required(),
  ori_seller_ids: Joi.array().items(Joi.number().integer())
});
