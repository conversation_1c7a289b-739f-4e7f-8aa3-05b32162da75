/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 10:58:30
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-17 15:41:27
 * @Description:
 */
import { Link } from '@umijs/max';
import { ColumnProps } from 'antd/es/table';
import StatusTag from '@/components/Tag/NewStatusTag';
import { ChannelType, DeviceType, RelationshipType, RevTrackTypeDesc } from './index';
import { DemandAndSupplyStatusDesc, StatusDesc } from '@/constants';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { ProfitModelTypeToString, ProfitModelType } from '@/constants';

export const SupplyColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    width: 150,
    render: (text, row) => (
      <HoverToolTip title={text}>
        <div style={{ textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}>
          <Link to={`/supply/publisher/detail?id=${row.seller_id}`} state={{ row }} rel="noreferrer">
            {text}
          </Link>
        </div>
      </HoverToolTip>
    )
  },
  {
    title: 'ID',
    dataIndex: 'seller_id',
    width: 100
  },
  {
    title: 'Partner',
    width: 150,
    dataIndex: 'partner_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher ID',
    dataIndex: 'publisher_id',
    width: 150,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    width: 150,
    title: 'Integration Type',
    dataIndex: 'integration_type_desc'
  },
  {
    title: 'Channel Type',
    width: 120,
    dataIndex: 'channel_type',
    render: text => <>{ChannelType[text]}</>
  },
  {
    title: 'Relationship',
    width: 120,
    dataIndex: 'relationship',
    render: text => <>{RelationshipType[text]}</>
  },
  {
    title: 'Device Type',
    width: 120,
    dataIndex: 'device_type',
    render: text => <>{DeviceType[text]}</>
  },

  {
    title: 'Revenue Tracking Type',
    width: 180,
    dataIndex: 'rev_track_type',
    render: text => <>{RevTrackTypeDesc[text]}</>
  },
  {
    title: 'Support OMID',
    width: 180,
    dataIndex: 'support_omid',
    render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
  },
  {
    title: 'Profit Model',
    width: 130,
    dataIndex: 'profit_model',
    render: text => <>{ProfitModelTypeToString[text]}</>
  },
  {
    title: 'Profit(%)',
    width: 100,
    dataIndex: 'profit_ratio',
    render: text => <>{text}</>
  },
  // {
  //   title: 'Rev Share(%)',
  //   width: 120,
  //   dataIndex: 'rev_share_ratio',
  //   render: (text, row) => (text && row.profit_model === ProfitModelType['Rev Share'] ? `${text}` : '-')
  // },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status',
    render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
  },
  {
    title: 'Banner Multi Size',
    width: 150,
    dataIndex: 'banner_multi_size',
    render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
  },
  {
    title: 'Native Size Filter',
    width: 150,
    dataIndex: 'native_strict_validation',
    render: status => {
      return status !== '-' ? <StatusTag value={status} statusDescMap={StatusDesc} /> : '-';
    }
  },
  {
    title: 'Adomain Filter',
    width: 150,
    dataIndex: 'adomain_filter',
    render: status => {
      return status !== '-' ? <StatusTag value={status} statusDescMap={StatusDesc} /> : '-';
    }
  },
];

export const SupplyCardColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name'
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc'
  },

  {
    title: 'Channel Type',
    dataIndex: 'channel_type',
    render: (text: any) => <>{ChannelType[text]}</>
  },
  {
    title: 'Relationship',
    dataIndex: 'relationship',
    render: (text: any) => <>{RelationshipType[text]}</>
  },
  {
    title: 'Device Type',
    dataIndex: 'device_type',
    render: (text: any) => <>{DeviceType[text]}</>
  },
  {
    title: 'Status',
    dataIndex: 'status',
    render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
  }
];

export const AuthorizationColumns: ColumnProps<SupplyAPI.SellerDemandAuth>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'ID',
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  }
];

export const profileRadioColumn: ColumnProps<SupplyAPI.SupplyListItem> = {
  title: 'Rev Share(%)',
  width: 120,
  dataIndex: 'rev_share_ratio',
  render: (text, row) => (text && row.profit_model === ProfitModelType['Rev Share'] ? `${text}` : '-')
};
