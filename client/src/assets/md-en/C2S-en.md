# 「RixEngine」 C2S（client to server）API Integration Document (For embedded self-developing Android SDK) 

## Integration and Implementation Document (For embedded self-developing apps/ads SDK/API document)

> LAST UPDATED 2023-8-12

### 1. Request URL

- Testing Endpoint: ` http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d`
- Formal Endpoint: `https://{subdomain}.svr.rixengine.com/rtb?sid={YOUR_SID}&token={YOUR_TOKEN}`
- `{subdomain}，{YOUR_SID}，{YOUR_TOKEN}`，app_id（Application ID），adslot_id(Ad slot ID). Those are generated by the platform, please contact the operation team for access.
- Testing endpoint can be tested with the following test parameters:

```Java
public static String *SID*= "36057"; // sid
public static String *TOKEN*= "c976563eb2f1f134222d2730294e552d"; // token

public static String *APP_ID*= "102512"; // app_id
// 4 types of ad units
public static String *BANNER_AD_PID*= "201304"; //banner AD id
public static String *NATIVE_AD_PID*= "201307";//native AD id
public static String *REWARD_VIDEO_AD_PID*= "201306";//reward video AD id
public static String *INTERSTITIAL_AD_PID*= "201299";//interstitial AD id
```

### 2. Request Method

- Method: `POST`
- Header: Content-Type Value: `application/json`
- This is similar to the oRTB protocol used to simplify integration and is suitable for application development integration

### 3. Request Parameters

| **Parent Object** | **Object/Parameter** | **Type** | **Required** | **Description** |
| --- | --- | --- | --- | --- |
|  | id | `string` | Yes | Unique bid request ID, generated by sdk |
|  | app_id | `string` | Yes | Unique identifier of the app in the **RixEngine system** |
|  | adslot_id | `string` | Yes | Unique identifier for the ad slot in the **RixEngine system** |
|  | adtype | `Int` | Yes | <a href="#2. Ad Type">Ad Type<a/>，refers to the list for details |
|  | sdkv | `string` | Yes | sdk version number（Only contains numbers and "." symbol） |
|  | omidpv | `string` | No | omsdk partner version, version number certified by IAB, such as "3.0.0" |
|  | bundle | `string` | Yes | App bundle name |
|  | app_name | `string` | Yes | App name |
|  | device | `object` | Yes | Device information |
| device | user_agent | `string` | Yes | user agent |
| device | did | `string` | No | IMEI |
| device | dpid | `string` | No | Android ID |
| device | mac | `string` | No | MAC address |
| device | ifa | `string` | Yes | Identifier for Advertising |
| device | oaid | `string` | No | Open Anonymous Device Identifier |
| device | geo | `object` | No | geo information |
| device.geo | latitude | `float` | No | Latitude of the device |
| device.geo | longitude | `float` | No | Longitude of the device |
| device.geo | country | `string` | No | Country Code [ISO-3166-1-alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) |
| device.geo | region | `string` | No | Region code using [ISO-3166-2](https://en.wikipedia.org/wiki/ISO_3166-2) |
| device | devicetype | `int` | Yes | Device Type. Refer to list <a href="#3. Device Type">Device Type</a> |
| device | language | `string` | No | Language [ISO-639-1-alpha-2](https://en.wikipedia.org/wiki/List_of_ISO_639-1_codes) |
| device | os | `int` | Yes | Device operating system “1” = IOS , “2” = Android, “0” = unknown |
| device | osv | `string` | Yes | System Version |
| device | make | `string` | No | Brand of the device |
| device | model | `string` | No | Model of the device |
| device | screen_width | `int` | No | The physical width of the screen in pixels |
| device | screen_height | `int` | No | The physical height of the screen in pixels |
| device | screen_density | `int` | No | Screen pixels |
| device | connectiontype | `int` | Yes | Network Type “1” = WI FI, “2” = 2G, “ 3” = 3G, “4” = 4G, “5” = 5G, “0” = unknown |
| regs | coppa | `int` | No | Flag indicating if this request is subject to the COPPA regulations established by the USA FTC, where 0 = no, 1 = yes |
| regs | gdpr | `int` | No | Flag indicating if this request is subject to the GDPR regulations established by the EU, where 0 = no, 1 = yes |
| regs | gdpr_consent | `string` | No | User consent information |
| regs | us_privacy | `string` | No | Applicable to requests subject to CCPA regulations. Must follow the U.S. Privacy string format per IAB spec. This attribute is always passed when applicable to affected regions](https://github.com/InteractiveAdvertisingBureau/USPrivacy/blob/master/CCPA/US%20Privacy%20String.md) |

##### 3.1 Request Examples

```JSON
{
  "id": "fgasikutgasurebuifgsiuafgisua",
  "app_id": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
  "adslot_id": "171764",
  "sdkv": "3.0.0",
  "adtype": 1,
  "device": {
      "user_agent": "Mozilla/5.0 (Linux; U; Android 2.3.6; it-it; GT-S5570I Build/GINGERBREAD) AppleWebKit/533.1 (KHTML, like Gecko) Version/4.0 Mobile Safari/533.1 (Mobile; afma-sdk-a-v6.1.0),gzip(gfe)",
      "did": "2536643785678345673845",
      "dpid": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "mac": "5C-FF-35-0B-95-B5",
      "ifa": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "oaid": "dfc59c16-4435-48cb-98a9-9bdd06108077",
      "geo": {
          "lat": 27.3333,
          "lon": 88.6167,
          "country": "USA",
          "region": "CA"
      },
      "devicetype": 1,
      "language": "CN",
      "os": 2,
      "osv": "9.0.1",
      "make": "xiaomi",
      "model": "hongmi",
      "screen_width": 640,
      "screen_height": 1136,
      "screen_density": 326,
      "connectiontype": 4
  },
  "regs": {
      "coppa": 0,
      "gdpr": 1,
      "gdpr_consent": "",
      "us_privacy": "1YN-"
  }
}
```

### 4. Response Parameters

| **Parent Object** | **Obeject/Parameter** | **Type** | **Required** | **Description** |
| --- | --- | --- | --- | --- |
|  | err_no | `int` | Yes | Error Codes. Refer to list <a href="#4. Error codes and error messages">Err No and Msg</a> |
|  | err_msg | `string` | Yes | Error messages |
|  | data | `object` | No | Ad information |
| data | id | `string` | Yes | Ad response unique ID |
| data | ads | `object array` | Yes | Array ads |
| data.ads | adm_type | `int` | Yes | Ad data format, refers to <a href="#1. Ad data format">List</a> |
| data.ads | crid | `string` | Yes | Creative id |
| data.ads | cid | `string` | Yes | Campaign id |
| data.ads | adomain | `string array` | No | Ad domain |
| data.ads | cat | `string array` | No | Ad categories |
| data.ads | bundle | `string` | No | Ad bundle name |
| data.ads | adm | `string` | Yes | Ad formats. Banner: html; Video: Vast; Native: html or JSON (Need to replace ${AUCTION_PRICE} with the actual price) |
| data.ads | width | `int` | Yes | Width of the ad |
| data.ads | height | `int` | Yes | Height of the ad |
| data.ads | imptrackers | `array` | Yes | Array impression tracker (There is no need to report data that is empty), Need to replace ${AUCTION_PRICE} with the actual price |
| data.ads | clicktrackers | `array` | Yes | Array click tracker (There is no need to report data that is empty), Need to replace ${AUCTION_PRICE} with the actual price |
| data.ads | deeplink | `string` | No | deeplink |
| data.ads | video_ext | `object` | No | video additional field |
| data.ads. video_ext | skip | `bool` | No | Indicates if the player will allow the video to be skipped (Response only when adtype is video) |
| data.ads. video_ext | skipafter | `int` | No | Number of seconds a video must play before skipping, only for skippable ads (Response only when adtype is video) |
| data.ads. video_ext | mute | `bool` | No | Whether to play silently by default |
| data.ads. video_ext | close | `bool` | No | Used to indicate whether rewarded videos are allowed to be closed |
| data.ads | banner_ext | `object` | No | banner additional fields |
| data.ads | native_ext | `object` | No | native ad additional fields |
| data.ads. native_ext | omid | `object` | No | omsdk related fields |
| data.ads. native_ext. omid | vendorKey | `string` | Yes | Key |
| data.ads. native_ext. omid | javascriptResourceUrl | `string` | Yes | To verify script address |
| data.ads. native_ext. omid | verificationParameters | `string` | Yes | To verify parameters |
| data.ads. native_ext | asset_type | `int` | No | <a href="#5. Native asset type">Asset type</a>, unknown as default |
| data.ads. native_ext | source | `string` | No | Source of the ad, empty as default |
| data.ads | price | `float` | No | Bid price in USD. When price is 0, it means no bid is returned, so there is no need to replace ${AUCTION_PRICE} |
| data.ads | nurl | `string` | No | Bidding success notification url, need to replace ${AUCTION_PRICE} with the actual price |
| data.ads | burl | `string` | No | Billing notification url, you need to replace ${AUCTION_PRICE} with the actual price |

### 5. Ad Request Samples (All source codes involved below can be used as a reference to modify and develop)

#### 5.1 Banner Request Sample

 banner request url

```apl
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 banner request parameter

```JSON
{
    "id": "b4f6dba2-70a5-4edf-a264-8ab8486295db|1676448348578",
    "adtype": 1,
    "app_id": "102512",
    "adslot_id": "201304",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 banner response

```JSON
{
    "data": {
        "id": "b4f6dba2-70a5-4edf-a264-8ab8486295db|1676448348578",
        "ads": [
            {
                "cid": "249",
                "bundle": "algorix.co",
                "adm": "<!DOCTYPE html><html><head> <meta charset='UTF-8'> <meta content='width=device-width, initial-scale=1.0' name='viewport'> <meta content='ie=edge' http-equiv='X-UA-Compatible'> <title></title></head><body><style> body {  margin: 0 } .mbm {  position: absolute;  top: 0;  left: 0;  width: 100%;  height: 100%;  background: url('https://ww0.svr-algorix.com/pic/b591ff9d6cf836e8494846579391af66.jpg') center no-repeat;  background-size: contain } .mbmad {  font-size: 12px;  position: absolute;  top: 0;  left: 0;  color: #eae3e3;  background: #333;  z-index: 9999;  opacity: 0.5; }</style><div> <span class=mbmad>AD</span> <img src='https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwC3ywWvTAmh7jDeA3ftgi0unrRaczw0tmhGZHPDZo43FrzAu8HJHWBPHLSQKRx7nPUM96HZB_Bp5MdsqYz2YjsvL8L2z5ShPtZ3gnXE0qt4dcmGfgon7HjqCcq0ckOXZnOUMKSQT5kuvn-G0y0nMmwKckhzLgzWxaRP-cnVhRAVZNRR__g-D28CatW3ec-UQ-wgTKU3DyKZfpNghZcxmlHcyCQzO_9zGDG1k15ssLMjdbWaboO5t7MRO5wQ1HunkHXc1e_1tWUdPGbjj4565tbC2ilqOVGHbMTNJWr8cBw9qk6iXJyjv1wWBZ8hW7XiGuF4scLeZudrH_nLAKmeZJ13CIQ6kksXzLEYJ03l8MyH8-CR9lMC5XzRk8Hynli1VKE4nmztOtbApC6feI2o_cEmoia-3JAMkxXIxzCOBMx-yCi7eoUsoh8rvhrporI6Xg%3D%3D' width='0' height='0' style='display:none'>  <a class=mbm id=imgbtn></a> <script>  document.getElementById('imgbtn').addEventListener('touchstart', function (e) {   e.preventDefault;   new Image().src = '{adx_click_url}';   new Image().src = 'https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwC3ywWvTAmh7jDeA3ftgi0unrRaczw0tmhGZHPDZo43FrzAu8HJHWBPHLSQKRx7nPUM96HZB_Bp5MdsqYz2YjsvL8L2z5ShPtZ3gnXE0qt4dcmGfgon7HjqCcq0ckOXZnOUMKSQT5kuvn-G0y0nMmwKckhzLgzWxaRP-cnVhRAVZNRR__g-D28CatW3ec-UQ-wgTKU3DyKZfpNghZcxmlHcyCQzO_9zGDG1k15ssLMjdbWaboO5t7MRO5wQ1HunkHXc1e_1tWUdPGbjj4565tbC2ilqOVGHbMTNJWr8cBw9qk6iXJyjv1wWBZ8hW7XiGuF4scLeZudrH_nLAKmeZJ13CIQ6kksXzLEYJ03l8MyH8-CR9lMC5XzRk8Hynli1VKE4nmztOtbApC6feI2o_cEmoia-3JAMkxXIxzCOBMx-yCi7eoUsoh8rvhrporI6Xg%3D%3D';      setTimeout(() => {    top.location = 'https://www.algorix.co/';   }, 50);  }, true); </script></div></body></html><img src=\"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}\" width=\"1\" height=\"1\" style=\"display:none;\"><script type=\"text/javascript\">!function(){(new Image).src=\"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}\"}();</script><script type=\"text/javascript\">!function(){(new Image).src=\"https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXmHzMWNJa9\"}();</script>",
                "width": 320,
                "height": 50,
                "price": 0.109,
                "adm_type": 1,
                "crid": "590_8179",
                "adomain": [
                    "algorix.co"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
                ],
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9O2vzWa8ppKgQd6pm12hYAGTZUx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_ycinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tPI802z-efN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCpm82J98TvGygi4Tm-Lz5utvPT1CHtYnyiW5lq1SPxZUPwEtJDOeBDM3XxVDFzURjncpxAAs8vVd3gCR4X34ScMCcbgOOgcOBqPBrM5ns05U0V3mmf7CRXPQlzBU2kykwcZVDe3M6TcIwR8ejAw1EDrumDOn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZj5SlDmPvLXzR74JbRbrl5YX_RL5i3Olg2hc0RVHWmm-o_4BG5Nm69-ryR9EJaKYDaAxbGyjz6sLppu53F3QUpmhEylwbfK_zIzqt8xOk__1ooiWRJsb3JjXhIkfcsBRgd7rD-xfaUSN74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb-6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_NYpNy4WTKukKZmGkRGw6PNth0IKFHdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaFk7KQMa-TbBnMueXDlFEoGSJc0gJD6ZuOoWPgR_4cxziKf5y3ALj2CYb-zmJpYib08uJf3mvfIlPMMDI_o0MpgVjO9e2vzWa8ppKgQd6pm12hYAGTZEx_JSa5sGssACp7WAthjTFAJjhOyY-8jfBT3gSIdcuQ7Oppcr27TZKoxY_zcinIq3YDrUVwlsxYcHr8yrcCNjfBBfa6JEQGpvcC&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.2 Rewarded Video Request Sample

 Rewarded video request url

```api
https://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 Rewarded video request parameter

```JSON
{
    "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
    "adtype": 4,
    "app_id": "102512",
    "adslot_id": "201306",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 Rewarded video response

```JSON
{
    "data": {
        "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
        "ads": [
            {
                "cid": "227",
                "bundle": "com.lazada.android",
                "adm": "
<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    <Ad id="AlgoriX-324ade2d8c53e5b37571aa04588a9de6">
        <InLine>
            <AdSystem>Algorix</AdSystem>
            <AdTitle>BELI SEKARANG</AdTitle>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
            </Impression>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=impress]]>
            </Impression>
            <Error>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=[ERRORCODE]]]>
            </Error>
            <Creatives>
                <Creative>
                    <Linear>
                        <Duration>00:00:20</Duration>
                        <MediaFiles>
                            <MediaFile width="1280" height="720" type="video/mp4" bitrate="1179" delivery="progressive" scalable="true" maintainAspectRatio="true">
                                <![CDATA[https://ww0.svr-algorix.com/pic/bf9c24d0b527d1598803dc35248633e8.mp4]]>
                            </MediaFile>
                        </MediaFiles>
                        <TrackingEvents>
                            <Tracking event="start">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=start]]>
                            </Tracking>
                            <Tracking event="midpoint">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=midpoint]]>
                            </Tracking>
                            <Tracking event="firstQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=firstQuartile]]>
                            </Tracking>
                            <Tracking event="thirdQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=thirdQuartile]]>
                            </Tracking>
                            <Tracking event="complete">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=complete]]>
                            </Tracking>
                        </TrackingEvents>
                        <VideoClicks>
                            <ClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </ClickThrough>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=click]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
                            </ClickTracking>
                        </VideoClicks>
                    </Linear>
                </Creative>
                <Creative>
                    <CompanionAds>
                        <Companion width="1280" height="720">
                            <StaticResource creativeType="image/png">
                                <![CDATA[https://ww0.svr-algorix.com/pic/449a2920e990ea5beff62b6295c97c8c.png]]>
                            </StaticResource>
                            <CompanionClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </CompanionClickThrough>
                            <TrackingEvent>
                                <Tracking event="creativeView">
                                    <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=companioncreativeView]]>
                                </Tracking>
                            </TrackingEvent>
                            <CompanionClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
                            </CompanionClickTracking>
                        </Companion>
                    </CompanionAds>
                </Creative>
            </Creatives>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXnHzMWNJa9]]>
            </Impression>
        </InLine>
    </Ad>
</VAST>
",
                "width": 1280,
                "height": 720,
                "price": 1.009,
                "adm_type": 3,
                "crid": "515_2143",
                "adomain": [
                    "lazada.sg"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
                ],
                "video_ext": {
                    "skip": false,
                    "mute": false,
                    "close": false,
                    "skipafter": 5
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z_Mo-8lkibZkz2JgUxKCeU9j5xTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-RsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIs43x-2eN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtn8GR88D_Gygi4Tm-Lz5utvPb0WCoLxCmT4wbnGvxfWPIFtMbHLRCajiRVWF7WQDiBpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZD5SlDmPvLXzR74JbRbrkpYX_RL5i3Olg2hc0RdHUnWw4Ps6LJ5q5dC3nwdLIryKRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRJsb3JjXhIkfcsBRgd7rD-xfaUSM74NBXihuLfivPhRMjqzhi3O1Sd1ldG4r4Hb_6tSBaC8kJI7fsRTEtCEvc1_At_RmumfLLL3MN_JYpNy4WTKukKZmGkRGw6PNth0JKFDdJZHSWtqt03rjEXOy9ttl6egrg8HSMsmsuU7GjXkuA60gxOgdPKJ9_q3nXRW_H202cRP6oGaDnbKQMa-TbBnMueXDlFEoGSpZ0ghD6pGOoWPgR_4cxziKf5y3ALj2C4X0j3xhYGnz_O4Skn_MNGXcfUF_pls1rA6bm6P4xGK329z-Mo-8lkibZkz2JgUxKCeU9j5wTWF0Hm5ewD1NKDkemqywgv4L4kzLKd2Uq94zNZC4XJiskdCcOHnUozhV-BsNkchTPiW_ueEBJGyeRoXsJ1Zd-bQgihT65NV1gQ==&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

###### Developers need to develop their VAST protocol parser and embedded video player and refer to the official standards of the IAB (Important):

https://iabtechlab.com/standards/vast/

The VAST protocol needs to support at least 2.0, preferably 3.0, 4.0

Both rewarded and interstitial video ads require the VAST protocol parser and embedded  video player

#### 5.3 Interstitial Request Sample

 Interstitial request url

```api
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 Interstitial request parameter

```JSON
{
    "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
    "adtype": 3,
    "app_id": "102512",
    "adslot_id": "201299",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 Interstitial response

```JSON
{
    "data": {
        "id": "34e2f577-9656-45c1-9e44-4dd347d13077|1676541181537",
        "ads": [
            {
                "cid": "227",
                "cat": [
                    "IAB24"
                ],
                "bundle": "com.lazada.android",
                "adm": "
<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    <Ad id="AlgoriX-46b8f8b972c42d012a4b85a56af7a8fb">
        <InLine>
            <AdSystem>Algorix</AdSystem>
            <AdTitle>BELI SEKARANG</AdTitle>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
            </Impression>
            <Impression>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=impress]]>
            </Impression>
            <Error>
                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=[ERRORCODE]]]>
            </Error>
            <Creatives>
                <Creative>
                    <Linear>
                        <Duration>00:00:20</Duration>
                        <MediaFiles>
                            <MediaFile width="480" height="320" type="video/mp4" bitrate="834" delivery="progressive" scalable="true" maintainAspectRatio="true">
                                <![CDATA[https://ww0.svr-algorix.com/pic/100ef65516fa50aafd5676ae6f6b5fed.mp4]]>
                            </MediaFile>
                        </MediaFiles>
                        <TrackingEvents>
                            <Tracking event="start">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=start]]>
                            </Tracking>
                            <Tracking event="midpoint">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=midpoint]]>
                            </Tracking>
                            <Tracking event="firstQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=firstQuartile]]>
                            </Tracking>
                            <Tracking event="thirdQuartile">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=thirdQuartile]]>
                            </Tracking>
                            <Tracking event="complete">
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=complete]]>
                            </Tracking>
                        </TrackingEvents>
                        <VideoClicks>
                            <ClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </ClickThrough>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=click]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow]]>
                            </ClickTracking>
                            <ClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
                            </ClickTracking>
                        </VideoClicks>
                    </Linear>
                </Creative>
                <Creative>
                    <CompanionAds>
                        <Companion width="480" height="320">
                            <StaticResource creativeType="image/jpeg">
                                <![CDATA[https://ww0.svr-algorix.com/pic/9a504019771756e8db6dc81ae82a39e9.jpg]]>
                            </StaticResource>
                            <CompanionClickThrough>
                                <![CDATA[https://www.lazada.sg?clickid=8FB5__AD_IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057]]>
                            </CompanionClickThrough>
                            <TrackingEvent>
                                <Tracking event="creativeView">
                                    <![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwCwzgqsSw6n7TTaACDtgnt6xbFWIGs3sDpCZHDGaN42SrzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5epPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwiY3352xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcdgwiXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=companioncreativeView]]>
                                </Tracking>
                            </TrackingEvent>
                            <CompanionClickTracking>
                                <![CDATA[https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
                            </CompanionClickTracking>
                        </Companion>
                    </CompanionAds>
                </Creative>
            </Creatives>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}]]>
            </Impression>
            <Impression>
                <![CDATA[https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXnHzMWNJa9]]>
            </Impression>
        </InLine>
    </Ad>
</VAST>
",
                "width": 480,
                "height": 320,
                "price": 1.009,
                "adm_type": 3,
                "crid": "515_2141",
                "adomain": [
                    "lazada.sg"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
                ],
                "video_ext": {
                    "skip": true,
                    "mute": true,
                    "close": false,
                    "skipafter": 5
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9bjPN2tjm6wfXzaMAwxITDE7jRtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBe5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIco0we-XN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk9Gd6-z3Gygi4Tm-Lz5utvPalCHxYxSzGt1q0SfwNUPEFtZfGLBDLj3ECXFuFRDnXpxAAs8vVd3gCR4X34ScMCZfgO-web0yKBr0_ys45UxVwzWejXhfPQlDEVWIynFUaXjLkZqTcIwR8ezA51kziumTBn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s-Xaz5SlDmPvLXzR74JbRbrlZYX_RL5i3Olg2hc0RJNWmm-o_4BG5Nm69-ryR9CJ76WS6Eic2al9YAGr47-xFfMXMi3Gil5et-Cx4HRjsZNk-KluoKJWZVby5TLk6Qiec1quNToD_EPcU-T8INBXTZgbe-jIgNhv6fwoF6iWs5ub35s-H3g9deCazEqZJnTrQPpjDQyb1_smuN1qWzQPPrUPO1Hp9-7Rzzum6tdNUZdyaWKrhYWNlDUMJ-DQdO-xEDOBmCb28hw9O5mm8rTMtWi60zbhHYzKJ8iz-kUcrp246O3SgOTI2c7NwvisGaGiLyeYbiFQCXdouiFjEk_HS4zvwtG74CArzP3UdIh0COAd5XwGKC3B4e3zXF6e2z2s6EdmGnRMmiac08tt0MThxWrprT02HXnw9fjPN2tjm6wfXzaMAwxITDE7jVtQyJxJVVVwjFFL3UG-9X-j-UQ50mEdMnR1K89ZYe-WpSzguqlLGTW7CBf5BVch8FTNzKSkvQHYXSVW4usMFpB7pkLnxK__N45&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.4 Native Ads Request Sample

 Native ads request url

```Plain
http://demo.use.svr.rixengine.com/rtb?sid=36057&token=c976563eb2f1f134222d2730294e552d
```

 Native ad request parameter

```JSON
{
    "id": "0f1eb423-0e05-42c1-944b-b4fab3f17f14|1676448436983",
    "adtype": 5,
    "app_id": "102512",
    "adslot_id": "201307",
    "sdkv": "3.5.1",
    "bundle": "com.rixengine.demo",
    "app_name": "Rixengine SDK Demo",
    "app_version": "1.0",
    "screen_orientation": 1,
    "device": {
        "user_agent": "Mozilla/5.0 (Linux; Android 12; Redmi K30 5G Build/SKQ1.211006.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.118 Mobile Safari/537.36",
        "did": "",
        "dpid": "",
        "mac": "",
        "ifa": "20eed27b-397b-47e9-964c-73e71b8d9ee7",
        "oaid": "e935372333dd9651",
        "geo": {
            "country": "CHN",
            "region": "CN"
        },
        "device_type": 1,
        "language": "zh",
        "os": 2,
        "osv": "12",
        "make": "Redmi",
        "model": "Redmi K30 5G",
        "carrier_id": "",
        "connectiontype": 1,
        "screen_width": 1080,
        "screen_height": 2261,
        "screen_density": 440
    },
    "regs": {
        "coppa": 0,
        "gdpr": 0,
        "gdpr_consent": "",
        "us_privacy": "1YN-"
    }
}
```

 Native ad response

```JSON
{
    "data": {
        "id": "0f1eb423-0e05-42c1-944b-b4fab3f17f14|1676448436983",
        "ads": [
            {
                "cid": "103",
                "bundle": "test.bestai.TextScanner",
                "adm": "
{
    "title": {
        "value": "Text Scanner"
    },
    "cta": {
        "value": "MORE"
    },
    "desc": {
        "value": "A scanner app, using OCR technology."
    },
    "icon": {
        "width": 50,
        "url":"https://ww0.svr-algorix.com/pic/5e52f36602b3e7488ca5b3bbb4c84935.png",
        "height": 50
    },
    "main": [
        {
            "width": 1200,
            "url":"https://ww0.svr-algorix.com/pic/22f6310cc6a0da84c91feae6fbe0a5c0.jpg",
            "height": 627
        }
    ],
    "link": {
        "url":"https://play.google.com/store/apps/details?id=com.bestai.TextScanner&clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D_{info}&advertising_id=20eed27b-397b-47e9-964c-73e71b8d9ee7&af_siteid=36057"
    }
}
",
                "width": 1200,
                "height": 627,
                "price": 0.109,
                "adm_type": 2,
                "crid": "160_8124",
                "adomain": [
                    "test.bestai.TextScanner"
                ],
                "imptrackers": [
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://d-s.svr-algorix.com/dsp/rtb/impress?c=IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D",
                   "https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://demo.use.svr.rixengine.com/tracker/cap?info=Lg_I1nZkn7pJZFd3UXaWePM3TuI6hPxocStZKcgzxuuQN1ap1KH3jg1uGiwXAdgqu6hachgrCsFPnc6i-xngtFNlaPXmHzMWNJa9"
                ],
                "clicktrackers": [
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                   "https://d-s.svr-algorix.com/dsp/rtb/click?c=IuzIGWir_uT1O3h_qwDlnQn6Ql2g6TeNUyTthS19nbAAcDsysT9EMnHKat46FrzAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOvcE56dTF2UAMctMfrLt4Wy8RYtumP4rDGKd_BrQkSlHfb59ygpAihUOci29YLK8qeHbrxgE5pastNKmFI7OlobITOddEkza7mTCMkLX5vWNWaCH_zI90oMjX0WI4IlzHc8CfJTfjfkgk-lqiDdSguVsXTJ9yCuHtAfB09tGGfLJiSbLNEbCSPN1hHJc9kloV3-BEbSi8qYeU_-GF40BXuCy1zJi7ngf8EsxgxD36MtHFuDzWIpuV-Ngm8kjti4IGy0Xby3DYB9dG3SrrAoQwsxZivkb0vLM6UR15g3AEp113MQFO6JE%3D",
                   "https://use.trk.rixengine.com/clk?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}"
                ],
                "native_ext": {
                    "omid": {
                        "vendorKey": "iabtechlab.com-omid",
                        "javascriptResourceUrl":"https://s3-us-west-2.amazonaws.com/updated-omsdk-files/compliance-js/omid-validation-verification-script-v1-ALGORIXCO-06272022.js",
                        "verificationParameters": "iabtechlab-Algorixco"
                    },
                    "asset_type": 1
                },
                "nurl":"https://use.trk.rixengine.com/win?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Ig-LJQEkGzaJi3ckDS9wCBMAn3wOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerepdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}",
                "burl":"https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArD3tOIc4wx-2RN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMLwCtk8GN8_znGygi4Tm-Lz5utvKWmDy4NyS3EsA7hHPxcXKAE75TNLxeejnJUDF6FFT-GpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=${AUCTION_PRICE}"
            }
        ]
    },
    "err_no": 1000,
    "err_msg": "success"
}
```

#### 5.5 Native Creative Adm Parameter Format

| **Parent Object** | **Obeject/Parameter** | **Type** | **Required** | **Description** |
| ---------- | -------- | -------------- | -------- | -------------------------------- |
|            | title    | `object`       | No       | Title                           |
| title      | value    | `string`       | Yes       | Title text, up to 90 bytes         |
|            | cta      | `object`       | No       | "call to action" button            |
| cta        | value    | `string`       | Yes       | Button text, up to 15 bytes        |
|            | desc     | `object`       | No       | Description                        |
| desc       | value    | `string`       | Yes       | Description text, up to 140 bytes          |
|            | icon     | `object`       | No       | icon image, ratio 1:1              |
| icon       | url      | `string`       | Yes       | icon image link                    |
| icon       | width    | `int`          | Yes       | icon size (unit: px)             |
| icon       | height   | `int`          | Yes       | icon size (unit: px)             |
|            | main     | `object array` | No       | Large image                          |
| main       | url      | `string`       | Yes       | Large image url                    |
| main       | width    | `int`          | Yes       | Large image width, usually 1200 (unit: px) |
| main       | height   | `int`          | Yes       | Large image height, usually 627 (unit: px) |
|            | link     | `object`       | Yes       | Landing page after clicking               |
| link       | url      | `string`       | Yes       | Landing page url                       |

### 6. Ads Processes and Rendering Methods

#### 6.1 Banner

1. **Add AlxBannerView (banner reference name) to the layout**

To display a banner ad, first place an **AlxBannerView** into the layout of the `Activity` or `Fragment` that you wish to use to display the ad. The easiest way is to add one to the corresponding XML layout file. Here is an example of an **AlxBannerView** showing an Activity:

```XML
# activity_banner.xml
<com.alxad.api.AlxBannerView
    android:id="@+id/ad_banner"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" />
```

You can also create **AlxBannerView** programmatically:

```Java
AlxBannerView bannerAdView = new AlxBannerView(this);
```

2. **Loading Ads**

Once the AlxBannerView is set up, the next step is to load the ads. This is done via the `loadAd()` method in the AlxBannerView class, which requires adding ad slot parameters.

The following example shows how to load an ad in an `Activity` via `onCreate()` method:

**MainActivity (Excerpt)**

```Java
public class BannerActivity extends AppCompatActivity {

    private AlxBannerView bannerView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_banner);
        bannerView = (AlxBannerView) findViewById(R.id.ad_banner);

        //Set whether the banner can be closed
        bannerView.setBannerCanClose(false);
        //Set the refresh event interval to 30 seconds, 0 means no automatic refresh
        bannerView.setBannerRefresh(30);
        //Load ads
        bannerView.loadAd("172746", bannerViewAdListener);
    }

    @Override
    protected void onDestroy() {
        if (bannerView != null) {
            bannerView.destroy();
        }
        super.onDestroy();
    }

    private AlxBannerViewAdListener bannerViewAdListener=new AlxBannerViewAdListener() {
        @Override
        public void onAdLoaded() {
            //The ad is successfully loaded
        }

        @Override
        public void onAdError(int errorCode, String errorMsg) {
            //The ad loading failed
        }

        @Override
        public void onAdClicked() {

        }

        @Override
        public void onAdShow() {

        }

        @Override
        public void onAdClose() {

        }
    };

}
```

**Ad Event**：`AlxBannerViewAdListener` is the event callback listener for Banner ads.

| Method        | Description             |
| ----------- | ---------------- |
| onAdLoaded  | Call when the ad is successfully loaded |
| onAdError   | Call when the ad loading failed |
| onAdClicked | Call when the ad is clicked    |
| onAdShow    | Call when the ad is displayed     |
| onAdClose   | Call when the ad is closed     |

**The Rendering Sample**

![](https://static.rixengine.com/a/platform/help/c2s/6.1-2-1.png)

Ad size: small size 320*50, large size 320*480

**Note (Important): For Banner ads (adm materials), please do not use webview to load html() in advance at the backend or when the user is invisible. This will lead to incorrect judgment in displaying, and produce IVT (invalid traffic) problems, which will affect the credibility of the App from the advertiser's side. Webview loadhtml() must be used to load and display ads when the ad control view is visible to the user, instead of pre-loading \*\***html\***\* materials at the backend. **

#### 6.2 Rewarded Video

1. **Create a Rewarded Video Ad Object**

To create an AlxRewardVideoAD object, the sample code is as follows:

```Java
public class RewardVideoActivity extends AppCompatActivity {
    private AlxRewardVideoAD rewardAD;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_reward_video);
        createRewardAd();
    }

    private void createRewardAd(){
        rewardAD = new AlxRewardVideoAD();
    }
 }
```

2. **Load the Ad**

Call the load() method of AlxRewardVideoAD to load the ad. The sample code is as follows:

```Java
/**
 * Load the rewarded ad
 */
public void loadAd() {
    rewardAD.load(this, "172750", listener);
}

private AlxRewardVideoADListener listener = new AlxRewardVideoADListener() {
    @Override
    public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
        //The ad is successfully loaded
    }
    @Override
    public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
        //The ad loading failed
    }

    @Override
    public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
    }

    @Override
    public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {

    }

    @Override
    public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {

    }

    @Override
    public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {

    }

    @Override
    public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
    }

    @Override
    public void onReward(AlxRewardVideoAD var1) {
    }

    @Override
    public void onRewardVideoCache(boolean isSuccess) {
    }
};
```

3. Display Ads

Call the showVideo(Activity activity) method of AlxRewardVideoAD to display the ad. Before doing this, you need to call the isReady() method to confirm whether the ad has been loaded. The sample code is shown as follows:

```Java
public void showAd(){
    if(rewardAD != null  && rewardAD.isReady()){
        rewardAD.showVideo(this);
    }
}
```

**Ad Event**：`AlxRewardVideoADListener` is the event callback listener for the rewarded video.

| Method        | Description             |
| ---------------------------- | -------------------- |
| onRewardedVideoAdLoaded      | Call when the ad is successfully loaded     |
| onRewardedVideoAdFailed      | Call when the ad loading failed     |
| onRewardedVideoAdPlayClicked | Call when the ad is clicked         |
| onRewardedVideoAdPlayStart   | Call when the ad is displayed         |
| onRewardedVideoAdPlayEnd     | Call when the ad playback is completed    |
| onRewardedVideoAdPlayFailed  | Call when the ad playback is failed |
| onReward                     | Call when the ad has sent rewards     |
| onRewardedVideoAdClosed      | Call when the ad is closed         |

The Rendering Sample

![](6-2-3-1.png)

#### 6.3 Interstitials

1. **Create an interstitial ad object**

Create an AlxInterstitialAD object. The sample code is as follows:

```Java
public class InterstitialActivity extends AppCompatActivity {
    private AlxInterstitialAD interstitialAD;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_interstitial);
        createInterstitialAd();
    }

    private void createInterstitialAd(){
        interstitialAD = new AlxInterstitialAD();
    }
}
```

2. **Load Ads**

Call the load() method of AlxInterstitialAD to load the ad. The sample code is as follows:

```Java
/**
 * load Ad
 */
public void loadAd() {
    interstitialAD.load(this, "172748", listener);
}

private AlxInterstitialADListener listener = new AlxInterstitialADListener() {
    @Override
    public void onInterstitialAdLoaded() {
        //The ad successfully loaded
    }

    @Override
    public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
        //The ad loading failed
    }

    @Override
    public void onInterstitialAdClicked() {
    }

    @Override
    public void onInterstitialAdShow() {
    }

    @Override
    public void onInterstitialAdClose() {
    }

    @Override
    public void onInterstitialAdVideoStart() {
    }

    @Override
    public void onInterstitialAdVideoEnd() {
    }

    @Override
    public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
    }

};
```

3. **Display Ads**

Call the show(Activity activity) method of AlxInterstitialAD to display the ad. Before doing this, you need to call the isReady() method to confirm whether the ad has been loaded. The Sample code is shown as follows:

```Java
private void showAd(){
    if (interstitialAD != null && interstitialAD.isReady()) {
        interstitialAD.show(this);
    }
}
```

**Ad Event**：`AlxInterstitialADListenerr` is the event callback listener for interstitials.

| Method        | Description             |
| -------------------------- | -------------------- |
| onInterstitialAdLoaded     | Call when the ad successfully loaded     |
| onInterstitialAdLoadFail   | Call when the ad loading failed      |
| onInterstitialAdClicked    | Call when the ad is clicked         |
| onInterstitialAdShow       | Call when the ad is displayed         |
| onInterstitialAdVideoStart | Call when the ad playback starts |
| onInterstitialAdVideoEnd   | Call when the ad playback completes |
| onInterstitialAdVideoError | Call when the ad playback is abnormal |
| onInterstitialAdClose      | Call when the ad is closed         |

#### 6.4 Native Ad

1. **Create an AlxNativeAdLoader object**

Create an AlxNativeAdLoader object, the sample code is shown as follows: 

```Java
private AlxNativeAdLoader loader;
@Override
protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_native);
    loader = new AlxNativeAdLoader.Builder(this, "172943").build();
}
```

2. **Load Ad**

AlxNativeAdLoader provides the loadAd() method to load ads. The sample code is as follows:

```Java
private void loadAd() {
    loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
        @Override
        public void onAdFailed(int errorCode, String errorMsg) {
            //The ad loading failed
        }

        @Override
        public void onAdLoaded(List<AlxNativeAd> ads) {
            //The ad successfully loaded
        }
    });
}
```

3. **Display Native Ad**

Here are the steps to display native ads:

a. Define native ads layout

 Need to customize a layout to display creatives in `AlxNativeAd`

 NOTE: Must serve `AlxNativeAdView` as the root layout of native ads, otherwise, monetization revenue may be affected.

 For native ads that use `RelativeLayout` to display the layout of creatives, an example of the layout Structure is shown as follows:

```XML
<com.alxad.api.nativead.AlxNativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    ... >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        ... >
        <!-- Multimedia View -->
        <com.alxad.api.nativead.AlxMediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            ... />
        <RelativeLayout
            ... >
        <TextView
            android:id="@+id/ad_title"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            ... />
        <!-- Other assets -->
        ...
        </RelativeLayout>
        <!-- Other assets -->
        ...
    </RelativeLayout>
</com.alxad.api.nativead.AlxNativeAdView>
```

b. Register and populate the asset view

After obtaining the AlxNativeAdView object, register and populate the asset view. Refer to the sample code below:

```Java
private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
        ImageView logo = (ImageView) nativeView.findViewById(R.id.ad_logo);
        ImageView icon = (ImageView) nativeView.findViewById(R.id.ad_icon);
        TextView title = (TextView) nativeView.findViewById(R.id.ad_title);
        TextView description = (TextView) nativeView.findViewById(R.id.ad_desc);
        TextView source = (TextView) nativeView.findViewById(R.id.ad_source);
        Button callToAction = (Button) nativeView.findViewById(R.id.ad_call_to_action);
        ImageView close = (ImageView) nativeView.findViewById(R.id.ad_close);
        AlxMediaView mediaView = (AlxMediaView) nativeView.findViewById(R.id.ad_media);

        //Register and populate the title view
        nativeView.setTitleView(title);
        title.setText(nativeAd.getTitle());

        //Register and populate the multimedia view
        nativeView.setMediaView(mediaView);
        mediaView.setMediaContent(nativeAd.getMediaContent());

        //Register and populate other asset views
        nativeView.setDescriptionView(description);
        nativeView.setIconView(icon);
        nativeView.setCallToActionView(callToAction);
        nativeView.setCloseView(close);
        nativeView.setAdSourceView(source);
        description.setText(nativeAd.getDescription());
        logo.setImageBitmap(nativeAd.getAdLogo());

        if (TextUtils.isEmpty(nativeAd.getAdSource())) {
            source.setVisibility(View.GONE);
        } else {
            source.setVisibility(View.VISIBLE);
            source.setText(nativeAd.getAdSource());
        }
        if (TextUtils.isEmpty(nativeAd.getCallToAction())) {
            callToAction.setVisibility(View.GONE);
        } else {
            callToAction.setVisibility(View.VISIBLE);
            callToAction.setText(nativeAd.getCallToAction());
        }

        nativeAd.setNativeEventListener(new AlxNativeEventListener() {
            @Override
            public void onAdClicked() {

            }

            @Override
            public void onAdImpression() {

            }

            @Override
            public void onAdClosed() {

            }
        });

        //Video ads listener
        if (nativeAd.getMediaContent() != null && nativeAd.getMediaContent().hasVideo()) {
            nativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {
                @Override
                public void onVideoStart() {
                }

                @Override
                public void onVideoEnd() {
                }

                @Override
                public void onVideoPlay() {
                }

                @Override
                public void onVideoPause() {
                }

                @Override
                public void onVideoMute(boolean isMute) {
                }
            });
        }

        //Register the native ad object
        nativeView.setNativeAd(nativeAd);
    }
```

Register and populate the creative assets in sequence as shown in the sample code above. 

**AlxMediaView** is used to display multimedia assets. If the loaded native ad includes a video asset, the video will be rendered in **AlxMediaView**. Otherwise, an image will be rendered in AlxMediaView.

c. Register the native ad object with AlxNativeAdView.

Refer to the sample code below:

```Java
nativeView.setNativeAd(nativeAd);
```

d. Display AlxNativeAdView

Add AlxNativeAdView to the layout to display the native ad. Refer to the sample code below:

```Java
private void loadAd() {
   AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
   loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
         @Override
         public void onAdLoaded(List<AlxNativeAd> ads) {
             ……
             AlxNativeAd nativeAd = ads.get(0);

             //Obtain AlxNativeAdView
             AlxNativeAdView nativeAdView =(AlxNativeAdView) getLayoutInflater().inflate(R.layout.native_ad_template, null);

             //Register and populate the native ad asset views
             initNativeAdView(nativeAd,nativeAdView);

             //Add AlxNativeAdView to the UI
             FrameLayout adFrameLayout = (FrameLayout) findViewById(R.id.ad_container);
             adFrameLayout.removeAllViews();
             adFrameLayout.addView(nativeAdView);
             ……
           }
     });
}

private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
     ……
     //Register and populate the title view
     nativeView.setTitleView(title);
     title.setText(nativeAd.getTitle());

      //Register and populate the multimedia view
      nativeView.setMediaView(mediaView);
      mediaView.setMediaContent(nativeAd.getMediaContent());

      //Register and populate other asset views
      ……

      //Register the native ad object
      nativeView.setNativeAd(nativeAd);
}
```

4. **Ad Event Listener**

Sample code to add an event listener for a native ad is shown as follows:

```Java
nativeAd.setNativeEventListener(new AlxNativeEventListener() {
    @Override
    public void onAdClicked() {
        //Call when the ad is clicked
    }

    @Override
    public void onAdImpression() {
        //CAll when the ad is displayed
    }

    @Override
    public void onAdClosed() {
        //Call when the ad is closed
    }
});
```

5. **Destroy Ads**

When the native ad is no longer displayed, you should destroy the ad. Refer to the sample code below:

```Java
nativeAd.destroy();
```

**Please refer to the below descriptions for more details of `AlxNativeAd`**

| Method                   | Description                                                       |
| ---------------------- | ---------------------------------------------------------- |
| getCreateType          | Ads type (e.g.: Large image, small image, groups of images, video, Other: Not defined) |
| getAdSource            | Ads source                                                   |
| getAdLogo              | Ads logo                                                   |
| getTitle               | Ads title                                                   |
| getIcon                | Ads Icon                                                 |
| getImages              | Ads image contents                                           |
| getMediaContent        | Ads multimedia content                                    |
| destroy                | Destroy ads object                                           |
| setNativeEventListener | Ads event listener                                           |

##### Large Image Template

The display format is as shown below:

![](https://static.rixengine.com/a/platform/help/c2s/6.4-5-1.png)

### 7. Supports mainstream app stores, browser redirections and DeepLink third-party applications, which can be pulled up to open technical details:

####  7.1 Navigate to Google Play, Huawei Store, Xiaomi Store, Samsung Store, and so on. Deeplink and Google Play must be supported. Other stores are not required to support it because it is optional

 Mainstream app store package names:

```Java
/**
 * Package name of Google app store 
 */
public static final String GOOGLE_PLAY_APP_PACKAGE_NAME = "com.android.vending";

/**
 * Package name of Huawei App Store
 */
public static final String HUAWEI_MARKET_APP_PACKE_NAME = "com.huawei.appmarket";

/**
 * Package name of Sumsung App Store
 */
public static final String SUMSUNG_MARKET_APP_PACK_NAME = "com.sec.android.app.samsungapps";

/**
 * Package name of Xiaomi App Store
 */
public static final String XIAOMI_MARKET_APP_PACK_NAME = "com.xiaomi.market";
/**
 * Package name of OPPO App Store
 */
public static final String OPPO_MARKET_APP_PACK_NAME = "com.oppo.market";
/**
 * Package name of VIVO App Store
 */
public static final String VIVO_MARKET_APP_PACK_NAME = "com.bbk.appstore";
```

 Sample Code:

```Java
/**
 * Open and redirect to the app store
 *
 * @return true (open succeeds), false (open fails)
 */
public static boolean isStartAppStore(Context context, String url) {
    if (context == null || TextUtils.isEmpty(url)) {
        return false;
    }

    try {
        //Determine which app store it is based on the URL
        int appStoreType = -1;//Store type
        String lowerUrl = url.toLowerCase();//Convert the url to lowercase and then make a unified judgment
        if (lowerUrl.contains("appmarket://details?")
                || lowerUrl.contains("market://com.huawei.appmarket.applink?")
                || lowerUrl.contains("hiapplink://com.huawei.appmarket?")) {//Only supports Huawei App Store
            appStoreType = APP_STORE_HUAWEI;
        } else if (lowerUrl.startsWith("http://www.samsungapps.com/appquery/appDetail.as?")
                || lowerUrl.startsWith("http://apps.samsung.com/appquery/appDetail.as?")) {//Samsung App Store
            appStoreType = APP_STORE_SAMSUNG;
        } else if (lowerUrl.startsWith("https://play.google.com/store/apps/details?")
                || lowerUrl.startsWith("http://play.google.com/store/apps/details?")) {//Google Play App Store
            appStoreType = APP_STORE_GOOGLE;
        } else if (lowerUrl.contains("market://details?")) {//universal app store used by Google, Huawei and Xiaomi
            appStoreType = APP_STORE_COMMON;
        }
        if (appStoreType == -1) {
            return false;
        }

        //Open the app store to determine
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        if (appStoreType == APP_STORE_HUAWEI) {//Huawei
            if (AlxUtil.isAppInstalled(context, AlxConst.HUAWEI_MARKET_APP_PACKE_NAME)) {
                intent.setPackage(AlxConst.HUAWEI_MARKET_APP_PACKE_NAME);
            }
        } else if (appStoreType == APP_STORE_SAMSUNG) {//Sumsung
            if (AlxUtil.isAppInstalled(context, AlxConst.SUMSUNG_MARKET_APP_PACK_NAME)) {
                intent.setPackage(AlxConst.SUMSUNG_MARKET_APP_PACK_NAME);
            }
        } else if (appStoreType == APP_STORE_GOOGLE) {//google
            if (AlxUtil.isAppInstalled(context, AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME)) {
                intent.setPackage(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME);
            }
        } else {
            if (AlxUtil.isAppInstalled(context, AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME)) {//Prioritize using google store
                intent.setPackage(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME);
            } else {
                String manufacturer = Build.MANUFACTURER;
                if ("xiaomi".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.XIAOMI_MARKET_APP_PACK_NAME)) {//Xiaomi App Store
                        intent.setPackage(AlxConst.XIAOMI_MARKET_APP_PACK_NAME);
                    }
                } else if ("huawei".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.HUAWEI_MARKET_APP_PACKE_NAME)) {//Huawei App Store
                        intent.setPackage(AlxConst.HUAWEI_MARKET_APP_PACKE_NAME);
                    }
                } else if ("oppo".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.OPPO_MARKET_APP_PACK_NAME)) {//oppo App Store
                        intent.setPackage(AlxConst.OPPO_MARKET_APP_PACK_NAME);
                    }
                } else if ("vivo".equalsIgnoreCase(manufacturer)) {
                    if (AlxUtil.isAppInstalled(context, AlxConst.VIVO_MARKET_APP_PACK_NAME)) {//vivo App Store
                        intent.setPackage(AlxConst.VIVO_MARKET_APP_PACK_NAME);
                    }
                }
            }
        }
        intent.setData(Uri.parse(url));
        context.startActivity(intent);
        return true;
    } catch (Exception e) {
        AlxAgent.onError(e);
        AlxLog.e(AlxLogLevel.ERROR, TAG, e.getMessage());
    }
    return false;
}
```

####  7.2 Mobile Browser Redirect

 For ads whose landing address after clicking is not an app store (Market protocol), you need to redirect to the system browser or built-in browser.

 Sample Code:

```Java
/**
 * Open a built-in browser or system browser
 *
 * @param context
 * @param url
 * @return
 */
public static boolean isStartBrowser(Context context, String url, AlxTracker tracker) {
    try {
        //built-in WebView
        if (context != null && !TextUtils.isEmpty(url)) {
            if (AlxConfig.ALX_IS_USE_INNER_BROWSER) {
                AlxLog.d(AlxLogLevel.MARK, TAG, "isStartBrowser():webview");
                AlxWebActivity.startWeb(context, new AlxWebActivity.Builder().setLoad(url).setTracker(tracker));
                return true;
            }
        }
        AlxLog.d(AlxLogLevel.MARK, TAG, "isStartBrowser():browser");
        //Redirect to an external browser
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setData(Uri.parse(url));
        context.startActivity(intent);
        return true;
    } catch (Exception e) {
        AlxLog.e(AlxLogLevel.ERROR, TAG, "isStartBrowser():error:" + e.getMessage());
        return false;
    }
}
```

####  7.3 DeepLink (E-commerce app; social media; short video APP and etc.) Redirect and Pull up

For redirecting to third-party APPs (WeChat, Facebook, Taobao, JD, Lazada etc.) the Deeplink function needs to be implemented.

Sample Code:

```Java
/**
 * Open deeplink
 *
 * @param deepLink
 * @return null represents success. If it is not null, it means an open error and returns the corresponding error message.
 */
public static String isStartDeepLink(Context context, String deepLink) {
    if (TextUtils.isEmpty(deepLink)) {
        return "deeplink is empty";
    }
    if (context == null) {
        return "context params is null";
    }
    try {
        PackageManager packageManager = context.getApplicationContext().getPackageManager();
        if (packageManager == null) {
            return "package is empty";
        }
        Intent intent = Intent.parseUri(deepLink, 0);
        if (intent.resolveActivity(packageManager) != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            return null;
        } else {
            // Load landing page url
            return "deeplink open failed";
        }
    } catch (Exception e) {
        e.printStackTrace();
        AlxLog.e(AlxLogLevel.ERROR, TAG, "startDeepLink-error:" + e.getMessage());
        return e.getMessage();
    }
}
```

### 8. Technical Details of Ads Data Reporting

#### 8.1 Table of Macro Placeholders in Reporting url

| Table of Macro Placeholders       |                           |
| ---------------- | ------------------------- |
| Macro           | Description                      |
| ${AUCTION_PRICE} | Biding placeholder                |
| [ERRORCODE]      | Error code placeholder in Vast protocol |

Sample Code:

```JSON
{
...
"imptrackers": [
   "https://use.trk.rixengine.com/imp?info=Lg_O1mlwlL10VBwgBTXWLrJlH71n3qArAXZOJ8s1weaUN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYbmASkYY-m4l6_JlGKmpxOnXZeoMFzSti9WZ1_j7Gygi4Tm-Lz5utvKXxWnMEnyfEtF7mGPxcUfJe68DIKRCeiSNRXAWGRjmBpxAAs8vVd3gCR4X34ScMCZSyb7sabkmOBrRsz805UxJwzWejDxeZQgaUVzBkmFcaWmTiZaTcIwR8ejAw00fltm_Fn24lSUzkj2RzFP0hIowFdIccpjHbSuVui4M4cUaSHohIETr69dkOE5DaoYEBgAQz6qGgiL2wGOjTGWNWFKbLblBXsT-7n0ms3duWYMHygq8jrx0NHLn-6gkAA2ss9qmu1DZloja4WmNdioo87-IiHfRCqZTg5HZSKbY34LqKBFta8s6eZT5SlDmPvLXzR74JbRbrlJYX_RL5i3Olg2hc0RdHWnWw4Ps6LJ5q5dC3nwdLI7yNRecjYHmv87otoofrg0_GQMbmDCB5c8iv-orctf9HkOK46pqCRZkIks_MlIIlYvZXt9zYBu9CJAyf-4JeXitzPbb4JQRHtLDbkUmzc9llbH4gtyns_tWdaC85NMCIqgTPjjQCe17cm_VcvmfTPLabaOFMpcC4WS--3u1gLFBr2rLB90IaPFDDLY3JBZC_yX7OBkm2zN9b7-AwyJTBOMiwqxKWkm0oEZs59OEXNOUr7bv0A1K-JX0AdlikoG6Tm6CPMfHUbSPclO_V3w8oFjlduwot5pOeuWO-AP8m1hKGbp62Tuf2UsjtxGN0NGry7rQSlTXqPnTMDA48uFQpgVjY5rHsxFixlrmta9SphRP-Iw-LJQEkGzaJi3ckDS9wCBMAnnwOIDNjxJj_j_YfoRLxJdqW-vcxdYeuS5ysyeGmJn_orXsBplJdwPATcDKohfAJNSv7EduhKAEerOpdjRDxqoo2o1G_S38iLUZ7HZJVj6uZq0wOsroSXQ==&price=**${AUCTION_PRICE}**",
],
"adm":"<VAST
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vast.xsd" version="2.0">
    ...
    <Error><![CDATA[https://d-s.svr-algorix.com/alx/track?c=IuzIGWir_uT1O3h_qwDhmVv4TQqg5DyLAi3t0HwpxbZbcGlk5TJHZ36RaNpoFLzAu8HJHWBPHLSWJxx7nPUM96HZB_Bh4cdsqYz2YjsvL8L8z5erPtZ3gnXE0qt4dcmGfAktrWbiC4WzfE_aKnSJKqOAH8wu5zK79h53Z2xAImlbDAzA1_oMopuCiwhUP4cPp7l6SG1VMpvtPJfEBuw2TOJgWF7KfoVgw8Zn4AXK0CojeLwjanL52xB64OpDY7STLJTy9fQcM5hQwzyskXCZkKP5_XdbbWb9nYJ548bPmjZpZQOBc8eYbWDofkgk_VTwVtStuApNTJF3W7XxVPt1-IWBc_R3Dr6KEaaSZJ1wCIQ_l0cK3udfci-8v4WHq7jDmwtYtGrblMPnjQr5EcRkwSXtZZyAwHfeP8n7o4Vz4F793dtakwLP9n3LQJ9mzjisFNIkrx86-VLfpak7Tzou12Aatxow&event=error&errcode=**[ERRORCODE]**]]>
     </Error>
         ...
      </VAST>"
...
}
```

#### 8.2 The reporting steps are as follows

1. Find the corresponding url array in the returned json data according to the corresponding event

​		For example, the ad impression event corresponds to the imptrackers:[] field of json data

2. Search the url string to see if there is a macro that needs to be replaced. If it exists, replace the macro placeholder with the corresponding value. See `Macro Placeholder Table` above

3. After replacing all the url reporting array, use the get request method to initiate a request, and the reporting is completed

### 9. GDPR, CCPA, CCOPA Privacy Policies

1. **GDPR**

As a publisher, you need to integrate `Consent Management Platform (CMP)` in your app and obtain user consent according to `“Mobile In-App CMP API v1.0: Transparency & Consent Framework”` and `“IAB Tech Lab – CMP API v2”` under ` IAB Europe` requirements.

If you are using a self-built `CMP`, then you need to store the collected user consent information in `SharedPreferences` (using the two `keys` `IABTCF_gdprApplies` and `IABTCF_TCString`).

` Transparency and Consent Framework (TCF) v2:``**Key** `

**Property Description**

![](9-1-1-1.png)

`RixEngine` is one of the `IAB Europe` Transparency and Consent Framework vendors(TCF). For more information about `RixEngine`’s compliance with `GDPR`, you can refer to `RixEngine`’s privacy policy.

`Self-developed SDK` needs to read the `GDPR` related user consent fields from the two `keys` of `IABTCF_gdprApplies` and `IABTCF_TCString` in `SharedPreferences`. You can also use the following `SDK` function to manually pass in the `GDPR` related user consent fields. Note: Please try to call after `SDK` is initialized.

You do not need to call the following functions if your `app` has integrated with `CMP` and stored the user consent field in `IABTCF_gdprApplies` and `IABTCF_TCString` under `SharedPreferences`.

Sample Code:

```Java
AlxAdSDK.setSubjectToGDPR(TRUE);
AlxAdSDK.setUserConsent(<#Base64ConsentString#>); // Base64 加密的 consent string
```

`"subject to GDPR"` signal can be set as `YES / TRUE` (the user is protected by `GDPR` ) or `NO / FALSE` (the user is not protected by `GDPR`). This function should only be called if the application has determined whether `GDPR` applies to the user. If this function is not called, `RixEngine` assumes that the application has not made such a determination, and therefore `RixEngine` will determine the applicability of `GDPR` on its own.

`setUserConsent` function provides `“0”`（the user does not agree），`“1”`（the user agrees）or more detailed consent information

(`Base64` encrypted `consent string`). This more detailed consent information is described in GDPR Transparency and Consent Framework supported by `IAB`, more information refers to  [`https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework`](https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework)

2. **CCPA**

The `California Consumer Privacy Act' (CCPA`) was issued to improve and strengthen the transparency and control of the use of personal privacy information of California users. As a developer, you are required to obtain consent for the use of the personal information of users in California. For more information about `RixEngine`’s compliance with `CCPA`, you can refer to `RixEngine`’s privacy policy.

**NOTE**: It is not an issue if you are upgrading from a previous version and have set the privacy signal. The new version of `SDK` will still use the values set in the previous version, so there is no need to manually set them again. But we recommend you use the new method.

You can refer to the code example below to pass in the `US Privacy` signal. Note: Please try to call it after the `self-developed SDK` is initialized.

For example, `subjectToUSPrivacy` under self-developed SDK accepts string values (such as `"1YYY"`). The string used by this signal is required to comply with the `IAB` US privacy string format.

3. **Age-Related Regulatory Requirement**

Use the method below if you have child-directed apps to flag specific end-users as children, as may be permitted or required by applicable law (e.g., `COPPA`, `GDPR`, etc.). Developers of child-directed apps are responsible for determining whether an app is permitted to flag at the end-user level or must treat all end-users as children.

Sample Code:

```Java
AlxAdSDK.setBelowConsentAge(TRUE);
```

The `BelowConsentAg`e method takes a Boolean value and can either be `TRUE` (If the end-user is a child, as defined by applicable regulations) or `FALSE` (If the end-user is not a child).

### 10. IAB OMSDK (Open Management SDK) Integration supports third-party visibility and verification measurement

IAB's official standard technical documents are as follows:

https://iabtechlab.com/standards/open-measurement-sdk/

After integrating OMSDK, you must pass the IAB official technical certification and pay thousands of dollars in annual membership fees. You will be certified by the industry and advertisers once you have met the standards. The ad fill rate is determined by the ad budget, and the advertising ECPM will not be limited. Google Admob, Facebook Audience Network, Max, IronSource, InMobi, AlogriX-RixEngine, Mintegral, and other well-known companies have all passed the IAB Open Measurement official whitelist certification. (Integration is optional but recommended.)

### 11. Obtaining the unique identifier of the ad (important)

 Including GAID of Google system and OAID of Mobile Hardcore Alliance system (including Huawei/Xiaomi/Oppo/Vivo)

####  11.1 How to obtain GAID (required):

​     Get the unique ad identifier GAID sample code in GMS:

```Java
public class GAIDTool {
    public static final class AdInfo {
        private final String advertisingId;
        private final boolean limitAdTrackingEnabled;

        AdInfo(String advertisingId, boolean limitAdTrackingEnabled) {
            this.advertisingId = advertisingId;
            this.limitAdTrackingEnabled = limitAdTrackingEnabled;
        }

        public String getId() {
            return this.advertisingId;
        }

        public boolean isLimitAdTrackingEnabled() {
            return this.limitAdTrackingEnabled;
        }
    }

    public static AdInfo getAdvertisingIdInfo(Context context) throws Exception {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            throw new IllegalStateException(
                    "Cannot be called from the main thread");
        }

        try {
            PackageManager pm = context.getPackageManager();
            pm.getPackageInfo(AlxConst.GOOGLE_PLAY_APP_PACKAGE_NAME, 0);
        } catch (Exception e) {
            e.printStackTrace();
            // throw e;
        }

        AdvertisingConnection connection = new AdvertisingConnection();
        Intent intent = new Intent(
                "com.google.android.gms.ads.identifier.service.START");
        intent.setPackage("com.google.android.gms");
        try{
            if (context.bindService(intent, connection, Context.BIND_AUTO_CREATE)) {
                try {
                    AdvertisingInterface adInterface = new AdvertisingInterface(
                            connection.getBinder());
                    AdInfo adInfo = new AdInfo(adInterface.getId(),
                            adInterface.isLimitAdTrackingEnabled(true));
                    return adInfo;
                } catch (Exception exception) {
                    throw exception;
                } finally {
                    context.unbindService(connection);
                }
            }
        }catch (Exception e){
            throw e;
        }
        throw new IOException("Google Play connection failed");
    }

    private static final class AdvertisingConnection implements
            ServiceConnection {
        boolean retrieved = false;
        private final LinkedBlockingQueue<IBinder> queue = new LinkedBlockingQueue<IBinder>(
                1);

        public void onServiceConnected(ComponentName name, IBinder service) {
            try {
                this.queue.put(service);
            } catch (InterruptedException localInterruptedException) {
                localInterruptedException.printStackTrace();
            }
        }

        public void onServiceDisconnected(ComponentName name) {
        }

        public IBinder getBinder() throws InterruptedException {
            if (this.retrieved) {
                throw new IllegalStateException();
            }
            this.retrieved = true;
            return (IBinder) this.queue.take();
        }
    }

    private static final class AdvertisingInterface implements IInterface {
        private IBinder binder;

        public AdvertisingInterface(IBinder pBinder) {
            binder = pBinder;
        }

        public IBinder asBinder() {
            return binder;
        }

        public String getId() throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            String id;
            try {
                data.writeInterfaceToken("com.google.android.gms.ads.identifier.internal.IAdvertisingIdService");
                binder.transact(1, data, reply, 0);
                reply.readException();
                id = reply.readString();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return id;
        }

        public boolean isLimitAdTrackingEnabled(boolean paramBoolean)
                throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            boolean limitAdTracking;
            try {
                data.writeInterfaceToken("com.google.android.gms.ads.identifier.internal.IAdvertisingIdService");
                data.writeInt(paramBoolean ? 1 : 0);
                binder.transact(2, data, reply, 0);
                reply.readException();
                limitAdTracking = 0 != reply.readInt();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return limitAdTracking;
        }
    }
}
```

####  11.2 How to obtain OAID (optional):

 Initiated and developed by the China* Mobile Security Alliance* (MSA), the standard Android user identification ID is mainly for Mainland China use. The official address is:

 http://www.msa-alliance.cn/

### 12. MRaid mobile rich media creative ad support (optional)

IAB official standards document:

https://iabtechlab.com/standards/mobile-rich-media-ad-interface-definitions-mraid/

Mraid recommends a minimum of 2.0, with 3.0 providing better results.

## Appendix

### 1. Ad Data Format

| Value  | Description       |
| --- | ---------- |
| 1   | `HTML `    |
| 2   | `JSON`     |
| 3   | `VAST XML` |

### 2. Ad Type

| Value  | Description                                                               |
| --- | ------------------------------------------------------------------ |
| 1   | `Banner`                                                           |
| 2   | `MREC` Ad                                                         |
| 3   | Interstitial ads (including: 1: full-screen video ads; 2: `banner` (loaded with webView) ads） |
| 4   | Rewarded Video                                                           |
| 5   | Native Ad                                                           |

### 3. Device Type

| **Value** | **Description** |
| ------ | -------- |
| 0      | Unknown     |
| 1      | Phone     |
| 2      | Tablet     |

### 4. Error Code & Error Information

| **Value** | **Description**               |
| ------ | ---------------------- |
| 1000   | Succeed                   |
| 2001   | No Filling                 |
| 3001   | Ad format doesn't match with placement |
| 3002   | Ad placement is not working           |
| 3003   | `App`is not working            |
| 3004   | `device` information error      |
| 3005   | `reg` information error          |

### 5. Native Creative Type

| **Value** | **Description** |
| ------ | -------- |
| 0      | Unknown     |
| 1      | Large image     |
| 2      | Small image     |
| 3      | Group image     |
| 4      | Video     |

### 6. Third-party Adapter Source Code Sample [adapts to mainstream integrated ad platform (including Admob/MAX/IronSouce/Topon third-party custom adapter)]:

#### 6.1 Admob adapter source code:

#####   Banner:

```Java
package com.admob.custom.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationBannerAd;
import com.google.android.gms.ads.mediation.MediationBannerAdCallback;
import com.google.android.gms.ads.mediation.MediationBannerAdConfiguration;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Banner Adapter
 */
public class AlxBannerAdapter extends Adapter implements MediationBannerAd {

    private static final String TAG = "AlxBannerAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<MediationBannerAd, MediationBannerAdCallback> mMediationLoadCallback;
    private MediationBannerAdCallback mMediationEventCallback;

    AlxBannerView mBannerView;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback, List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadBannerAd(@NonNull MediationBannerAdConfiguration configuration, @NonNull MediationAdLoadCallback<MediationBannerAd, MediationBannerAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadBannerAd");
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    load(context);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    @NonNull
    @Override
    public View getView() {
        return mBannerView;
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private void load(Context context) {
        mBannerView = new AlxBannerView(context);
        // auto refresh ad  default = open = 1, 0 = close
        mBannerView.setBannerRefresh(0);
        //mBannerView.setBannerRefresh(15);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mMediationLoadCallback != null) {
                    mMediationEventCallback = mMediationLoadCallback.onSuccess(AlxBannerAdapter.this);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onAdClicked() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdImpression();
                    mMediationEventCallback.onAdOpened();
                }
            }

            @Override
            public void onAdClose() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdClosed();
                }
            }
        };
        mBannerView.loadAd(unitid, alxBannerADListener);
    }


    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
```

#####   Native:

```Java
package com.admob.custom.adapter;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.formats.NativeAd;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationNativeAdCallback;
import com.google.android.gms.ads.mediation.MediationNativeAdConfiguration;
import com.google.android.gms.ads.mediation.UnifiedNativeAdMapper;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AlxNativeAdapter extends Adapter {
    private static final String TAG = "AlxNativeAdapter";

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<UnifiedNativeAdMapper, MediationNativeAdCallback> mMediationLoadCallback;
    private MediationNativeAdCallback mMediationEventCallback;

    private AlxNativeAd nativeAd;
    private CustomNativeAdMapper nativeAdMapper;

    @Override
    public void initialize(@NonNull Context context, @NonNull InitializationCompleteCallback initializationCompleteCallback, @NonNull List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadNativeAd(@NonNull MediationNativeAdConfiguration configuration, @NonNull MediationAdLoadCallback<UnifiedNativeAdMapper, MediationNativeAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadNativeAd " + Thread.currentThread().getName());
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    loadAds(context, unitid);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    private void loadAds(final Context context, String adId) {
        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(context, adId).build();
        loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
            @Override
            public void onAdFailed(int errorCode, String errorMsg) {
                Log.i(TAG, "onAdLoadedFail:" + errorCode + ";" + errorMsg);
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onAdLoaded(List<AlxNativeAd> list) {
                Log.i(TAG, "onAdLoaded:");

                if (list == null || list.isEmpty()) {
                    loadError(100, "no data ads");
                    return;
                }

                try {
                    nativeAd = list.get(0);
                    if (nativeAd == null) {
                        loadError(100, "no data ads");
                        return;
                    }

                    nativeAdMapper = new CustomNativeAdMapper(context, nativeAd);
                    if (mMediationLoadCallback != null) {
                        Log.i(TAG, "onAdLoaded:listener-ok");
                        mMediationEventCallback = mMediationLoadCallback.onSuccess(nativeAdMapper);
                    }
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                    loadError(101, e.getMessage());
                }

            }
        });
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private class CustomNativeAdMapper extends UnifiedNativeAdMapper {

        private AlxNativeAd bean;
        private Context context;
        private AlxNativeAdView mRootView;

        public CustomNativeAdMapper(Context context, AlxNativeAd bean) {
            this.bean = bean;
            this.context = context;
            bindListener();
            init();
        }

        private void init() {
            if (bean == null) {
                return;
            }
            setHeadline(bean.getTitle());
            setBody(bean.getDescription());
            setPrice(bean.getPrice() + "");
            setAdvertiser(bean.getAdSource());
            setCallToAction(bean.getCallToAction());
            setIcon(new SimpleImage(bean.getIcon()));
            setImages(getImageList());
            setHasVideoContent(bean.getMediaContent().hasVideo());

            mRootView = new AlxNativeAdView(context);
            AlxMediaView mediaView = new AlxMediaView(context);
            mediaView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            mediaView.setMediaContent(bean.getMediaContent());
            mRootView.setMediaView(mediaView);
            setMediaView(mediaView);
        }

        @Override
        public void trackViews(@NonNull View view, @NonNull Map<String, View> map, @NonNull Map<String, View> map1) {
            Log.i(TAG, "trackViews");
            if (view instanceof ViewGroup) {
                Log.i(TAG, "trackViews: rootView is ViewGroup");
                ViewGroup rootView = (ViewGroup) view;
                try {
                    if (mRootView != null) {
                        rootView.removeView(mRootView);
                    }
                    if (mRootView == null) {
                        mRootView = new AlxNativeAdView(context);
                    }
                    mRootView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                    if (map != null && !map.isEmpty()) {
                        for (Map.Entry<String, View> entry : map.entrySet()) {
                            Log.i(TAG, "register:key=" + entry.getKey());
                            mRootView.addView(entry.getKey(), entry.getValue());
                        }
                    }
                    if (map1 != null && !map1.isEmpty()) {
                        for (Map.Entry<String, View> entry : map1.entrySet()) {
                            Log.i(TAG, "register2:key=" + entry.getKey());
                        }
                    }
                    mRootView.setNativeAd(bean);
                    rootView.addView(mRootView, 0);
                } catch (Exception e) {
                    e.printStackTrace();
                    Log.e(TAG, e.getMessage());
                }
            } else {
                Log.i(TAG, "trackViews: rootView is other");
            }
        }

        @Override
        public void untrackView(@NonNull View view) {
            Log.i(TAG, "untrackView");
        }

        private List<NativeAd.Image> getImageList() {
            List<NativeAd.Image> imageList = new ArrayList<>();
            if (bean.getImages() != null && bean.getImages().size() > 0) {
                for (AlxImage item : bean.getImages()) {
                    if (item != null) {
                        imageList.add(new SimpleImage(item));
                    }
                }
            }
            return imageList;
        }

        public AlxNativeAdView getAlgorixAdView() {
            return mRootView;
        }

        private void bindListener() {
            if (bean == null) {
                return;
            }
            bean.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    Log.d(TAG, "onAdClick");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.reportAdClicked();
                    }
                }

                @Override
                public void onAdImpression() {
                    Log.d(TAG, "onAdShow");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.reportAdImpression();
                        mMediationEventCallback.onAdOpened();
                    }
                }

                @Override
                public void onAdClosed() {
                    Log.d(TAG, "onAdClose");
                    if (mMediationEventCallback != null) {
                        mMediationEventCallback.onAdClosed();
                    }
                }
            });
        }


        private class SimpleImage extends NativeAd.Image {

            private AlxImage image;

            public SimpleImage(AlxImage image) {
                this.image = image;
            }

            @Override
            public double getScale() {
                return 0;
            }

            @Nullable
            @Override
            public Drawable getDrawable() {
                return null;
            }

            @Nullable
            @Override
            public Uri getUri() {
                if (image != null) {
                    return Uri.parse(image.getImageUrl());
                }
                return null;
            }
        }
    }

    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    @NonNull
    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @NonNull
    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
```

##### Interstitial:

```Java
package com.admob.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationInterstitialAd;
import com.google.android.gms.ads.mediation.MediationInterstitialAdCallback;
import com.google.android.gms.ads.mediation.MediationInterstitialAdConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Interstitial Adapter
 */
public class AlxInterstitialAdapter extends Adapter implements MediationInterstitialAd {

    private static final String TAG = "AlxInterstitialAdapter";

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    private MediationAdLoadCallback<MediationInterstitialAd, MediationInterstitialAdCallback> mMediationLoadCallback;
    private MediationInterstitialAdCallback mMediationEventCallback;

    AlxInterstitialAD interstitialAd;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback, List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter: initialize");
        if (context == null) {
            initializationCompleteCallback.onInitializationFailed(
                    "Initialization Failed: Context is null.");
            return;
        }
        initializationCompleteCallback.onInitializationSucceeded();
    }

    @Override
    public void loadInterstitialAd(@NonNull MediationInterstitialAdConfiguration configuration, @NonNull MediationAdLoadCallback<MediationInterstitialAd, MediationInterstitialAdCallback> callback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx-admob-adapter: loadInterstitialAd");
        mMediationLoadCallback = callback;
        String parameter = configuration.getServerParameters().getString("parameter");
        if (!TextUtils.isEmpty(parameter)) {
            parseServer(parameter);
        }
        initSdk(configuration.getContext());
    }

    private void initSdk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            loadError(1, "alx unitid is empty.");
            return;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            loadError(1, "alx sid is empty.");
            return;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            loadError(1, "alx appid is empty.");
            return;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            loadError(1, "alx token is empty");
            return;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            loadError(1, "alx host is empty");
            return;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    preloadAd(context);
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            e.printStackTrace();
            loadError(1, "alx sdk init error");
        }
    }

    @Override
    public void showAd(@NonNull Context context) {
        Log.i(TAG, "alx showAd");
        if (interstitialAd != null) {
            if (!interstitialAd.isReady()) {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdFailedToShow(new AdError(1, "isReady: Ad not loaded or Ad load failed"
                            , AlxAdSDK.getNetWorkName()));
                }
                return;
            }
            if (context != null && context instanceof Activity) {
                interstitialAd.show((Activity) context);
            } else {
                Log.i(TAG, "context is not an Activity");
                interstitialAd.show(null);
            }
        }
    }

    private void loadError(int code, String message) {
        if (mMediationLoadCallback != null) {
            mMediationLoadCallback.onFailure(new AdError(code, message, AlxAdSDK.getNetWorkName()));
        }
    }

    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

    private void preloadAd(final Context context) {
        interstitialAd = new AlxInterstitialAD();
        interstitialAd.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mMediationLoadCallback != null) {
                    mMediationEventCallback = mMediationLoadCallback.onSuccess(AlxInterstitialAdapter.this);
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                loadError(errorCode, errorMsg);
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.reportAdImpression();
                    mMediationEventCallback.onAdOpened();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mMediationEventCallback != null) {
                    mMediationEventCallback.onAdClosed();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
            }
        });
    }


    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        VersionInfo result = getAdapterVersionInfo(versionString);
        if (result != null) {
            return result;
        }
        return new VersionInfo(0, 0, 0);
    }

    private VersionInfo getAdapterVersionInfo(String version) {
        if (TextUtils.isEmpty(version)) {
            return null;
        }
        try {
            String[] arr = version.split("\\.");
            if (arr == null || arr.length < 3) {
                return null;
            }
            int major = Integer.parseInt(arr[0]);
            int minor = Integer.parseInt(arr[1]);
            int micro = Integer.parseInt(arr[2]);
            return new VersionInfo(major, minor, micro);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
```

##### RewardVideo:

```Java
package com.admob.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.mediation.Adapter;
import com.google.android.gms.ads.mediation.InitializationCompleteCallback;
import com.google.android.gms.ads.mediation.MediationAdLoadCallback;
import com.google.android.gms.ads.mediation.MediationConfiguration;
import com.google.android.gms.ads.mediation.MediationRewardedAd;
import com.google.android.gms.ads.mediation.MediationRewardedAdCallback;
import com.google.android.gms.ads.mediation.MediationRewardedAdConfiguration;
import com.google.android.gms.ads.mediation.VersionInfo;
import com.google.android.gms.ads.rewarded.RewardItem;

import org.json.JSONObject;

import java.util.List;

/**
 * Google Mobile ads AlgoriX Reward Video Adapter
 */
public class AlxRewardVideoAdapter extends Adapter implements MediationRewardedAd {
    private final String TAG = "AlxRewardVideoAdapter";
    private static final String ALX_AD_UNIT_KEY = "parameter";

    private AlxRewardVideoAD alxRewardVideoAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;
    private Context mContext;
    private MediationAdLoadCallback<MediationRewardedAd, MediationRewardedAdCallback> mediationAdLoadCallBack;
    private MediationRewardedAdCallback mMediationRewardedAdCallback;

    @Override
    public void initialize(Context context, InitializationCompleteCallback initializationCompleteCallback
            , List<MediationConfiguration> list) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.e(TAG, "alx initialize...");
        for (MediationConfiguration configuration : list) {
            Bundle serverParameters = configuration.getServerParameters();
            String serviceString = serverParameters.getString(ALX_AD_UNIT_KEY);
            if (!TextUtils.isEmpty(serviceString)) {
                parseServer(serviceString);
            }
        }
        if (initSDk(context)) {
            initializationCompleteCallback.onInitializationSucceeded();
        } else {
            initializationCompleteCallback.onInitializationFailed("alx sdk init error");
        }
    }

    @Override
    public VersionInfo getVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        String[] splits = versionString.split("\\.");

        if (splits.length >= 3) {
            int major = Integer.parseInt(splits[0]);
            int minor = Integer.parseInt(splits[1]);
            int micro = Integer.parseInt(splits[2]) * 100 + Integer.parseInt(splits[3]);
            return new VersionInfo(major, minor, micro);
        }

        return new VersionInfo(0, 0, 0);
    }

    @Override
    public VersionInfo getSDKVersionInfo() {
        String versionString = AlxAdSDK.getNetWorkVersion();
        String[] splits = versionString.split("\\.");
        if (splits.length >= 3) {
            int major = Integer.parseInt(splits[0]);
            int minor = Integer.parseInt(splits[1]);
            int micro = Integer.parseInt(splits[2]);
            return new VersionInfo(major, minor, micro);
        }
        return new VersionInfo(0, 0, 0);
    }

    @Override
    public void showAd(Context context) {
        Log.e(TAG, "alx showAd...");
        if (!(context instanceof Activity)) {
            Log.e(TAG, "context is not Activity");
            mMediationRewardedAdCallback.onAdFailedToShow(new AdError(1,
                    "An activity context is required to show Sample rewarded ad."
                    , AlxAdSDK.getNetWorkName())
            );
            return;
        }
        mContext = context;
        if (!alxRewardVideoAD.isReady()) {
            mMediationRewardedAdCallback.onAdFailedToShow(new AdError(1, "No ads to show."
                    , AlxAdSDK.getNetWorkName()));
            return;
        }
        alxRewardVideoAD.showVideo((Activity) context);
    }

    @Override
    public void loadRewardedAd(MediationRewardedAdConfiguration configuration
            , MediationAdLoadCallback<MediationRewardedAd, MediationRewardedAdCallback> mediationAdLoadCallback) {
        Log.d(TAG, "alx-admob-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.d(TAG, "alx loadRewardedAd");
        Context context = configuration.getContext();
        mediationAdLoadCallBack = mediationAdLoadCallback;
        Bundle serverParameters = configuration.getServerParameters();
        String serviceString = serverParameters.getString(ALX_AD_UNIT_KEY);
        if (!TextUtils.isEmpty(serviceString)) {
            parseServer(serviceString);
        }
        initSDk(context);
    }

    private boolean initSDk(final Context context) {
        if (TextUtils.isEmpty(unitid)) {
            Log.d(TAG, "alx unitid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx unitid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(sid)) {
            Log.d(TAG, "alx sid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx sid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(appid)) {
            Log.d(TAG, "alx appid is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx appid is empty."
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(token)) {
            Log.d(TAG, "alx token is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx token is empty"
                    , AlxAdSDK.getNetWorkName()));
            return false;
        }
        if (TextUtils.isEmpty(host)) {
            Log.d(TAG, "alx host is empty");
            mediationAdLoadCallBack.onFailure(new AdError(1, "alx host is empty", AlxAdSDK.getNetWorkName()));
            return false;
        }

        try {
            Log.i(TAG, "alx host: " + host + " alx token: " + token + " alx appid: " + appid + "alx sid: " + sid);
            // init
            if(isDebug != null){
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //sdk init success, begin load ad
                    alxRewardVideoAD = new AlxRewardVideoAD();
                    alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {
                        @Override
                        public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdLoaded");
                            if (mediationAdLoadCallBack != null)
                                mMediationRewardedAdCallback = (MediationRewardedAdCallback) mediationAdLoadCallBack
                                        .onSuccess(AlxRewardVideoAdapter.this);
                        }


                        @Override
                        public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                            Log.d(TAG, "onRewardedVideoAdFailed: " + errMsg);
                            if (mediationAdLoadCallBack != null) mediationAdLoadCallBack
                                    .onFailure(new AdError(errCode, errMsg, AlxAdSDK.getNetWorkName()));
                        }

                        @Override
                        public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                            if (mMediationRewardedAdCallback != null && mContext instanceof Activity) {
                                ((Activity) mContext).runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        // runOnUiThread
                                        mMediationRewardedAdCallback.reportAdImpression();
                                        mMediationRewardedAdCallback.onAdOpened();
                                        mMediationRewardedAdCallback.onVideoStart();
                                    }
                                });
                            }
                        }

                        @Override
                        public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdPlayEnd: ");
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.onVideoComplete();
                        }

                        @Override
                        public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                            Log.d(TAG, "onShowFail: " + errMsg);
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.onAdFailedToShow(
                                        new AdError(errCode, errMsg, AlxAdSDK.getNetWorkName()));
                        }

                        @Override
                        public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdClosed: ");
                            if (mMediationRewardedAdCallback != null) {
                                mMediationRewardedAdCallback.onAdClosed();
                            }
                        }

                        @Override
                        public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onRewardedVideoAdPlayClicked: ");
                            if (mMediationRewardedAdCallback != null)
                                mMediationRewardedAdCallback.reportAdClicked();
                        }

                        @Override
                        public void onReward(AlxRewardVideoAD var1) {
                            Log.d(TAG, "onReward: ");
                            if (mMediationRewardedAdCallback != null) {
                                mMediationRewardedAdCallback.onUserEarnedReward(new RewardItem() {
                                    @Override
                                    public String getType() {
                                        return "";
                                    }

                                    @Override
                                    public int getAmount() {
                                        return 1;
                                    }
                                });
                            }
                        }
                    });
                }
            });
//            // set GDPR
//            AlxAdSDK.setSubjectToGDPR(true);
//            // set GDPR Consent
//            AlxAdSDK.setUserConsent("1");
//            // set COPPA
//            AlxAdSDK.setBelowConsentAge(true);
//            // set CCPA
//            AlxAdSDK.subjectToUSPrivacy("1YYY");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }


    private void parseServer(String s) {
        if (TextUtils.isEmpty(s)) {
            Log.d(TAG, "serviceString  is empty ");
            return;
        }
        Log.d(TAG, "serviceString   " + s);
        try {
            JSONObject json = new JSONObject(s);
            host = json.getString("host");
            appid = json.getString("appid");
            sid = json.getString("sid");
            token = json.getString("token");
            unitid = json.getString("unitid");
            String debug = json.optString("isdebug");
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage() + "");
        }
    }

}
```

##### AlxMetaInf:

```Java
package com.admob.custom.adapter;

/**
 * Adapter version information
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";

}
```

#### 6.2 Max adapter source code:

#####   Banner & Native & Interstitial & RewardVideo 4-in-1

```Java
package com.applovin.mediation.adapters;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaContent;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.applovin.impl.sdk.utils.BundleUtils;
import com.applovin.mediation.MaxAdFormat;
import com.applovin.mediation.adapter.MaxAdViewAdapter;
import com.applovin.mediation.adapter.MaxAdapterError;
import com.applovin.mediation.adapter.MaxInterstitialAdapter;
import com.applovin.mediation.adapter.MaxNativeAdAdapter;
import com.applovin.mediation.adapter.MaxRewardedAdapter;
import com.applovin.mediation.adapter.listeners.MaxAdViewAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxInterstitialAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxNativeAdAdapterListener;
import com.applovin.mediation.adapter.listeners.MaxRewardedAdapterListener;
import com.applovin.mediation.adapter.parameters.MaxAdapterInitializationParameters;
import com.applovin.mediation.adapter.parameters.MaxAdapterResponseParameters;
import com.applovin.mediation.nativeAds.MaxNativeAd;
import com.applovin.mediation.nativeAds.MaxNativeAdView;
import com.applovin.sdk.AppLovinPrivacySettings;
import com.applovin.sdk.AppLovinSdk;
import com.applovin.sdk.AppLovinSdkUtils;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Applovin ads AlgoriX Adapter
 */
public class AlgorixMediationAdapter extends MediationAdapterBase implements MaxAdViewAdapter, MaxInterstitialAdapter, MaxRewardedAdapter, MaxNativeAdAdapter {
    private static final String TAG = "AlgorixMediationAdapter";
    private static final String ADAPTER_VERSION = "3.8.0";

    private static final int DEFAULT_IMAGE_TASK_TIMEOUT_SECONDS = 10;

    private static final AtomicBoolean initialized = new AtomicBoolean();
    private static InitializationStatus status;

    private AlxBannerView bannerAD;
    private AlxInterstitialAD interstitialAD;
    private AlxRewardVideoAD rewardVideoAD;
    private AlxNativeAd nativeAD;
    private AlxNativeAdView nativeAdView;

    public AlgorixMediationAdapter(AppLovinSdk appLovinSdk) {
        super(appLovinSdk);
    }

    @Override
    public void initialize(MaxAdapterInitializationParameters parameters, Activity activity, final OnCompletionListener onCompletionListener) {
        Log.d(TAG, "initialize alx sdk……");
        Log.d(TAG, "alx-applovin-adapter-version:" + ADAPTER_VERSION);
        try {
            status = InitializationStatus.INITIALIZING;
            Context context = (activity != null) ? activity.getApplicationContext() : getApplicationContext();

            Bundle bundle = parameters.getCustomParameters();
            String host = bundle.getString("host");
            String appid = bundle.getString("appid");
            String sid = bundle.getString("sid");
            String token = bundle.getString("token");
            String debug = bundle.getString("isdebug");
            Boolean isDebug = null;
            if(debug != null){
                if(debug.equalsIgnoreCase("true")){
                    isDebug = Boolean.TRUE;
                }else if(debug.equalsIgnoreCase("false")){
                    isDebug = Boolean.FALSE;
                }
            }

            Log.d(TAG, "alx-applovin-init:host=" + host + " token=" + token + "  sid=" + sid + " appid=" + appid);

            if (TextUtils.isEmpty(host) || TextUtils.isEmpty(appid) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(token)) {
                Log.d(TAG, "initialize alx params: host or appid or sid or token is null");
                status = InitializationStatus.DOES_NOT_APPLY;
                onCompletionListener.onCompletion(status, null);
            } else {
                if(isDebug != null) {
                    AlxAdSDK.setDebug(isDebug.booleanValue());
                }
                AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                    @Override
                    public void onInit(boolean isOk, String msg) {
                        status = InitializationStatus.INITIALIZED_SUCCESS;
                        onCompletionListener.onCompletion(status, null);
                    }
                });
                // // set GDPR
                // // Subject to GDPR Flag: Please pass a Boolean value to indicate if the user is subject to GDPR regulations or not.
                // // Your app should make its own determination as to whether GDPR is applicable to the user or not.
                // AlxAdSDK.setSubjectToGDPR(true);

                if (parameters != null) {
                    // Set GDPR Consent value
                    String strGDPRConsent = "0";
                    if (TextUtils.isEmpty(parameters.getConsentString())) {
                        if (AppLovinPrivacySettings.hasUserConsent(context)) {
                            try {
                                SharedPreferences mPreferences = PreferenceManager.getDefaultSharedPreferences(context);
                                strGDPRConsent = mPreferences.getString("IABTCF_TCString", "");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (TextUtils.isEmpty(strGDPRConsent)) {
                                strGDPRConsent = "1";
                            }
                        }
                        AlxAdSDK.setUserConsent(strGDPRConsent);
                    } else {
                        AlxAdSDK.setUserConsent(parameters.getConsentString());
                    }
                    Log.i(TAG, "Max parameter hasUserConsent:" + parameters.hasUserConsent()
                            + " getConsentString:" + parameters.getConsentString()
                            + " isAgeRestrictedUser:" + parameters.isAgeRestrictedUser()
                            + " hasUserConsent-2:" + AppLovinPrivacySettings.hasUserConsent(context));
                }

                // // set COPPA true or false
                // AlxAdSDK.setBelowConsentAge(true);
                // // set CCPA
                // AlxAdSDK.subjectToUSPrivacy("1YYY");

            }
        } catch (Exception e) {
            Log.d(TAG, "initialize alx error:" + e.getMessage());
            status = InitializationStatus.INITIALIZED_FAILURE;
            onCompletionListener.onCompletion(status, null);
        }
    }

    @Override
    public String getSdkVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getAdapterVersion() {
        return ADAPTER_VERSION;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        if (bannerAD != null) {
            bannerAD.destroy();
            bannerAD = null;
        }

        if (interstitialAD != null) {
            interstitialAD.destroy();
            interstitialAD = null;
        }

        if (rewardVideoAD != null) {
            rewardVideoAD.destroy();
            rewardVideoAD = null;
        }

        if (nativeAD != null) {
            nativeAD.destroy();
            nativeAD = null;
        }

        if (nativeAdView != null) {
            nativeAdView.destroy();
            nativeAdView = null;
        }
    }

    //banner load
    @Override
    public void loadAdViewAd(MaxAdapterResponseParameters parameters, MaxAdFormat maxAdFormat, Activity activity, final MaxAdViewAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadAdViewAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onAdViewAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        bannerAD = new AlxBannerView(activity);
        bannerAD.setBannerRefresh(0);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (listener != null) {
                    listener.onAdViewAdLoaded(bannerAD);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.e(TAG, "onAdError: errCode=" + errorCode + ";errMsg=" + errorMsg);
                if (listener != null) {
                    listener.onAdViewAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onAdClicked() {
                if (listener != null) {
                    listener.onAdViewAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (listener != null) {
                    listener.onAdViewAdDisplayed();
                }
            }

            @Override
            public void onAdClose() {
                if (listener != null) {
                    listener.onAdViewAdHidden();
                }
            }
        };
        // 320 * 50 banner
        bannerAD.loadAd(adId, alxBannerADListener);
        // MREC
        //bannerAD.loadAd(adId, AlxBannerView.AlxAdParam.FORMAT_MREC, alxBannerADListener);
    }

    //interstitial ad load
    @Override
    public void loadInterstitialAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxInterstitialAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadInterstitialAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onInterstitialAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        interstitialAD = new AlxInterstitialAD();
        interstitialAD.load(activity, adId, new AlxInterstitialADListener() {
            @Override
            public void onInterstitialAdLoaded() {
                if (listener != null) {
                    listener.onInterstitialAdLoaded();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                Log.e(TAG, "onInterstitialAdLoadFail: errCode=" + errorCode + ";errMsg=" + errorMsg);
                if (listener != null) {
                    listener.onInterstitialAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (listener != null) {
                    listener.onInterstitialAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (listener != null) {
                    listener.onInterstitialAdDisplayed();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (listener != null) {
                    listener.onInterstitialAdHidden();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {

            }
        });
    }

    //interstitial ad show
    @Override
    public void showInterstitialAd(MaxAdapterResponseParameters parameters, Activity activity, MaxInterstitialAdapterListener listener) {
        Log.d(TAG, "showInterstitialAd");
        if (interstitialAD != null && interstitialAD.isReady()) {
            interstitialAD.show(activity);
        } else {
            Log.d(TAG, "showInterstitialAd: ad no ready");
            listener.onInterstitialAdDisplayFailed(MaxAdapterError.AD_NOT_READY);
        }
    }

    //reward ad load
    @Override
    public void loadRewardedAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxRewardedAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadRewardedAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onRewardedAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        rewardVideoAD = new AlxRewardVideoAD();
        rewardVideoAD.load(activity, adId, new AlxRewardVideoADListener() {
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdLoaded");
                if (listener != null) {
                    listener.onRewardedAdLoaded();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.e(TAG, "onRewardedVideoAdFailed: errCode=" + errCode + ";errMsg=" + errMsg);
                if (listener != null) {
                    listener.onRewardedAdLoadFailed(MaxAdapterError.NO_FILL);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayStart");
                if (listener != null) {
                    listener.onRewardedAdDisplayed();
                    listener.onRewardedAdVideoStarted();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayEnd");
                if (listener != null) {
                    listener.onRewardedAdVideoCompleted();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                Log.d(TAG, "onRewardedVideoAdPlayFailed");
                if (listener != null) {
                    listener.onRewardedAdDisplayFailed(new MaxAdapterError(errCode, errMsg));
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdClosed");
                if (listener != null) {
                    listener.onRewardedAdHidden();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                Log.d(TAG, "onRewardedVideoAdPlayClicked");
                if (listener != null) {
                    listener.onRewardedAdClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                Log.d(TAG, "onReward");
                if (listener != null) {
                    listener.onUserRewarded(getReward());
                }
            }
        });
    }

    //reward ad
    @Override
    public void showRewardedAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxRewardedAdapterListener listener) {
        Log.d(TAG, "showRewardedAd");
        if (rewardVideoAD != null && rewardVideoAD.isReady()) {
            rewardVideoAD.showVideo(activity);
        } else {
            Log.d(TAG, "showRewardedAd: ad no ready");
            listener.onRewardedAdDisplayFailed(MaxAdapterError.AD_NOT_READY);
        }
    }

    //native ad
    @Override
    public void loadNativeAd(MaxAdapterResponseParameters parameters, Activity activity, final MaxNativeAdAdapterListener listener) {
        String adId = parameters.getThirdPartyAdPlacementId();
        Log.d(TAG, "loadNativeAd ad id:" + adId);
        if (TextUtils.isEmpty(adId)) {
            listener.onNativeAdLoadFailed(MaxAdapterError.INVALID_CONFIGURATION);
            return;
        }
        Context applicationContext = (activity != null) ? activity.getApplicationContext() : getApplicationContext();
        NativeAdListener nativeAdListener = new NativeAdListener(parameters, applicationContext, listener);
        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(activity, adId).build();
        loader.loadAd(new AlxAdParam.Builder().build(), nativeAdListener);
    }

    private class NativeAdListener implements AlxNativeAdLoadedListener {
        private MaxAdapterResponseParameters parameters;
        private MaxNativeAdAdapterListener listener;
        final Bundle serverParameters;
        private Context context;

        public NativeAdListener(MaxAdapterResponseParameters parameters, Context context, MaxNativeAdAdapterListener listener) {
            this.parameters = parameters;
            this.context = context;
            this.listener = listener;
            serverParameters = parameters.getServerParameters();
        }

        @Override
        public void onAdFailed(int errorCode, String errorMsg) {
            Log.e(TAG, "native-onAdLoadedFail: errCode=" + errorCode + ";errMsg=" + errorMsg);
            if (listener != null) {
                listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
            }
        }

        public void reportEvent(int event, String desc) {
//            if (alxNativeAD != null) {
//                alxNativeAD.reportEvent(event, desc);
//            }
        }

        @Override
        public void onAdLoaded(List<AlxNativeAd> ads) {
            if (ads == null || ads.isEmpty()) {
                if (listener != null) {
                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                }
                return;
            }
            nativeAD = ads.get(0);
            if (nativeAD == null) {
                if (listener != null) {
                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                }
                return;
            }

            String templateName = BundleUtils.getString("template", "", serverParameters);
            final boolean isTemplateAd = AppLovinSdkUtils.isValidString(templateName);
            if (isTemplateAd && TextUtils.isEmpty(nativeAD.getTitle())) {
                e("Native ad (" + nativeAD + ") does not have required assets.");
                listener.onNativeAdLoadFailed(new MaxAdapterError(-5400, "Missing Native Ad Assets"));
                return;
            }

            nativeAD.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    if (listener != null) {
                        listener.onNativeAdClicked();
                    }
                }

                @Override
                public void onAdImpression() {
                    if (listener != null) {
                        listener.onNativeAdDisplayed(null);
                    }
                }

                @Override
                public void onAdClosed() {
                }
            });

            getCachingExecutorService().execute(new Runnable() {
                @Override
                public void run() {
                    Future<Drawable> iconDrawableFuture = null;
                    try {
                        if (nativeAD.getIcon() != null && !TextUtils.isEmpty(nativeAD.getIcon().getImageUrl()) && context != null) {
                            reportEvent(202, "[max] icon load");
                            iconDrawableFuture = createDrawableFuture(nativeAD.getIcon().getImageUrl(), context.getResources());
                            reportEvent(203, "[max] icon success");
                        }
                    } catch (Throwable th) {
                        reportEvent(204, "[max] icon error:" + th.getMessage());
                        e("Image fetching tasks failed", th);
                    }

                    Future<Drawable> imageDrawableFuture = null;
                    try {
                        if (nativeAD.getImages() != null && nativeAD.getImages().size() > 0) {
                            AlxImage image = nativeAD.getImages().get(0);
                            if (image != null && !TextUtils.isEmpty(image.getImageUrl()) && context != null) {
                                reportEvent(202, "[max] image load");
                                imageDrawableFuture = createDrawableFuture(image.getImageUrl(), context.getResources());
                                reportEvent(203, "[max] image success");
                            }
                        }
                    } catch (Throwable th) {
                        reportEvent(204, "[max] image error:" + th.getMessage());
                        e("Image fetching tasks failed", th);
                    }

                    Drawable iconDrawable = null;
                    Drawable mediaViewImageDrawable = null;
                    try {
                        // Execute and timeout tasks if incomplete within the given time
                        int imageTaskTimeoutSeconds = BundleUtils.getInt("image_task_timeout_seconds", DEFAULT_IMAGE_TASK_TIMEOUT_SECONDS, parameters.getServerParameters());
                        if (iconDrawableFuture != null) {
                            iconDrawable = iconDrawableFuture.get(imageTaskTimeoutSeconds, TimeUnit.SECONDS);
                        }
                        if (imageDrawableFuture != null) {
                            mediaViewImageDrawable = imageDrawableFuture.get(imageTaskTimeoutSeconds, TimeUnit.SECONDS);
                        }
                    } catch (Throwable th) {
                        e("Image fetching tasks failed", th);
                    }

                    final MaxNativeAd.MaxNativeAdImage icon = iconDrawable != null ? new MaxNativeAd.MaxNativeAdImage(iconDrawable) : null;
                    final Drawable mediaDrawable = mediaViewImageDrawable;

                    AppLovinSdkUtils.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (nativeAD == null) {
                                if (listener != null) {
                                    listener.onNativeAdLoadFailed(MaxAdapterError.NO_FILL);
                                }
                                return;
                            }

                            View mediaView = null;
                            AlxMediaContent mediaContent = nativeAD.getMediaContent();
                            if (mediaContent != null) {
                                AlxMediaView alxMediaView = new AlxMediaView(context);
                                alxMediaView.setMediaContent(mediaContent);
                                mediaView = alxMediaView;
                            } else if (mediaDrawable != null && context != null) {
                                ImageView imageView = new ImageView(context);
                                imageView.setImageDrawable(mediaDrawable);
                                mediaView = imageView;
                            }

                            ImageView logoView = null;
                            if (nativeAD.getAdLogo() != null && context != null) {
                                logoView = new ImageView(context);
                                logoView.setImageBitmap(nativeAD.getAdLogo());
                            }

                            MaxNativeAd.Builder maxBuilder = new MaxNativeAd.Builder()
                                    .setAdFormat(MaxAdFormat.NATIVE)
                                    .setTitle(nativeAD.getTitle())
                                    .setBody(nativeAD.getDescription())
                                    .setCallToAction(nativeAD.getCallToAction())
                                    .setAdvertiser(nativeAD.getAdSource())
                                    .setIcon(icon)
                                    .setMediaView(mediaView)
                                    .setOptionsView(logoView);

                            if (AppLovinSdk.VERSION_CODE >= 11_04_03_99) {
                                maxBuilder.setMainImage(new MaxNativeAd.MaxNativeAdImage(mediaDrawable));
                            }

                            MaxNativeAd maxNativeAd = new MaxAlgorixNativeAd(maxBuilder);

                            reportEvent(205, "[max] max show");
                            Log.d(TAG, "Native ad fully loaded:");
                            if (listener != null) {
                                listener.onNativeAdLoaded(maxNativeAd, null);
                            }
                        }
                    });
                }
            });
        }
    }

    private class MaxAlgorixNativeAd extends MaxNativeAd {

        public MaxAlgorixNativeAd(Builder builder) {
            super(builder);
        }

        @Override
        public void prepareViewForInteraction(final MaxNativeAdView maxNativeAdView) {
            final AlxNativeAd nativeAD = AlgorixMediationAdapter.this.nativeAD;
            if (nativeAD == null) {
                e("Failed to register native ad view. Native ad is null");
                return;
            }

            nativeAdView = new AlxNativeAdView(maxNativeAdView.getContext());
            View mainView = maxNativeAdView.getMainView();
            maxNativeAdView.removeView(mainView);
            nativeAdView.addView(mainView);
            maxNativeAdView.addView(nativeAdView);

            nativeAdView.setIconView(maxNativeAdView.getIconImageView());
            nativeAdView.setTitleView(maxNativeAdView.getTitleTextView());
            nativeAdView.setAdSourceView(maxNativeAdView.getAdvertiserTextView());
            nativeAdView.setDescriptionView(maxNativeAdView.getBodyTextView());
            nativeAdView.setCallToActionView(maxNativeAdView.getCallToActionButton());

            View mediaView = getMediaView();
            if (mediaView instanceof AlxMediaView) {
                nativeAdView.setMediaView((AlxMediaView) mediaView);
            } else if (mediaView instanceof ImageView) {
                nativeAdView.setImageView(mediaView);
            }
            nativeAdView.setNativeAd(nativeAD);
        }
    }

}
```

####  6.3 IronSource adapter source code:

##### Banner:

```Java
package com.ironsource.adapters.custom.algorix;

import android.app.Activity;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.ironsource.mediationsdk.ISBannerSize;
import com.ironsource.mediationsdk.adunit.adapter.BaseBanner;
import com.ironsource.mediationsdk.adunit.adapter.listener.BannerAdListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

public class AlgoriXCustomBanner extends BaseBanner<AlgoriXCustomAdapter> {
    public static final String TAG = "AlgoriXCustomBanner";

    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    private AlxBannerView mBannerView;
    private BannerAdListener mListener;

    public AlgoriXCustomBanner(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @Override
    public void loadAd(final AdData adData, final Activity activity, ISBannerSize isBannerSize, BannerAdListener listener) {
        Log.d(TAG, "loadAd");
        mListener = listener;
        algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
            @Override
            public void onInitSuccess() {
                String unitid = (String) adData.getConfiguration().get("unitid");
                Log.d(TAG, "onInitSuccess: unitid :" + unitid);
                if (TextUtils.isEmpty(unitid)) {
                    if (mListener != null) {
                        mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, 1, "unitid is empty");
                    }
                    return;
                }
                requestBanner(unitid, activity);
            }

            @Override
            public void onInitFailed(int i, String s) {
                Log.d(TAG, "onInitFailed: errorCode=" + i + ";errorMsg=" + s);
                if (mListener != null) {
                    mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, i, s);
                }
            }
        });
    }

    private void requestBanner(String unitid, Activity activity) {
        mBannerView = new AlxBannerView(activity);
        mBannerView.setBannerRefresh(0);
        AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mListener != null) {
                    FrameLayout.LayoutParams params;
                    if (mBannerView.getLayoutParams() != null && mBannerView.getLayoutParams() instanceof FrameLayout.LayoutParams) {
                        params = (FrameLayout.LayoutParams) mBannerView.getLayoutParams();
                    } else {
                        params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
//                        params.gravity = Gravity.CENTER;
                    }
                    mListener.onAdLoadSuccess(mBannerView, params);
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                if (mListener != null) {
                    mListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errorCode, errorMsg);
                }
            }

            @Override
            public void onAdClicked() {
                if (mListener != null) {
                    mListener.onAdClicked();
                    mListener.onAdLeftApplication();
                }
            }

            @Override
            public void onAdShow() {
                if (mListener != null) {
                    mListener.onAdOpened();
                }
            }

            @Override
            public void onAdClose() {
            }
        };
        mBannerView.loadAd(unitid, alxBannerADListener);
    }

    @Override
    public void destroyAd(AdData adData) {
        if (mBannerView != null) {
            mBannerView.destroy();
        }
    }
}
```

##### Interstitial:

```Java
package com.ironsource.adapters.custom.algorix;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.ironsource.mediationsdk.adunit.adapter.BaseInterstitial;
import com.ironsource.mediationsdk.adunit.adapter.listener.InterstitialAdListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

/**
 * IronSource Rewarded ad adapter
 */
public class AlgoriXCustomInterstitial extends BaseInterstitial<AlgoriXCustomAdapter> {

    private static final String TAG = "AlgoriXCustomInterstitial";
    private AlxInterstitialAD alxInterstitialAD;
    private String unitid = "";
    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    InterstitialAdListener mInterstitialAdListener;
    private Context mContext;

    public AlgoriXCustomInterstitial(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void loadAd(final AdData adData, Activity activity, InterstitialAdListener interstitialAdListener) {
        Log.d(TAG, "loadAd:");
        mContext = activity;
        try {
            mInterstitialAdListener = interstitialAdListener;
            algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
                @Override
                public void onInitSuccess() {
                    unitid = (String) adData.getConfiguration().get("unitid");
                    Log.d(TAG, "onInitSuccess: unitid :" + unitid);

                    startAdLoad(mContext);
                }

                @SuppressLint("LongLogTag")
                @Override
                public void onInitFailed(int i, String s) {
                    Log.d(TAG, "Init Failed errCode:" + i + " errMsg: " + s);
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @SuppressLint("LongLogTag")
    private void startAdLoad(Context context) {
        Log.d(TAG, "startAdLoad:");
        alxInterstitialAD = new AlxInterstitialAD();
        alxInterstitialAD.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdLoadSuccess();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errorCode, errorMsg);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdShowSuccess();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mInterstitialAdListener != null) {
                    mInterstitialAdListener.onAdClosed();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {
                if (mInterstitialAdListener != null) {
                }
            }

            @Override
            public void onInterstitialAdVideoEnd() {
                if (mInterstitialAdListener != null) {

                }
            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
                if (mInterstitialAdListener != null) {
                }
            }
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public void showAd(AdData adData, InterstitialAdListener interstitialAdListener) {
        if (mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            if (alxInterstitialAD != null) {
                alxInterstitialAD.show(activity);
            }
        } else {
            Log.e(TAG, "context is not an Activity");
        }

    }

    @Override
    public boolean isAdAvailable(AdData adData) {
        if (alxInterstitialAD != null) {
            return alxInterstitialAD.isReady();
        }
        return false;

    }

}
```

##### RewardVideo:

```Java
package com.ironsource.adapters.custom.algorix;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.util.Log;

import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.ironsource.mediationsdk.adunit.adapter.listener.RewardedVideoAdListener;
import com.ironsource.mediationsdk.adunit.adapter.BaseRewardedVideo;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrorType;
import com.ironsource.mediationsdk.model.NetworkSettings;

/**
 * IronSource Rewarded ad adapter
 */
public class AlgoriXCustomRewardedVideo extends BaseRewardedVideo<AlgoriXCustomAdapter> {

    private static final String TAG = "AlgoriXCustomRewardedVideo";
    private AlxRewardVideoAD alxRewardVideoAD;

    private String unitid = "";
    AlgoriXCustomAdapter algoriXCustomAdapter = getNetworkAdapter();
    RewardedVideoAdListener mRewardedVideoAdListener;

    private Context mContext;

    public AlgoriXCustomRewardedVideo(NetworkSettings networkSettings) {
        super(networkSettings);
    }

    @SuppressLint("LongLogTag")
    @Override
    public void showAd(AdData adData, RewardedVideoAdListener listener) {
        if(mContext instanceof Activity) {
            Activity activity = (Activity) mContext;
            if(alxRewardVideoAD != null){
                alxRewardVideoAD.showVideo(activity);
            }
        }else {
            Log.e(TAG, "context is not an Activity");
        }

    }

    @Override
    public boolean isAdAvailable(AdData adData) {
        if (alxRewardVideoAD != null) {
            return alxRewardVideoAD.isReady();
        }
        return false;

    }

    @SuppressLint("LongLogTag")
    @Override
    public void loadAd(final AdData adData, Activity activity, RewardedVideoAdListener rewardedVideoAdListener) {
        Log.d(TAG, "loadAd:");
        mContext = activity;
        mRewardedVideoAdListener = rewardedVideoAdListener;
        algoriXCustomAdapter.init(adData, activity, new NetworkInitializationListener() {
            @Override
            public void onInitSuccess() {
                unitid = (String) adData.getConfiguration().get("unitid");
                Log.d(TAG, "onInitSuccess: unitid :" + unitid);

                startAdLoad(mContext);
            }

            @Override
            public void onInitFailed(int i, String s) {
                Log.d(TAG, "Init Failed errCode:" + i + " errMsg: " + s);
            }
        });
    }
    @SuppressLint("LongLogTag")
    private void startAdLoad(Context context){
        Log.d(TAG, "startAdLoad:");
        alxRewardVideoAD = new AlxRewardVideoAD();
        alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {

                Log.d(TAG, "onRewardedVideoAdLoaded:");
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdLoadSuccess();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.d(TAG, "onRewardedVideoAdFailed:");
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdLoadFailed(AdapterErrorType.ADAPTER_ERROR_TYPE_NO_FILL, errCode, errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdStarted();
                    mRewardedVideoAdListener.onAdOpened();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdEnded();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdShowFailed(errCode,errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdClosed();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                if(mRewardedVideoAdListener != null){
                    mRewardedVideoAdListener.onAdRewarded();
                }

            }
        });
    }
}
```

##### AlgoriXCustomAdapter：

```Java
package com.ironsource.adapters.custom.algorix;


import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxSdkInitCallback;
import com.ironsource.mediationsdk.adunit.adapter.BaseAdapter;
import com.ironsource.mediationsdk.adunit.adapter.listener.NetworkInitializationListener;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdData;
import com.ironsource.mediationsdk.adunit.adapter.utility.AdapterErrors;

import java.util.Map;

public class AlgoriXCustomAdapter extends BaseAdapter {

    private static final String TAG = "AlgoriXCustomAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = AlxMetaInf.ADAPTER_SDK_HOST_URL;
    private Boolean isDebug = null;
    private NetworkInitializationListener mNetworkInitializationListener;

    @Override
    public void init(AdData adData, Context context, NetworkInitializationListener networkInitializationListener) {
        Log.d(TAG, "alx-ironsource-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "AlgoriX SDK Init");

        try {
            mNetworkInitializationListener = networkInitializationListener;
            if (parseServer(adData)) {
                Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

                if (isDebug != null) {
                    AlxAdSDK.setDebug(isDebug.booleanValue());
                }
                AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                    @Override
                    public void onInit(boolean isOk, String msg) {
                        if (isOk) {
                            mNetworkInitializationListener.onInitSuccess();
                        } else {
                            mNetworkInitializationListener.onInitFailed(AdapterErrors.ADAPTER_ERROR_MISSING_PARAMS, "AlxSdk Init Failed");
                        }
                    }
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private boolean parseServer(AdData adData) {
        try {
            Map<String, Object> serverExtras = adData.getConfiguration();
//            if (serverExtras.containsKey("host")) {
//                host = (String) serverExtras.get("host");
//            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mNetworkInitializationListener != null) {
                mNetworkInitializationListener.onInitFailed(AdapterErrors.ADAPTER_ERROR_MISSING_PARAMS, "alx host | unitid | token | sid | appid is empty");
            }
            return false;
        }
        return true;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getAdapterVersion() {
        return AlxMetaInf.ADAPTER_VERSION;
    }
}
```

##### AlxMetaInf：

```Java
package com.ironsource.adapters.custom.algorix;

/**
 * IronSource Adapter version information
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    //String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "https://alpha.svr.rixengine.com/rtb";

}
```

#### 6.4  Topon adapter source code

##### Banner：

```Java
package com.anythink.custom.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxBannerView;
import com.rixengine.api.AlxBannerViewAdListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.banner.unitgroup.api.CustomBannerAdapter;

import java.util.Map;

/**
 * TopOn Banner ad adapter
 */
public class AlxBannerAdapter extends CustomBannerAdapter {
    private static final String TAG = "AlxBannerAdapter";
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;
    AlxBannerView mBannerView;


    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> localExtras) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        } else {
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx apppid | token | sid | appid is empty.");
            }
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }

            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    Log.i(TAG, "sdk onInit:" + isOk);
                    loadAd(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void loadAd(Context context) {
        mBannerView = new AlxBannerView(context);
        final AlxBannerViewAdListener alxBannerADListener = new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onAdClicked() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdClicked();
                }
            }

            @Override
            public void onAdShow() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdShow();
                }
            }

            @Override
            public void onAdClose() {
                if (mImpressionEventListener != null) {
                    mImpressionEventListener.onBannerAdClose();
                }
            }
        };
        // auto refresh ad  default = open = 1, 0 = close
        mBannerView.setBannerRefresh(0);
        mBannerView.loadAd(unitid, alxBannerADListener);
    }

    @Override
    public View getBannerView() {
        return mBannerView;
    }

    @Override
    public void destory() {
        if (mBannerView != null) {
            mBannerView.destroy();
            mBannerView = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

}
```

##### Native：

```Java
package com.anythink.custom.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;

import com.rixengine.api.AlxAdParam;
import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxImage;
import com.rixengine.api.AlxSdkInitCallback;
import com.rixengine.api.nativead.AlxMediaContent;
import com.rixengine.api.nativead.AlxMediaView;
import com.rixengine.api.nativead.AlxNativeAd;
import com.rixengine.api.nativead.AlxNativeAdLoadedListener;
import com.rixengine.api.nativead.AlxNativeAdLoader;
import com.rixengine.api.nativead.AlxNativeAdView;
import com.rixengine.api.nativead.AlxNativeEventListener;
import com.anythink.nativead.api.ATNativePrepareInfo;
import com.anythink.nativead.unitgroup.api.CustomNativeAd;
import com.anythink.nativead.unitgroup.api.CustomNativeAdapter;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * TopOn in-feed ad adapter
 */
public class AlxNativeAdapter extends CustomNativeAdapter {
    private final String TAG = AlxNativeAdapter.class.getSimpleName();

    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void startAdLoad(final Context context) {
        AlxNativeAdLoadedListener loadListener = new AlxNativeAdLoadedListener() {
            @Override
            public void onAdFailed(int errorCode, String errorMsg) {
                Log.i(TAG, "onAdLoadedFail:" + errorCode + ";" + errorMsg);
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onAdLoaded(List<AlxNativeAd> ads) {
                if (ads == null || ads.isEmpty()) {
                    if (mLoadListener != null) {
                        mLoadListener.onAdLoadError("100", "no fill");
                    }
                    return;
                }

                AlgorixNativeAd[] result = new AlgorixNativeAd[ads.size()];
                boolean isOk = false;
                try {
                    for (int i = 0; i < ads.size(); i++) {
                        AlxNativeAd item = ads.get(i);
                        AlgorixNativeAd bean = new AlgorixNativeAd(context, item);
                        bean.setAdData();
                        result[i] = bean;
                    }
                    isOk = true;
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage());
                    e.printStackTrace();
                    isOk = false;
                    if (mLoadListener != null) {
                        mLoadListener.onAdLoadError("101", e.getMessage());
                    }
                }
                if (isOk) {
                    if (mLoadListener != null) {
                        mLoadListener.onAdCacheLoaded(result);
                    }
                }
            }
        };

        AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(context, unitid).build();
        loader.loadAd(new AlxAdParam.Builder().build(), loadListener);
    }

    private class AlgorixNativeAd extends CustomNativeAd {

        private Context mContext;

        private AlxNativeAd mNativeAd;
        private AlxNativeAdView mAdContainer;
        private AlxMediaView mMediaView;

        public AlgorixNativeAd(Context context, AlxNativeAd nativeAd) {
            mContext = context.getApplicationContext();
            mNativeAd = nativeAd;
        }

        public void setAdData() {
            if (mNativeAd == null) {
                return;
            }
            bindListener();

            setTitle(mNativeAd.getTitle());
            setDescriptionText(mNativeAd.getDescription());

            String iconUrl = "";
            String imageUrl = "";
            if (mNativeAd.getIcon() != null) {
                iconUrl = mNativeAd.getIcon().getImageUrl();
            }
            List<String> list = new ArrayList<>();
            List<AlxImage> imageList = mNativeAd.getImages();
            if (imageList != null && imageList.size() > 0) {
                AlxImage image0 = imageList.get(0);
                if (image0 != null) {
                    imageUrl = image0.getImageUrl();
                }
                for (AlxImage item : imageList) {
                    if (item != null && item.getImageUrl() != null) {
                        list.add(item.getImageUrl());
                    }
                }
            }
            setIconImageUrl(iconUrl);
            setMainImageUrl(imageUrl);
            setImageUrlList(list);
            setAdFrom(mNativeAd.getAdSource());
            setCallToActionText(mNativeAd.getCallToAction());
        }

        @Override
        public Bitmap getAdLogo() {
            if (mNativeAd != null) {
                return mNativeAd.getAdLogo();
            }
            return null;
        }

        @Override
        public void prepare(View view, ATNativePrepareInfo nativePrepareInfo) {
            if (view == null) {
                return;
            }

            try {
                if (mAdContainer == null) {
                    return;
                }
                if (nativePrepareInfo != null) {
                    List<View> clickViewList = nativePrepareInfo.getClickViewList();
                    if (clickViewList != null && !clickViewList.isEmpty()) {
                        for (int i = 0; i < clickViewList.size(); i++) {
                            String key = String.valueOf(1000 + i);
                            mAdContainer.addView(key, clickViewList.get(i));
                        }
                    }
                }
                if (mMediaView != null) {
                    mAdContainer.setMediaView(mMediaView);
                }
                mAdContainer.setNativeAd(mNativeAd);
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            }
        }

        @Override
        public boolean isNativeExpress() {
            Log.d(TAG, "isNativeExpress");
            return false;
        }

        @Override
        public ViewGroup getCustomAdContainer() {
            Log.d(TAG, "getCustomAdContainer");
            mAdContainer = new AlxNativeAdView(mContext);
            return mAdContainer;
        }

        @Override
        public View getAdMediaView(Object... objects) {
            Log.d(TAG, "getAdMediaView");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
                mMediaView = new AlxMediaView(mContext);
                if (mNativeAd != null && mNativeAd.getMediaContent() != null) {
                    mNativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {

                        @Override
                        public void onVideoStart() {
                            notifyAdVideoStart();
                        }

                        @Override
                        public void onVideoEnd() {
                            notifyAdVideoEnd();
                        }
                    });
                    mMediaView.setMediaContent(mNativeAd.getMediaContent());
                }
                return mMediaView;
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
            return null;
        }

        @Override
        public void clear(View view) {
            Log.d(TAG, "clear");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
        }

        @Override
        public void destroy() {
            Log.d(TAG, "destroy");
            try {
                if (mMediaView != null) {
                    mMediaView.destroy();
                    mMediaView = null;
                }
                if (mAdContainer != null) {
                    mAdContainer.destroy();
                    mAdContainer = null;
                }
            } catch (Exception e) {
                e.printStackTrace();
                Log.e(TAG, e.getMessage());
            }
        }

        private void bindListener() {
            if (mNativeAd == null) {
                return;
            }
            mNativeAd.setNativeEventListener(new AlxNativeEventListener() {
                @Override
                public void onAdClicked() {
                    notifyAdClicked();
                }

                @Override
                public void onAdImpression() {
                    notifyAdImpression();
                }

                @Override
                public void onAdClosed() {
                    notifyAdDislikeClick();
                }
            });
        }

    }

    @Override
    public void destory() {
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }
}
```

##### Interstitial：

```Java
package com.anythink.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxInterstitialAD;
import com.rixengine.api.AlxInterstitialADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.interstitial.unitgroup.api.CustomInterstitialAdapter;

import java.util.Map;

/**
 * TopOn Interstitial ad adapter
 */
public class AlxInterstitialAdapter extends CustomInterstitialAdapter {

    private static final String TAG = "AlxInterstitialAdapter";

    private AlxInterstitialAD alxInterstitialAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void startAdLoad(Context context) {
        alxInterstitialAD = new AlxInterstitialAD();
        alxInterstitialAD.load(context, unitid, new AlxInterstitialADListener() {

            @Override
            public void onInterstitialAdLoaded() {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onInterstitialAdLoadFail(int errorCode, String errorMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errorCode + "", errorMsg);
                }
            }

            @Override
            public void onInterstitialAdClicked() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdClicked();
                }
            }

            @Override
            public void onInterstitialAdShow() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdShow();
                }
            }

            @Override
            public void onInterstitialAdClose() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdClose();
                }
            }

            @Override
            public void onInterstitialAdVideoStart() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoStart();
                }
            }

            @Override
            public void onInterstitialAdVideoEnd() {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoEnd();
                }
            }

            @Override
            public void onInterstitialAdVideoError(int errorCode, String errorMsg) {
                if (mImpressListener != null) {
                    mImpressListener.onInterstitialAdVideoError(String.valueOf(errorCode), errorMsg);
                }
            }
        });
    }


    @Override
    public void show(Activity activity) {
        if (alxInterstitialAD != null) {
            alxInterstitialAD.show(activity);
        }
    }

    @Override
    public void destory() {
        if (alxInterstitialAD != null) {
            alxInterstitialAD.destroy();
            alxInterstitialAD = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public boolean isAdReady() {
        if (alxInterstitialAD != null) {
            return alxInterstitialAD.isReady();
        }
        return false;
    }
}
```

##### RewardVideo：

```Java
package com.anythink.custom.adapter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.rixengine.api.AlxAdSDK;
import com.rixengine.api.AlxRewardVideoAD;
import com.rixengine.api.AlxRewardVideoADListener;
import com.rixengine.api.AlxSdkInitCallback;
import com.anythink.rewardvideo.unitgroup.api.CustomRewardVideoAdapter;

import java.util.Map;

/**
 * TopOn rewarded video adapter
 */
public class AlxRewardVideoAdapter extends CustomRewardVideoAdapter {

    private static final String TAG = "AlxRewardVideoAdapter";
    private AlxRewardVideoAD alxRewardVideoAD;
    private String unitid = "";
    private String appid = "";
    private String sid = "";
    private String token = "";
    private String host = "";
    private Boolean isDebug = null;

    @Override
    public void loadCustomNetworkAd(Context context, Map<String, Object> serverExtras, Map<String, Object> map1) {
        Log.d(TAG, "alx-topon-adapter-version:" + AlxMetaInf.ADAPTER_VERSION);
        Log.i(TAG, "loadCustomNetworkAd");
        if (parseServer(serverExtras)) {
            initSdk(context);
        }
    }

    private boolean parseServer(Map<String, Object> serverExtras) {
        try {
            if (serverExtras.containsKey("host")) {
                host = (String) serverExtras.get("host");
            }
            if (serverExtras.containsKey("appid")) {
                appid = (String) serverExtras.get("appid");
            }
            if (serverExtras.containsKey("sid")) {
                sid = (String) serverExtras.get("sid");
            }
            if (serverExtras.containsKey("token")) {
                token = (String) serverExtras.get("token");
            }
            if (serverExtras.containsKey("unitid")) {
                unitid = (String) serverExtras.get("unitid");
            }

            if (serverExtras.containsKey("isdebug")) {
                Object obj = serverExtras.get("isdebug");
                String debug = null;
                if (obj != null && obj instanceof String) {
                    debug = (String) obj;
                }
                Log.e(TAG, "alx debug mode:" + debug);
                if (debug != null) {
                    if (debug.equalsIgnoreCase("true")) {
                        isDebug = Boolean.TRUE;
                    } else if (debug.equalsIgnoreCase("false")) {
                        isDebug = Boolean.FALSE;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (TextUtils.isEmpty(host) || TextUtils.isEmpty(unitid) || TextUtils.isEmpty(token) || TextUtils.isEmpty(sid) || TextUtils.isEmpty(appid)) {
            Log.i(TAG, "alx host | unitid | token | sid | appid is empty");
            if (mLoadListener != null) {
                mLoadListener.onAdLoadError("", "alx host | unitid | token | sid | appid is empty.");
            }
            return false;
        }
        return true;
    }

    private void initSdk(final Context context) {
        try {
            Log.i(TAG, "alx ver:" + AlxAdSDK.getNetWorkVersion() + " alx host: " + host + " alx token: " + token + " alx appid: " + appid + " alx sid: " + sid);

            if (isDebug != null) {
                AlxAdSDK.setDebug(isDebug.booleanValue());
            }
            AlxAdSDK.init(context, host, token, sid, appid, new AlxSdkInitCallback() {
                @Override
                public void onInit(boolean isOk, String msg) {
                    //if (isOk){
                    startAdLoad(context);
                    //}
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void startAdLoad(Context context) {
        alxRewardVideoAD = new AlxRewardVideoAD();
        alxRewardVideoAD.load(context, unitid, new AlxRewardVideoADListener() {

            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                if (mLoadListener != null) {
                    mLoadListener.onAdCacheLoaded();
                }
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errCode + "", errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayStart();
                }
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayEnd();
                }
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
                if (mLoadListener != null) {
                    mLoadListener.onAdLoadError(errCode + "", errMsg);
                }
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdClosed();
                }
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onRewardedVideoAdPlayClicked();
                }
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                if (mImpressionListener != null) {
                    mImpressionListener.onReward();
                }
            }

        });
    }

    @Override
    public void show(Activity activity) {
        if (alxRewardVideoAD != null) {
            alxRewardVideoAD.showVideo(activity);
        }
    }


    @Override
    public void destory() {
        if (alxRewardVideoAD != null) {
            alxRewardVideoAD.destroy();
            alxRewardVideoAD = null;
        }
    }

    @Override
    public String getNetworkPlacementId() {
        return unitid;
    }

    @Override
    public String getNetworkSDKVersion() {
        return AlxAdSDK.getNetWorkVersion();
    }

    @Override
    public String getNetworkName() {
        return AlxAdSDK.getNetWorkName();
    }

    @Override
    public boolean isAdReady() {
        if (alxRewardVideoAD != null) {
            return alxRewardVideoAD.isReady();
        }
        return false;
    }
}
```

##### AlxMetaInf:

```Java
package com.anythink.custom.adapter;

/**
 * Adapter version information
 *
 * @date 2022-2-15
 */
public interface AlxMetaInf {

    String ADAPTER_VERSION = "3.8.0";
    //String ADAPTER_SDK_HOST_URL = "https://raftingadx.svr.rixengine.com/rtb";
    String ADAPTER_SDK_HOST_URL = "http://testaa.rixengine.com/rtb";
}
```

### 7. Third-party integration backend configuration process (for reference only)

##### 7.1 Admob

1. Create an application (ignore if you already have one)

1). First, log in to the Admob account, add a third-party ad platform and make changes in accordance with guidelines provided by Admob [https://apps.admob.com/](https://apps.admob.com/). Need to create a new app first, if you don’t have one. (the test case is provided below)

![](5-1-1-1-1.png)

![](5-1-1-1-2.png)

2). Then add and create the corresponding ad unit

![](5-1-1-2-1.png)

![](5-1-1-2-2.png)

![](5-1-1-2-3.png)

2. Add Alx mediation groups

1). Create an Alx mediation group

![](5-1-2-1-1.png)

2). Create an Alx ad unit. Support rewarded videos, interstitials and banners (take rewarded videos as an example)

Fill in the ad unit name `Alx rewarded video mediation` and select the ad unit format as `Rewarded`

![](5-1-2-2-1.png)

3). Then click `ADD AD UNITS` and choose the corresponding app and ad unit

![](5-1-2-3-1.png)

4). Click `DONE` 

To next interface to setting up a waterfall ad source and a custom event

Fill in with name and eCPM

![](5-1-2-4-1.png)

![](5-1-2-4-2.png)

5). Click `CONTINUE` 

Fill in with Ad unit parameter (Important!)

Then fill in ad unit mapping details:

Enter `com.admob.custom.adapter.AlxRewardVideoAdapter` (RewardVideo)

Other Adapter Class Name:

Rewarded Video `com.admob.custom.adapter.AlxRewardVideoAdapter`

Banner `com.admob.custom.adapter.AlxBannerAdapter`

Interstitial `com.admob.custom.adapter.AlxInterstitialAdapter`



    Parameters are：
```
{

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
`appid`, `unitid`, `token` and `sid` need to apply at RixEngine (consult operation team for more details）

`appid` refers to App ID at RixEngine

`token` refers to Token at RixEngine

`sid` refers to ID at RixEngine

`unitid` refers to placement ID at RixEngine

![](5-1-2-5-1.png)

6.) Click `DONE` after filling

Go to the following interface

![](5-1-2-6-1.png)

7). Click `SAVE`

And back to the home page. It’s all set.

![](5-1-2-7-1.png)

##### 7.2 MAX

1. First, add your app to Applovin's official webstie according to the document provided by MAX [Applovin｜Everything you need to grow your mobile apps（](https://www.applovin.com/)[applovin.com](https://www.applovin.com/)[）](https://www.applovin.com/). Need to create a new app first, if you don’t have one. (the test case is provided below)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-1.png)

Create an Android App

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-2.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-3.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-4.png)

2. GO back to the homepage after adding the app and click to add a custom network as shown in the picture 

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-5.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-6.png)

3. Choose `SDK` as the Network Type, enter Custom Networks Name and enter Android Adapter Class Name as

com.applovin.mediation.adapters.AlgorixMediationAdapter （Important! The path must be correct）

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-7.png)

---

---

4. Copy the adapter file from the Rixengine compression package to the corresponding directory in the project as shown below



![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-8.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-9.png)

5. Import Rixengine SDK and add ProGuard Configuration

Copy `rixengine.*.*.*.aar` from the SDK compression package to `Application Module/libs` folder (If not exist, create manually), then add the following codes to `build.gradle` in your Module app：

//In Unity, the application folder is exported as unityLibrary
```
repositories {

    flatDir {
    
        dirs 'libs'
    
    }

}

dependencies {

    compile(name: 'rixengine.*.*.*', ext: 'aar') // Replace with the specific sdk
version number

}
```
Add the following codes to the .pro file under the APP folder (for Android, the file is usually exported as proguard-rules.pro, for Unity, the file is exported as proguard-unity.txt):

```

-keep class com.rixengine.\*_ {_;}

-keep class admob.custom.adapter.\*_ {_;}

-keep class anythink.custom.adapter.\*_ {_;}

-keep class com.tradplus.custom.adapter.\*_ {_;}

-keep class com.applovin.mediation.adapters.\*_ {_;}

-keep class com.ironsource.adapters.\*_ {_;}

```

6. Click `Create Ad Unit` on the right to create ad placement in sequence after creating Networks. Four ad types are supported.

BANNER; INTER; NATIVE; REWARD

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-10.png)

**It is important to choose `Manual` (custom rendering) when creating native ads, otherwise, there will be no filling**

**For specific code implementation, please refer to the integration case in the demo**

```Java
mAdLoader = new MaxNativeAdLoader(MAX_NATIVE_AD, context);mAdLoader.setNativeAdListener(mMaxNativeAdListener);mAdLoader.loadAd(createNativeAdView()); //Manual (custom rendering)
```

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-11.png)

---

7. Click `banner` after creating ad units (take banner as an example)

---

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-12.png)

---

Scroll down and click `Custom Network (SDK)-Algorix` in the picture 

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-13.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-14.png)

Enable `Status` in the picture

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-15.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-16.png)

And fill in the ad parameters, which need to be applied on the Rixengine platform

**Custom Parameters Format**
```
{

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
---

`appid` refers to App ID in Rixengine

`token` refers to Token in Rixengine

`sid` refers to ID in Rixengine

`unitid` refers to Placement ID in Rixengine

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-17.png)

8. Click `Save` after filling

---

9. Can adjust the price in the picture to prioritize Rixengine ad when conducting an ad integration test

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.2-18.png)



---

##### 7.3 IronSource

1. First, Log in to IronSource's official website at [ironSource | Turning Apps Into Scalable Businesses (](https://www.is.com/)[is.com](https://www.is.com/)[)](https://www.is.com/) and Add your app according to the document provided by IronSource. Need to create a new app first, if you don’t have one. (the test case is provided below)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-1.png)

2. Go back to the homepage after adding the app and click `SDK Networks`

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-2.png)

3. Then click `Available Networks` in the picture below and choose `Custom Adapter`

(Note: To activate the Custom Adapter option, contact your account manager as you might not see it at first.)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-3.png)

4. Enter Network Key: 15b958455 (This is important cause the Network key is only dedicated to AlgoriX)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-4.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-5.png)

5. Copy the adapter file from Algorix compression package to the corresponding directory shown below

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-6.png)



6. Enter the corresponding `sid` and `token` that is applied in Rixengine (consult operation team for more details)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-7.png)

Need to manually change the host in the adapter since IronSource is a special

`host` refers to host in the SSP

`appid` refers to App ID in the SSP

`token` refers to Toekn in the SSP

`sid` refers to ID in the SSP

`unitid` refers to Placement ID in the SSP

7. Click `Setup` after filling

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-8.png)

8. Enter the corresponding `appid` and `unitid`

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-9.png)

9. Click the icon of Algorix shown in the picture to check if the corresponding ad unit is effective (ensure the status is active)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-10.png)

10. Then go to the Mediation page to check the detailed rank

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-11.png)

11. Can adjust the ad loading order by manually changing the value of eCPM

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-12.png)

12. To test whether the integration succeeds, you can add a new test device in `Mediation Testing` under `Testing` for apps already online

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-13.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-14.png)

13. Then choose the corresponding ad source and click `Test` button to activate

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-15.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.3-16.png)

##### 7.4 Topon

1. Step 1: Download the Mediation SDK package

[https://docs.toponad.com/#/en-us/android/download/package?_t=YzYVOgZulwVFdIaaqaD1mMC713U6521H](https://docs.toponad.com/#/en-us/android/download/package?_t=YzYVOgZulwVFdIaaqaD1mMC713U6521H) 

![](5-4-1-1-1.png)

Note: choose SDK with the latest version and recommend selecting gradle as an integrating  approach for ones not in mainland China

Medaition ad platform options: Huawei; Mintegral; UnityAds; Admob

2. Step 2: refer to the links below for integration documents:

[https://docs.toponad.com/#/en-us/android/android_doc/android_sdk_config_access](https://docs.toponad.com/#/en-us/android/android_doc/android_sdk_config_access) 

       Mainly set up the custom ad platform on Topon, add ad adapter mapping, and customize ad parameters.

Enter ad unit mapping details:

com.anythink.custom.adapter.AlxRewardVideoAdapter (RewardVideo)

Four ad unit types are supported, detailed Adapter Class Names are shown below:

Rewarded Video `com.anythink.custom.adapter.AlxRewardVideoAdapter`

Banner `com.anythink.custom.adapter.AlxBannerAdapter`

Interstitial `com.anythink.custom.adapter.AlxInterstitialAdapter`

Native `com.anythink.custom.adapter.AlxNativeAdapter`

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-2.png)

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-3.png)

Add custom ad parameters after completing the configuration

![](https://static.rixengine.com/a/platform/help/c2s/appendix-7.4-4.png)

**Refer to the format in the figure**
```
{

"host": "http://testaa.rixengine.com/rtb",

    "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
    
    "unitid": "171998",
    
    "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
    
    "isdebug": "false",
    
    "sid": "60188"

}
```
---

`host` refers to host in the SSP

`appid` refers to App ID in the SSP

`token` refers to Token in the SSP

`sid` refers to ID in the SSP

`united` refers to Placement ID in the SSP

3. Rixengine SDK Android Integration Document:

1). Copy `rixengine.*.*.*.aar` from this SDK compression package to `Application Module/libs` folder (If not exist, create manually), then add the following codes to `build.gradle` in your Module app

```
repositories {

    flatDir {
    
        dirs 'libs'
    
    }

}

dependencies {

           compile(name: 'rixengine.*.*.*', ext: 'aar') // Replace with the specific sdk

}
```
2). Place the adapter codes which are under the `com` directory to `src/main/java` source code directory of your Android project, please note that copy the full path, full path name: `com.anythink.custom.adapter`

Details are shown below:

![](https://static.rixengine.com/a/platform/help/c2s/6.2-1-1.png)

4. Android ProGuard Configuration (if display splash ads in Android, then the packaging needs to be added): 

```
-dontwarn com.rixengine.**
 -keep class com.rixengine.** {*;}
 -keep class com.anythink.** {*;}
 -keep public class com.anythink.network.**
 -keepclassmembers class com.anythink.network.** {
   public *;
 }
 -dontwarn com.anythink.hb.**
 -keep class com.anythink.hb.**{ *;}****
 -dontwarn com.anythink.china.api.**
 -keep class com.anythink.china.api.**{ *;}****
 -keep class com.anythink.myoffer.ui.**{ *;}
 -keepclassmembers public class com.anythink.myoffer.ui.** {
   public *;
 }
```

## Change Log

| Date | Changing Contents |
| --- | --- |
| 2021.09.27 | Removes `dltrackers`, `dlerrtrackers`, `imperrtrackers` fields under `response` |
| 2021.09.29 | Adds `data.debug` field under `response` |
| 2021.10.22 | Delete the `data.debug` field and add `data.ads.video_ext`, `data.ads.banner_ext`, `data.ads.native_ext` fields |
| 2021.10.25 | Request a new ad type field `adtype`, which is used to verify whether the ad placement and ad type match. The returned `data.ads.adtype` field is renamed |
| 2021.10.26 | Update error code list |
| 2021.11.22 | Update `native` protocol |
| 2022.02.15 | Return bid information and `nurl`, `burl` information, add `data.ads.price`, `data.ads.nurl`, `data.ads.burl` fields |
| 2022.06.29 | Add the ` data.ads.``video_ext.mute ` field to return whether the video ad is played silently by default |
| 2022.07.18 | Add three parameters in `data.ads.native_ext.omid` which is used to return  `Native` ad type `omsdk`: `vendorKey`, `javascriptResourceUrl`, `verificationParameters` for `3.2.8` and above versions. The `omid` validation script for `Banner` and `Video` ads has been integrated into `adm`. |
| 2022.08.08 | Added `data.ads.``video_ext.close` field to indicate whether rewarded videos are allowed to be closed. |
| 2022.11.07 | Added `native_ext.asset_type` and `native_ext.source` fields. Used to return the native creative type and ad source respectively |
| 2022.12.29 | Added `omidpv` field for passing `omsdk partner version` |
| 2023.08.12 |  |

Copyright © 2023 RixEngine. All Rights Reserved
