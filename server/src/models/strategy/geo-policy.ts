import { UserType } from '@/constants';
import dbUtil, { TransactionReturnType } from '@/db/mysql';
import { GeoPolicyAPI } from '@/types/geo-policy';
import { buildSQLSetClause } from '@rixfe/rix-tools';

const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];

class GeoPolicyModel {
  /**
   * 获取地理策略key列表
   * @param tnt_id 租户ID
   */
  async getGeoPolicyKeyList(
    tnt_id: number
  ): Promise<GeoPolicyAPI.GeoPolicyKeyListItem[]> {
    return dbUtil.query(
      `SELECT 
        k.id, 
        k.policy_key, 
        k.remark, 
        k.op_id, 
        (case when u.type in (?) then "System" else u.account_name end) as op_name,
        u.status as op_status,
        UNIX_TIMESTAMP(k.update_time) as update_time, 
        k.status
      FROM 
        stg_geo_edge_policy_key k
      LEFT JOIN 
        user u ON k.op_id = u.user_id
      WHERE 
        k.tnt_id = ?
      ORDER BY
        k.update_time DESC
    `,
      [SystemType, tnt_id, tnt_id]
    );
  }

  /**
   * 获取地理策略key关联列表
   * @param tnt_id 租户ID
   */
  async getGeoPolicyKeyRelationList(
    tnt_id: number
  ): Promise<GeoPolicyAPI.GeoPolicyKeyRelationListItem[]> {
    return dbUtil.query(
      `SELECT 
        r.id, 
        r.policy_key_id, 
        k.policy_key, 
        k.status as policy_key_status, 
        r.seller_id, 
        s.seller_name, 
        r.op_id, 
        (case when u.type in (?) then "System" else u.account_name end) as op_name, 
        u.status as op_status, 
        r.status, 
        UNIX_TIMESTAMP(r.update_time) as update_time
      FROM stg_geo_edge_policy_key_relation r 
      LEFT JOIN 
        stg_geo_edge_policy_key k ON r.policy_key_id = k.id
      LEFT JOIN 
        user u ON r.op_id = u.user_id
      LEFT JOIN 
        seller s ON r.seller_id = s.seller_id
      WHERE
        r.seller_id != 0 AND
        r.tnt_id = ?
      ORDER BY
        r.update_time DESC
    `,
      [SystemType, tnt_id, tnt_id]
    );
  }

  /**
   * 添加地理策略key
   * @param params 添加参数
   * @param tools 事务工具
   * @returns 添加的key数据
   */
  async addGeoPolicyKey(
    params: Omit<GeoPolicyAPI.AddGeoPolicyKeyParams, 'is_default'>,
    tools: TransactionReturnType,
    is_duplicate?: boolean
  ): Promise<
    Omit<
      GeoPolicyAPI.GeoPolicyKeyListItem,
      | 'is_default'
      | 'op_name'
      | 'op_status'
      | 'update_time'
      | 'op_id'
      | 'status'
    >
  > {
    const { query } = tools;
    const { policy_key, remark, op_id, tnt_id } = params;

    const res = await query({
      sql: `INSERT INTO stg_geo_edge_policy_key (policy_key, remark, op_id, tnt_id, status) VALUES (?, ?, ?, ?, ?) ${
        is_duplicate
          ? 'ON DUPLICATE KEY UPDATE remark = values(remark), op_id = values(op_id), status = values(status)'
          : ''
      }`,
      values: [policy_key, remark, op_id, tnt_id, 1]
    });

    return {
      id: res.insertId,
      unique_id: `${res.insertId}-${policy_key}`,
      policy_key,
      remark
    };
  }

  /**
   * 更新地理策略key
   * @param params 更新参数
   * @param tools 事务工具
   * @returns 是否更新成功
   */
  async updateGeoPolicyKey(
    params: Omit<GeoPolicyAPI.UpdateGeoPolicyKeyParams, 'is_default'>,
    tools: TransactionReturnType
  ): Promise<boolean> {
    const { query } = tools;
    const { id, remark, op_id, tnt_id, status } = params;

    const updateObj = buildSQLSetClause([
      ['remark', remark],
      ['op_id', op_id],
      ['status', status]
    ]);

    const res = await query({
      sql: `UPDATE stg_geo_edge_policy_key SET ? WHERE id = ? AND tnt_id = ?`,
      values: [updateObj, id, tnt_id]
    });
    return res.affectedRows > 0;
  }

  /**
   * 添加地理策略key关联
   * @param params 添加参数
   * @param tools 事务工具
   * @param is_duplicate 是否允许重复（存在时更新）
   * @returns 是否添加成功
   */
  async addGeoPolicyKeyRelation(
    params: GeoPolicyAPI.AddGeoPolicyKeyRelationParams,
    tools: TransactionReturnType,
    is_duplicate?: boolean
  ): Promise<boolean> {
    const { query } = tools;
    const { policy_key_id, seller_id, tnt_id, op_id } = params;
    const res = await query({
      sql: `INSERT INTO stg_geo_edge_policy_key_relation (policy_key_id, seller_id, op_id, tnt_id) VALUES (?, ?, ?, ?) ${
        is_duplicate
          ? 'ON DUPLICATE KEY UPDATE status = 1, policy_key_id = VALUES(policy_key_id), seller_id = VALUES(seller_id), op_id = VALUES(op_id), tnt_id = VALUES(tnt_id)'
          : ''
      }`,
      values: [policy_key_id, seller_id, op_id, tnt_id]
    });
    return res.affectedRows > 0;
  }

  /**
   * 更新地理策略key关联
   * @param params 更新参数
   * @param tools 事务工具
   * @returns 是否更新成功
   */
  async updateGeoPolicyKeyRelation(
    params: GeoPolicyAPI.UpdateGeoPolicyKeyRelationParams,
    tools?: TransactionReturnType
  ): Promise<boolean> {
    const { id, status, policy_key_id, seller_id, tnt_id, op_id } = params;

    const updateObj = buildSQLSetClause([
      ['status', status],
      ['policy_key_id', policy_key_id],
      ['seller_id', seller_id],
      ['op_id', op_id],
      ['tnt_id', tnt_id]
    ]);

    const sql = `UPDATE stg_geo_edge_policy_key_relation SET ? WHERE id = ? AND tnt_id = ?`;

    if (tools) {
      const res = await tools.query({
        sql,
        values: [updateObj, id, tnt_id]
      });
      return res.affectedRows > 0;
    } else {
      const res = await dbUtil.query(sql, [updateObj, id, tnt_id]);
      return res.affectedRows > 0;
    }
  }

  /**
   * 检查是否存在活跃状态的seller_id
   * @param params 检查参数
   * @param tools 事务工具
   * @returns 是否存在
   */
  async getExistActiveRelationBySellerId(
    seller_id: number,
    tnt_id: number,
    tools: TransactionReturnType
  ): Promise<GeoPolicyAPI.GeoPolicyKeyRelationListItem[]> {
    const { query } = tools;
    // 判断 active 状态的 seller_id 是否存在
    const res = await query({
      sql: `SELECT r.*, k.policy_key FROM stg_geo_edge_policy_key_relation r LEFT JOIN stg_geo_edge_policy_key k ON r.policy_key_id = k.id WHERE r.seller_id = ? AND r.tnt_id = ? AND r.status = 1`,
      values: [seller_id, tnt_id]
    });
    return res;
  }

  /**
   * 获取默认的地理策略key
   * @param params 查询参数
   * @param tools 事务工具（可选）
   * @returns 默认key数据
   */
  async getDefaultGeoPolicyKey(
    params: GeoPolicyAPI.DefaultPolicyKeyParams,
    tools?: TransactionReturnType
  ): Promise<
    Omit<
      GeoPolicyAPI.GeoPolicyKeyListItem & {
        relation_id: number;
      },
      'is_default'
    >[]
  > {
    const { tnt_id } = params;
    const sql = `SELECT k.id, k.policy_key, r.id as relation_id FROM stg_geo_edge_policy_key k LEFT JOIN stg_geo_edge_policy_key_relation r ON k.id = r.policy_key_id WHERE r.seller_id = 0 AND k.tnt_id = ? AND r.status = 1`;

    if (tools) {
      return tools.query({
        sql,
        values: [tnt_id]
      });
    } else {
      return dbUtil.query(sql, [tnt_id]);
    }
  }

  /**
   * 根据 policy_key 查找指定的 policy_key
   * @param params 查询参数
   * @param tools 事务工具（可选）
   * @returns 指定的 policy_key 数据
   */
  async getGeoPolicyKeyByPolicyKey(
    params: {
      policy_key: string;
      tnt_id: number;
    },
    tools?: TransactionReturnType
  ): Promise<GeoPolicyAPI.GeoPolicyKeyListItem[]> {
    const { policy_key, tnt_id } = params;
    const sql = `SELECT * FROM stg_geo_edge_policy_key WHERE policy_key = ? AND tnt_id = ?`;
    if (tools) {
      return tools.query({
        sql,
        values: [policy_key, tnt_id]
      });
    } else {
      return dbUtil.query(sql, [policy_key, tnt_id]);
    }
  }
}

export default new GeoPolicyModel();
