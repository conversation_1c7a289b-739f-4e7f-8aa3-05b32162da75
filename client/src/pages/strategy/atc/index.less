.container {
  background-color: #fff;
  // margin: 0 16px;
  padding: 28px 36px;
  border-radius: 6px;
  height: 100%;
  .top {
    margin-bottom: 32px;
    :global {
      .ant-radio-button-wrapper {
        .ant-radio-button {
          border-radius: 6px;
        }
        background-color: #fff;
      }
      .ant-radio-group {
        background-color: #fff;
      }
      .ant-radio-button-checked {
        background-color: var(--background-color);
      }
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    max-width: 498px;
    align-items: center;
    button {
      &:last-child {
        margin-left: 12px;
      }
    }
  }
}

.spin-container {
  height: 100%;
}
