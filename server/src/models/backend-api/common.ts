import dbUtils from '@/db/mysql';
import { BackendCommonAPI } from '@/types/backend-api';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class BackendCommonModel implements BackendCommonAPI.BackendCommonModel {
  async validateApiToken(
    params: BackendCommonAPI.ValidateTokenParams
  ): Promise<{ user_id: number; tnt_id: number; account_name: string }[]> {
    const { user_id, token, cs_domain } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['u.user_id=?', user_id],
      ['u.token=?', token],
      [
        't.cs_domain=?',
        process.env.NODE_ENV === 'development' ? null : cs_domain
      ]
    ]);

    const sql = `
      select 
        u.user_id,
        u.account_name,
        t.tnt_id
      from user u
      left join tenant t on t.tnt_id = u.tnt_id
      ${clause}
    `;
    return await dbUtils.permissionQuery(sql, values);
  }
}

export const beCommonModel = new BackendCommonModel();
