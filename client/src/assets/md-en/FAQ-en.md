

# 1 Demand Partner FAQ

## 1.1 Technical Integration FAQ

#### **Q1: What are the integration methods for the demand side?**

A1: ORTB 2.5 protocol. For other integration methods, please contact RixEngine team.

#### **Q2: Where are the endpoints located?**

A2: USE (Eastern United States), APSE (Asia Pacific-Singapore), EUW(Western Europe - Netherlands).

## 1.2 Business-related FAQ

#### **Q1: Is the bidding based on Net Price or Gross Price?**

A1: Default is Net Price.

#### Q2: What auction types are supported (1=First price, 2=Second price)?

A2: Both are supported and can be configured on the platform. The default is first price.

#### Q3: Can the inventory be filtered based on maximum Bid Floor?

A3: Yes.

#### Q4: How are the ads billed?

A4: ADM (JS) billing method is adopted by default, and ADM (IMG-Pixel) and bURL billing are also supported.

#### Q5: Does RixEngine support lurl reporting?

A5: Yes.

#### Q6: Does RixEngine support the Supply Chain?

A6: Yes, can be configured on the console, and is turned off by default.

#### Q7: Does RixEngine support inventory filtering?

A7: Yes, inventory filtering can be configured in Pretargeting.

#### Q8: Does RixEngine support blacklist and whitelist operations for inventory?

A8: Yes, can be configured on the console.

#### Q9: What is the maximum acceptable bid response time?

A9: Default is 1000ms. The maximum acceptable response time is determined by the downstream (Publisher side). Tmax can be configured on the console.

#### Q10: Does RixEngine support Deeplink/Universal Link?

A10: Yes.

#### Q11: Does RixEngine support the report API function?

A11: Yes, relevant document information can be found on the Advertiser account management page [Account].

# 2 Supply Partner FAQ

## 2.1 Technical Integration FAQ

#### Q1: What are the integration methods for the supply side?

A1: RTB, Prebid, SDK, and Vast Tag. Please refer to the technical integration document for specific integration details.

#### Q2: Where are the endpoints located?

A2: USE (Eastern United States), APSE (Asia Pacific-Singapore).

#### Q3: Does RixEngine support testing? Will the revenue be billed for during the testing period?

A3: Yes, testing status can be enabled by changing the publisher status to Testing. Revenue made during the testing period will not be billed.

## 2.2 Business-related FAQ

#### Q1: How are the ads billed?

A1: Supports ADM and bURL, and uses ADM (JS) billing method by default.

#### Q2: What is the ad expiry time limit?

A2: One hour.

#### Q3: Does RixEngine support pulling data by the report API?

A3: Yes. Relevant documents can be found on the Publisher account management page [Account].

<!-- # 3 Client-facing FAQ

#### Q1: How is the cost of third-party traffic monitoring tools and creative quality monitoring tools calculated?

A1: Please consult with RixEngine team.

#### Q2: What does monthly total million ad requests mean in monthly invoice?

A2: It refers to the total number of ad requests sent to the Demand partners, that is, the number of Out Requests or Requests that are shown under the Advertiser dimension by using the data dashboard.

#### Q3: What is the traffic monitoring tool?

A3：Pixalate, this function is a value-added service, please contact RixEngine team.

#### Q4: What is the creative quality monitoring tool?

A4: GeoEdge, this function is a value-added service, please contact RixEngine team. -->
