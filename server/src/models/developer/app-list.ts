/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 11:06:31
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 12:01:26
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { FloorType } from '@/constants/strategy';
import dbUtil, { PoolConnection, QueryParamType } from '@/db/mysql';
import { AppListAPI } from '@/types/app-list';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
class AppModel implements AppListAPI.App {
  async isAppExists(params: AppListAPI.IsAppExistsParams): Promise<any[]> {
    //  seller_id + bundle唯一，seller_id + app_name + platform唯一
    const {
      tnt_id,
      seller_id,
      app_id,
      app_name,
      bundle,
      platform = 0
    } = params;

    const sql = `select app_name, platform, bundle from seller_app where tnt_id=? and seller_id=? and 
    (bundle=? or (app_name=? and platform=?)) ${
      app_id ? ` and app_id != ?` : ''
    }`;
    return await dbUtil.query(sql, [
      tnt_id,
      seller_id,
      bundle,
      app_name,
      platform,
      app_id
    ]);
  }
  async countSellerApp(params: AppListAPI.IsAppExistsParams): Promise<any> {
    const { tnt_id, seller_id } = params;
    const sql = `select 1 from seller_app sp 
      left join seller s on s.seller_id = sp.seller_id
      where s.integration_type = 4 and sp.tnt_id=? and sp.seller_id=? 
      limit 1`;
    return await dbUtil.query(sql, [tnt_id, seller_id]);
  }
  async isPlacementExists(
    params: AppListAPI.IsPlacementExistsParams
  ): Promise<any[]> {
    const { plm_name, plm_id, tnt_id, app_id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['plm_name = ?', plm_name],
      ['tnt_id = ?', tnt_id],
      ['app_id = ?', app_id],
      ['plm_id != ?', plm_id ? plm_id : null]
    ]);
    const sql = `select plm_name from seller_placement ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async addApp(params: AppListAPI.AddAppParams): Promise<boolean> {
    const {
      app_name,
      bundle,
      platform = 0,
      store_url,
      ios_bundle = '',
      category = '',
      screen_orientation = 0,
      status = 1,
      seller_id,
      tnt_id
    } = params;

    const sql = `insert into seller_app (app_name, seller_id, bundle, platform, store_url,ios_bundle, category, screen_orientation, status, tnt_id) values ?`;

    // 二维数组参数，插入一条数据
    return !!(await dbUtil.query(sql, [
      [
        [
          app_name,
          seller_id,
          bundle,
          platform,
          store_url,
          ios_bundle,
          category,
          screen_orientation,
          status,
          tnt_id
        ]
      ]
    ]));
  }

  async addPlacement(params: AppListAPI.AddPlacementParams): Promise<boolean> {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtil.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const {
        app_id,
        ad_width = 0.0,
        assets = '',
        plm_name,
        ad_height = 0.0,
        pos = 0,
        price = 0.0,
        protocols = '',
        support_html = 0,
        support_video = 0,
        video_api = '',
        companionad = 0,
        skip = 0,
        maxduration = 0,
        minduration = 0,
        banner_api = '',
        bid_floor = 0.0,
        ad_format,
        placement_type,
        status = 1,
        mute = 0,
        tnt_id,
        seller_id,
        op_id,
        content
      } = params;

      const oneSPData = [
        plm_name,
        app_id,
        ad_format,
        placement_type,
        ad_width,
        ad_height,
        price,
        bid_floor,
        assets,
        support_html,
        support_video,
        video_api,
        companionad ? 1 : 0,
        skip ? 1 : 0,
        maxduration,
        minduration,
        protocols,
        banner_api,
        pos,
        status,
        mute ? 1 : 0,
        tnt_id
      ];

      const plmSql = `insert into seller_placement (plm_name, app_id, ad_format, placement_type, ad_width, ad_height,price,bid_floor, assets, support_html, support_video, video_api, companionad, skip, maxduration, minduration, protocols,banner_api, pos, status, mute, tnt_id) values ?`;

      const plm = await query({ sql: plmSql, values: [[oneSPData]] });
      if (content && Object.keys(content).length > 0) {
        const plmId = plm.insertId;
        const floorSql = `insert into floor (type,buyer_id,seller_id,plm_id,country,bid_floor,op_id,status,tnt_id) values ?`;

        const floorValues = Object.entries(content).map(
          ([country, country_bid_floor]) => {
            return [
              FloorType['(SSP)Supply-Placement + Country'],
              0,
              seller_id,
              plmId,
              country,
              country_bid_floor,
              op_id,
              1,
              tnt_id
            ];
          }
        );

        await query({ sql: floorSql, values: [floorValues] });
      }
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      console.log(e);
      getLogger('error').error(`addPlacement failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(e);
    }
  }

  async updateApp(params: AppListAPI.UpdateAppParams): Promise<boolean> {
    const {
      app_id,
      app_name,
      store_url,
      category = '',
      tnt_id,
      platform = 0
    } = params;
    const sql = `update seller_app set app_name=?, store_url=?, category=?, platform=? where app_id=? and tnt_id=?`;
    return !!(await dbUtil.query(sql, [
      app_name,
      store_url,
      category,
      platform,
      app_id,
      tnt_id
    ]));
  }

  async updatePlacement(
    params: AppListAPI.UpdatePlacementParams
  ): Promise<boolean> {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtil.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const {
        plm_id,
        ad_width = 0.0,
        assets = '',
        plm_name,
        ad_height = 0.0,
        pos = 0,
        price = 0.0,
        protocols = '',
        support_html = 0,
        support_video = 0,
        video_api = '',
        companionad = 0,
        skip = 0,
        maxduration = 0,
        minduration = 0,
        banner_api = '',
        bid_floor = 0.0,
        tnt_id,
        ad_format,
        mute,
        content,
        op_id,
        seller_id,
        isChangeFloor
      } = params;

      const updateObj = buildSQLSetClause([
        ['ad_width', ad_width],
        ['ad_height', ad_height],
        ['assets', assets],
        ['plm_name', plm_name],
        ['pos', pos],
        ['price', price],
        ['protocols', protocols],
        ['support_html', support_html],
        ['support_video', support_video],
        ['companionad', companionad],
        ['skip', skip ? 1 : 0],
        ['maxduration', maxduration],
        ['minduration', minduration],
        ['banner_api', banner_api],
        ['bid_floor', bid_floor],
        ['video_api', video_api],
        ['ad_format', ad_format],
        ['mute', mute ? 1 : 0]
      ]);

      const sql = `update seller_placement set ? where plm_id=? and tnt_id=?`;
      await dbUtil.query(sql, [updateObj, plm_id, tnt_id]);

      const floorSqls: QueryParamType[] = [];
      if (content) {
        const delSql = `delete from floor where plm_id=? and tnt_id=?`;
        const countries = Object.keys(content);
        if (countries.length === 0) {
          floorSqls.push({ sql: delSql, values: [plm_id, tnt_id] });
        } else {
          const floorValues = countries.map((country: string) => {
            return [
              FloorType['(SSP)Supply-Placement + Country'],
              0,
              seller_id,
              plm_id,
              0,
              country,
              content[country],
              op_id,
              1,
              tnt_id
            ];
          });

          const floorSql = `insert into floor (type,buyer_id,seller_id,plm_id,ad_format,country,bid_floor,op_id,status,tnt_id) values ?`;

          floorSqls.push(
            { sql: delSql, values: [plm_id, tnt_id] },
            { sql: floorSql, values: [floorValues] }
          );
        }

        await Promise.all(floorSqls.map(sql => query(sql)));
      }

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      console.log(e);
      getLogger('error').error(`addPlacement failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(e);
    }
  }

  async getAppList(tnt_id: number): Promise<AppListAPI.AppListItem[]> {
    const sql = `select app_id,app_name,bundle from seller_app where tnt_id=?`;
    const list = await dbUtil.query(sql, [tnt_id]);
    return list;
  }

  async getAllPlacementList(tnt_id: number) {
    const sql = `select plm_id,plm_name from seller_placement where tnt_id=?`;
    return await dbUtil.query(sql, [tnt_id]);
  }
}

export default new AppModel();
