/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 14:30:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-06 16:36:05
 * @Description:
 */

import fs from 'fs';
import path from 'path';
import Router from 'koa-router';
import {
  resolveError,
  resolveCtxDefaultData,
  resolveLogger,
  resolveCsDomain,
  resolveRefreshPage
} from '@/middleware';
import {
  refreshSessionExpired,
  validLogin,
  getGlobalApi,
  validApiPermission,
  resolveCtxRole,
  verifySignature
} from '@/middleware/api';

const router = new Router({ prefix: '/api' })
  .use(resolveError)
  .use(resolveCsDomain)
  .use(getGlobalApi)
  .use(validLogin)
  .use(resolveLogger)
  .use(resolveCtxRole)
  .use(validApiPermission)
  .use(refreshSessionExpired)
  .use(resolveRefreshPage)
  .use(verifySignature)
  .use(resolveCtxDefaultData);

// 递归读取文件
function generateReadDir(filePath: string) {
  fs.readdirSync(filePath).forEach(async file => {
    if (file === 'index.js' || file === 'index.ts') {
      return;
    }
    const childPath = path.resolve(filePath, file);
    // 文件夹
    if (fs.statSync(childPath).isDirectory()) {
      generateReadDir(childPath);
    } else {
      const { default: route } = await import(childPath);
      router.use(route.routes()).use(route.allowedMethods());
    }
  });
}
// 递归读取当前文件夹下的文件
generateReadDir(__dirname);

export default router;
