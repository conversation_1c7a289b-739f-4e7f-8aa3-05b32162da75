/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-26 20:45:59
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getFloorList } from '@/services/api';

export default function useFloorList() {
  const { data, run, loading, error } = useCustomRequest(getFloorList, {
    cacheKey: 'floor-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
