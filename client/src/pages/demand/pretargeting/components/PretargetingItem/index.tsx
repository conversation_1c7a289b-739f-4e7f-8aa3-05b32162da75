import Modal from '@/components/Modal/NormalModal';
import MobileAppModal from '@/pages/demand/pretargeting/components/MobileAppListModal';
import {
  applyDataToSchema,
  generateTableColumnSchema,
  transformPretargetingData
} from '@/pages/demand/pretargeting/helper/table-schema';
import { PretargetingDataItem } from '@/pages/demand/pretargeting/helper/transformPretargetListData';
import { usePretargetingStore } from '@/pages/demand/pretargeting/store';
import { updatePretargetStatus } from '@/services/api';
import { fetchData as httpFetchData } from '@/utils';
import { ExclamationCircleOutlined, QuestionCircleOutlined, StopOutlined } from '@ant-design/icons';
import { message, Spin, Tooltip } from 'antd';
import React, { useMemo, useState } from 'react';
import PretargetTableItem from '../PretargetTableItem';
import styles from './index.less';
import PretargetingItemTitle from './PretargetingItemTitle';

type PretargetingItemType = {
  fetchData: () => void;
  handleDeleteItem: () => void;
  supplyMapByValue?: Record<string | number, string>;
  adSizeMapByValue?: Record<string | number, string>;
  currentItem: PretargetingDataItem;
};

const PretargetingItem: React.FC<PretargetingItemType> = ({
  fetchData,
  supplyMapByValue,
  adSizeMapByValue,
  currentItem
}) => {
  const { campaign_id, campaign_name } = currentItem;
  const { currentDemand } = usePretargetingStore();
  const buyer_id = currentDemand?.buyer_id || 0;
  const [statusLoading, setStatusLoading] = useState(false);

  // 转换数据并生成表格配置
  const tableData = useMemo(() => {
    const baseSchema = generateTableColumnSchema({ adSizeMapByValue, supplyMapByValue });
    const transformedData = transformPretargetingData(currentItem);

    return applyDataToSchema(baseSchema, transformedData);
  }, [currentItem, adSizeMapByValue, supplyMapByValue]);

  const handleChangeStatus = (status: number) => {
    const params = {
      op: 3,
      campaign_id: campaign_id,
      status: status,
      buyer_id: buyer_id
    };
    const onSuccess = () => {
      message.success('Success');
      fetchData();
    };
    httpFetchData({ setLoading: setStatusLoading, request: updatePretargetStatus, params, onSuccess });
  };

  const handlePauseActive = (status: number) => {
    Modal.confirm({
      title: `${campaign_name}`,
      icon: <ExclamationCircleOutlined />,
      content:
        status !== 3
          ? `Are you sure to ${status === 1 ? 'active' : 'pause'} this item?`
          : 'Are you sure to delete this item?',
      onOk() {
        handleChangeStatus(status);
      },
      okButtonProps: {
        danger: status === 3
      },
      okText: status === 3 ? 'Delete' : 'Confirm'
    });
  };

  return (
    <Spin spinning={statusLoading}>
      <div className={styles['pretargeting-item-container']}>
        {/* 顶部按钮区 */}
        <PretargetingItemTitle handlePauseActive={handlePauseActive} currentItem={currentItem} />
        {/* 表格区域 */}
        <div className={styles['pretarget-table']}>
          {tableData.map((val, index) => (
            <div
              className={styles['pretarget-table-item']}
              key={val.name}
              style={{ minWidth: val.minWidth, flex: val.minWidth }}
            >
              <div className={styles['pretarget-table-title']}>
                {val.isDisabled ? <StopOutlined style={{ paddingRight: 5 }} /> : null}
                {val.title}
                {val.tooltip && (
                  <Tooltip title={val.tooltip}>
                    <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
                  </Tooltip>
                )}
                {val.name === 'mobile_app' && val.values.length ? (
                  <MobileAppModal title="Bundle(Domain)" values={val.values} />
                ) : null}
              </div>
              <PretargetTableItem values={val.values} key={index} minWidth={val.minWidth || 0} />
            </div>
          ))}
        </div>
      </div>
    </Spin>
  );
};

export default PretargetingItem;
