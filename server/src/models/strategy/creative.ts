/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-20 14:32:03
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-20 16:38:33
 * @Description:
 */
import { CreativeAPI } from '@/types/creative';
import dbUtil from '@/db/mysql';
import { getTzTimeSql } from '@/constants/time-zone';

class CreativeModel implements CreativeAPI.Creative {
  async getCreativeList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<CreativeAPI.CreativeListItem[]> {
    const sql = `
      select
        c.id as id,
        c.buyer_id as buyer_id,
        c.seller_id as seller_id,
        c.type as type,
        c.content as content,
        c.remark as remark,
        c.op_id as op_id,
        c.tnt_id as tnt_id,
        c.status as status,
        ${getTzTimeSql('c.update_time', cur_time_zone)} as update_time,
        u.account_name as op_name,
        u.status as op_status,
        u.type as account_type,
        b.buyer_name as buyer_name,
        s.seller_name as seller_name
      from (select id,buyer_id,seller_id,type,content,remark,op_id,tnt_id,status,update_time from stg_creative where tnt_id = ?) as c
      left join user as u on c.op_id = u.user_id and u.tnt_id = ?
      left join buyer as b on c.buyer_id = b.buyer_id and b.tnt_id = ?
      left join seller as s on c.seller_id = s.seller_id and s.tnt_id = ?
      order by c.update_time desc
      `;

    return await dbUtil.query(sql, [tnt_id, tnt_id, tnt_id, tnt_id]);
  }

  async addCreative(params: CreativeAPI.AddCreativeParams): Promise<boolean> {
    const {
      seller_id,
      buyer_id,
      type,
      op_id,
      tnt_id,
      remark = '',
      content
    } = params;
    const contentStr = content.join(',');
    const arr = seller_id.map((v) => [v, buyer_id, type, contentStr, remark, op_id, tnt_id]);
    const sql = `insert into stg_creative(seller_id,buyer_id,type,content,remark,op_id,tnt_id) values ?`;
    return !!(await dbUtil.query(sql, [arr]));
  }

  async updateCreative(
    params: CreativeAPI.updateCreativeParams
  ): Promise<boolean> {
    const {
      id,
      seller_id,
      buyer_id,
      type,
      op_id,
      tnt_id,
      remark,
      content,
      status
    } = params;
    const contentStr = content.join(',');
    const sql = `
      update stg_creative set 
        seller_id = ?,
        buyer_id = ?,
        type = ?,
        content = ?,
        remark = ?,
        op_id = ?,
        status = ?
      where id = ? and tnt_id = ?
    `;
    const values = [seller_id, buyer_id, type, contentStr, remark, op_id, status, id, tnt_id];
    return !!(await dbUtil.query(sql, values));
  }
}

export const creativeModel = new CreativeModel();
