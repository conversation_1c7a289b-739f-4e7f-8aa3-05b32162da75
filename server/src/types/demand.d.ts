/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:24:47
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 16:41:26
 * @FilePath: /saas.rix-platform/server-ts/src/types/demand.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { Context } from 'koa';
declare namespace DemandAPI {
  type DemandListItem = {
    buyer_id: number;
    buyer_name: string;
    integration_type: number;
    status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
  };

  type AddDemandParams = {
    buyer_name: string;
    demand_account_name: string;
    integration_type: number;
    status: number;
    tnt_id: number;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    profit_status: number;
    op_id: number;
    auction_type: number;
    imp_track_type: number;
    schain_required: number;
    schain_hops: number;
    schain_complete: number;
    pass_display_manager: number;
    display_manager_filter: number;
    idfa_required: number;
    filter_mraid: number;
    multi_format: number;
    banner_multi_size: number;
    native_format: number;
    native_version: number;
    max_pxl_ivt_ratio: number;
    max_hm_ivt_ratio: number;
    dp_id: number;
    omid_track: number;
  };

  type UpdateDemandParams = AddDemandParams & {
    buyer_id: number;
    profit_id: number;
    imp_track_type: number;
    user_id: number;
  };

  interface Demand {
    getDemandList(
      tnt_id: number,
      cur_time_zone: string,
      isTesting?: boolean
    ): Promise<DemandListItem[]>;
    addDemand(params: AddDemandParams): Promise<boolean>;
    updateDemand(params: AddDemandParams): Promise<boolean>;
    isDemandNameExists(
      buyer_name: string,
      tnt_id: number,
      buyer_id?: number
    ): Promise<any[]>;
    isDemandAccountExists(account_name: string, tnt_id: number): Promise<any[]>;
  }

  interface DemandModel {
    getDemandList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<DemandListItem[]>;
    addDemand(params: AddDemandParams): Promise<boolean>;
    isDemandNameExists(
      seller_name: string,
      tnt_id: number,
      seller_id?: number
    ): Promise<any[]>;
    updateDemand(params: string[]): Promise<boolean>;
  }

  interface DemandCtrlInterface {
    getDemandList(ctx: Context): void;
    addDemand(ctx: Context): void;
  }

  type DemandEndpointItem = {
    id: number;
    buyer_id: number;
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    qps: number;
    status: number;
    create_time: string;
    update_time: string;
  };

  type AddEndpointParams = {
    buyer_id: number;
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    status: number;
    server_region: number;
    ad_format: number;
    tnt_id: number;
  };

  type PretargetContentItem = {
    id: number;
    campaign_id: number;
    buyer_id: number;
    level: number;
    content: string;
    create_time: string;
    update_time: string;
  };

  type PretargetCampaignItem = {
    campaign_id: number;
    campaign_name: string;
    buyer_id: number;
    status: number;
    pt_flag: string;
    create_time: string;
    update_time: string;
    items: PretargetContentItem[];
  };

  type PretargetUpdateItem = {
    campaign_id: number;
    campaign_name: string;
    buyer_id: number;
    items: PretargetContentItem[];
    tnt_id: number;
    op_id: number;
  };

  type PretargetUpdateStatusItem = {
    campaign_id: number;
    buyer_id: number;
    status: number;
    tnt_id: number;
    op_id: number;
  };
}
