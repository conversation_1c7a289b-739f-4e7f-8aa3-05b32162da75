/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:55:25
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 17:03:32
 * @Description:
 */
import { MenuType } from '@/constants/permission';
import { QueryParamType } from '@/db/mysql';
import { delRedisByKey, setRedisByKey } from '@/db/redis';
import { commonModel, roleModel } from '@/models';
import { PermissionAPI } from '@/types/permission';
import { getConfig } from '@/utils';

const { redisConfig } = getConfig();

class RoleServices implements PermissionAPI.RoleServices {
  async addRole(options: PermissionAPI.AddRoleParams) {
    return await roleModel.addRole(options);
  }

  async updateRole(options: PermissionAPI.UpdateRoleNameParams) {
    return await roleModel.updateRole(options);
  }

  async editRolePms(options: PermissionAPI.EditRolePmsParams) {
    const { id, permissions = [], pms_change, tnt_id } = options;
    if (!pms_change) return Promise.resolve(true);
    const sqls: QueryParamType[] = [
      {
        sql: 'delete from role_permission_rl where role_id=? and tnt_id=?',
        values: [id, tnt_id]
      }
    ];

    if (permissions?.length) {
      const pmsArr = permissions.map((item: PermissionAPI.RolePmsItem) => [
        id,
        item.rsc_id,
        item.type,
        tnt_id
      ]);
      sqls.push({
        sql: 'insert into role_permission_rl (role_id, rsc_id, type,tnt_id) values ?',
        values: [pmsArr]
      });
    }

    const data = await roleModel.editRolePms(sqls);
    // 更新该角色的 权限
    if (data) {
      const key = `${redisConfig.platform_key}_ROLE_PMS_${id}`;
      const menus = permissions.map(v => v.rsc_id);
      const [apis, pms] = await Promise.all([
        commonModel.getInterfaceByMenu(menus),
        commonModel.getMenuByRole(id)
      ]);
      const api_list = apis
        .filter((v: any) => v.path && v.path.trim())
        .map((v: any) => v.path.trim());
      const menu_access = pms
        .filter((v: any) => v.type === MenuType.Menu)
        .map((v: any) => v.access)
        .filter((v: string) => v && v.trim());
      const btn_access = pms
        .filter((v: any) => v.type === MenuType.Operation)
        .map((v: any) => v.access)
        .filter((v: string) => v && v.trim());
      const value = {
        btn_access: [...new Set(btn_access)],
        menu_access: [...new Set(menu_access)],
        api_list: [...new Set(api_list)]
      };
      await setRedisByKey(key, value);
    }
    return data;
  }

  async getAllRole(tnt_id: number) {
    const result = await roleModel.getAllRole(tnt_id);
    if (Array.isArray(result) && result.length) {
      return result.map(v => {
        let pms_list = [];
        if (v.permissions) {
          pms_list = v.permissions.split(',').filter((t: string) => t.trim());
        }
        const obj = {
          ...v,
          pms_list
        };
        delete obj.permissions;
        return obj;
      });
    }
    return result;
  }

  async isRoleExist(role_name: string, type: number, tnt_id: number) {
    return await roleModel.isRoleExist(role_name, type, tnt_id);
  }

  async deleteRole(options: PermissionAPI.DelRoleParams) {
    const { id, tnt_id } = options;
    const data = await roleModel.deleteRole(id, tnt_id);
    if (data) {
      // 清除角色权限
      const key = `${redisConfig.platform_key}_ROLE_PMS_${id}`;
      // 删除
      delRedisByKey(key);
    }
  }
}
export default new RoleServices();
