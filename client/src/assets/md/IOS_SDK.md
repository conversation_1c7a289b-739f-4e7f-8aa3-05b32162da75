# 🌟 iOS SDK 中文接入文档 🌟 

## **步骤1：接入前的准备**

在开始接入 SDK 前，请确认您已经在Rixengine开通账号，并且配置好自己的应用，主要是**HOST**, **SID**, **Token**, **ID**, **APP_ID** 和 **广告位 Placement ID**.

配置好应用和广告位只要在您的 Xcode 项目中导入 SDK，并在中加入相关指令即可。



## **步骤2：开始集成**

1. 需要使用Xcode14版本及以上。
2. Target iOS 9.0及以上。

通过SDK下载页面可获取到集成需要依赖的库文件压缩包及相关的引入提示，请根据以下步骤集成并确认：

3. 解压后，将解压后的Framework文件及资源文件引用到Xcode项目中，
4. 根据SDK引入提示，在xcode的target中添加相关的引用依赖库。

![](https://static.rixengine.com/a/platform/help/sdk/ios-1.png)

 

**注：请在****Mac****系统下解压生成Framework库，否则可能导致库文件不可用**



### 2.1 SDK初始化说明

初始化SDK 开发者需要在在**AppDelegate**里面调用以下初始化代码初始化prebidSDK 。

```Swift
    Prebid.initializeSDK(host: AdConfig.YOUR_HOST, sid: AdConfig.YOUR_SID, token: AdConfig.YOUR_TOKEN, appid: AdConfig.YOUR_APP_ID) { status, error in
    if let error = error {
        print("Initialization Error: \(error.localizedDescription)")
                return
            
        }
    }
```



### 2.2 **Banner横幅广告**

横幅广告，又名Banner广告，固定于app顶部、中部、底部、或其他位置，横向贯穿整个app页面；当用户与app互动时，Banner广告会停留在屏幕上，并可在一段时间后自动刷新。

#### 主要API

生命周期事件回调

您可以实现全部或部分BannerViewDelegate，以跟踪点击成功或者Banner请求失败等广告生命周期事件。

```Swift
@objc public protocol BannerViewDelegate : NSObjectProtocol {
 
    @objc func bannerViewPresentationController() -> UIViewController?
 
    @objc optional func bannerView(_ bannerView: PrebidMobile.BannerView, didReceiveAdWithAdSize adSize: CGSize)
 
    @objc optional func bannerView(_ bannerView: PrebidMobile.BannerView, didFailToReceiveAdWith error: Error)
 
    @objc optional func bannerViewWillLeaveApplication(_ bannerView: PrebidMobile.BannerView)
 
    @objc optional func bannerViewWillPresentModal(_ bannerView: PrebidMobile.BannerView)
 
    @objc optional func bannerViewDidDismissModal(_ bannerView: PrebidMobile.BannerView)
}
```

#### 接入代码示例

加载并显示Banner广告

1.在您的View Controller文件中，引入对应头文件，定义好Banner对象以及定义如下例所示：

```Swift
import PrebidMobile
var bannerView:BannerView!
```

2.需要添加如下代码来对定义好的BannerView进行初始化 和加载请求

```Swift
// 1. Create an Ad View
let banner = BannerView(frame: CGRect(origin: .zero, size: adSize),
                        configID: AdConfig.ALX_BANNER_AD_ID,
                        adSize: adSize)
banner.delegate = self 
// 2. Load an Ad
banner.loadAd()
```

3.完成请求后跟踪点击成功或者Banner广告关闭等广告生命周期事件

```Swift
 extension PBBannerVC:BannerViewDelegate{
    func bannerViewPresentationController() -> UIViewController? {
        self
    }
 
    func bannerView(_ bannerView: PrebidMobile.BannerView, didReceiveAdWithAdSize adSize: CGSize){
        NSLog("Banner: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
 
    func bannerView(_ bannerView: BannerView, didFailToReceiveAdWith error: Error) {
        let error1=error as NSError
        let msg = "\(error1.code):\(error1.localizedDescription)"
        
        NSLog("Banner: ad load failed: \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func bannerViewWillPresentModal(_ bannerView: PrebidMobile.BannerView){
        NSLog("Banner: ad impress")
    }
    
    func bannerViewDidDismissModal(_ bannerView: PrebidMobile.BannerView){
        NSLog("Banner: ad close")
    }
}
```

### 2.3 **激励视频广告**

激励视频广告是指将短视频融入到app场景当中，成为app“任务”之一，用户观看短视频广告后可以得到一些应用内奖励。

#### 主要API

激励视频广告生命周期事件回调

您可以实现全部或部分RewardedAdUnitDelegate，以跟踪点击成功或者激励视频广告请求失败等广告生命周期事件。

```Swift
@objc public protocol RewardedAdUnitDelegate : NSObjectProtocol {
 
    /// Called when an ad is loaded and ready for display
    @objc optional func rewardedAdDidReceiveAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when user is able to receive a reward from the app
    @objc optional func rewardedAdUserDidEarnReward(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the load process fails to produce a viable ad
    @objc optional func rewardedAd(_ rewardedAd: PrebidMobile.RewardedAdUnit, didFailToReceiveAdWithError error: (Error)?)
 
    /// Called when the interstitial view will be launched,  as a result of show() method.
    @objc optional func rewardedAdWillPresentAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the interstial is dismissed by the user
    @objc optional func rewardedAdDidDismissAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when an ad causes the sdk to leave the app
    @objc optional func rewardedAdWillLeaveApplication(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when user clicked the ad
    @objc optional func rewardedAdDidClickAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
}
```

在实现上述事件回调之前，请务必先设置delegate:

```Swift
rewardedAdUnit.delegate = self
```

#### **接入代码示例**

加载并显示激励广告

1.在控制器头文件中加入SDK头文件，声明RewardedAdUnit属性

```Swift
import PrebidMobile
 
var adUnit:RewardedAdUnit!
```

2.在ViewController的实现文件中初始化并加载广告数据：

```Swift
adUnit=RewardedAdUnit(configID: AdConfig.ALX_REWARD_VIDEO_AD_ID)
 
rewardedAdUnit.delegate = self
```

3.展示激励视频广告前必须先调用isReady判断广告是否有效，

```Swift
guard adUnit.isReady else{
    return
}
adUnit.show(from: self)
```

4.实现 RewardedAdUnitDelegate 来跟踪点击成功或者激励视频广告请求失败等广告生命周期事件

```Swift
 extension PBRewardVideoVC: RewardedAdUnitDelegate {
 
    func rewardedAdDidReceiveAd(_ rewardedAd: RewardedAdUnit) {
        NSLog("Reward: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
 
    func rewardedAd(_ rewardedAd: RewardedAdUnit, didFailToReceiveAdWithError error: Error?) {
        var msg:String=""
        if let error = error {
            let error1=error as NSError
            msg = "\(error1.code):\(error1.localizedDescription)"
        }else{
            msg=error?.localizedDescription ?? ""
        }
        
        NSLog("Reward: ad load failed : \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func rewardedAdWillPresentAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad impression")
    }
    
    func rewardedAdUserDidEarnReward(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: user earned reward")
    }
    
    func rewardedAdDidClickAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad click")
    }
    
    func rewardedAdDidDismissAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad close")
    }
 
}
```



### 2.4 **插屏广告**

插屏广告是移动广告的一种常见形式，在应用流程中弹出，当应用展示插页式广告时，用户可以选择点按广告，访问其目标网址，也可以将其关闭，返回应用。插屏广告氛围全屏图文和全屏视频两种

![](https://static.rixengine.com/a/platform/help/sdk/ios-2.4-1.png)

**插屏图文**

![](https://static.rixengine.com/a/platform/help/sdk/ios-2.4-2.png)

 **插屏视频**

#### 主要API

生命周期事件回调

您可以实现全部或部分InterstitialAdUnitDelegate，以跟踪点击成功或者插屏全屏广告请求失败等广告生命周期事件。

```Swift
@objc public protocol InterstitialAdUnitDelegate : NSObjectProtocol {
 
    /// Called when an ad is loaded and ready for display
    @objc optional func interstitialDidReceiveAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when the load process fails to produce a viable ad
    @objc optional func interstitial(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit, didFailToReceiveAdWithError error: (Error)?)
 
    /// Called when the interstitial view will be launched,  as a result of show() method.
    @objc optional func interstitialWillPresentAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when the interstitial is dismissed by the user
    @objc optional func interstitialDidDismissAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when an ad causes the sdk to leave the app
    @objc optional func interstitialWillLeaveApplication(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when user clicked the ad
    @objc optional func interstitialDidClickAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
}
```

#### **接入代码示例**

加载并显示插屏广告

1.在控制器头文件中加入SDK头文件，声明InterstitialAdUnitDelegate属性

```Swift
import PrebidMobile
 
 var adUnit:InterstitialRenderingAdUnit!
```

2.在ViewController的实现文件中初始化并加载广告数据：

```Swift
 adUnit=InterstitialRenderingAdUnit(configID: AdConfig.ALX_INTERSTITIAL_AD_ID)
adUnit.adFormats = [.video,.banner]
adUnit.delegate = self
adUnit.loadAD()
```

3.展示插屏广告前必须先调用isReady判断广告是否有效:

```Swift
guard adUnit.isReady else{
    return
}
adUnit.show(from: self)
```

4.实现InterstitialAdUnitDelegate来跟踪点击成功或者插屏广告请求失败等广告生命周期事件

```Swift
extension PBInterstitialVC: InterstitialAdUnitDelegate {
    func interstitialDidReceiveAd(_ interstitial: InterstitialRenderingAdUnit) {
        NSLog("Interstitial: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
    
    func interstitial(_ interstitial: InterstitialRenderingAdUnit, didFailToReceiveAdWithError error: Error?) {
        var msg:String=""
        if let error = error {
            let error1=error as NSError
            msg = "\(error1.code):\(error1.localizedDescription)"
        }else{
            msg=error?.localizedDescription ?? ""
        }
        
        NSLog("Interstitial: ad load failed : \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func interstitialWillPresentAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad impression")
    }
    
    func interstitialDidClickAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad click")
    }
 
    func interstitialDidDismissAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad close")
    }
 
    
}
```

 

### 2.5 **Native 原生广告**

SDK为接入方提供了可自定义布局的信息流广告，包含大图、小图、组图和视频等基本样式类型（具体的布局摆放可自行定义）

#### 主要API

生命周期事件回调

您可以实现全部或部分NativeAdEventDelegate，以跟踪点击成功或者平台模板广告请求失败等广告生命周期事件。

事件主要分两部分一部分是加载过程中的回调

```Swift
@objc public protocol NativeAdDelegate {
 
    /**
     * A successful Prebid Native ad is returned
     *
     * @param ad use this instance for displaying
     */
    @objc func nativeAdLoaded(ad: PrebidMobile.NativeAd)
 
    /**
     * Prebid Native was not found in the server returned response,
     * Please display the ad as regular ways
     */
    @objc func nativeAdNotFound()
 
 
    /**
     * Prebid Native ad was returned, however, the bid is not valid for displaying
     * Should be treated as on ad load failed
     */
    @objc func nativeAdNotValid()
}
```

第二部分是加载完成后点击事件展示等回调

```Swift
@objc public protocol NativeAdEventDelegate {
 
    /**
     * Sent when the native ad is expired.
     */
    @objc optional func adDidExpire(ad: PrebidMobile.NativeAd)
 
    /**
     * Sent when the native view is clicked by the user.
     */
    @objc optional func adWasClicked(ad: PrebidMobile.NativeAd)
 
    /**
     * Sent when  an impression is recorded for an native ad
     */
    @objc optional func adDidLogImpression(ad: PrebidMobile.NativeAd)
}
```

#### **接入代码示例**

加载并显示平台模板广告

1.在控制器头文件中加入SDK头文件，声明NativeAd  NativeRequest属性

```Swift
import PrebidMobile
var nativeUnit: NativeRequest!
var nativeAd: NativeAd?
```

2.在ViewController的实现文件中初始化并加载广告数据：

```Swift
{
        // 1. Create a NativeRequest
        nativeUnit = NativeRequest(configId: AdConfig.ALX_NATIVE_AD_ID)
        
        // 2. Configure the NativeRequest
        nativeUnit.context = ContextType.Social
        nativeUnit.placementType = PlacementType.FeedContent
        nativeUnit.contextSubType = ContextSubType.Social
        nativeUnit.eventtrackers = eventTrackers
        
        // 3. Make a bid request to Prebid Server
        nativeUnit.fetchDemand { [weak self] result, kvResultDict in
            NSLog("native fetch result \(result.name())")
            
            guard let self = self else {
                return
            }
            
            // 4. Find cached native ad
            guard let kvResultDict = kvResultDict, let cacheId = kvResultDict[PrebidLocalCacheIdKey] else {
                return
            }
            
            // 5. Create a NativeAd
            guard let nativeAd = NativeAd.create(cacheId: cacheId) else {
                return
            }
            
            self.nativeAd = nativeAd
            
            // 6. Render the native ad
            self.titleLabel.text = nativeAd.title
            self.bodyLabel.text = nativeAd.text
            
            if let iconString = nativeAd.iconUrl {
                ImageHelper.downloadImageAsync(iconString) { result in
                    if case let .success(icon) = result {
                        DispatchQueue.main.async {
                            self.iconView.image = icon
                        }
                    }
                }
            }
            
            if let imageString = nativeAd.imageUrl {
                ImageHelper.downloadImageAsync(imageString) { result in
                    if case let .success(image) = result {
                        DispatchQueue.main.async {
                            self.mainImageView.image = image
                        }
                    }
                }
            }
            
            self.callToActionButton.setTitle(nativeAd.callToAction, for: .normal)
            self.sponsoredLabel.text = nativeAd.sponsoredBy
            
            self.nativeAd?.delegate = self
            self.nativeAd?.registerView(view: self.view, clickableViews: [self.callToActionButton])
        }
    }
```

 3.实现NativeAdEventDelegate来跟踪点击成功或者原生广告展示等生命周期事件

```Swift
    extension PBNativeVC:NativeAdEventDelegate{
    func adDidExpire(ad:NativeAd){
        NSLog("native: ad expire")
    }
 
    func adWasClicked(ad:NativeAd){
        NSLog("native: ad click")
    }
 
    func adDidLogImpression(ad:NativeAd){
        NSLog("native: ad impression")
    }
    
}
```

## SDK 返回错误码说明

| 错误码 | 说明                                                |
| ------ | --------------------------------------------------- |
| 0      | Prebid demand fetch successful                      |
| 1      | Prebid server not specified                         |
| 2      | Prebid server does not recognize account id         |
| 3      | Prebid server does not recognize config id          |
| 4      | Prebid server does not recognize the size requested |
| 5      | Network Error                                       |
| 6      | Prebid server error                                 |
| 7      | Prebid Server did not return bids                   |
| 8      | Prebid demand timedout                              |
| 9      | Prebid server url is invalid                        |
| 10     | Prebid unknown error occurred                       |
| 1000   | Response structure is invalid                       |
| 7000   | Internal SDK error                                  |
| 7001   | Wrong arguments                                     |
| 7002   | No VAST tag in media data                           |
| 8000   | SDK misuse                                          |

## 附录

### MAX 第三方自定义适配器

首先在官网 <a href="https://www.applovin.com/" target="_blank">Applovin｜Everything you need to grow your mobile apps（applovin.com）</a> ，创建添加自己的app应用，具体按照MAX官网说明文档进行调整,如果没有创建应用，首先创建一个应用（下面是测试用例）

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.0-1.png" alt="MAX 第三方自定义适配器" class="img-w-full-center">

创建一个iOS应用

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.0-2.png" alt="MAX 第三方自定义适配器" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.0-3.png" alt="MAX 第三方自定义适配器" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.0-4.png" alt="MAX 第三方自定义适配器" class="img-w-full-center">

#### 4.1 添加完app后，回到主界面然后点击Networks，点击图中的

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.1-1.png" alt="4.1-1" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.1-2.png" alt="4.1-2" class="img-w-full-center">

#### 4.2 选择SDK 接入方式

输入Custom Networks Name：<span style="color: red;">Rixengine</span> 并且输入适配器包路径 <span style="color: red;">AlxMaxMediationAdapterSwift（重要，名字一定要对）</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.2-1.png" alt="4.2-1" class="img-w-full-center">

#### 4.3 把Rixengine压缩包里面的适配器文件拷贝到项目中（<span style="color: red;">使用swift方式，objc方式已逐渐废弃淘汰</span>）：

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.3-1.png" alt="4.3-1" class="img-w-full-center">

#### 4.4 创建完Networks后，点击右侧的 Ad Units 依次创建广告位，支持4种类型 BANNER，INTER，NATIVE，REWARD 。

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.4-1.png" alt="4.4-1" class="img-w-full-center">

<span style="color: red;">创建NATIVE 类型广告时要注意选择Manual（自渲染）这个很重要，</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.4-2.png" alt="4.4-2" class="img-w-full-center">

#### 4.5 创建完广告位之后，点击 banner（以BANNER为例）

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.5-1.png" alt="4.5-1" class="img-w-full-center">

拉到下面，点击途中的开启

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.5-2.png" alt="4.5-2" class="img-w-full-center">

并且填入广告参数，广告参数在Rixengine SSP 平台申请（具体如下图）

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-4.5-3.png" alt="4.5-3" class="img-w-full-center">

<b>图中的 Custom Parameters 格式，参数为：</b>

```json
{
    "unitid": "201304",
    "appid": "102512",
    "token": "adc4321b73e7bb097ed9bffd27dc45d9",
    "sid": "36083",
    "isdebug": "false"
}
```

其中appid，unitid，token，sid在Rixengine SSP平台申请（具体可咨询运营对接人员）

- <span style="color: red;">appid对应SSP平台的App ID</span>
- <span style="color: red;">token对应SSP平台的Token</span>
- <span style="color: red;">sid对应SSP平台的ID</span>
- <span style="color: red;">unitid对应SSP平台的Placement ID</span>

#### 4.6 输入完成后，点击保存即可。

#### 4.7 广告集成测试，可以调整图中的

来优先显示Rixengine广告

### Admob 第三方自定义适配器

#### 5.1 创建应用（如果已经有请忽略）

<span>1. 首先在AdMob官网登录，添加第三方广告平台，具体按照AdMob官网说明文档进行调整[https://apps.admob.com/](https://apps.admob.com/) 如果没有创建应用，首先创建一个应用（下面是测试用例）</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-1.png" alt="5.1-1" class="img-w-full-center">


<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-2.png" alt="5.1-2" class="img-w-full-center">

<span>2. 然后添加创建对应的广告单元</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-3.png" alt="5.1-3" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-4.png" alt="5.1-4" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-5.png" alt="5.1-5" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.1-6.png" alt="5.1-6" class="img-w-full-center">

#### 5.2 添加中介组

<span>1. 首先创建 Rixengine 中介组</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-1.png" alt="5.2-1" class="img-w-full-center">

<span>2. 创建添加Rixengine 广告单元，支持激励视频广告，插屏广告，横幅广告。(以激励视频为例) 填入名称激励视频中介，选择广告单元类型为激励广告：</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-2.png" alt="5.2-2" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-3.png" alt="5.2-3" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-4.png" alt="5.2-4" class="img-w-full-center">

<span>3. 然后点击-添加广告单元，选择对应的应用和广告单元</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-5.png" alt="5.2-5" class="img-w-full-center">
 
<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-6.png" alt="5.2-6" class="img-w-full-center">

<span>4. 点击-完成</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-7.png" alt="5.2-7" class="img-w-full-center">

到下一个界面广告瀑布流-添加自定义事件，输入名称和价格

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-8.png" alt="5.2-8" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-9.png" alt="5.2-9" class="img-w-full-center">

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-10.png" alt="5.2-10" class="img-w-full-center">

<span>5. <span style="color: red;">点击继续</span></span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-11.png" alt="5.2-11" class="img-w-full-center">

<span style="color: red;">输入一下广告单元信息（重要）</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-12.png" alt="5.2-12" class="img-w-full-center">
 
然后输入映射广告单元信息：
 
输入AdsDemo.AlxAdmobRewardAdapter  (项目名+适配器名)。支持的具体Adapter类名:

- <span style="color: red;">激励视频 AlxAdmobRewardAdapter</span>
- <span style="color: red;">横幅广告 AlxAdmobBannerAdapter</span>
- <span style="color: red;">插屏广告 AlxAdmobInterstitialAdapter</span>
- <span style="color: red;">原生广告 AlxAdmobNativeAdapter</span>

参数为：

```json
{
    "unitid": "201306",
    "appid": "102532",
    "token": "adc4321b73e7bb097ed9bffd27dc45d9",
    "sid": "36083",
    "isdebug": "false"
}
```

其中appid，unitid，token，sid在Rixengine SSP平台申请（具体可咨询运营对接人员）

- <span style="color: red;">appid对应SSP平台的App ID</span>
- <span style="color: red;">token对应SSP平台的Token</span>
- <span style="color: red;">sid对应SSP平台的ID</span>
- <span style="color: red;">unitid对应SSP平台的Placement ID</span>
 
<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-13.png" alt="5.2-13" class="img-w-full-center">

<span>6. 输入完成后点击-完成到以下界面</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-14.png" alt="5.2-14" class="img-w-full-center">

<span>7. 点击保存按钮 ，然后点击关闭回到主页面就完成了。</span>

<img src="https://static.rixengine.com/a/platform/help/sdk/ios-5.2-15.png" alt="5.2-15" class="img-w-full-center">
