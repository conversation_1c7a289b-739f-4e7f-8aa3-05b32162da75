.app-item-container {
  display: flex;
  align-items: center;
  padding: 16px 0px;
  width: 100%;
  border: 1px solid #e2eaeb;
  border-radius: 6px;
  margin-bottom: 16px;
  cursor: pointer;
  .item-left {
    position: relative;
    padding-left: 24px;
    .profile-container {
      width: 50px;
      height: 50px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
      > span {
        font-size: 40px;
        font-family: 'Satoshi Variable';
        font-weight: 400;
        color: #fff;
        display: flex;
        align-items: center;
      }
      div {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 14px;
        height: 14px;
        border-radius: 6px 0px;
        background-color: #fff;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #a4c639;
        img {
          width: 14px;
          height: 14px;
        }
      }
    }
  }
  .item-right {
    padding-left: 12px;
    flex: 1;
    padding-right: 20px;

    display: flex;
    flex-direction: column;
    align-self: flex-start;
    justify-content: space-between;
    height: 50px;

    .app-info-title {
      display: flex;
      width: 100%;

      .app-name {
        flex: 1;
        width: 0;

        color: var(--text-color);
        font-weight: bold;
        white-space: nowrap;
      }

      .line {
        color: #e2eaeb;
        margin: 0px 12px;
      }

      .app-id {
        color: #8d9799;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    .gray {
      font-size: 14px;
      color: #5e6466;
      line-height: 22px;
    }
    .ellipsis {
      display: inline-block;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
