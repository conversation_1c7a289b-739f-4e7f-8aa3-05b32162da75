/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-02-22 10:14:49
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Table, ConfigProvider } from 'antd';
import type { TableProps } from 'antd';
import { ColumnProps } from 'antd/es/table';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';
import CusLoadingIcon from '@/components/LoadingIcon';
import { SpinProps } from 'antd';
import { usePrevious } from 'ahooks';
type OriginalTableProps<T, K> = TableProps<T> & {
  loading: boolean | SpinProps;
  columns: ColumnProps<T>[];
  dataSource: T[];
  rowKey: string;
  isBtnTable?: boolean; // 通过样式控制行高
  emptyRender?: () => JSX.Element;
};

function OriginalTable<T extends object>({
  columns,
  dataSource,
  rowKey,
  isBtnTable = true,
  emptyRender,
  ...rest
}: OriginalTableProps<T, keyof T>): JSX.Element {
  const [dataLength, setDataLength] = useState<number>(dataSource?.length || 0);
  const prevDataLength = usePrevious(dataLength);
  const [isEmpty, setIsEmpty] = useState<boolean>(false);
  useEffect(() => {
    setDataLength(dataSource?.length || 0);
  }, [dataSource]);
  useEffect(() => {
    if (prevDataLength! >= 0 && !dataLength) {
      setIsEmpty(true);
    }
  }, [prevDataLength]);
  // 自定义空状态
  const normalEmptyRender = () => (
    <div className={styles['content-empty']}>
      <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
      <span>No Data</span>
    </div>
  );
  return (
    <ConfigProvider renderEmpty={(!isEmpty && emptyRender) || normalEmptyRender}>
      <Table<T>
        {...rest}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={rowKey}
        size="small"
        loading={{ spinning: rest.loading as boolean, indicator: <CusLoadingIcon /> }}
        // bordered
        className={`${styles['original-table']} ${isBtnTable ? styles['original-btn-table'] : ''}`}
      />
    </ConfigProvider>
  );
}

export default OriginalTable;
