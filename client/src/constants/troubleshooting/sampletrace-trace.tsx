/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-21 20:13:56
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 14:15:10
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';

import { SampleRegionMap, SampleRegionOptions, SampleType } from './index';

export const TraceDemandSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tag ID',
    type: 'input',
    key: 'tag_id',
    value: ''
  },
  {
    name: 'Server Region',
    type: 'select',
    key: 'region',
    value: [],
    options: SampleRegionOptions,
    placeholder: 'Please Select Server Region',
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },

  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Bundle',
    type: 'input',
    key: 'bundle',
    value: '',
    options: [],
    placeholder: 'Please Input Bundle'
  }
];

export const TraceBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Troubleshooting',
    icon: 'rix-trace'
  },
  {
    name: 'Sample Trace Task',
    url: '/troubleshooting/sample-trace/task'
  },
  {
    name: 'Trace'
  }
];

export const TraceColumnKeysMap = {
  [SampleType.SupplyRequest]: ['seller_id', 'block_reason', 'bundle', 'placement_id', 'region', 'ad_format'],
  [SampleType.SupplyResponse]: ['seller_id', 'bundle', 'cid', 'crid', 'adomain', 'region', 'ad_format', 'placement_id'],
  [SampleType.DemandRequest]: ['buyer_id', 'seller_id', 'bundle', 'placement_id', 'region', 'ad_format'],
  [SampleType.DemandResponse]: [
    'tag_id',
    'buyer_id',
    'seller_id',
    'bundle',
    'bid_status',
    'cid',
    'crid',
    'adomain',
    'region',
    'ad_format',
    'placement_id'
  ]
};
