/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 11:45:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 16:51:00
 * @Description:
 */
import Router from 'koa-router';
import { profitController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/profit' });

const routers = router
  .post('/addProfit', validTesting, profitController.addProfit)
  .post('/updateProfit', profitController.updateProfit)
  .post('/addBundleProfit', profitController.addBundleProfit)
  .post('/updateBundleProfit', profitController.updateBundleProfit)
  .post('/getProfitList', profitController.getProfitList);

export default routers;
