/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-28 15:08:06
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 11:47:22
 * @Description:
 */
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { AdvertiserBillingMetrics } from '@/constants/report/billing-report';
import {
  ExportTypeMap,
  StatusTypeMap
} from '@/constants/report/exported-report';
import {
  advBillingModel,
  demandModel,
  exportLogModel,
  supplyModel
} from '@/models';
import { BillingAPI } from '@/types/billing';
import { array2Object, createDirectory } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import { getDownTaskInfo } from '@/utils/report';
import { queryBQDataWithCache } from '@/utils/report/report-util';
import {
  adaptGenerateBigQuerySQL,
  buildMultiTableQuery,
  concatSQLFragments,
  LabelGenerationParams,
  SQLFragments,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';
import { Country } from '@/constants/report/common-report';
import { AdFormatToLabel } from '@/constants/report/common-report';

class AdvBillingService {
  async getAdvertiserBillingList(
    params: any,
    labels: LabelGenerationParams
  ): Promise<any> {
    const sql = await this.initSQL(params, false, labels);
    const data = await queryBQDataWithCache(sql, labels);

    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        // billing 报表没有单独计算的指标
        metrics: [],
        context: {
          start_date: params.start_date,
          end_date: params.end_date
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return { total, data: result };
  }

  async initSQL(params: any, isAll: boolean, labels: LabelGenerationParams) {
    let { columns = [], partner_id = [], tnt_id } = params;
    // 移除 columns 中的 month，这个是零时区的，不能参与 sql 片段
    columns = columns.filter((column: string) => column !== 'month');
    // 固定的指标
    const FIXED_METRICS = [...AdvertiserBillingMetrics];
    // 判断 columns 是否包含 buyer_id，如果包含则需要将 buyer_id 加入到 columns 中的
    // 这是为了指定查询的表 billing_demand_report
    const hasBuyerIdDimension = columns.includes('buyer_id');
    const queryColumns = hasBuyerIdDimension
      ? columns
      : [...columns, 'buyer_id'];
    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        // 没有 buyer_request
        metrics: FIXED_METRICS,
        columns: queryColumns
      },
      { api_url: labels.tag }
    );
    // 如果存在查询 ad_format, country, app_bundle_id 维度，则需要添加 IS NOT NULL 的判断条件
    const conditions = ['ad_format', 'country', 'app_bundle_id'];
    conditions.forEach(column => {
      if (columns.includes(column)) {
        fragments.where?.push(`${column} IS NOT NULL`);
      }
    });

    // 仅在原始columns不包含buyer_id时移除相关字段
    if (!hasBuyerIdDimension) {
      const removeBuyerId = (item: string) => !item.includes('buyer_id');

      fragments.select = fragments.select.filter(removeBuyerId);
      fragments.groupBy = fragments.groupBy?.filter(removeBuyerId);
      fragments.orderBy = fragments.orderBy?.filter(removeBuyerId);
    }

    let multiTableQuery: SQLFragments = fragments;
    // 如何构建买家伙伴关系表 buyerPartnerFragments
    // 1. 判断params.columns 是否存在 partner_id 则 select partner_id,partner_name，否则传[]
    // 2. group 和 select 结果一致
    // 3. where条件 判断params里是否存在 partner_id数字数组，并拼接成 partner_id in (12106, 12104, 12100)
    const partner_id_columns = columns.includes('partner_id')
      ? ['partner_id', 'partner_name']
      : [];
    const where_columns =
      partner_id.length > 0 ? [`partner_id in (${partner_id.join(',')})`] : [];
    const buyerPartnerTable = concatSQLFragments({
      select: ['buyer_id', ...partner_id_columns],
      from: '`saas-373106.saas_others.buyer_partner`',
      where: [...where_columns, `tnt=${tnt_id}`],
      groupBy: ['buyer_id', ...partner_id_columns]
    });
    const buyerPartnerFragments: SQLFragments = {
      select: partner_id_columns,
      from: `(${buyerPartnerTable})`,
      where: [],
      groupBy: partner_id_columns
    };
    multiTableQuery = buildMultiTableQuery(
      {
        fragments,
        alias: 'billing_overview'
      },
      [
        {
          table: {
            fragments: buyerPartnerFragments,
            alias: 'buyer_partner'
          },
          type: 'INNER JOIN',
          on: 'billing_overview.buyer_id = buyer_partner.buyer_id'
        }
      ]
    );
    return concatSQLFragments({
      ...multiTableQuery,
      select: [...multiTableQuery.select, 'count(*) over() as total'],
      limit: isAll ? '' : multiTableQuery.limit
    });
  }

  async downloadAllReport(
    params: BillingAPI.AdvertiserParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { columns, cur_time_zone, tnt_id, cur_condition_str } = params;

      // 如果存在查询 ad_format, country, app_bundle_id 维度，需要移除固定指标 request 和 response
      const needRemoveMetrics = ['ad_format', 'country', 'app_bundle_id'].some(
        column => columns.includes(column)
      );
      const filterMetrics = AdvertiserBillingMetrics.filter(metric => {
        if (needRemoveMetrics) {
          return !['request', 'response'].includes(metric);
        }
        return true;
      });

      const ori_condition = JSON.parse(cur_condition_str);
      ori_condition.metrics = filterMetrics;
      const dimensions = columns.map(v =>
        v === 'partner_id' ? 'adv_partner_id' : v
      );
      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } =
        getDownTaskInfo(
          cur_time_zone,
          tnt_id,
          dimensions,
          filterMetrics,
          'Advertiser_Billing_Reporting'
        );
      // 由于表义不明，修改 headerCloumns 中 key 为 request 的 header 为 Out Request
      headerCloumns.forEach(column => {
        if (column.key === 'request') {
          column.header = 'Out Request';
        }
      });

      const exportLogRow = await exportLogModel.createExportLog(
        user_id,
        JSON.stringify(ori_condition),
        ExportTypeMap['Advertiser Billing Reporting'],
        csvName,
        requestPath
      );
      if (exportLogRow?.insertId) {
        const result = {
          code: Code.SUCCESS,
          name: csvName,
          type: ExportTypeMap['Advertiser Billing Reporting']
        };
        resolve(result);
      }
      const sql = await this.initSQL(params, true, labels);
      const data = await advBillingModel.downloadAllReport(sql, labels);

      if (data) {
        const [supplyList, demandList] = await Promise.all([
          supplyModel.getDownloadSupplyList({ tnt_id }),
          demandModel.getDownloadDemandList({ tnt_id })
        ]);
        const sellerMap = array2Object(supplyList || [], 'seller_id');
        const buyerMap = array2Object(demandList || [], 'buyer_id');
        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
        });
        writeStream.on('error', async err => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });

        data
          .on('error', async (err: any) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(
              `bigquery stream error, error=[${err.message}]`
            );
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((data: BillingAPI.BillingListItem) => {
              const item = transformOneRowSQL(data, {
                metrics: [],
                context: {
                  start_date: params.start_date,
                  end_date: params.end_date
                }
              });
              const {
                buyer_id,
                seller_id,
                partner_id,
                partner_name,
                ad_format
              } = item;
              const seller = sellerMap[seller_id] || {};
              const buyer = buyerMap[buyer_id] || {};
              item.buyer = buyer
                ? `${buyer?.buyer_name}(${buyer_id})`
                : `${buyer_id}`;

              item.seller = seller
                ? `${seller?.seller_name}(${seller_id})`
                : `${seller_id}`;
              item.adv_partner = partner_id
                ? `${partner_name}(${partner_id})`
                : '-';
              item.ad_format = AdFormatToLabel[ad_format] || '-';
              item.country = Country[item.country] || 'Unknown';

              if (item.ad_width && item.ad_height) {
                item.ad_size = `${item.ad_width} * ${item.ad_height}`;
              }
              return item;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }
}

export const advBillingService = new AdvBillingService();
