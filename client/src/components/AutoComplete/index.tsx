import { AutoComplete, AutoCompleteProps } from 'antd';
import classNames from 'classnames';
import styles from './index.less';

type NormalAutoCompleteProps = AutoCompleteProps;

const NormalAutoComplete = (props: NormalAutoCompleteProps) => {
  return (
    <AutoComplete
      {...props}
      className={classNames(styles['normal-autocomplete'], 'normal-autocomplete', props.className)}
    />
  );
};

export default NormalAutoComplete;
