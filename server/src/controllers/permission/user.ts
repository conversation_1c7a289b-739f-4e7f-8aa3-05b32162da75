/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-28 17:28:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:59:57
 * @Description:
 */
import { Context } from 'koa';
import { getLogger } from '@/config/log4js';
import { pmsUserService, userService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';

import {
  AddUserScheme,
  EditUserScheme,
  DeleteUserScheme,
  ForceLogOutScheme
} from '@/schema/common';
import { md5, getConfig } from '@/utils';
import { delRedisByKey, getRedisByMultipleKeys } from '@/db/redis';
import {
  TenantUserLimitCount,
  RESULT,
  DemoTenantUserLimitCount
} from '@/constants';

const { encryptionStr, redisConfig } = getConfig();

class UserCtrl {
  @validateBody(AddUserScheme)
  async addOneUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id, user_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    formData.password = md5(encryptionStr + formData.password);

    const [data, UserCount] = await Promise.all([
      userService.isAccountNameExists(formData),
      pmsUserService.getUserListCount(tnt_id)
    ]);
    // 测试租户用户限制 100
    const UserLimitCount =
      tnt_id === 1047 ? DemoTenantUserLimitCount : TenantUserLimitCount;
    if (data && data.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
    } else if (UserCount >= UserLimitCount) {
      result = getCtxResult('USER_COUNT_LIMIT');
    } else {
      const res = await pmsUserService.addOneUser(formData, user_id);
      result = getCtxResult('SUCCESS');
      getLogger('app').info(
        `addUser success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  async getUserList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id, user_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    formData.user_id = user_id;

    let res = await pmsUserService.getUserList(formData);
    if (Array.isArray(res) && res.length) {
      const keys = res.map(v => {
        const key = md5(`${redisConfig.platform_key}_${v.user_id}_${tnt_id}`);
        return key;
      });
      const data: any = await getRedisByMultipleKeys(keys);
      let loginUserIds: any[] = [];
      if (Array.isArray(data) && data.length) {
        loginUserIds = data.map(v => v.user_id);
      }
      res = res.map(v => ({
        ...v,
        is_login: loginUserIds.includes(v.user_id) ? 1 : 0
      }));
    }
    result = getCtxResult('SUCCESS', res);

    ctx.body = result;
  }

  @validateBody(EditUserScheme)
  async editUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    // eslint-disable-next-line no-unused-expressions
    formData.new_password &&
      (formData.new_password = md5(encryptionStr + formData.new_password));
    const data = await pmsUserService.editUser(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(
        `editUser success formData=[${JSON.stringify(formData)}]`
      );
    }

    ctx.body = result;
  }

  // 删除用户需要删掉对应的redis key
  @validateBody(DeleteUserScheme)
  async deleteUser(ctx: Context) {
    let result = { ...RESULT };

    const formData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    const { user_id } = formData;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    const data = await pmsUserService.deleteUser(formData);
    if (data) {
      await delRedisByKey(key);
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(
        `deleteUser success formData=[${JSON.stringify(formData)}]`
      );
    }

    ctx.body = result;
  }

  // 强制用户登出
  @validateBody(ForceLogOutScheme)
  async forceLogOut(ctx: Context) {
    let result = { ...RESULT };

    const { tnt_id } = ctx.session!;
    const { user_id } = ctx.request.body;
    const key = md5(`${redisConfig.platform_key}_${user_id}_${tnt_id}`);
    await delRedisByKey(key);
    result = getCtxResult('SUCCESS');

    ctx.body = result;
  }
}

export const pmsUserCtrl = new UserCtrl();
