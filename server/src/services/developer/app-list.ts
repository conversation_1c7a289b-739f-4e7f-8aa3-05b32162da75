/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:25:42
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 11:56:21
 * @Description:
 */
import { appModel } from '@/models';
import { AppListAPI } from '@/types/app-list';

class AppService implements AppListAPI.AppService {
  //  seller_id + bundle唯一，seller_id + app_name + platform唯一
  async isAppExists(params: AppListAPI.IsAppExistsParams): Promise<{
    flag: boolean;
    type: 'APP_NAME_EXISTS' | 'APP_BUNDLE_EXISTS' | '';
  }> {
    const data = await appModel.isAppExists(params);
    const { app_name, platform, bundle } = params;
    if (data && data.length) {
      const isApp = data.some(
        item => item.app_name === app_name && +item.platform === +platform
      );
      const isBundle = data.some(item => item.bundle === bundle);
      return {
        flag: isApp || isBundle,
        // eslint-disable-next-line no-nested-ternary
        type: isApp ? 'APP_NAME_EXISTS' : isBundle ? 'APP_BUNDLE_EXISTS' : ''
      };
    }
    return {
      flag: false,
      type: ''
    };
  }
  async countSellerApp(params: AppListAPI.IsAppExistsParams): Promise<any> {
    return await appModel.countSellerApp(params);
  }
  async isPlacementExists(
    params: AppListAPI.IsPlacementExistsParams
  ): Promise<any[]> {
    return await appModel.isPlacementExists(params);
  }

  async addApp(params: AppListAPI.AddAppParams): Promise<boolean> {
    return await appModel.addApp(params);
  }

  async addPlacement(params: AppListAPI.AddPlacementParams): Promise<boolean> {
    return await appModel.addPlacement(params);
  }

  async updateApp(params: AppListAPI.UpdateAppParams): Promise<boolean> {
    return await appModel.updateApp(params);
  }

  async updatePlacement(
    params: AppListAPI.UpdatePlacementParams
  ): Promise<boolean> {
    return await appModel.updatePlacement(params);
  }

  async getAppList(tnt_id: number): Promise<AppListAPI.AppListItem[]> {
    return await appModel.getAppList(tnt_id);
  }

  async getAllPlacementList(tnt_id: number) {
    return await appModel.getAllPlacementList(tnt_id);
  }
}

export default new AppService();
