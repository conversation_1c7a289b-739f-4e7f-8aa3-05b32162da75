import { Context } from 'koa';
import moment from 'moment';
import { BackendAPI } from '@/types/backend-api';
import { beFullReportServices } from '@/services';
import { validateBeApiBody } from '@/utils/validate-params';
import { BeFullReportScheme } from '@/schema/backend-api/full-report';
import { APIRESULT } from '@/constants/backend-api/common';
import { Code, Message } from '@/codes';
import { setRedisByKey } from '@/db/redis';

class FullReportController implements BackendAPI.FullReportCtrl {
  @validateBeApiBody(BeFullReportScheme, false)
  async getFullReport(ctx: Context) {
    const result = { ...APIRESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.state!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const list = await beFullReportServices.getFullReport(formData, api_url);
    const { redis_count_key, redis_count, redis_expired } = ctx.state;
    console.log(redis_count_key, redis_count, redis_expired);
    if (list) {
      result.data = list;
      result.status.code = Code.SUCCESS;
      result.status.msg = Message.SUCCESS;
      result.timestamp = moment
        .utc(new Date())
        .format('ddd MMM D HH:mm:ss Z YYYY');
      await setRedisByKey(redis_count_key, redis_count, redis_expired);
      ctx.body = result;
    }

    ctx.body = result;
  }
}

export const beFullReportCtrl = new FullReportController();
