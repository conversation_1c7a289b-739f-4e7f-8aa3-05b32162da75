function notify_lark() {
  local deploy_status=$1
  local env=$2
  local webhook_url=$3
  local color="green"
  if [ "$deploy_status" != "success" ]; then
    color="red"
  fi
  local msg="[${env}] deploy ${deploy_status}"
  echo ${msg}
  local author=$(git log -1 --pretty=%an)
  local commit_message=$(git log -1 --pretty=%B)
  local job_url="https://gitlab.algorix.co/fe-saas/saas.rix-platform/-/jobs/${CI_JOB_ID}"

  local message="{\"msg_type\":\"interactive\",\"card\":{\"header\":{\"title\":{\"tag\":\"plain_text\",\"content\":\"[Env: ${env}] CI/CD Info\"},\"template\":\"${color}\"},\"elements\":[{\"tag\":\"column_set\",\"flex_mode\":\"stretch\",\"horizontal_spacing\":\"8px\",\"horizontal_align\":\"left\",\"columns\":[{\"tag\":\"column\",\"width\":\"weighted\",\"vertical_align\":\"top\",\"vertical_spacing\":\"8px\",\"elements\":[{\"tag\":\"markdown\",\"content\":\"**Branch**: ${CI_COMMIT_BRANCH}\\n**Message**: ${msg}\\n**Author**: ${author}\\n**Commit Message**: ${commit_message}\",\"text_align\":\"left\",\"text_size\":\"normal\"}],\"weight\":1}],\"margin\":\"16px 0px 0px 0px\"},{\"tag\":\"action\",\"layout\":\"default\",\"actions\":[{\"tag\":\"button\",\"text\":{\"tag\":\"plain_text\",\"content\":\"View Job\"},\"type\":\"default\",\"width\":\"default\",\"size\":\"medium\",\"behaviors\":[{\"type\":\"open_url\",\"default_url\":\"${job_url}\"}]}]}]}}"
  curl -X POST ${webhook_url} -d "$message" -H 'Content-Type: application/json'
}
