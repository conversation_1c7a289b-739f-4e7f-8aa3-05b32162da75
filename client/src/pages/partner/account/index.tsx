import React, { useState, useEffect, useMemo } from 'react';
import { Button, message, Spin, Switch } from 'antd';
import styles from './index.less';
import { fetchData } from '@/utils';
import PageContainer from '@/components/RightPageContainer';
import NormalTitle from '@/components/NormalTitle';
import OriginalTable from '@/components/Table/OriginalTable';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';
import type { OperateRenderItem } from '@/components/OperateRender';
import { StatusMap } from '@/constants';
import { PartnerColumnsColumns, AccountBreadOptions, PartnerTypeOptions } from '@/constants/partner/account';
import { useSearchParams, history } from '@umijs/max';
import { editDashboardUser, getPartnerAccount, getPartnerList } from '@/services/api';
import CreateAccount from '../components/CreateAccount';
import { PartnerType } from '@/constants/partner';
import { RoleType } from '@/constants/permission/role';
import useCustomRequest from '@/hooks/useCustomRequest';

const Page: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<PartnerAPI.PartnerUser[]>([]);
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<'Create' | 'Edit' | 'Password'>('Create');
  const [editItem, setEditItem] = useState<PartnerAPI.PartnerUser>();

  const partner_id = searchParams.get('id') || 0;
  const [partnerData, setPartnerData] = useState<PartnerAPI.PartnerListItem | undefined>(undefined);
  const { run: reload, loading: partnerLoading } = useCustomRequest(getPartnerList, {
    cacheKey: 'partner-all-list',
    onSuccess: (data: PartnerAPI.PartnerListItem[]) => {
      if (!data.length) {
        history.push('/404');
      } else {
        setPartnerData(pre => data.find(v => v.partner_id === +partner_id));
      }
    }
  });

  useEffect(() => {
    if (+partner_id) {
      getAccount(+partner_id);
      reload();
    }
  }, []);

  const { options, disabledOpt, hideAddBtn } = useMemo(() => {
    const len = tableData.length;
    // 过滤掉不允许的类型
    let allow = [RoleType['Demand Partner'], RoleType['Supply Partner']];
    if (partnerData?.type !== PartnerType['Advertiser & Publisher']) {
      const ignore =
        partnerData?.type === PartnerType.Advertiser ? RoleType['Supply Partner'] : RoleType['Demand Partner'];
      allow = allow.filter(v => v !== ignore);
    }
    const arr = tableData.map(v => +v.role_type);
    const options = PartnerTypeOptions.filter(v => allow.includes(v.value));
    const hideAddBtn = !partnerData
      ? true
      : partnerData?.type === PartnerType['Advertiser & Publisher']
      ? len >= 2
      : len >= 1;
    return { hideAddBtn, options, disabledOpt: arr };
  }, [partnerData, tableData]);

  const handleReset = (params: PartnerAPI.PartnerUser) => {
    setType('Password');
    setEditItem(params);
    setVisible(true);
  };

  const handleEdit = (params: PartnerAPI.PartnerUser) => {
    setEditItem(params);
    setType('Edit');
    setVisible(true);
  };

  const handleSwitch = (e: boolean, row: PartnerAPI.PartnerUser, type: 'status' | 'api_status') => {
    setLoading(true);
    const params = {
      user_id: row?.user_id,
      [type]: e ? StatusMap.Active : StatusMap.Paused,
      ori_data: { [type]: row[type], user_id: row.user_id }
    };

    fetchData({
      request: editDashboardUser,
      params,
      onSuccess: () => {
        message.success('Success');
        getAccount(+partner_id);
      }
    });
  };

  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Reset Password',
      onClick: handleReset,
      icon: <RixEngineFont type="rix-resetPwd" />
    }
  ];

  const AccountNameOptions: OperateRenderItem[] = [
    {
      label: '',
      onClick: handleEdit,
      icon: <RixEngineFont type="edit" className={styles['account-edit']} />,
      text: 'account_name'
    }
  ];

  const onNameRender = (_: string, params: PartnerAPI.PartnerUser) => (
    <OperateRender btnOptions={AccountNameOptions} params={params} />
  );

  const onOptRender = (_: string, params: PartnerAPI.PartnerUser) => (
    <OperateRender btnOptions={OperateOptions} params={params} />
  );

  const onStatusRender = (_: number, row: PartnerAPI.PartnerUser) => (
    <Switch
      checked={_ === StatusMap.Active}
      onChange={e => handleSwitch(e, row, 'status')}
      loading={loading}
      checkedChildren="Active"
      unCheckedChildren="Paused"
    />
  );

  const onApiStatusRender = (_: number, row: PartnerAPI.PartnerUser) => (
    <Switch
      checked={_ === StatusMap.Active}
      onChange={e => handleSwitch(e, row, 'api_status')}
      loading={loading}
      checkedChildren="Active"
      unCheckedChildren="Paused"
    />
  );

  const getAccount = (partner_id: number) => {
    fetchData({
      request: getPartnerAccount,
      params: { partner_id: partner_id || 0 },
      setLoading,
      onSuccess: (data: PartnerAPI.PartnerUser[]) => {
        setTableData(data);
      }
    });
  };

  const columns = useMemo(() => {
    const arr = PartnerColumnsColumns;
    const aIndex = arr.findIndex(v => v.dataIndex === 'account_name');
    const sIndex = arr.findIndex(v => v.dataIndex === 'status');
    const apiIndex = arr.findIndex(v => v.dataIndex === 'api_status');
    const oIndex = arr.findIndex(v => v.dataIndex === 'operate');
    if (aIndex !== -1) {
      arr[aIndex].render = onNameRender;
    }
    if (sIndex !== -1) {
      arr[sIndex].render = onStatusRender;
    }
    if (apiIndex !== -1) {
      arr[apiIndex].render = onApiStatusRender;
    }
    if (oIndex !== -1) {
      arr[oIndex].render = onOptRender;
    }
    return arr;
  }, []);

  return (
    <PageContainer
      options={AccountBreadOptions}
      isBack
      handleGoBack={() => history.go(-1)}
      className={styles['page-container-account']}
    >
      <Spin spinning={loading || partnerLoading}>
        <div className={styles['account-container']}>
          <div className={styles['top']}>
            <NormalTitle
              blackName="Partner Account"
              grayName={
                partnerData?.partner_name
                  ? `${partnerData?.partner_name}(${partnerData?.partner_id})`
                  : `${partnerData?.partner_id}`
              }
              isTitle={true}
              bottom={28}
            />
          </div>
          {!hideAddBtn && (
            <Button
              type="primary"
              disabled={!partnerData || !partnerData.partner_id}
              style={{ marginBottom: 16 }}
              onClick={() => {
                setVisible(true);
                setType('Create');
              }}
            >
              Create Account
            </Button>
          )}
          <OriginalTable
            loading={false}
            columns={columns}
            dataSource={tableData}
            rowKey="user_id"
            scroll={{ y: 'calc(100vh - 220px)' }}
          />
        </div>
      </Spin>
      <CreateAccount
        visible={visible}
        name={partnerData ? `${partnerData.partner_name}(${partnerData.partner_id})` : ''}
        type={type}
        partnerData={partnerData}
        options={options}
        disabledOpt={disabledOpt}
        item={editItem}
        onClose={() => {
          setVisible(false);
        }}
        onSave={() => {
          setVisible(false);
          getAccount(+partner_id);
        }}
      />
    </PageContainer>
  );
};

export default Page;
