import Joi from 'joi';
import { validDateParams } from '@/schema/report/utils';
import { BackendAPI } from '@/types/backend-api';
import {
  BeDashboardMetrics,
  BeDashboardDimension
} from '@/constants/backend-api/full-report';
import { BeApiTimeZoneMap } from '@/constants/time-zone';

const metricsValidation = Joi.array()
  .min(1)
  .items(
    Joi.string()
      .custom((value, helpers) => {
        // 防止 SQL 注入攻击
        const isRight = BeDashboardMetrics.includes(value);
        if (!isRight) {
          return helpers.error('metricsValidation invalid');
        }
        return value;
      })
      .message('The mertics is invalid')
  )
  .required();

const dimensionValidation = Joi.array().items(
  Joi.string()
    .custom((value, helpers) => {
      const isRight = BeDashboardDimension.includes(value);
      if (!isRight) {
        return helpers.error('dimensionValidation invalid');
      }
      return value;
    })
    .message('The dimension is invalid')
);

const bundleValidation = Joi.string().custom((value, helpers) => {
  const buldes = value.split(',');
  const isRight = buldes.length <= 20000;
  if (!isRight) {
    return helpers.error('number.maximum', { limit: 20000 });
  }
  return value;
});

export const BeFullReportScheme = Joi.object({
  region: Joi.array().items(
    Joi.string().valid('USE', 'APAC', 'EUW').required()
  ),
  country: Joi.array().items(Joi.string().max(3).required()),
  ad_format: Joi.array().items(
    Joi.number().integer().valid(1, 2, 3, 4).required()
  ),
  placement_id: Joi.string().max(500),
  app_bundle_id: bundleValidation.messages({
    'number.maximum': 'Maximum inquiries of {#limit} bundles'
  }),
  buyer_id: Joi.array().items(Joi.number().integer().required()).messages({
    'any.required': 'adv_id is required',
    'number.base': 'adv_id must be a number',
    'number.integer': 'adv_id must be an integer',
    'array.base': 'adv_id must be an number array',
    'array.includesRequiredUnknowns': 'adv_id must be an number array',
    'array.includesRequired': 'adv_id must be an number array'
  }),
  seller_id: Joi.array().items(Joi.number().integer().required()).messages({
    'any.required': 'pub_id is required',
    'number.base': 'pub_id must be a number',
    'number.integer': 'pub_id must be an integer',
    'array.base': 'pub_id must be an number array',
    'array.includesRequiredUnknowns': 'pub_id must be an number array',
    'array.includesRequired': 'pub_id must be an number array'
  }),
  metrics: metricsValidation,
  columns: dimensionValidation,
  start_date: Joi.string()
    .pattern(/^\d{8}$/)
    .required(), // 8位的数字 默认当天
  end_date: Joi.string()
    .pattern(/^\d{8}$/)
    .required(), // 8位的数字 默认当天
  start: Joi.number().integer().min(0).required(),
  end: Joi.number()
    .integer()
    .min(1)
    .required()
    .when('start', {
      // 必须大于start
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .max(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: relation => relation + 500
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    })
    .when('start', {
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .min(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: relation => relation + 1
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    }),
  order: Joi.string().valid('desc', 'asc'),
  order_key: Joi.array()
    .items(
      Joi.string().valid(
        'date',
        'buyer_net_revenue',
        'request',
        'seller_id',
        'buyer_id'
      )
    )
    .messages({
      '*': 'Invalid order key'
    }),
  split_time: Joi.number().integer().valid(0, 1),
  timezone: Joi.string().valid(...Object.keys(BeApiTimeZoneMap)),
  tnt_id: Joi.number().integer(),
  cur_time_zone: Joi.string(),
  tz_start_date: Joi.string(),
  tz_end_date: Joi.string()
}).custom((value: BackendAPI.FullReportPaeams, helpers: any) => {
  const { start_date, end_date, columns: dimensions, tnt_id } = value;
  const isBigDimension = dimensions?.some((item: string) =>
    ['app_bundle_id', 'placement_id'].includes(item)
  );
  if (isBigDimension) {
    const { isVaild, message } = validDateParams(
      start_date,
      end_date,
      tnt_id === 1053 ? 15 : 7
    );
    return isVaild ? value : helpers.message(message);
  }
  const { isVaild, message } = validDateParams(start_date, end_date, 15);
  return isVaild ? value : helpers.message(message);
});
