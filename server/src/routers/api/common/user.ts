/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-02 10:08:33
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-10 11:17:02
 * @Description:
 */

import Router from 'koa-router';
import { userCtrl, pmsUserCtrl } from '@/controllers';

const router = new Router({ prefix: '/user' });

const routers = router
  .post('/resetPassword', userCtrl.resetPasswordByUserId)
  .post('/getCurrentUser', userCtrl.currentUser)
  .post('/logIn', userCtrl.login)
  .post('/logOut', userCtrl.logout)
  .post('/confirmPassword', userCtrl.confirmPassword)
  .post('/sendEmail', userCtrl.sendEmail)
  .post('/getDashboardUser', userCtrl.getDashboardUser)
  .post('/editDashboardUser', userCtrl.editDashboardUser)
  .post('/isAccountNameExists', userCtrl.isAccountNameExists)
  .post('/vaildPassword', userCtrl.vaildPassword)
  .post('/forceLogOut', pmsUserCtrl.forceLogOut)
  .post('/addOneUser', pmsUserCtrl.addOneUser)
  .post('/getUserList', pmsUserCtrl.getUserList)
  .post('/editUser', pmsUserCtrl.editUser)
  .post('/deleteUser', pmsUserCtrl.deleteUser)
  .post('/resetAccountName', userCtrl.updateAccountName)
  .post('/restUserPwd', userCtrl.resetPassword)
  .post('/switchAccount', userCtrl.switchAccount)
  .post('/getUserLinkList', userCtrl.getUserLinkList)
  .post('/authLogin', userCtrl.authLogin);

export default routers;
