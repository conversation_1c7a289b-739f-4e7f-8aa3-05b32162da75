/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-09-19 10:19:43
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-10 17:30:19
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import StatusTag from '@/components/Tag/NewStatusTag';
import { StatusDesc, StatusOptions } from '@/constants';
import { ColumnProps } from 'antd/lib/table';

import moment from 'moment';
import EllipsisPopover from '@/components/EllipsisPopover';
export const IvtType = {
  pixalate: 1,
  human: 2
};
export const IvtTypeOptions = [
  {
    label: 'Px',
    value: 1
  },
  {
    label: 'Hm',
    value: 2
  }
];
export const IvtTypeDesc: { [key: number]: string } = {
  1: 'Px',
  2: 'Hm'
};
export const IvtConfigBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy',
    icon: 'rix-strategy'
  },
  {
    name: 'IVT'
  }
];

export const IvtConfigColumnOptions: ColumnProps<StrategyAPI.IvtConfigItem>[] = [
  {
    title: 'Publisher',
    width: 180,
    dataIndex: 'seller_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.seller_id ? `${params.seller_name}(${params.seller_id})` : '-'}>
        <span>{params.seller_id ? `${params.seller_name}(${params.seller_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Advertiser',
    width: 180,
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}>
        <span>{params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Type',
    width: 120,
    dataIndex: 'type',
    render: (_, row) => (
      <HoverToolTip title={IvtTypeDesc[row['type']] ? IvtTypeDesc[row['type']] : '-'}>
        <span>{IvtTypeDesc[row['type']] ? IvtTypeDesc[row['type']] : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Traffic Ratio',
    width: 120,
    dataIndex: 'ratio'
  },
  {
    title: 'Bundle',
    width: 220,
    dataIndex: 'bundle',
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (Array.isArray(list) && list.length && <EllipsisPopover dataSource={list} />) || '-';
    }
  },
  {
    title: 'Modify By',
    width: 120,
    dataIndex: 'op_name'
  },
  {
    title: 'Updated On',
    width: 160,
    dataIndex: 'update_time',
    render: (_: string) => {
      return moment(_).format('YYYY-MM-DD HH:mm:ss');
    }
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: _ => <StatusTag value={_} statusDescMap={{ 1: 'Active', 2: 'Paused' }} />
  },
  {
    title: 'Operation',
    width: 120,
    fixed: 'right',
    dataIndex: 'operate'
  }
];

export const IvtConfigSearchOption: TopBarSearchItem[] = [
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    mode: 'multiple',
    options: []
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: []
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: '',
    mode: 'multiple',
    options: IvtTypeOptions
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions
  }
];

export const DefaultFormData = {
  tnt_id: undefined,
  seller_id: [],
  buyer_id: [],
  bundle: undefined,
  type: 1,
  ratio: 1
};
