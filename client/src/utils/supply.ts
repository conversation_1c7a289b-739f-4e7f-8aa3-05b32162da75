export function trim(str: any) {
  return Boolean(str) === true ? (str + '').replace(/(^\s*)|(\s*$)/g, '') : '';
}

function randomStr(a?: any) {
  if (a) {
    return (a ^ ((Math.random() * 16) >> (a / 4))).toString(16);
  }
  // @ts-ignore
  return ([1e7] + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, randomStr);
}

export function generateMaxCode(obj: any) {
  const domain = trim(obj.domain);
  const token = trim(obj.token);
  const sid = trim(obj.seller_id);
  const app_id = trim(obj.app_id);
  const width = parseInt(obj.width, 10);
  const height = parseInt(obj.height, 10);
  if (token && sid && app_id && width && height && domain) {
    const adDivId = 'dojs' + randomStr();
    const htmlCodes =
      '<script type="text/javascript" src="https://static.rixengine.com/rixjt.js"></script>' +
      '<div id="' +
      adDivId +
      '"></div><script>if (typeof rixengine === "undefined") {loaded=true;window.location="applovin://failLoad";}else {var data = {"endpoint": "https://' +
      domain +
      '/rtb","adDivId": "' +
      adDivId +
      '","sid": "' +
      sid +
      '","token": "' +
      token +
      '","app": {"app_id": "' +
      app_id +
      '","app_bundle_id": "%%BUNDLE%%","app_name": "%%APPNAME_ESC%%"},"adslot": {"adslot_id": "%%PLACEMENTID%%","banner": {"width": ' +
      width +
      ',"height": ' +
      height +
      '}},"user": {"ip": "%%IPADDRESS%%"},"device": {"device_id": [{"id": "%%ADVERTISING_ID_IFA%%"}],"advertising_id": {"id": "%%ADVERTISING_ID_IFA%%"}}};' +
      'rixengine.loadAd(data, function () {console.log("SUCCESS");window.location = "applovin://loaded";}, function () {loaded=true; window.location="applovin://failLoad";})}</script>';
    return htmlCodes;
  }
  return 'params invalid';
}

export function generateDFPCode(obj: any) {
  const token = trim(obj.token);
  const sid = trim(obj.sid);
  const app_id = trim(obj.appkey);
  const adslot_id = trim(obj.adslotid);
  const app_bundle_id = trim(obj.bundle_id);
  const width = parseInt(obj.width, 10);
  const height = parseInt(obj.height, 10);
  const default_adsolt_id = obj.default_adsolt_id;
  const dgpr = obj.dgpr;
  const app_name = obj.appname || '';
  if (token && sid && app_id && adslot_id && app_bundle_id && width && height) {
    const adDivId = 'dojs' + randomStr();
    let htmlCodes =
      '<script type="text/javascript" src="https://xya.mbm.duunion.com/dfpalgorix.js?t=%%CACHEBUSTER%%"></script>' +
      '<div id="' +
      adDivId +
      '"></div><script>';
    if (default_adsolt_id) {
      htmlCodes +=
        'function onErrFn() {' +
        'var googletag = undefined;googletag = googletag || {};googletag.cmd = googletag.cmd || [];' +
        '(function () {var gads = document.createElement("script");gads.type = "text/javascript";gads.onload = gads.onreadystatechange = function () {' +
        'if (gads.readyState && /loaded|complete/.test(gads.readyState)) {gads.onload = gads.onreadystatechange = undefined;}' +
        'var __googletag = document.createElement("script");__googletag.innerHTML = ["googletag.cmd.push(function () { googletag.defineSlot(\'",' +
        '"' +
        default_adsolt_id +
        '",' +
        '"\', [",' +
        width +
        ',' +
        '","' +
        ',' +
        height +
        ',' +
        '"],\'",' +
        '"' +
        adDivId +
        '",' +
        '"\').addService(googletag.pubads());googletag.enableServices();googletag.display(\'",' +
        '"' +
        adDivId +
        '",' +
        '"\');});"' +
        '].join("");document.body.appendChild(__googletag);} gads.src = "https://securepubads.g.doubleclick.net/tag/js/gpt.js";document.body.appendChild(gads);})();' +
        '}';
    } else {
      htmlCodes += 'function onErrFn() {console.log("not ad available")};';
    }
    if (dgpr) {
      htmlCodes +=
        '(function () {var data = {"adDivId": "' +
        adDivId +
        '","sid": "' +
        sid +
        '","token": "' +
        token +
        '","app": {"app_id": "' +
        app_id +
        '","app_bundle_id": "' +
        app_bundle_id +
        '","app_name": encodeURIComponent("' +
        app_name +
        '")},"adslot": {"adslot_id": "' +
        adslot_id +
        '","banner": {"width": ' +
        width +
        ',"height": ' +
        height +
        '}},"user": {"ip": "","ext": {"consent": "${ADDTL_CONSENT}"}},"device": {"device_id": [{"id":  "%%ADVERTISING_IDENTIFIER_PLAIN%%" || "%%PATTERN:ADVERTISING_IDENTIFIER_PLAIN%%"}],' +
        '"advertising_id": {"id":  "%%ADVERTISING_IDENTIFIER_PLAIN%%" || "%%PATTERN:ADVERTISING_IDENTIFIER_PLAIN%%"}},"regs": {"gdpr": "${GDPR}"}};' +
        'if (typeof algorix === "undefined") {onErrFn();}' +
        'else {algorix.loadAd(data, function () {console.log("SUCCESS");}, onErrFn);}})()</script>';
    } else {
      htmlCodes +=
        '(function () {var data = {"adDivId": "' +
        adDivId +
        '","sid": "' +
        sid +
        '","token": "' +
        token +
        '","app": {"app_id": "' +
        app_id +
        '","app_bundle_id": "' +
        app_bundle_id +
        '","app_name": encodeURIComponent("' +
        app_name +
        '")},"adslot": {"adslot_id": "' +
        adslot_id +
        '","banner": {"width": ' +
        width +
        ',"height": ' +
        height +
        '}},"user": {"ip": ""},"device": {"device_id": [{"id":  "%%ADVERTISING_IDENTIFIER_PLAIN%%" || "%%PATTERN:ADVERTISING_IDENTIFIER_PLAIN%%"}],' +
        '"advertising_id": {"id":  "%%ADVERTISING_IDENTIFIER_PLAIN%%" || "%%PATTERN:ADVERTISING_IDENTIFIER_PLAIN%%"}}};' +
        'if (typeof algorix === "undefined") {onErrFn();}' +
        'else {algorix.loadAd(data, function () {console.log("SUCCESS");}, onErrFn);}})()</script>';
    }
    return htmlCodes;
  }
  return 'params invalid';
}

// export function generateMWCode(obj: any) {
//   const token = trim(obj.token);
//   const sid = trim(obj.sid);
//   const app_id = trim(obj.appkey);
//   const adslot_id = trim(obj.adslotid);
//   const domain = trim(obj.pkg);
//   const width = parseInt(obj.width, 10);
//   const height = parseInt(obj.height, 10);
//   const name = trim(obj.appname);
//   if (token && sid && app_id && adslot_id && domain && name && width && height) {
//     const adDivId = 'dojs' + randomStr();
//     const htmlCodes = '<script type="text/javascript" src="https://static.svr-algorix.com/mwalgorix.js"></script>' +
//           '<div id="' +
//           adDivId +
//           '"></div><script>(function () {function onErrFn() {console.log("not ad available")};if (typeof algorix === "undefined") {onErrFn();}else {var data = {"adDivId": "' +
//           adDivId +
//           '","sid": "' +
//           sid +
//           '","token": "' +
//           token +
//           '","site": {"id": "' +
//           app_id +
//           '","domain": "' +
//           domain +
//           '","name": encodeURIComponent("' +
//           name +
//           '")},"adslot": {"adslot_id": "' +
//           adslot_id +
//           '","banner": {"width": ' +
//           width +
//           ',"height": ' +
//           height +
//           '}},"user": {"ip": ""}}};' +
//           'algorix.loadAd(data, function () {console.log("SUCCESS");}, function () {onErrFn();});})()</script>';
//     return htmlCodes;
//   }
//   return 'params invalid';
// }

export function generateMWCode(obj: any) {
  const domain = trim(obj.domain);
  const token = trim(obj.token);
  const sid = trim(obj.seller_id);
  const app_id = trim(obj.app_id);
  const adslot_id = trim(obj.plm_id);
  const pkg = trim(obj.bundle);
  const width = parseInt(obj.width, 10);
  const height = parseInt(obj.height, 10);
  const name = trim(obj.app_name);

  if (token && sid && app_id && adslot_id && pkg && name && width && height && domain) {
    const adDivId = 'dojs' + randomStr();
    const htmlCodes =
      '<script type="text/javascript" src="https://static.rixengine.com/rixmwjt.js"></script>' +
      '<div id="' +
      adDivId +
      '"></div><script>(function () {function onErrFn() {console.log("not ad available")};if (typeof rixengine === "undefined") {onErrFn();}else {var data = {"endpoint": "https://' +
      domain +
      '/rtb","adDivId": "' +
      adDivId +
      '","sid": "' +
      sid +
      '","token": "' +
      token +
      '","site": {"id": "' +
      app_id +
      '","domain": "' +
      pkg +
      '","name": encodeURIComponent("' +
      name +
      '")},"adslot": {"adslot_id": "' +
      adslot_id +
      '","banner": {"width": ' +
      width +
      ',"height": ' +
      height +
      '}},"user": {"ip": ""}}};' +
      'rixengine.loadAd(data, function () {console.log("SUCCESS");}, function () {onErrFn();});})()</script>';
    return htmlCodes;
  }
  return '参数错误';
}
