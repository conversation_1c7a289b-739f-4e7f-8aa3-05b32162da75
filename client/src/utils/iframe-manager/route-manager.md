# 路由管理工具使用指南

## 概述

`RouteManager` 是一个用于管理应用路由变化的独立工具类，提供了完整的路由信息获取、处理和消息发送功能。

## 主要功能

1. **路由信息获取** - 从 UmiJS 的路由配置中获取路由名称和路径
2. **路由变化检测** - 检测路由切换并获取当前和上一个路由信息
3. **消息发送** - 将路由变化信息发送到父应用或其他组件
4. **错误处理** - 完整的错误处理和验证机制

## 基本使用

### 导入工具类

```typescript
import { RouteManager, handleRouteChange } from '@/utils/iframe-manager/route-manager';
import { IframeMessage } from '@/hooks/useIframe/type';
```

### 在 onRouteChange 中使用

```typescript
export function onRouteChange(props: any) {
  // 使用RouteManager处理路由变化
  const routeChangeInfo = RouteManager.handleRouteChange(props);
  
  if (routeChangeInfo) {
    console.log('Route change detected:', routeChangeInfo);
    
    // 发送路由变化消息到父应用
    try {
      iframeManager.sendMessage(IframeMessage.ROUTE_CHANGE, {
        next: routeChangeInfo.next,
        cur: routeChangeInfo.cur,
        timestamp: new Date().getTime()
      });
    } catch (error) {
      console.error('Failed to send route change message:', error);
    }
  }
}
```

## 数据结构

### RouteInfo 接口

```typescript
interface RouteInfo {
  path: string;  // 路由路径
  name: string;  // 路由名称
}
```

### RouteChangeInfo 接口

```typescript
interface RouteChangeInfo {
  next: RouteInfo;  // 将要跳转的路由
  cur: RouteInfo;   // 当前路由
}
```

## 高级功能

### 获取当前路由信息

```typescript
const currentRoute = RouteManager.getCurrentRoute();
if (currentRoute) {
  console.log('Current route:', currentRoute);
}
```

### 获取前一个路由信息

```typescript
const previousRoute = RouteManager.getPreviousRoute();
if (previousRoute) {
  console.log('Previous route:', previousRoute);
}
```

### 验证路由信息

```typescript
const isValid = RouteManager.isValidRoute(routeInfo);
if (isValid) {
  console.log('Route is valid');
}
```

### 格式化路由信息

```typescript
const formatted = RouteManager.formatRouteInfo(routeInfo);
console.log(formatted); // "Route Name (/path/to/route)"
```

### 重置路由状态

```typescript
RouteManager.reset();
```

## 在 React 组件中使用

### 监听路由变化

```typescript
import React, { useEffect } from 'react';
import { useLocation } from '@umijs/max';
import { RouteManager } from '@/utils/iframe-manager/route-manager';

const MyComponent: React.FC = () => {
  const location = useLocation();

  useEffect(() => {
    // 获取当前路由信息
    const currentRoute = RouteManager.getCurrentRoute();
    if (currentRoute) {
      console.log('Current route in component:', currentRoute);
    }
  }, [location]);

  return <div>My Component</div>;
};
```

### 自定义路由处理

```typescript
import React from 'react';
import { RouteManager } from '@/utils/iframe-manager/route-manager';

const CustomRouteHandler: React.FC = () => {
  const handleCustomRouteChange = (props: any) => {
    const routeChangeInfo = RouteManager.handleRouteChange(props);
    
    if (routeChangeInfo) {
      // 自定义处理逻辑
      console.log('Custom route change handling:', routeChangeInfo);
      
      // 可以在这里添加自定义的业务逻辑
      // 例如：发送到不同的服务、记录日志等
    }
  };

  return <div>Custom Route Handler</div>;
};
```

## 消息类型定义

### 在 type.ts 中添加新的消息类型

```typescript
// 在 IframeMessage 命名空间中添加
export const ROUTE_CHANGE = 'route_change';

// 在 TypedMessageMap 接口中添加
[IframeMessage.ROUTE_CHANGE]: { 
  next: { path: string; name: string }; 
  cur: { path: string; name: string }; 
  timestamp: number 
};
```

## 注意事项

1. **路由配置**：确保路由配置中有正确的 `name` 字段
2. **消息发送**：在发送消息前检查 iframeManager 是否已初始化
3. **错误处理**：所有操作都有完整的错误处理机制
4. **性能考虑**：路由变化检测是轻量级的，不会影响应用性能
5. **调试模式**：可以通过控制台查看详细的路由变化日志

## 示例输出

当路由从 `/welcome` 跳转到 `/user/login` 时，会输出类似以下信息：

```javascript
{
  next: { path: "/user/login", name: "login" },
  cur: { path: "/welcome", name: "welcome" }
}
```

## 错误处理

工具类提供了完整的错误处理机制：

```typescript
try {
  const routeChangeInfo = RouteManager.handleRouteChange(props);
  if (routeChangeInfo) {
    // 处理路由变化
  }
} catch (error) {
  console.error('Route change handling failed:', error);
}
```

## 迁移指南

### 从原有代码迁移

**原有代码:**
```typescript
export function onRouteChange(props: any) {
  console.log('Route change:', props);
}
```

**新代码:**
```typescript
export function onRouteChange(props: any) {
  const routeChangeInfo = RouteManager.handleRouteChange(props);
  if (routeChangeInfo) {
    // 处理路由变化
    console.log('Route change:', routeChangeInfo);
  }
}
``` 
