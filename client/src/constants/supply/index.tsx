/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 15:39:24
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-10-23 14:09:42
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { DemandAndSupplyStatusOptions } from '@/constants';

export const PassUrlStatus = {
  Active: 1,
  Paused: 2
};

export const PassUrlStatusOptions = [
  {
    label: 'Active',
    value: 1
  },
  {
    label: 'Pasued',
    value: 2
  }
];
export const RevTrackTypeOptions = [
  {
    label: 'ADM',
    value: 1
  },
  {
    label: 'bURL',
    value: 2
  }
];

export const RevTrackType = {
  ADM: 1,
  bURL: 2
};

export const RevTrackTypeDesc: API.NumberType = {
  1: 'ADM',
  2: 'bURL'
};
export const RelationshipOptions = [
  {
    label: 'Direct',
    value: 1
  },
  {
    label: 'Reseller',
    value: 2
  }
];

export const ChannelOptions = [
  {
    label: 'App',
    value: 1
  },
  {
    label: 'Site',
    value: 2
  }
];

export const DeviceOptions = [
  {
    label: 'Mobile',
    value: 1
  },
  {
    label: 'PC',
    value: 2
  },
  {
    label: 'CTV',
    value: 3
  },
  {
    label: 'Others',
    value: 4
  }
];

export const ChannelType: API.NumberType = {
  1: 'App',
  2: 'Site'
};
export const ChannelTypeMap: API.StringType = {
  App: 1,
  Site: 2
};
export const DeviceType: API.StringType = {
  1: 'Mobile',
  2: 'PC',
  3: 'CTV',
  4: 'Others'
};
export const RelationshipType: API.StringType = {
  1: 'Direct',
  2: 'Reseller'
};

export const PublishType: API.StringType = {
  RTB: 1,
  SDK: 5,
  DFP: 6,
  MW: 7,
  MAX: 9
};

export const PublishTypeToLabel: API.StringType = {
  1: 'RTB',
  5: 'SDK',
  6: 'DFP',
  7: 'MW',
  9: 'MAX'
};

export const SupplyBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Supply',
    icon: 'rix-supply'
  },
  {
    name: 'Publisher List'
  }
];

export const EndpointBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Supply',
    url: '/supply/publisher',
    icon: 'rix-supply'
  },
  {
    name: 'Publisher List',
    url: '/supply/publisher'
  },
  {
    name: 'Endpoint'
  }
];

export const AccountBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Supply',
    url: '/supply/publisher',
    icon: 'rix-supply'
  },
  {
    name: 'Publisher List',
    url: '/supply/publisher'
  },
  {
    name: 'Account'
  }
];

export const SupplySearchOption: TopBarSearchItem[] = [
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    mode: 'multiple',
    options: []
  },
  {
    name: 'Integration Type',
    type: 'select',
    key: 'integration_type',
    value: '',
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Relationship',
    type: 'select',
    key: 'relationship',
    value: '',
    options: RelationshipOptions,
    mode: 'multiple'
  },
  {
    name: 'Device',
    type: 'select',
    key: 'device_type',
    value: '',
    options: DeviceOptions,
    mode: 'multiple'
  },
  {
    name: 'Channel',
    type: 'select',
    key: 'channel_type',
    value: '',
    options: ChannelOptions,
    mode: 'multiple'
  },
  {
    // Revenue Tracking Type 筛选项
    name: 'Revenue Tracking',
    type: 'select',
    key: 'rev_track_type',
    value: [],
    options: RevTrackTypeOptions,
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    options: DemandAndSupplyStatusOptions,
    mode: 'multiple'
  }
];

export const DetailBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Supply',
    icon: 'rix-supply',
    url: '/supply/publisher'
  },
  {
    name: 'Publisher List',
    url: '/supply/publisher'
  },
  {
    name: 'Detail'
  }
];
