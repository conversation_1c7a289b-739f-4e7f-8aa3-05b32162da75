/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-13 18:48:29
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-21 11:56:09
 * @Description:
 */

import { UserType } from '@/constants';
import { getTzTimeSql } from '@/constants/time-zone';
import { queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { BoardAPI } from '@/types/ai-board';
import { getConfig, md5 } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGetBQTableName,
  concatSQLFragments,
  LabelGenerationParams
} from '@rixfe/rix-tools';
import moment from 'moment-timezone';

const getNewBQTable = async (
  start_date: string,
  end_date: string,
  cur_time_zone: string,
  columns: string[] = []
) => {
  await updateBQConfigAdapter();
  return await adaptGetBQTableName(
    {
      split_time: 1,
      start_date,
      end_date,
      tz_start_date: start_date,
      tz_end_date: end_date,
      tnt_id: 0,
      order: '',
      order_key: [],
      cur_user_id: 0,
      cur_role_id: 0,
      cur_time_zone,
      columns
    },
    { api_url: '' }
  );
};

const { BoardCaCheTime } = getConfig();

// !注释都不删除，后续可能会用到
class BoardModel implements BoardAPI.BoardModel {
  async getNewOverview(
    sql: { today: string; yesterday: string },
    label: LabelGenerationParams
  ) {
    const { today, yesterday } = sql;
    const [today_data, yesterday_data] = await Promise.all([
      queryStackOverflow(today, {
        cacheTime: BoardCaCheTime * 3,
        ...label
      }),
      queryStackOverflow(yesterday, {
        cacheTime: BoardCaCheTime * 3,
        ...label
      })
    ]);
    return [today_data, yesterday_data];
  }

  async getTodayHours(tnt_id: number, label: LabelGenerationParams) {
    const date = moment()
      .tz('Etc/UTC')
      .subtract(1, 'days')
      .format('YYYY-MM-DD');

    // 不能使用 billing 表
    const tableName = await getNewBQTable(date, date, 'Etc/UTC', [
      'hour',
      'ad_format'
    ]);

    const sql = concatSQLFragments({
      select: [
        `FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', max(day_hour)) as last_hour`
      ],
      from: tableName,
      where: [`date >= '${date}'`, `tnt_id = ${tnt_id}`],
      limit: '1'
    });
    const data = await queryStackOverflow(sql, { ...label });

    let last_hour = '';
    if (Array.isArray(data) && data.length > 0) {
      last_hour = data[0].last_hour;
    }
    return Promise.resolve(last_hour);
  }

  async getTopCountry(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams,
    limit?: number
  ): Promise<BoardAPI.TopCountryAdFormatItem[]> {
    const curUtcMoment = moment.tz(cur_hour, 'Etc/Utc');
    const date = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .subtract(1, 'days')
      .format('YYYY-MM-DD');

    const tableName = await getNewBQTable(date, date, cur_time_zone, [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [
        `country`,
        `ad_format`,
        `concat(ad_width,'*',ad_height) as ad_size`,
        `sum(buyer_net_revenue) as revenue`,
        `sum(seller_net_revenue) as sl_revenue`
      ],
      from: tableName,
      where: [`date = '${date}'`, `tnt_id = ${tnt_id}`, `ad_format != 0`],
      groupBy: [`country`, `ad_format`, `ad_size`],
      having: [`revenue > 0`],
      orderBy: [`revenue desc`],
      limit: limit ? `${limit}` : ''
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getSellerDemand(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams
  ): Promise<BoardAPI.SupplyDemandItem[]> {
    const curUtcMoment = moment.tz(cur_hour, 'Etc/Utc');
    const date = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .subtract(1, 'days')
      .format('YYYY-MM-DD');

    const tableName = await getNewBQTable(date, date, cur_time_zone, [
      // 不涉及 request，可以直接查 billing 表
      'seller_id',
      'buyer_id'
      // 'country',
      // 'ad_format'
    ]);

    const sql = concatSQLFragments({
      select: [
        `seller_id`,
        `buyer_id`,
        `country`,
        `ad_format`,
        `sum(buyer_net_revenue) as revenue`,
        `sum(seller_net_revenue) as sl_revenue`
      ],
      from: tableName,
      where: [`date = '${date}'`, `tnt_id = ${tnt_id}`, `ad_format != 0`],
      groupBy: [`seller_id`, `buyer_id`, `country`, `ad_format`],
      having: [`revenue > 0`],
      orderBy: [`revenue desc`]
    });
    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getTopAdFormatEcpmAndEcpr(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams
  ): Promise<BoardAPI.EcpmAndEcprItem[]> {
    const curUtcMoment = moment.tz(cur_hour, 'Etc/Utc');
    const start_date = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .subtract(7, 'days')
      .format('YYYY-MM-DD');
    const end_date = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .subtract(1, 'days')
      .format('YYYY-MM-DD');

    const tableName = await getNewBQTable(start_date, end_date, cur_time_zone, [
      'ad_format'
    ]);

    const sql = concatSQLFragments({
      select: [
        `FORMAT_TIMESTAMP('%Y-%m-%d', date) as date`,
        `ad_format`,
        `sum(buyer_net_revenue) as revenue`,
        `case when sum(impression) = 0 then 0 else ((sum(buyer_net_revenue) * 1000) / sum(impression)) end as ecpm`,
        `case when sum(request) = 0 then 0 else ((sum(buyer_net_revenue) * 1000000) / sum(request)) end as ecpr`
      ],
      from: tableName,
      where: [
        `date >= '${start_date}'`,
        `date <= '${end_date}'`,
        `tnt_id = ${tnt_id}`,
        `ad_format != 0`
      ],
      groupBy: [`ad_format`, `date`],
      orderBy: [`date asc`, `revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getBudgetAndTraffic({
    cur_hour,
    cur_time_zone,
    tnt_id,
    labels
  }: {
    cur_hour: string;
    cur_time_zone: string;
    tnt_id: number;
    labels: LabelGenerationParams;
  }): Promise<BoardAPI.BudgetAndTrafficItem[]> {
    const curUtcMoment = moment.tz(cur_hour, 'Etc/Utc');
    const date = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .subtract(1, 'days')
      .format('YYYY-MM-DD');

    const tableName = await getNewBQTable(date, date, cur_time_zone, [
      'ad_format',
      'country',
      'buyer_id'
    ]);

    const sql = concatSQLFragments({
      select: [
        `ad_format`,
        `country`,
        `buyer_id`,
        `round(sum(buyer_net_revenue), 2) as revenue`,
        `(case when sum(request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 / sum(request) as numeric), 2) end) as ecpr`
      ],
      from: tableName,
      where: [`date = '${date}'`, `ad_format != 0`, `tnt_id = ${tnt_id}`],
      groupBy: [`ad_format`, `country`, `buyer_id`],
      orderBy: [`revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...labels
    });
  }

  async trafficRequest(params: BoardAPI.AddNotificationParams) {
    const {
      title,
      content,
      rule_id,
      tnt_ids,
      user_id,
      ext_1 = 1,
      ext_2 = 0,
      tnt_id,
      msg,
      key
    } = params;
    if (tnt_ids.length) {
      return !!(await dbUtils.execWaterfallTransaction([
        // 插入发送信息
        {
          sql: 'insert into alert_msg_log(title, content, rule_id, tnt_id) values ?',
          values: [[[title, content, rule_id, tnt_id]]]
        },
        // 插入发送记录
        {
          sql: 'insert into sent_msg_log(type, content, ext_1, ext_2, tnt_id, op_id, mixed_key) values ?',
          values: [[[1, msg, ext_1, ext_2, tnt_id, user_id, key]]]
        }
      ]));
    }
    return true;
  }

  // 获取发送traff req记录
  async getTffReqLog(tnt_id: number, cur_time_zone: string) {
    const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];
    const sql = `
      select
        smt.id,
        smt.content,
        smt.ext_1,
        smt.ext_2,  
        smt.mixed_key,
        smt.op_id,
        (case when u.type in (${SystemType.join(
          ','
        )}) then "System" else u.account_name end) as op_name,
        ${getTzTimeSql('smt.create_time', cur_time_zone)} as create_time
      from (select * from sent_msg_log where tnt_id=? and type=1) as smt
      left join user as u on smt.op_id=u.user_id
      order by create_time desc
    `;
    return await dbUtils.query(sql, [tnt_id]);
  }

  async isExistLog(params: BoardAPI.isExistLogParams) {
    const { tnt_id, buyer_ids, ad_format, country } = params;
    const date = moment().tz('Etc/UTC').format('YYYYMMDD');
    const key = md5(
      `${tnt_id}_${buyer_ids.join(',')}_${ad_format}_${country}_${date}`
    );
    const sql = `select 1 from sent_msg_log where mixed_key=? and tnt_id = ? limit 1`;
    const data = await dbUtils.query(sql, [key, tnt_id]);
    return data && data.length;
  }
}

export const boardModel = new BoardModel();
