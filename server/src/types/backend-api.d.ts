import { Context } from 'koa';
import { DashboardAPI } from './dashboard';
export declare namespace BackendAPI {
  type FullReportPaeams = {
    start: number;
    end: number;
    start_date: string;
    end_date: string;
    split_time: number;
    order_key: string[];
    order: string;
    dimensions: string[];
    columns: string[];
    metrics: string[];
    adv_id: number[];
    pub_id: number[];
    ad_format: number[];
    bundle: string;
    country: string[];
    unit_id: string;
    timezone: string;
    tnt_id: number;
  };

  interface FullReportCtrl {
    getFullReport(ctx: Context): void;
  }

  interface FullReportServices {
    getFullReport(params: DashboardAPI.DashboardParams, api_url: string): Promise<{
      total: number;
      data: DashboardAPI.DashboardListItem[];
    }>;
  }

  interface FullReportModel {
    getFullReport(
      params: DashboardAPI.GetListParams
    ): Promise<DashboardAPI.DashboardListItem[]>;
  }
}

export declare namespace BackendCommonAPI {
  type ValidateTokenParams = {
    user_id: number;
    token: string;
    cs_domain: string;
  };

  interface BackendCommonCtrl {}

  interface BackendCommonServices {}

  interface BackendCommonModel {
    validateApiToken(
      params: ValidateTokenParams
    ): Promise<{ user_id: number; tnt_id: number }[]>;
  }
}
