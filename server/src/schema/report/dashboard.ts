/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-07-07 17:50:03
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-07 16:01:09
 * @Description:
 */
import Jo<PERSON> from 'joi';
import {
  AllDashboardDimension,
  AllDashboardMetrics
} from '@/constants/report/dashboard';

import { validDateParams } from './utils';

// FullReporting parameter verification
const NumReg = /^\d{8}$/;
const metricsValidation = Joi.array()
  .min(1)
  .items(
    Joi.string()
      .custom((value, helpers) => {
        // 防止 SQL 注入攻击
        const isRight = AllDashboardMetrics.includes(value);
        if (!isRight) {
          return helpers.error('metricsValidation invalid');
        }
        return value;
      })
      .message('The mertics is invalid')
  )
  .required();

const dimensionValidation = Joi.array().items(
  Joi.string()
    .custom((value, helpers) => {
      const isRight = AllDashboardDimension.includes(value);
      if (!isRight) {
        return helpers.error('dimensionValidation invalid');
      }
      return value;
    })
    .message('The dimension is invalid')
);

export const bundleValidation = Joi.string().custom((value, helpers) => {
  const buldes = value.split(',');
  // const reg = /^[a-zA-Z0-9_.]+$/;
  const isRight = buldes.length <= 20000;
  if (!isRight) {
    return helpers.error('number.maximum', { limit: 20000 });
  }
  return value;
});

const dashboardScheme = {
  region: Joi.array().items(
    Joi.string().valid('USE', 'APAC', 'EUW').required()
  ),
  country: Joi.array().items(Joi.string().max(3).required()),
  platform: Joi.array().items(Joi.string().pattern(/^\d+$/).required()),
  ad_size: Joi.array().items(Joi.string().pattern(/^\d+$/).required()),
  ad_format: Joi.array().items(Joi.string().pattern(/^\d+$/).required()),
  placement_id: Joi.string().max(500),
  app_bundle_id: bundleValidation.messages({
    'number.maximum': 'Maximum inquiries of {#limit} bundles'
  }),
  buyer_id: Joi.alternatives().try(
    Joi.number().integer(), // 用于上游报表
    Joi.array().items(Joi.number().integer().required())
  ),
  seller_id: Joi.alternatives().try(
    Joi.number().integer(), // 用于下游报表
    Joi.array().items(Joi.number().integer().required())
  ),
  metrics: metricsValidation,
  columns: dimensionValidation,
  start_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  end_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天

  start: Joi.number().integer().min(0).required(),
  end: Joi.number()
    .integer()
    .min(1)
    .required()
    .when('start', {
      // 必须大于start
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .max(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: (relation) => relation + 500
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    })
    .when('start', {
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .min(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: (relation) => relation + 1
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    }),
  order: Joi.string().valid('desc', 'asc'),
  instl: Joi.array().items(Joi.number().integer().valid(0, 1)),
  inventory: Joi.array().items(Joi.number().integer().valid(0, 1, 2)),
  device_type: Joi.array().items(Joi.number().integer().valid(0, 1, 2, 3, 4, 5, 6, 7)),
  hour: Joi.array().items(Joi.number().integer().min(0).max(23)),
  partner_id: Joi.array().items(
    Joi.object({
      seller_partner_id: Joi.array().items(Joi.number()),
      buyer_partner_id: Joi.array().items(Joi.number())
    })
  )
};

const customDashboardVaild = (value: any, helpers: any) => {
  const {
    start_date, end_date, buyer_id, seller_id, columns, tnt_id
  } = value;
  const isBigDimension = columns?.some((item: string) => ['app_bundle_id', 'placement_id'].includes(item));
  if (isBigDimension) {
    const { isVaild, message } = validDateParams(
      start_date,
      end_date,
      tnt_id === 1053 ? 15 : 7
    );
    return isVaild ? value : helpers.error(message);
  }
  const { isVaild, message } = validDateParams(start_date, end_date, 15);
  if (typeof buyer_id === 'number' || typeof seller_id === 'number') {
    const { isVaild, message } = validDateParams(start_date, end_date, 31);
    return isVaild ? value : helpers.error(message);
  }
  return isVaild ? value : helpers.error(message);
};
export const DashboardScheme = Joi.object({
  ...dashboardScheme
}).custom(customDashboardVaild);
export const DownloadDashboardScheme = Joi.object({
  ...dashboardScheme,
  start: Joi.number(),
  end: Joi.number()
}).custom(customDashboardVaild);

// Pub and Adv report parameter verification
const customDashboardVaildForPubAndAdv: Joi.CustomValidator = (value, helpers) => {
  const { start_date, end_date, columns } = value;
  const isBigDimension = columns?.some((item: string) => ['app_bundle_id', 'placement_id'].includes(item));
  const limit = isBigDimension ? 3 : 31;

  // todo 这种自定义的方式后续改成全局配置message，采用 helpers.error 的方式 处理
  const { isVaild, message } = validDateParams(start_date, end_date, limit);
  if (!isVaild && message) {
    const errorMessages: Record<string, string>[] = [
      { custom: 'System error', code: 'system_error' },
      { custom: 'Wrong date format', code: 'invalid_date_format' },
      { custom: 'The end_date must be larger or equal to start_date', code: 'invalid_date_range' },
      { custom: `The date range must be less than ${limit} days`, code: 'date_range_exceeded' },
      { custom: 'The end_date must be less than or equal to today', code: 'future_date_not_allowed' }
    ];

    const errorMessage = errorMessages.find((item) => item.code === message) || errorMessages[0];

    return helpers.message(errorMessage);
  }
  return value;
};
export const PubDashbaordScheme = Joi.object({ ...dashboardScheme }).custom(
  customDashboardVaildForPubAndAdv
);

export const AdvDashbaordScheme = Joi.object({ ...dashboardScheme }).custom(
  customDashboardVaildForPubAndAdv
);

export const DownloadPubAndAdvDashboardScheme = Joi.object({
  ...dashboardScheme,
  start: Joi.number(),
  end: Joi.number()
}).custom(customDashboardVaildForPubAndAdv);

// compare chart
export const WelComeDashboardScheme = Joi.object({
  ...dashboardScheme,
  start_date: Joi.string().pattern(NumReg), // 8位的数字 默认当天
  end_date: Joi.string().pattern(NumReg), // 8位的数字 默认当天
  dates: Joi.array()
    .items(
      Joi.object({
        start: Joi.string().pattern(/^\d{4}-\d{2}-\d{2} \d{2}:00:00$/),
        end: Joi.string().pattern(/^\d{4}-\d{2}-\d{2} \d{2}:00:00$/)
      })
    )
    .max(2)
});

export const UpdateMsgStatusScheme = Joi.object({
  // 多选和单选
  msg_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer().required())
  )
});
