import { ProfitType } from '@/constants/strategy/profit';
import { FormInstance } from 'antd';
import { useCallback, useState } from 'react';

type UseProfitDrawerStateProps = {
  formRef: React.RefObject<FormInstance<any>>;
  currentTab: string;
};

export const useProfitDrawerState = ({ formRef, currentTab }: UseProfitDrawerStateProps) => {
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  // 打开抽屉
  const openDrawer = useCallback(() => {
    setVisible(true);
  }, []);

  // 关闭抽屉
  const closeDrawer = useCallback(() => {
    setVisible(false);
    setIsEdit(false);
  }, []);

  // 设置为编辑模式并打开抽屉
  const openEditDrawer = useCallback((params: any) => {
    setIsEdit(true);
    openDrawer();
    formRef.current?.setFieldsValue(params);
  }, [formRef, openDrawer]);

  // 设置为创建模式并打开抽屉
  const openCreateDrawer = useCallback((params: any) => {
    setIsEdit(false);
    openDrawer();
    
    const { type: paramsType } = params;

    // 处理 DemandBundle 类型
    if (paramsType === ProfitType.DemandBundle) {
      const bundleFormValues = {
        type: ProfitType.DemandBundle,
        profit_ratio: 30,
        buyer_id: [],
        seller_id: [],
        bundle: []
      };
      formRef.current?.setFieldsValue(bundleFormValues);
      return;
    }

    // 处理其他类型
    const { type, seller_id, buyer_id, ...rest } = params as any;
    const commonFormValues = {
      ...rest,
      type: ProfitType['Seller-Demand'],
      profit_ratio: 30,
      seller_id: type === ProfitType.Seller ? seller_id : [],
      buyer_id: type === ProfitType.Demand ? buyer_id : []
    };
    formRef.current?.setFieldsValue(commonFormValues);
  }, [formRef, openDrawer]);

  return {
    drawerVisible: visible,
    isDrawerEdit: isEdit,
    openEditDrawer,
    openCreateDrawer,
    closeDrawer
  };
}; 
