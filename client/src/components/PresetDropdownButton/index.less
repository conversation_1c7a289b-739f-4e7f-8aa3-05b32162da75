.preset-dropdown-button {
  width: fit-content;
  margin-right: 12px;

  :global(.ant-btn) {
    margin-right: 0 !important;

    &:last-child {
      z-index: 10 !important;
      &::after {
        background-color: #f5f5f5 !important;
      }
    }
  }
}

.dropdown {
  width: min(240px, 100%);
  max-height: calc(100vh - 300px);

  .menuItem {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
    padding: 4px 8px;
    border-radius: 8px;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s;

    display: flex;
    align-items: center;

    & > div {
      flex: 1;
      display: inline-block;
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    & > span:hover {
      color: red;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: #e6e8eb;
    }

    &.disabled {
      background: #f5f5f5;
      cursor: not-allowed;
    }

    &.selected {
      background: var(--primary-3);

      & > span:first-child {
        color: var(--primary-color);
        font-weight: 500;
      }

      &:hover {
        background: var(--primary-3);
      }
    }
  }

  .container {
    padding: 8px 4px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

    max-height: calc(100vh - 300px);
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 4px;
    }
  }
}
