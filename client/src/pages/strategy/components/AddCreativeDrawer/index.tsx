/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-20 15:50:44
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-13 11:42:09
 * @Description:
 */
import { useModel } from 'umi';
import React, { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import BundleTableEdit from '@/components/BundleTableEdit';
import NormalInput from '@/components/Input/NormalInput';
import { fetchData, handleFilterSelect } from '@/utils';

import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import { BlockTypeOptions, IsAllMap } from '@/constants/strategy/creative';

import { addCreative, updateCreative } from '@/services/api';

type AddCreativeProps = {
  item: StrategyAPI.CreativeListItem | undefined;
  visible: boolean;
  isEdit: boolean;
  demandList: DemandAPI.DemandListItem[];
  supplyList: SupplyAPI.SupplyListItem[];
  handleClose: () => void;
  reload: () => void;
};

const DefaultFormData = {
  buyer_id: undefined,
  seller_id: undefined,
  type: undefined,
  content: undefined,
  status: 1,
  remark: undefined
};
const AddCreativeDrawer: React.FC<AddCreativeProps> = ({
  item,
  visible,
  isEdit,
  demandList,
  supplyList,
  handleClose,
  reload
}) => {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [isAll, setIsAll] = useState(IsAllMap.DEFAULT);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      return;
    }
    if (isEdit && item) {
      const content = (item!.content as string).split(',');
      form.setFieldsValue({ ...item, content });
    }
  }, [isEdit, visible]);
  const handleConfirm = () => {
    form.submit();
  };
  const onSuccess = () => {
    message.success('success');
    handleClose();
    reload();
  };
  const handleFinish = (values: StrategyAPI.CreativeListItem) => {
    let content = values.content;
    if (typeof content === 'string') {
      content = (content as string).split(',');
    }
    const params = {
      ...values,
      content,
      op_id: initialState?.currentUser?.user_id,
      id: isEdit ? item?.id : undefined,
      ori_data: item || {}
    };
    isEdit
      ? fetchData({ request: updateCreative, params, onSuccess, setLoading: setIsLoading })
      : fetchData({ request: addCreative, params, onSuccess, setLoading: setIsLoading });
  };
  const onCancel = () => {
    handleClose();
  };

  const handleValueChange = (changeValue: any) => {
    const { seller_id } = changeValue;
    if (!seller_id?.length) setIsAll(IsAllMap.DEFAULT);
    if (seller_id?.length) {
      const isAll = seller_id.includes(0);
      setIsAll(isAll ? IsAllMap.YES : IsAllMap.NO);
    }
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Block` : `Create Block`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      maskClosable={true}
      loading={isLoading}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
      >
        <Form.Item
          name="buyer_id"
          label="Advertiser:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Advertiser'
            }
          ]}
        >
          <NormalSelect
            placeholder="Please Select Advertiser"
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
          >
            <NormalSelect.Option value={0} key={-1}>
              All Advertisers
            </NormalSelect.Option>
            {demandList &&
              demandList.map((item: DemandAPI.DemandListItem, index: number) => {
                return (
                  <NormalSelect.Option value={item.buyer_id} key={index}>
                    {item.buyer_name}({item.buyer_id})
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>
        <Form.Item
          name="seller_id"
          label="Publisher:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Publisher'
            }
          ]}
        >
          <NormalSelect
            placeholder="Please Select Publisher"
            showSearch
            optionFilterProp="children"
            filterOption={handleFilterSelect}
            mode={isEdit ? undefined : 'multiple'}
          >
            <NormalSelect.Option value={0} key={-1} disabled={isAll === IsAllMap.NO}>
              All Publishers
            </NormalSelect.Option>
            {supplyList &&
              supplyList.map((item: SupplyAPI.SupplyListItem, index: number) => {
                return (
                  <NormalSelect.Option
                    value={item.seller_id}
                    key={index}
                    disabled={isAll === IsAllMap.YES || item.status === DemandAndSupplyStatusMap.Testing}
                  >
                    {item.seller_name}({item.seller_id})
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>
        <Form.Item
          name="type"
          label="Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Type'
            }
          ]}
        >
          <NormalSelect options={BlockTypeOptions} placeholder="Please Select Type" />
        </Form.Item>
        <Form.Item
          label="Content"
          name="content"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Input Content'
            }
          ]}
        >
          <BundleTableEdit
            editTitle="Edit Content"
            editTips="Enter the content(one per line)"
            deleteAllTitle="Delete All Content"
            deleteAllTips="Are you sure to delete all content?"
            open={visible}
            contentMaxHeight={isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'}
            editSingle={true}
          />
        </Form.Item>
        <Form.Item
          label="Remark"
          name="remark"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              message: 'Please Input Remark'
            }
          ]}
        >
          <NormalInput placeholder='Please Input Remark'/>
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddCreativeDrawer;
