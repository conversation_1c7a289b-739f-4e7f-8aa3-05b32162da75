/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-22 19:41:09
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 20:06:47
 * @Description:
 */
import Jo<PERSON> from 'joi';
const addFloorSchema = {
  // 1 for adformat + country, 2 for supply + adformat + country, 3 for supply-placement + country, 4 for supply + demand + adformat + country, 5 for supply-placement + demand + country, 99 for supply-placement + country
  type: Joi.number().integer().valid(1, 2, 3, 4, 5, 99),
  ad_format: Joi.number().integer().valid(1, 2, 3, 4),
  country: Joi.alternatives().try(
    Joi.array().items(Joi.string().max(3)),
    Joi.string().max(3)
  ),
  op_id: Joi.number().integer(),
  bid_floor: Joi.number(),
  plm_id: Joi.alternatives().try(
    Joi.array().items(Joi.number().integer()),
    Joi.number().integer()
  )
};

export const AddFloorSchema = Joi.object(addFloorSchema);

export const UpdateFloorSchema = Joi.object({
  ...addFloorSchema,
  id: Joi.number().integer(),
  status: Joi.number().integer().valid(1, 2)
});
