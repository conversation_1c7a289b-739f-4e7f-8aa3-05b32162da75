/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-08-30 17:18:19
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 18:06:17
 * @Description:
 */
import styles from './index.less';
import { Line } from '@ant-design/charts';

import { Spin } from 'antd';

import CusLoadingIcon from '@/components/LoadingIcon';
import RixEngineFont from '@/components/RixEngineFont';
import moment from 'moment-timezone';

type borokenLineProps = {
  dataSource: API.BackResult<DashboardAPI.DashboardListItem>;
  loading: boolean;
  metric: string;
  appearAnimation?: boolean;
};

const BrokenLine = ({ dataSource, loading, metric, appearAnimation }: borokenLineProps) => {
  // 分组
  const groupBy = (array: any[], key: string) => {
    const grouped: any = {};
    array.forEach(item => {
      const keyValue = item[key];
      if (!grouped[keyValue]) {
        grouped[keyValue] = [];
      }
      grouped[keyValue].push(item);
    });

    return Object.values<any>(grouped);
  };

  // 创建图表数据
  const createData = (source: any[], hours: string[], seriesField: string, yField: string) => {
    if (!source || !Array.isArray(source)) return [];
    const sourceData = source.map(item => {
      let day = '';
      let hour = '';
      for (let key in item) {
        if (key === 'date') {
          day = moment(item[key]).format('YYYY-MM-DD');
          hour = moment(item[key]).format('HH:mm');
          break;
        }
      }
      return {
        ...item,
        day,
        hour
      };
    });
    const seriesFieldData = groupBy(sourceData, seriesField);
    seriesFieldData.forEach((item: any, index: number) => {
      if (item.length !== hours.length) {
        hours.forEach(hour => {
          const hour_index = item.findIndex((i: any) => i.hour === hour);
          if (hour_index === -1) {
            item.push({
              [seriesField]: item[0][seriesField],
              hour: hour,
              [yField]: 0
            });
          }
        });
      }

      item.sort((a: any, b: any) => {
        return +a.hour.slice(0, 2) - b.hour.slice(0, 2);
      });
      item.forEach((i: any) => {
        for (let key in i) {
          if (typeof i[key] === 'string' && key !== 'day' && key !== 'hour') {
            i[key] = +i[key];
          }
        }
      });
    });

    const data = seriesFieldData.flat();

    return data;
  };

  const createHours = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(`${i < 10 ? '0' + i : i}:00`);
    }
    return hours;
  };
  const yField = metric;
  const hours = createHours();
  const data = createData(dataSource.data, hours, 'day', yField);

  const config = {
    data,
    theme: {
      styleSheet: {
        backgroundColor: '#fff'
      }
    },
    xField: 'hour',
    yField: yField,
    seriesField: 'day',
    height: 300,
    smooth: true,
    appendPadding: 16,
    yAxis: {
      label: {
        formatter: (v: any) => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, s => `${s},`)
      },
      grid: {
        line: {
          style: {
            stroke: '#ccc',
            opacity: 0.3
          }
        }
      }
    },
    xAxis: {},
    animation: !appearAnimation
      ? false
      : {
          appear: {
            animation: 'wave-in',
            duration: 3000
          }
        },
    color: ['#1979C9', '#D62A0D', '#FAA219'],
    legend: {
      offsetX: 16
    }
  };

  return (
    <Spin spinning={loading} indicator={<CusLoadingIcon />}>
      <div style={{ width: '100%', borderRadius: '0 0 6px 6px', overflow: 'hidden' }}>
        {!dataSource || !dataSource.data || !dataSource.data.length ? (
          <div className={styles['content-empty']}>
            <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
            <span>No Data</span>
          </div>
        ) : (
          <Line {...config} />
        )}
      </div>
    </Spin>
  );
};
export default BrokenLine;
