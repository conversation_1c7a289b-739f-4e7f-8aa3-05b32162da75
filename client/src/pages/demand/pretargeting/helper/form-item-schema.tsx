import BundleTableEdit from '@/components/BundleTableEdit';
import AllSelectSearch from '@/components/Input/AllSelectSearch';
import InputNumber from '@/components/Input/InputNumber';
import Input from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { MobileOs, Platform } from '@/constants/demand/pretargeting';
import { CountryOptions } from '@/constants/global-mapping/country';
import { DeviceBrandOptions } from '@/constants/global-mapping/device-brand';
import PretargetCheckbox from '@/pages/demand/pretargeting/components/PretargetCheckbox';
import { isValidBundle, isValidDomain } from '@/utils';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Form, Tooltip } from 'antd';
import type { Rule } from 'antd/lib/form';
import React from 'react';
import { LevelConfigMapGroup } from './level-mapping';

// 维护 表单项的 schema(和 ant form item 的入参保持一致)，然后用于生成 form item 项

// 定义 LevelComponent 组件
export type LevelComponentProps = {
  levelName?: string;
  levelOptions?: { label: string; value: number }[];
  levelLabel?: string;
};

export const LevelComponent: React.FC<LevelComponentProps> = ({ levelName, levelOptions, levelLabel }) => {
  // 如果 levelName 为空，则不显示
  if (!levelName) {
    return null;
  }

  // 如果 levelOptions 为空，把 form item 隐藏，NormalRadio 换成 InputNumber
  if (!levelOptions || levelOptions.length === 0) {
    return (
      <>
        <span style={{ color: '#a9a9a9' }}>{levelLabel}</span>
        <Form.Item name={levelName} hidden>
          <InputNumber />
        </Form.Item>
      </>
    );
  }

  // form item 的形式
  return (
    <Form.Item label={levelLabel} name={levelName} noStyle>
      <NormalRadio options={levelOptions} size="small" />
    </Form.Item>
  );
};

// 定义表单项配置的数据结构
export type PretargetFormItemSchema = {
  label: string; // 表单项标签
  key: string; // 表单项唯一标识
  name: string; // 表单字段名
  tooltip?: string; // 提示信息
  rules?: Rule[]; // 验证规则
  dependencies?: string[]; // 依赖项
  children: React.ReactNode; // 表单控件组件
  levelComp?: React.ReactElement; // 级别控制组件
  defaultValue?: {
    // 默认值配置
    value?: string | number | boolean | string[] | number[] | boolean[]; // 字段默认值
    level?: number; // 级别默认值
  };
};

type Option = {
  label: string;
  value: string;
};

// 定义需要传入的 options
export type OptionsSchema = {
  supplyOptions: Option[];
  adSizeOptions: Option[];
};

export const generatePretargetFormItemSchemas = (options: OptionsSchema): PretargetFormItemSchema[] => {
  return [
    {
      label: 'Campaign Name',
      key: 'campaign_name',
      name: 'campaign_name',
      rules: [{ required: true, message: 'Please Input Campaign  Name' }],
      children: <Input placeholder="Please Input Campaign Name" />
    },
    {
      label: LevelConfigMapGroup.seller.label,
      key: LevelConfigMapGroup.seller.key,
      name: LevelConfigMapGroup.seller.name,
      children: (
        <Select placeholder="Please Select Publisher" options={options.supplyOptions} mode="multiple" showSearch />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.seller.levelOptions} />,
      defaultValue: {
        // allow
        level: LevelConfigMapGroup.seller.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.country.label,
      key: LevelConfigMapGroup.country.key,
      name: LevelConfigMapGroup.country.name,
      children: <Select placeholder="Please Select Country" options={CountryOptions} mode="multiple" showSearch />,
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.country.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.country.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.category.label,
      key: LevelConfigMapGroup.category.key,
      name: LevelConfigMapGroup.category.name,
      children: (
        <Select
          placeholder="Please Select Category"
          options={Object.entries(demandCampaign.Category).map(([key, value]) => {
            return {
              label: value,
              value: key
            };
          })}
          mode="multiple"
          showSearch
        />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.category.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.category.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.network.label,
      key: LevelConfigMapGroup.network.key,
      name: LevelConfigMapGroup.network.name,
      children: (
        <Select
          placeholder="Please Select Network"
          options={Object.entries(demandCampaign.Network).map(([key, value]) => {
            return {
              label: value,
              value: key
            };
          })}
          mode="multiple"
          showSearch
        />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.network.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.network.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.time_slot.label,
      key: LevelConfigMapGroup.time_slot.key,
      name: LevelConfigMapGroup.time_slot.name,
      tooltip: '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported',
      children: (
        <AllSelectSearch
          showSearch
          mode="multiple"
          placeholder="Please Select Delivery Time Slot"
          optionFilterProp="children"
          filterOption={(input, option: any) => option && option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0}
          options={Object.entries(demandCampaign.TimeSlot).map(([key, value]) => {
            return {
              label: value,
              value: key
            };
          })}
        />
      ),
      levelComp: <LevelComponent levelLabel="Allow" />,
      defaultValue: {
        level: LevelConfigMapGroup.time_slot.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.mobile_os.label,
      key: LevelConfigMapGroup.mobile_os.key,
      name: LevelConfigMapGroup.mobile_os.name,
      children: <PretargetCheckbox options={MobileOs} />,
      levelComp: <LevelComponent levelLabel="Allow" />,
      defaultValue: {
        level: LevelConfigMapGroup.mobile_os.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.platform.label,
      key: LevelConfigMapGroup.platform.key,
      name: LevelConfigMapGroup.platform.name,
      children: <PretargetCheckbox options={Platform} />,
      levelComp: <LevelComponent levelLabel="Allow" />,
      defaultValue: {
        level: LevelConfigMapGroup.platform.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.mobile_inventory.label,
      key: LevelConfigMapGroup.mobile_inventory.key,
      name: LevelConfigMapGroup.mobile_inventory.name,
      children: (
        <PretargetCheckbox
          options={Object.entries(demandCampaign.MoblieInventory).map(([key, value]) => {
            return {
              label: value,
              value: key
            };
          })}
        />
      ),
      levelComp: <LevelComponent levelLabel="Allow" />,
      defaultValue: {
        level: LevelConfigMapGroup.mobile_inventory.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.ad_platform.label,
      key: LevelConfigMapGroup.ad_platform.key,
      name: LevelConfigMapGroup.ad_platform.name,
      children: (
        <Select
          placeholder="Please Select Ad Format"
          options={Object.entries(demandCampaign.AdPlatform).map(([key, value]) => {
            return {
              label: value,
              value: key
            };
          })}
          mode="multiple"
        />
      ),
      levelComp: <LevelComponent levelLabel="Allow" />,
      defaultValue: {
        level: LevelConfigMapGroup.ad_platform.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.ad_size.label,
      key: LevelConfigMapGroup.ad_size.key,
      name: LevelConfigMapGroup.ad_size.name,
      children: (
        <Select placeholder="Please Select Ad Size" options={options.adSizeOptions} mode="multiple" showSearch />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.ad_size.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.ad_size.levelOptions[0].value
      }
    },
    // {
    //   label: 'Site',
    //   key: 'site',
    //   name: 'site',
    //   children: <Select placeholder="Please Select Site" options={[]} />
    // },
    {
      label: LevelConfigMapGroup.brand.label,
      key: LevelConfigMapGroup.brand.key,
      name: LevelConfigMapGroup.brand.name,
      children: (
        <Select placeholder="Please Select Device Brand" options={DeviceBrandOptions} mode="multiple" showSearch />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.brand.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.brand.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.mobile_app.label,
      key: LevelConfigMapGroup.mobile_app.key,
      name: LevelConfigMapGroup.mobile_app.name,
      tooltip: 'Bundle:com.tencent.xin\rDomain:rixengine.com',
      children: (
        <BundleTableEdit
          editTitle={
            <>
              <span>Edit Bundle(Domain) List </span>
              <Tooltip title="The total number of bundles is limited to 15,000">
                <QuestionCircleOutlined
                  style={{
                    color: '#5e6466',
                    paddingLeft: '3px',
                    cursor: 'pointer'
                  }}
                />
              </Tooltip>
            </>
          }
          editTips={`Enter the Bundle(Domain) (one per line)`}
          deleteAllTitle="Delete All"
          deleteAllTips={`Are you sure to delete these all?`}
          contentMaxHeight={150}
          validator={(val: string) => isValidBundle(val) || isValidDomain(val)}
          limitCreateNum={15000}
        />
      ),
      levelComp: <LevelComponent levelOptions={LevelConfigMapGroup.mobile_app.levelOptions} />,
      defaultValue: {
        level: LevelConfigMapGroup.mobile_app.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.max_price.label,
      key: LevelConfigMapGroup.max_price.key,
      name: LevelConfigMapGroup.max_price.name,
      rules: [
        ({ getFieldValue, getFieldError, setFields }) => ({
          validator(_, value) {
            const minPrice = getFieldValue(LevelConfigMapGroup.min_price.name);
            const minPriceHasError = getFieldError(LevelConfigMapGroup.min_price.name)?.length > 0;

            // 如果最大价格小于最小价格且最小价格没有错误，则报错
            if (value && minPrice && value < minPrice) {
              if (!minPriceHasError) {
                return Promise.reject(new Error('Max Floor Price must be greater than Min Floor Price'));
              }
              // 如果最小价格有错误，提前返回，防止触发后续的清除错误逻辑
              return Promise.resolve();
            }

            // 如果最小价格有错误，则清除错误
            if (minPriceHasError) {
              setFields([{ name: LevelConfigMapGroup.min_price.name, errors: [] }]);
            }

            return Promise.resolve();
          }
        })
      ],
      children: (
        <InputNumber placeholder="Please Input Maximum Floor Price" style={{ width: '100%' }} max={50} min={0} />
      ),
      levelComp: <LevelComponent />,
      defaultValue: {
        level: LevelConfigMapGroup.max_price.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.min_price.label,
      key: LevelConfigMapGroup.min_price.key,
      name: LevelConfigMapGroup.min_price.name,
      rules: [
        ({ getFieldValue, getFieldError, setFields }) => ({
          validator(_, value) {
            const maxPrice = getFieldValue(LevelConfigMapGroup.max_price.name);
            const maxPriceHasError = getFieldError(LevelConfigMapGroup.max_price.name)?.length > 0;
            if (value && maxPrice && value > maxPrice) {
              if (!maxPriceHasError) {
                return Promise.reject(new Error('Min Floor Price must be less than Max Floor Price'));
              }
              return Promise.resolve();
            }

            if (maxPriceHasError) {
              setFields([{ name: LevelConfigMapGroup.max_price.name, errors: [] }]);
            }

            return Promise.resolve();
          }
        })
      ],
      children: (
        <InputNumber placeholder="Please Input Minimum Floor Price" style={{ width: '100%' }} max={60} min={0} />
      ),
      levelComp: <LevelComponent />,
      defaultValue: {
        level: LevelConfigMapGroup.min_price.levelOptions[0].value
      }
    },
    {
      label: LevelConfigMapGroup.server_region.label,
      key: LevelConfigMapGroup.server_region.key,
      name: LevelConfigMapGroup.server_region.name,
      children: (
        <NormalRadio
          options={[
            { label: 'DEFAULT', value: 'default' },
            { label: 'APAC', value: 'apac' },
            { label: 'USE', value: 'use' },
            { label: 'EUW', value: 'euw' }
          ]}
          nums={2}
        />
      ),
      levelComp: <LevelComponent />,
      defaultValue: {
        level: LevelConfigMapGroup.server_region.levelOptions[0].value,
        value: 'default'
      }
    }
  ];
};

// 从表单项配置中提取默认值，用于初始化表单
export const extractFormDefaultValues = (formItemSchemas: PretargetFormItemSchema[]) => {
  return formItemSchemas.reduce((defaultValuesMap, schemaItem) => {
    const { defaultValue, name: fieldName } = schemaItem;
    if (defaultValue) {
      // 设置字段的默认值
      if (defaultValue.value !== undefined) {
        defaultValuesMap[fieldName] = defaultValue.value;
      }
      // 设置字段对应的级别默认值
      if (defaultValue.level !== undefined) {
        defaultValuesMap[`${fieldName}-level`] = defaultValue.level;
      }
    }
    return defaultValuesMap;
  }, {} as Record<string, any>);
};

// 根据表单项配置生成 Ant Design Form.Item 组件数组
export function renderFormItemsFromSchema(formItemSchemas: PretargetFormItemSchema[]) {
  return formItemSchemas.map(schemaItem => {
    return (
      <Form.Item
        label={
          <>
            <span style={{ marginRight: 16 }}>{schemaItem.label}</span>
            {schemaItem.levelComp &&
              React.cloneElement(schemaItem.levelComp, { levelName: `${schemaItem.name}-level` })}
          </>
        }
        tooltip={schemaItem.tooltip}
        key={schemaItem.key}
        name={schemaItem.name}
        rules={schemaItem.rules}
        dependencies={schemaItem.dependencies}
      >
        {schemaItem.children}
      </Form.Item>
    );
  });
}
