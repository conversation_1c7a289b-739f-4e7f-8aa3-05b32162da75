/**
 * 定义 profit 页面级状态管理
 *
 * 每个页面下创建 store.tsx, 完成类型、初始值、hook定义，并导出
 */
import { createScopeHooks } from '@/stores/scope';

type Option = { label: string; value: number };

export type ProfitPageStateType = {
  demandOptions: Option[];
  supplyOptions: Option[];
};

// 默认值，mount 时实现初始化，
export const ProfitPageState: ProfitPageStateType = {
  demandOptions: [],
  supplyOptions: []
};

// 调用 useProfitPageStore 返回 snapshot 对象
// 如果需要更细粒度的处理，可以先调用 useProfitPageState 然后 useSnapshot 手动选择范围
// 调用 useProfitPageState 返回 proxy 对象，改动对象内的值可实现 re-render
export const { useScopeStore: useProfitPageStore, useScopeState: useProfitPageState } =
  createScopeHooks<ProfitPageStateType>();
