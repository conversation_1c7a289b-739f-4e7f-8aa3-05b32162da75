/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-04-27 15:59:57
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-20 12:47:08
 * @Description:
 */
import { DownOutlined, SmileOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Dropdown, Space } from 'antd';
import React from 'react';
import styles from './index.less';

type DropdownProps = {
  items: MenuProps['items'];
  title: string;
  icon?: React.ReactNode;
};

const App: React.FC<DropdownProps> = ({ items, title, icon }) => (
  <Dropdown
    menu={{ items }}
    autoAdjustOverflow
    placement="bottomRight"
    dropdownRender={(menus: React.ReactNode) => <div className={styles['dropdown-container']}>{menus}</div>}
  >
    <div onClick={e => e.preventDefault()} className={styles['title']}>
      <Space>
        {title}
        {icon || <DownOutlined />}
      </Space>
    </div>
  </Dropdown>
);

export default App;
