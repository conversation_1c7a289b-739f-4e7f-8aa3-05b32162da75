/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-28 14:57:39
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-04 11:51:44
 * @Description:
 */
import { Context } from 'koa';
declare namespace BillingAPI {
  type PublisherBillingListItem = {
    id: number;
    date: string;
    seller_id: number;
    buyer_id: number;
    seller: string;
    buyer: string;
    seller_net_revenue: number;
    buyer_net_revenue?: number;
    seller_payment_impression: number;
    impression: number;

    profit: number;
    profit_rate: number;
    total_request: number;
    partner_id?: number[];
    pub_partner: string;
  };
  type BillingListItem = {
    id: number;
    seller_id: number;
    buyer_id: number | string;
    buyer: string;
    seller: string;
    buyer_gross_ecpm: string;
    buyer_gross_revenue: string;
    buyer_net_ecpm: string | number;
    buyer_net_revenue: number;
    date: string;
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    seller_payment_impression: number;
    total_request: string;
    win: string;
    win_rate: string;
    block_request: string;
    profit: number;
    profit_rate: number;
    partner_id: number;
    partner_name: string;
    adv_partner: string;
    pub_partner: string;
  };
  type AdvertiserParams = {
    tnt_id: number;
    tnt: number;
    buyer_id: number[];
    seller_id: number[];
    partner_id?: number[];
    columns: string[];
    start?: number;
    start_date: string;
    end?: number;
    end_date: string;
    order: string;
    order_key: string[];
    isAll: boolean;
    tz_start_date: string;
    tz_end_date: string;
    cur_time_zone: string;
    cur_condition_str: string;
  };
  type PublisherParams = AdvertiserParams;
  type sqlValue = {
    conditions: string;
    dimension: string;
    groupBy: string;
    order: string;
    limit: string;
    table?: string;
    changeBuyer?: boolean;
  };

  type QueryInnerParams = {
    condition: string;
    dimensions: string;
    group_dimension: string;
    order: string;
    limit: string;
    leftJoinCond: string;
    date_time_str: string;
    isBuyer?: boolean;
  }
  interface AdvertiserCtrl {
    getAdvertiserBillingList(ctx: Context): Promise<void>;
    downloadAdvertiserBillingList(ctx: Context): Promise<void>;
  }

  interface AdvertiserSerivce {
    getAdvertiserBillingList(params: AdvertiserParams, api_url: string, isPaing?: boolean): Promise<any>;
    initParams(params: AdvertiserParams): sqlValue;
    downloadAllReport(params: AdvertiserParams, user_id: number, api_url: string): Promise<any>;
  }

  interface AdvertiserModel {
    getAdvertiserBillingList(options: sqlValue, api_url: string): Promise<any>;
    downloadAllReport(options: sqlValue, api_url: string): Promise<any>;
    countList(options: sqlValue, api_url: string): Promise<any>;
  }

  interface PublisherCtrl {
    getPublisherBillingList(ctx: Context): Promise<void>;
    downloadPublisherBillingList(ctx: Context): Promise<void>;
  }

  interface PublisherSerivce {
    getPublisherBillingList(params: PublisherParams, api_url: string): Promise<any>;
    downloadAllReport(params: PublisherParams, user_id: number, api_url: string): Promise<any>;
    initParams(params: PublisherParams): sqlValue;
    paserResult(data: any, index: number): any;
  }

  interface PublisherModel {
    getPublisherBillingList(options: sqlValue, api_url: string): Promise<any>;
    downloadAllReport(options: sqlValue, api_url: string): Promise<any>;
    countList(options: sqlValue, api_url: string): Promise<any>;
  }
}
