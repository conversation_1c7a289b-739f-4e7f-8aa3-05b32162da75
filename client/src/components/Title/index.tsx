import { Typography } from 'antd';
import { TitleProps } from 'antd/lib/typography/Title';
import { CSSProperties, useMemo } from 'react';
interface Props extends TitleProps {}

export const TitleDefaultStyle: CSSProperties = {
  borderLeft: '4px solid var(--primary-color)',
  borderRadius: 4,
  backgroundColor: 'rgb(243 245 245)',
  paddingLeft: 8,
  marginBlock: 8,
  paddingBlock: 4
};

const Title = ({ level = 5, children, ...props }: Props) => {
  return (
    <Typography.Title level={level} style={TitleDefaultStyle} {...props}>
      {children}
    </Typography.Title>
  );
};

export default Title;

type TocOption = {
  href: string;
  title: string;
};

export const useTitleComponentMap = (tocOptions: TocOption[]) => {
  return useMemo(() => {
    return tocOptions.reduce((acc, item) => {
      const key = item.href.slice(1);
      acc[key] = (
        <Title level={5} id={key} style={TitleDefaultStyle}>
          {item.title}
        </Title>
      );
      return acc;
    }, {} as Record<string, React.ReactNode>);
  }, [tocOptions]);
};
