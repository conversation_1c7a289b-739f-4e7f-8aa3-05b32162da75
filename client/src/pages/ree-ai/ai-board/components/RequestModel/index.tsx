/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-03 21:27:48
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 18:55:27
 * @Description:
 */
import React from 'react';
import { Checkbox, Col, Row, message } from 'antd';
import NormalModal from '@/components/Modal/NormalModal';
import styles from './index.less';
import { fetchData } from '@/utils';
import { trafficRequest } from '@/services/ai-board';
import { AdFormatMap } from '@/constants';
import { Countries } from '@/constants/global-mapping/country';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import { TrafficTypeMap } from '@/constants/ree-ai/ai-board';

type Props = {
  visible: boolean;
  onCancel: () => void;
  onSave: () => void;
  item?: { ad_format: number; country: string; buyer_options: BoardAPI.BudgetAndTrafficItem['buyer_options'] };
};
const Modal: React.FC<Props> = ({ visible, onCancel, item, onSave }) => {
  const [needAdsTxt, setNeedAdsTxt] = React.useState(false);
  const [trafficType, setTrafficType] = React.useState<number>(TrafficTypeMap.app);
  const handleRequest = () => {
    const ids = item?.buyer_options.map(v => v.buyer_id) || [];
    const params = {
      ad_format: item?.ad_format,
      country: item?.country,
      ext_1: needAdsTxt ? 1 : 2,
      ext_2: trafficType,
      buyer_ids: ids
    };

    fetchData({
      request: trafficRequest,
      params,
      onSuccess: () => {
        onSave();
        message.success('Request has been sent successfully !');
      }
    });
  };
  const onChange = (checkedValues: CheckboxValueType[]) => {
    setNeedAdsTxt(checkedValues.includes('ads.txt'));
  };
  const handleTrafficTypeChange = (checkedValues: CheckboxValueType[]) => {
    const traffictype = checkedValues.find(v => v !== trafficType);
    setTrafficType(traffictype as number);
  };
  return (
    <div>
      <NormalModal
        open={visible}
        onCancel={onCancel}
        cancelText="cancel"
        okText="Request"
        onOk={handleRequest}
        title="Request More Inventory"
      >
        <div className={styles['title']}>Requesting the following inventory from RIX:</div>
        <div className={styles['content']}>
          <div>
            <span>{Countries[item?.country || '']}; </span>
            <span>{AdFormatMap[item?.ad_format || 0]}</span>
          </div>
          <Checkbox.Group style={{ width: '100%' }} onChange={handleTrafficTypeChange} value={[trafficType]}>
            <Row>
              <Col span={4}>
                <Checkbox value={TrafficTypeMap.app}>app</Checkbox>
              </Col>
              <Col span={4}>
                <Checkbox value={TrafficTypeMap.site}>site</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
          <Checkbox.Group style={{ width: '100%' }} onChange={onChange}>
            <Row>
              <Col span={8}>
                <Checkbox value="ads.txt">ads.txt</Checkbox>
              </Col>
            </Row>
          </Checkbox.Group>
        </div>
      </NormalModal>
    </div>
  );
};

export default Modal;
