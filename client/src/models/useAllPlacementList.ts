/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-08 11:57:32
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-08 11:58:46
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllPlacementList } from '@/services/api';

type Props = {
  dataSource: AppAPI.PlacementListItem[];
  reload: () => Promise<AppAPI.PlacementListItem[]>;
  loading: boolean;
};

export default function useAllPlacementList(): Props {
  const { data, run, loading, error } = useCustomRequest(getAllPlacementList, {
    cacheKey: 'all-plm-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
