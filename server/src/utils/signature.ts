import child_process from 'child_process';
import Hex from 'crypto-js/enc-hex';
import hmacSHA256 from 'crypto-js/hmac-sha256';

// 上次 git commit 的 hash 值
const secretKey = child_process
  .execSync('git log -1 --format=%H')
  .toString()
  .trim();

/**
 * 生成签名
 * @param params 参数
 * @returns 签名
 */
export function generateSignature(params: Record<string, any>) {
  const sortedParams = Object.entries(params)
    .filter(([_, value]) => value !== undefined)
    .sort((a, b) => a[0].localeCompare(b[0]))
    .map(([key, value]) => `${key}=${JSON.stringify(value)}`);

  const stringToSign = sortedParams.join('&');
  // 使用 HMAC-SHA256 生成签名
  return hmacSHA256(stringToSign, secretKey).toString(Hex).slice(0, 32);
}
