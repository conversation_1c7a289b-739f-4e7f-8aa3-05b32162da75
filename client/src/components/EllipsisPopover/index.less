
.list-tag {
  height: 26px;
  line-height: 26px;
  border-radius: 4px;
  background: #EEF0F0;
  color: #303333;
  cursor: pointer;
  display: inline-block;
}
.tag-container {
  position: relative;
  width: 100%;
  height: 26px;
  top: 3px;
  cursor: pointer;
}
.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  .list-tag {
    margin-bottom: 0px;
  }
}
.list {
  padding: 12px;
  .header {
    .header-total {
      background: #F3F5F5;
      height: 32px;
      color: #5E6466;
      line-height: 32px;
      border-radius: 6px;
      margin-top: 12px;
      span {
        padding-left: 12px;
      }
    }
  }
  .item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    height: 39px;
    line-height: 39px;
    border-block-end: 1px solid rgba(5, 5, 5, .06);
    color: var(--text-color);
  }
  > div {
    width: 100%;
    display: inline-block;
  }
}

.content-bottom {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    margin-bottom: 6px;
  }
  :global {
    .ant-typography-copy-success, .ant-typography-copy-success:hover, .ant-typography-copy-success:focus {
      background: var(--primary-color);
    }
    .ant-typography-copy {
      margin-left: 0px;
    }
  }
}

.copy-container {
  background: var(--primary-color);
  height: 32px;
  line-height: 32px;
  text-align: center;
  color: #fff;
  margin-top: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.ellipsis-popover-overaly {
  :global {
    .ant-popover-inner {
      .ant-popover-inner-content {
        padding: 0px;
      }
    }
  }
}
