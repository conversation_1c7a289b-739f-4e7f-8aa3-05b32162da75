/* eslint-disable new-cap */
// 更新partner数据
import parquet from '@dsnp/parquetjs';
import path from 'path';
import fs from 'fs';
import dbUtil from '@/db/mysql';
import { uploadFile } from '@/db/bigquery';
import { getLogger } from '@/config/log4js';

// @ts-ignore
const buyer_schema = new parquet.ParquetSchema({
  buyer_id: { type: 'INT64' },
  buyer_name: { type: 'UTF8' },
  status: { type: 'INT_8' },
  partner_name: { type: 'UTF8' },
  partner_id: { type: 'INT64' },
  tnt_id: { type: 'INT64' }
});

// @ts-ignore
const seller_schema = new parquet.ParquetSchema({
  seller_id: { type: 'INT64' },
  seller_name: { type: 'UTF8' },
  status: { type: 'INT_8' },
  partner_name: { type: 'UTF8' },
  partner_id: { type: 'INT64' },
  tnt_id: { type: 'INT64' }
});

// 同步上游partner 那个租户的
export const syncBuyerPartner2Gcp = async (tnt_id: number) => {
  const sql = `
    select
      byr.buyer_id, 
      byr.buyer_name,
      byr.status,
      coalesce(bp.dp_name, '') as partner_name,
      coalesce(bp.partner_id, 0) as partner_id,
      byr.tnt_id
    from buyer as byr
    left join buyer_parent as bp on bp.dp_id=byr.dp_id
    where byr.tnt_id=?
  `;
  const data: any[] = await dbUtil.query(sql, [tnt_id]);

  const file_path = path.join(__dirname, `../../../tmp/${tnt_id}_buyer_partner.parquet`);
  if (!fs.existsSync(path.join(__dirname, '../../../tmp'))) {
    fs.mkdirSync(path.join(__dirname, '../../../tmp'));
  }
  const writer = await parquet.ParquetWriter.openFile(buyer_schema, file_path);
  if (data.length) {
    data.forEach(async (v) => {
      await writer.appendRow(v);
    });
  }
  const gcp_file = process.env.NODE_ENV === 'prod' ? `saas_others/partner/buyer/tnt=${tnt_id}/partner.parquet` : `saas_others/test/partner/buyer/tnt=${tnt_id}/partner.parquet`;

  await writer.close();
  // 上传
  await uploadFile({ fileName: gcp_file, filePath: file_path, bucket: 'console-rix-engine' });

  getLogger('app').info(`demand partner sync to gcp success tnt_id=${tnt_id}`);
};

// 同步下游partner 那个租户的
export const syncSellerPartner2Gcp = async (tnt_id: number) => {
  const sql = `
    select
      sl.seller_id, 
      sl.seller_name,
      sl.status,
      coalesce(sp.sp_name, '') as partner_name,
      coalesce(sp.partner_id, 0) as partner_id,
      sl.tnt_id
    from seller as sl
    left join seller_parent as sp on sp.sp_id=sl.sp_id
    where sl.tnt_id=?
  `;
  const data: any[] = await dbUtil.query(sql, [tnt_id]);

  const file_path = path.join(__dirname, `../../../tmp/${tnt_id}_seller_partner.parquet`);
  if (!fs.existsSync(path.join(__dirname, '../../../tmp'))) {
    fs.mkdirSync(path.join(__dirname, '../../../tmp'));
  }
  const writer = await parquet.ParquetWriter.openFile(seller_schema, file_path);
  if (data.length) {
    data.forEach(async (v) => {
      await writer.appendRow(v);
    });
  }
  await writer.close();
  const gcp_file = process.env.NODE_ENV === 'prod' ? `saas_others/partner/seller/tnt=${tnt_id}/partner.parquet` : `saas_others/test/partner/seller/tnt=${tnt_id}/partner.parquet`;

  // 上传
  await uploadFile({ fileName: gcp_file, filePath: file_path, bucket: 'console-rix-engine' });

  getLogger('app').info(`supply partner sync to gcp success tnt_id=${tnt_id}`);
};
