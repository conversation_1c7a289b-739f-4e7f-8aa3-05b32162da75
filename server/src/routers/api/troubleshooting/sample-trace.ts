/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-22 11:18:30
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 18:34:50
 * @Description:
 */
import Router from 'koa-router';
import { sampleTraceCtrl } from '@/controllers';
import { parseDateParams } from '@/utils/report';

const router = new Router({ prefix: '/sample' });

const routers = router
  .post(
    '/getSampleTraceTaskList',
    parseDateParams,
    sampleTraceCtrl.getSampleTraceTaskList
  )
  .post(
    '/addSampleTraceTask',
    parseDateParams,
    sampleTraceCtrl.addSampleTraceTask
  )
  .post(
    '/getSampleTraceList',
    parseDateParams,
    sampleTraceCtrl.getSampleTraceList
  )
  .post(
    '/updateSampleTraceTask',
    parseDateParams,
    sampleTraceCtrl.updateSampleTraceTask
  );

export default routers;
