import { TopBarSearchItem } from '@/components/TopBar';
import {
  GeoPolicyKeyRelationSearchOption,
  GeoPolicyKeySearchOption,
  GeoPolicyTabType
} from '@/constants/strategy/geo-policy';
import { useEffect, useState } from 'react';

export type SelectOption<T extends string | number> = {
  label: string;
  value: T;
};

export type PolicyKeyOption = SelectOption<number> & {
  status: number;
  is_default: number;
};

/**
 * Custom hook to manage search options.
 * @param supplyOptions Supply options.
 * @param policyKeyOptions Policy key options.
 * @param currentTab Current tab.
 * @returns The search options for the current tab.
 */
export const useSearchOptions = (
  supplyOptions: SelectOption<string>[],
  policyKeyOptions: PolicyKeyOption[],
  currentTab: GeoPolicyTabType
) => {
  // searchOptions 初始化，然后异步设置三个不同的 searchOptions，并缓存
  const [cacheMap, setCacheMap] = useState<Record<GeoPolicyTabType, TopBarSearchItem[]>>({
    key: GeoPolicyKeySearchOption,
    auth: GeoPolicyKeyRelationSearchOption
  });

  useEffect(() => {
    setCacheMap(prev => {
      const newCacheMap = { ...prev };
      let hasChanges = false;

      // 合并 supplyOptions 和 policyKeyOptions 的处理逻辑
      if (supplyOptions.length > 0 || policyKeyOptions.length > 0) {
        const newRelationOptions = GeoPolicyKeyRelationSearchOption.map(item => {
          let options = item.options;
          if (item.key === 'seller_id' && supplyOptions.length > 0) {
            options = supplyOptions;
          }
          if (item.key === 'policy_key_id' && policyKeyOptions.length > 0) {
            options = policyKeyOptions;
          }
          return {
            ...item,
            options
          };
        });

        if (JSON.stringify(newRelationOptions) !== JSON.stringify(prev.auth)) {
          newCacheMap.auth = newRelationOptions;
          hasChanges = true;
        }
      }

      // 只有当确实有变化时才返回新的对象，避免不必要的重新渲染
      return hasChanges ? newCacheMap : prev;
    });
  }, [supplyOptions, policyKeyOptions]);

  return cacheMap[currentTab];
};
