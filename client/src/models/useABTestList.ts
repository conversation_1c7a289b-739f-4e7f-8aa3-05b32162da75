/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-02-21 15:21:53
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-21 15:21:54
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getABTestList } from '@/services/api';

type Props = {
  dataSource: StrategyAPI.ABTestListItem[];
  reload: () => Promise<StrategyAPI.ABTestListItem[]>;
  loading: boolean;
};

export default function useABTestList(): Props {
  const { data, run, loading, error } = useCustomRequest(getABTestList, {
    cacheKey: 'ab-test-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
