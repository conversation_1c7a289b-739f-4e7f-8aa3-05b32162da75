import { demandCampaign } from '@/constants/demand/demand-campaign';

type ItemProps = {
  name: string;
  key: string;
  type: string;
  defaultValues: any;
  level: number | undefined;
  values: any;
  isDisabled?: boolean;
  minWidth?: number;
  tooltip?: string;
};

function getFormatValue(values: string[], name: any) {
  return values.map((item: string) => {
    return ((demandCampaign as any)[name][item] as string) || '';
  });
}

export function formatCampaignData(
  campaignList: DemandAPI.PretargetCampaignItem[],
  dataSource: SupplyAPI.SupplyListItem[],
  adSizeMapByValue: Record<string, string>
): any[] {
  return campaignList.map(val => {
    // TODO 不可拓展性？
    const attrs: any = Array(21).fill('');
    // eslint-disable-next-line no-return-assign
    val.items.forEach((item: any) => (attrs[item.level] = item.content));
    const country: ItemProps = {
      name: 'Country',
      key: 'country',
      type: 'left',
      isDisabled: !!attrs[2],
      defaultValues: attrs[1] || attrs[2] ? (attrs[1] || attrs[2]).split(',') : [],
      level: attrs[1] ? 1 : attrs[2] ? 2 : undefined,
      values: [],
      minWidth: 150
    };
    const platform: ItemProps = {
      name: 'Device',
      key: 'platform',
      type: 'center',
      defaultValues: attrs[3] ? attrs[3].split(',') : [],
      level: 3,
      values: [],
      minWidth: 160
    };
    const moblieOS: ItemProps = {
      name: 'OS',
      key: 'mobile_os',
      type: 'center',
      defaultValues: attrs[4] ? attrs[4].split(',') : [],
      level: 4,
      values: [],
      minWidth: 160
    };
    const moblieInventory: ItemProps = {
      name: 'Inventory',
      key: 'mobile_inventory',
      type: 'center',
      defaultValues: attrs[5] ? attrs[5].split(',') : [],
      level: 5,
      values: [],
      minWidth: 100
    };
    const category: ItemProps = {
      name: 'Category',
      key: 'category',
      type: 'left',
      isDisabled: !!attrs[7],
      defaultValues: attrs[6] || attrs[7] ? (attrs[6] || attrs[7]).split(',') : [],
      level: attrs[6] ? 6 : attrs[7] ? 7 : undefined,
      values: [],
      minWidth: 180
    };
    const adPlatform: ItemProps = {
      name: 'Ad Format',
      key: 'ad_platform',
      type: 'center',
      defaultValues: attrs[8] ? attrs[8].split(',') : [],
      level: 8,
      values: [],
      minWidth: 180
    };
    const deviceBrand: ItemProps = {
      name: 'Device Brand',
      key: 'brand',
      type: 'right',
      isDisabled: !!attrs[22],
      defaultValues: attrs[21] || attrs[22] ? (attrs[21] || attrs[22]).split(',') : [],
      level: attrs[21] ? 21 : attrs[22] ? 22 : undefined,
      values: [],
      minWidth: 160
    };
    const site: ItemProps = {
      name: 'Site',
      key: 'site',
      type: 'right',
      defaultValues: attrs[9] ? attrs[9].split(',') : [],
      level: 9,
      values: [],
      minWidth: 160
    };
    const mobileApp: ItemProps = {
      name: 'Bundle(Domain)',
      key: 'mobile_app',
      type: 'right',
      isDisabled: !!attrs[11],
      defaultValues: attrs[10] || attrs[11] ? (attrs[10] || attrs[11]).split(',') : [],
      level: attrs[10] ? 10 : attrs[11] ? 11 : undefined,
      values: [],
      minWidth: 240
    };
    const adSize: ItemProps = {
      name: 'Ad Size',
      key: 'ad_size',
      type: 'center',
      isDisabled: !!attrs[13],
      defaultValues: attrs[12] || attrs[13] ? (attrs[12] || attrs[13]).split(',') : [],
      level: attrs[12] ? 12 : attrs[13] ? 13 : undefined,
      values: [],
      minWidth: 130
    };
    const network: ItemProps = {
      name: 'Network',
      key: 'network',
      type: 'left',
      isDisabled: !!attrs[17],
      defaultValues: attrs[16] || attrs[17] ? (attrs[16] || attrs[17]).split(',') : [],
      level: attrs[16] ? 16 : attrs[17] ? 17 : undefined,
      values: [],
      minWidth: 170
    };
    const seller: ItemProps = {
      name: 'Publisher',
      key: 'seller',
      type: 'left',
      isDisabled: !!attrs[19],
      defaultValues:
        attrs[18] || attrs[19] ? (attrs[18] || attrs[19]).split(',').map((item: string) => item.trim()) : [],
      level: attrs[18] ? 18 : attrs[19] ? 19 : undefined,
      values: [],
      minWidth: 160
    };
    const timeSlot: ItemProps = {
      name: 'Delivery Time Slot',
      key: 'time_slot',
      type: 'left',
      defaultValues: attrs[20] ? attrs[20].split(',') : [],
      level: 20,
      values: [],
      minWidth: 200,
      tooltip: '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported.'
    };
    timeSlot.values = getFormatValue(timeSlot.defaultValues, 'TimeSlot');
    seller.values = seller.defaultValues.map((item: string) => {
      const val = dataSource.find((value: SupplyAPI.SupplyListItem) => +value.seller_id === +item);
      if (val) {
        return `${val.seller_name}(${val.seller_id})`;
      }
      return item;
    });
    platform.values = getFormatValue(platform.defaultValues, 'Platform');
    moblieOS.values = getFormatValue(moblieOS.defaultValues, 'MoblieOS');
    category.values = getFormatValue(category.defaultValues, 'Category');
    adPlatform.values = getFormatValue(adPlatform.defaultValues, 'AdPlatform');
    deviceBrand.values = getFormatValue(deviceBrand.defaultValues, 'DeviceBrand');
    moblieInventory.values = getFormatValue(moblieInventory.defaultValues, 'MoblieInventory');
    adSize.values = adSize.defaultValues.map((item: string) => {
      return adSizeMapByValue[item] || '';
    });
    network.values = getFormatValue(network.defaultValues, 'Network');
    country.values = getFormatValue(country.defaultValues, 'Country');
    mobileApp.values = JSON.parse(JSON.stringify(mobileApp.defaultValues));
    site.values = JSON.parse(JSON.stringify(site.defaultValues));
    const max_price = +attrs[14] || 0;
    const min_price = +attrs[23] || 0;
    const serverRegion = attrs[15] || 'default';
    // 页面显示顺序
    const items = [
      seller,
      timeSlot,
      country,
      platform,
      moblieOS,
      adPlatform,
      deviceBrand,
      adSize,
      category,
      moblieInventory,
      network,
      mobileApp
    ];

    const result = {
      campaignId: val.campaign_id,
      max_price,
      min_price,
      serverRegion,
      items: items,
      campaign_name: val.campaign_name,
      status: val.status,
      update_time: val.update_time,
      op_name: val.op_name
    };
    return result;
  }).sort((a, b) => new Date(b.update_time).getTime() - new Date(a.update_time).getTime());
}

export const checkLevelChange = (curValues: DemandAPI.LeftType[], defValues: DemandAPI.PretargetEditItemProps[]) => {
  return curValues.filter(cur => {
    const found = defValues.find(defVal => {
      return defVal.key === cur.key;
    });
    const def = found?.defaultValue;
    return def?.level !== cur.level;
  });
};

export const handleFillDefault = (options: DemandAPI.PretargetEditItemProps[], values: DemandAPI.LeftType[]) => {
  const arr: DemandAPI.LeftType[] = [];
  options.forEach(item => {
    const index = values.findIndex(val => val.key === item.key);
    if (index !== -1) {
      const val = {
        value: item.defaultValue.value,
        level: item.defaultValue.level,
        label: item.name,
        key: item.key
      };
      arr.push(val);
    }
  });
  return arr;
};

export const formatFormValue = (options: DemandAPI.LeftType[]) => {
  return options
    .filter(item => {
      if (Array.isArray(item.value)) {
        return item.level && item.value.length > 0;
      }
      return item.level && item.value;
    })
    .map(item => {
      return {
        level: item.level,
        content: Array.isArray(item.value) ? item.value.join(',') : item.value
      };
    });
};
