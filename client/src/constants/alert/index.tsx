/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-19 16:16:03
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-20 15:18:09
 * @Description:
 */

import { ColumnProps } from 'antd/lib/table';
import { TopBarSearchItem } from '@/components/TopBar';

import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { UnReadOptions, UnReadType } from '../base';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

export const AlertColumns: ColumnProps<CommonAPI.NotificationListItem>[] = [
  {
    title: 'Title',
    dataIndex: 'id',
    width: 140,
    render: (_, row) => (
      <StatusTag type={(row['unread'] && row['unread'] === UnReadType.unread && 'paused') || ''}>
        {row['title']}
      </StatusTag>
    )
  },
  {
    dataIndex: 'content',
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Content</span>
        <Tooltip title="(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported">
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    width: 220,
    render: (txt, params) => (
      <HoverToolTip title={txt || '-'}>
        <span>{(txt && `${txt}`) || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    dataIndex: 'create_time',
    width: 80
  }
];

export const AlertSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time',
    type: 'date',
    key: 'create_time',
    value: [],
    isDate: true
  },
  {
    name: 'Keywords',
    type: 'input',
    key: 'content',
    value: '',
    placeholder: 'Please Input Content',
    tooltip: 'Supports fuzzy search.The matching items are title and content.',
    fuzzySearch: {
      extraKeys: ['title']
    }
  },
  {
    name: 'Status',
    type: 'select',
    key: 'unread',
    mode: 'multiple',
    value: '',
    options: UnReadOptions.filter(item => item.value !== UnReadType.all),
    placeholder: 'Please Select Status'
  }
];

export const AlertTabOptions = [
  { label: 'Revenue', value: 1 },
  { label: 'IVT', value: 2 },
  { label: 'QPS', value: 3 },
  { label: 'Cap', value: 4 },
  { label: 'Trading', value: 5 }
];

export const AlertTab = {
  Revenue: 1,
  IVT: 2,
  Cap: 3,
  QPS: 4,
  Trading: 5
};
