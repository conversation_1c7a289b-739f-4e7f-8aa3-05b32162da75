/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-04 11:19:25
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-07 16:06:50
 * @Description:
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-05-04 11:54:31
 * @Description:
 */
import { request } from '@umijs/max';

export async function getSupplyList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyListItem>>('/api/supply/getSupplyList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyListWithTesting(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyListItem>>('/api/supply/getSupplyListWithTesting', {
    data: body || {},
    ...(options || {})
  });
}
export async function getDashboardSupplyList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyListItem>>('/api/supply/getDashboardSupplyList', {
    data: body || {},
    ...(options || {})
  });
}
export async function addSupply(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/supply/addSupply', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateSupply(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/supply/updateSupply', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyAuth(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SellerDemandAuth>>('/api/supply/getSupplyAuth', {
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyAppPlacement(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SellerPlacement>>('/api/supply/getSupplyAppPlacement', {
    data: body || {},
    ...(options || {})
  });
}

export async function setSupplyAuth(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/supply/setSupplyAuth', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyByUser(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyListItem[]>>('/api/supply/getSupplyByUser', {
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyEndpoint(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyEndpoint>>('/api/supply/getSupplyEndpoint', {
    data: body || {},
    ...(options || {})
  });
}
