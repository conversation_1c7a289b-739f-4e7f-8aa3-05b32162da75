/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-08 13:58:36
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:58:30
 * @Description:
 */

import type { InfoBarTabsProps } from '@/components/InfoBar';
import RixEngineFont from '@/components/RixEngineFont';
import StatusTag from '@/components/Tag/NewStatusTag';
import OldStatusTag from '@/components/Tag/StatusTag';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { history } from 'umi';
import {
  AuctionTypeToString,
  ImpTrackingTypeToString,
  NativeFormatTypeToString,
  NativeVersionTypeToString,
  SchainCompleteMapByValueKey,
  ZipMap
} from '.';
import {
  DemandAndSupplyStatusDesc,
  DemandAndSupplyStatusMap,
  ProfitModelTypeToString,
  StatusDesc,
  StatusMap
} from '..';
import Title from '@/components/Title';

export const DemandInfoTabs: InfoBarTabsProps<DemandAPI.DemandListItem> = [
  {
    title: 'Basic',
    key: 'basic',
    access: 'DemandPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />
    },
    rowNum: 3, // 每行号展示几列
    children: [
      {
        title: <Title>Basic Info</Title>,
        key: 'basic-info',
        columns: [
          {
            title: 'ID',
            dataIndex: 'buyer_id'
          },
          {
            title: 'Advertiser',
            dataIndex: 'buyer_name',
            render: (_, row, maxWidth) => {
              const isTesting = row.status === DemandAndSupplyStatusMap.Testing;
              return isTesting ? (
                <div>
                  <span>{_}</span>
                  <Tooltip title={<span>Created By system, only for testing</span>}>
                    <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
                  </Tooltip>
                </div>
              ) : (
                <HoverToolTip title={_} maxWidth={maxWidth}>
                  <span>{_}</span>
                </HoverToolTip>
              );
            }
          },
          {
            title: 'Partner',
            dataIndex: 'partner_id',
            render: (_, row, maxWidth) => {
              const val = +_ > 0 ? `${row.partner_name}(${_})` : '-';
              return (
                <HoverToolTip title={val} maxWidth={maxWidth}>
                  <span>{val}</span>
                </HoverToolTip>
              );
            }
          },
          {
            title: 'Integration Type',
            dataIndex: 'integration_type_desc'
          },
          {
            title: 'Status',
            dataIndex: 'status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          }
        ]
      },
      {
        title: <Title>Profit Settings</Title>,
        key: 'profit-settings',
        columns: [
          {
            title: 'Profit',
            dataIndex: 'profit_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Profit(%)',
            width: 100,
            dataIndex: 'profit_ratio',
            render: (txt, params) => <span>{params.profit_status === DemandAndSupplyStatusMap.Active ? txt : '-'}</span>
          },
          {
            title: 'Profit Model',
            width: 120,
            dataIndex: 'profit_model',
            render: text => <>{ProfitModelTypeToString[text]}</>
          },
        ]
      },
      {
        title: <Title>Tracking Settings</Title>,
        key: 'tracking-settings',
        columns: [
          {
            title: 'Auction Type',
            dataIndex: 'auction_type',
            render: text => <>{AuctionTypeToString[text]}</>
          },
          {
            title: 'Impression Track Type',
            dataIndex: 'imp_track_type',
            render: text => <>{ImpTrackingTypeToString[text]}</>
          },
          {
            title: 'OMID Track',
            dataIndex: 'omid_track',
            tooltip: () => 'An impression is counted if it meets the MRC viewability standard, with at least 50% of the banner visible in the active browser window for a minimum of 1 second.',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          }
        ]
      },
      {
        title: <Title>Filter and Compatibility Strategy</Title>,
        key: 'filter-and-compatibility-strategy',
        columns: [
          {
            title: 'Pass Supply Chain',
            width: 150,
            dataIndex: 'schain_required',
            tooltip: () => 'Whether to send schain to Demand',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Schain Complete Required',
            width: 240,
            dataIndex: 'schain_complete',
            tooltip: () => 'Filter requests with schain.compelete equal to 0',
            render: (_, row) => (
              <OldStatusTag type={SchainCompleteMapByValueKey[row['schain_complete']]?.toLowerCase() || ''}>
                {SchainCompleteMapByValueKey[row['schain_complete']]}
              </OldStatusTag>
            )
          },
          {
            title: 'Schain Hops Filter',
            width: 150,
            dataIndex: 'schain_hops',
            tooltip: () => 'Filter requests sent to Demand with more than N schain-hops, where N is between 1 and 10',
            render: (_, row) => <>{row['schain_hops'] || 'Unlimited'}</>
          },
          {
            title: 'Pass Display Manager',
            width: 150,
            dataIndex: 'pass_display_manager',
            tooltip: () => 'Whether to send displaymanager to Demand',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Display Manager Filter',
            width: 150,
            dataIndex: 'display_manager_filter',
            tooltip: () => 'Filter requests where display manager is empty',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Mraid Traffic Filter',
            width: 150,
            dataIndex: 'filter_mraid',
            tooltip: () => 'Filter requests that lack support for Mraid',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'IFA Required',
            dataIndex: 'idfa_required',
            tooltip: () => 'Filter requests with an empty IFA(gaid/idfa)',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Multi Format',
            dataIndex: 'multi_format',
            tooltip: () =>
              'Support both a banner ad and a video ad format within a single BidRequest.imp at the same index (e.g. imp[0])',
            render: _ => <StatusTag value={_} statusDescMap={StatusDesc} />
          },
          {
            title: 'Banner Multi Size',
            dataIndex: 'banner_multi_size',
            tooltip: () => 'Add ad size 300*250 for requests with a 320*50 banner',
            render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
          },
          {
            title: 'Native Format',
            dataIndex: 'native_format',
            tooltip: () => 'Support imp.native format conversion, String by default',
            render: text => <>{NativeFormatTypeToString[text]}</>
          },
          {
            title: 'Native Version',
            dataIndex: 'native_version',
            tooltip: () => 'Native protocol version sent to Demand. Default value: 1.2',
            render: text => <>{NativeVersionTypeToString[text]}</>
          },
          {
            title: 'Max Px IVT(%)',
            dataIndex: 'max_pxl_ivt_ratio',
            tooltip: () => 'Requests exceeding the configured IVT threshold will be blocked',
            access: 'isPixalateHuman',
            render: txt => <span>{+txt === -1 ? 'Unlimited' : txt}</span>
          },
          {
            title: 'Max Hm IVT(%)',
            dataIndex: 'max_hm_ivt_ratio',
            tooltip: () => 'Requests exceeding the configured IVT threshold will be blocked',
            access: 'isPixalateHuman',
            render: txt => <span>{+txt === -1 ? 'Unlimited' : txt}</span>
          }
        ]
      }
    ]
  },
  {
    title: 'Endpoint',
    key: 'endpoint',
    access: 'DemandEndpointPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
      onClick: row => {
        history.push(`/demand/advertiser/endpoint?id=${row!.buyer_id}`, {
          row
        });
      }
    },
    children: [
      {
        title: 'USE | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Banner_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Banner_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Banner_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'USE | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Native_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Native_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Native_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'USE | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Video_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Video_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Video_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'APAC | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Banner_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Banner_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Banner_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'APAC | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Native_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Native_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Native_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'APAC | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Video_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Video_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Video_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'EUW | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Banner_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Banner_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Banner_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'EUW | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Native_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Native_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Native_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      },
      {
        title: 'EUW | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Video_url'
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Video_socket_timeout'
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Video_gzip',
            render: _ => <StatusTag value={_} statusDescMap={ZipMap} />
          }
        ]
      }
    ]
  },
  {
    title: 'Account',
    key: 'account',
    access: 'DemandAccountPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
      onClick: row => {
        history.push(`/demand/advertiser/account?id=${row!.buyer_id}&u_id=${row!.user_id}&name=${row!.buyer_name}`, {
          row
        });
      }
    },
    children: [
      {
        title: 'Advertiser Reporting',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            showCopy: true,
            dataIndex: 'reporting_url',
            copyText: row => {
              return row.cs_domain || '';
            },
            render: (txt, params) => {
              return params.cs_domain || '';
            }
          },
          {
            title: 'User Name',
            showCopy: true,
            dataIndex: 'demand_account_name'
          },
          {
            title: 'Status',
            width: 85,
            dataIndex: 'demand_account_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Password',
            dataIndex: 'buyer_id',
            render: (txt, row) => {
              return (
                <a
                  href="#"
                  onClick={e => {
                    e.preventDefault();
                    history.push(
                      `/demand/advertiser/account?id=${row!.buyer_id}&u_id=${row!.user_id}&name=${row!.buyer_id}`,
                      {
                        row
                      }
                    );
                  }}
                >
                  {row.demand_account_status && row.demand_account_status === StatusMap.Active ? 'Edit' : 'Create'}
                  <RixEngineFont type="rix-edit-detail" style={{ marginLeft: 5 }}></RixEngineFont>
                </a>
              );
            }
          }
        ]
      },
      {
        title: 'Reporting API',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'Doc',
            dataIndex: 'buyer_id',
            render: txt => (
              <a target="_blank" href="/help/demandapi-v2" rel="noreferrer">
                Demand Report API
              </a>
            )
          },
          {
            title: 'API Status',
            width: 100,
            dataIndex: 'api_status',
            render: _ => <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
          },
          {
            title: 'Sid',
            showCopy: true,
            dataIndex: 'buyer_id'
          },
          {
            title: 'Token',
            dataIndex: 'token',
            showCopy: true
          }
        ]
      }
    ]
  },
  {
    title: 'Authorization',
    key: 'authorization',
    access: 'DemandAuthorizationPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
      onClick: row => {
        history.push(`/demand/advertiser/auth?buyer_id=${row!.buyer_id}`, {
          row
        });
      }
    },
    type: 'table',
    rowKey: 'auth_seller_id',
    tableData: [],
    columns: [
      {
        title: 'Publisher',
        dataIndex: 'auth_seller_name',
        ellipsis: { showTitle: false },
        width: 220,
        render: _ => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        )
      },
      {
        title: 'Publisher ID',
        width: 220,
        dataIndex: 'auth_seller_id',
        ellipsis: { showTitle: false },
        render: _ => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        )
      },
      {
        title: 'Integration Type',
        width: 150,
        dataIndex: 'integration_type_desc'
      }
    ]
  },
  {
    title: 'Pretargeting',
    key: 'pretargeting',
    access: 'DemandPretargetingPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
      onClick: row => {
        history.push(`/demand/advertiser/pretargeting?id=${row!.buyer_id}`, {
          row
        });
      }
    }
  }
];
