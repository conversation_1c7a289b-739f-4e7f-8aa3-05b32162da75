/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:54:31
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 16:50:28
 * @Description:
 */

import Router from 'koa-router';
import { capController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/cap' });

const routers = router
  .post('/addCap', validTesting, capController.addCap)
  .post('/updateCap', capController.updateCap)
  .post('/getCapList', capController.getCapList);

export default routers;
