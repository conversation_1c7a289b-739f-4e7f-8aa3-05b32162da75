/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:24:47
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-01 17:49:30
 * @LastEditTime: 2023-05-09 22:46:34
 * @FilePath: /saas.rix-platform/server-ts/src/types/supply.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { Context } from 'koa';
import { AppListAPI } from './app-list';

declare namespace SupplyAPI {
  type addSupplySqls = {
    addSeller: string;
    addProfit: string;
    addUser: string;
    addUserRole: string;
    getPermission?: string;
    addPermission?: string;
  };

  type SupplyListItem = {
    seller_id: number;
    seller_name: string;
    integration_type: number;
    status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
    channel_type: string;
    device_type: string;
    relationship: string;
    cus_status: number;
  };

  type AddSupplyParams = {
    seller_name: string;
    seller_account_name: string;
    integration_type: number;
    status: number;
    channel_type: string;
    device_type: string;
    relationship: string;
    tnt_id: number;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    cus_status: number;
    op_id: number;
    tagid_status: number;
    pass_nurl: number;
    pass_burl: number;
    pass_lurl: number;
    rev_track_type: number;
    sp_id: number;
    publisher_id: string;
    support_omid: number;
    adomain_filter: number;
    native_strict_validation: number;
    banner_multi_size: number;
  };

  type UpdateSupplyParams = AddSupplyParams & {
    seller_id: number;
    profit_id: number;
    pass_nurl?: number;
    pass_burl?: number;
    pass_lurl: number;
    rev_track_type?: number;
    profitRatioChange?: boolean;
    user_id: number;
  };

  // type AddSupplyUserParams = {
  //   user_id: number;
  //   op_user_id: number;
  //   tnt_id: number;
  // };
  interface Supply {
    getSupplyList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<SupplyListItem[]>;
    addSupply(params: AddSupplyParams): Promise<boolean>;
    isSupplyNameExists(
      seller_name: string,
      tnt_id: number,
      seller_id?: number
    ): Promise<any[]>;
    isSupplyUserNameExists(
      account_name: string,
      tnt_id: number,
      user_id?: number
    ): Promise<any[]>;
    updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean>;
  }

  interface SupplyModel {
    getSupplyList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<SupplyListItem[]>;
    addSupply(params: AddSupplyParams): Promise<boolean>;
    isSupplyNameExists(
      seller_name: string,
      tnt_id: number,
      seller_id?: number
    ): Promise<any[]>;
    updateSupply(params: string[]): Promise<boolean>;
  }

  interface SupplyCtrlInterface {
    getSupplyList(ctx: Context): void;
    addSupply(ctx: Context): void;
  }

  type SellerDemandAuth = {
    buyer_id: number;
    level: number;
    pub_id: number;
    buyer_name: string;
    status: number;
    integration_type: number;
  };

  type SellerPlacement = AppListAPI.PlacementListItem & {
    demand_list: SellerDemandAuth[];
  };

  type SellerAppItem = AppListAPI.AppListItem & {
    demand_list: SellerDemandAuth[];
  };

  type SetSupplyAuthParams = {
    level: number;
    buyer_ids: number[];
    pub_id: number; // seller_id/app_id/xxx
    op_id: number;
    status: number;
    app_id: number;
    plm_id: number;
    tnt_id: number;
  };
}
