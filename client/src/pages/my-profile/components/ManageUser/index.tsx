/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-22 10:50:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-04-23 17:39:09
 * @Description:
 */
import React, { useEffect, useState, useImperativeHandle } from 'react';
import styles from './index.less';
import { useModel } from '@umijs/max';
import { Form, message } from 'antd';

import OriginalTable from '@/components/Table/OriginalTable';
import PageContainer from '@/components/RightPageContainer';
import AddUser from '../AddUser';
import NormalModal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';

import { ProfileColumnOptions, ProfileListItem } from '@/constants/base';
import type { OperateRenderItem } from '@/components/OperateRender';
import { ColumnProps } from 'antd/es/table';
import { fetchData } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { deleteUser } from '@/services/api';

type Props = {
  onRef: any;
};

const Page: React.FC<Props> = ({ onRef }) => {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRow, setCurrentRow] = useState<ProfileListItem>();
  const [dataSource, setDataSource] = useState<ProfileListItem[]>([]);

  const { dataSource: userList, reload, loading } = useModel('useUserList');

  useImperativeHandle(onRef, () => {
    return {
      handleAddUser: handleAddUser
    };
  });

  const attentionModel = (params: any) => {
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>Are you sure? This user will be permanently deleted and no longer accessible through API.</div>
          <div>Do you want to delete this user?</div>
        </>
      ),
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        const deleteSuccess = () => {
          message.success('Delete success');
          reload();
        };
        fetchData({ request: deleteUser, params: params, onSuccess: deleteSuccess });
      },
      okButtonProps: {
        danger: true
      }
    });
  };
  const handleEditUser = (params: ProfileListItem) => {
    setVisible(true);
    setIsEdit(true);
    setCurrentRow({ ...params, op_user_id: initialState?.currentUser?.user_id as number });
  };
  const handleUserDelete = (params: ProfileListItem) => {
    attentionModel(params);
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditUser,
      icon: <RixEngineFont type="edit" />,
      accessCode: 'editUserAuth'
    },
    {
      label: 'Delete',
      onClick: handleUserDelete,
      icon: <RixEngineFont type="rix-trash" className={styles['delete-btn']} />,
      isDelete: true,
      accessCode: 'deleteUserAuth'
    }
  ];
  const tmpColumns: ColumnProps<ProfileListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 135,
      render: (txt, params) => <OperateRender btnOptions={OperateOptions} params={params} />
    }
  ];

  const column = [...ProfileColumnOptions, ...tmpColumns];
  useEffect(() => {
    reload();
  }, []);
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue(initialState.currentUser);
    }
  }, [initialState?.currentUser]);

  useEffect(() => {
    setDataSource(userList);
  }, [userList]);

  const handleAddUser = () => {
    setIsEdit(false);
    setVisible(true);
  };

  return (
    <PageContainer className={styles['user-page']}>
      <OriginalTable
        loading={loading}
        columns={column}
        dataSource={dataSource}
        rowKey="user_id"
        scroll={{ y: 'calc(100vh - 220px)', x: 600 }}
      />
      <AddUser
        visible={visible}
        isEdit={isEdit}
        setVisible={setVisible}
        rowUserInfo={currentRow}
        reloadUserList={reload}
      />
    </PageContainer>
  );
};

export default Page;
