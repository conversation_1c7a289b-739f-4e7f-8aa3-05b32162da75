import { Context, Next } from 'koa';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';

/**
 * 设置接口超时（针对单独接口）
 * @param time
 * @returns
 */
export const setInterfaceTimeOut = function (time: number) {
  return async function (ctx: Context, next: Next) {
    await Promise.race([
      new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          clearTimeout(timer);
          getLogger('error').error(
            `user_id=${ctx.session?.user_id} tnt_id=${
              ctx.session?.tnt_id
            } ${time} ${new Date().toLocaleString()} Request timeout`
          );
          const result = getCtxResult('REQUEST_TIMEOUT', []);
          ctx.body = result;
          resolve(1);
        }, time);
        // eslint-disable-next-line no-async-promise-executor
      }),
      new Promise(async (resolve, reject) => {
        try {
          await next();
          resolve(1);
        } catch (error: any) {
          getLogger('error').error(
            `user_id=${ctx.session?.user_id}  tnt_id=${
              ctx.session?.tnt_id
            } session_id=${ctx.session?.session_id} error=[${error.message}]}`
          );
          reject(error);
        }
      })
    ]);
  };
};

/**
 * 设置全局接口超时
 * @param time
 * @returns
 */
export const setTimeOutMiddler = (time: number) => {
  return async (ctx: Context, next: Next) => {
    ctx.request.socket.setTimeout(time);
    await next();
  };
};
