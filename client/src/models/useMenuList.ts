/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-10 18:33:24
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-04-06 19:49:11
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllMenu } from '@/services/api';

export default function useMenuList() {
  const { data, run, loading, error } = useCustomRequest(getAllMenu, {
    cacheKey: 'menu-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading
  };
}
