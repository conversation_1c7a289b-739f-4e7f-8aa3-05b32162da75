/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-03 21:33:34
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 18:46:17
 * @Description:
 */

import { Context } from 'koa';
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { dashboardModel } from '@/models';
import {
  DownloadPubAndAdvDashboardScheme,
  PubDashbaordScheme
} from '@/schema/report/dashboard';
import { dashboardService } from '@/services';
import { DashboardAPI } from '@/types/dashboard';
import { transformLimitStr, updateBQConfigAdapter } from '@/utils/report/slim';
import { getCtxBackResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import {
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  includeToday,
  transformOneRowSQL
} from '@rixfe/rix-tools';
import eventStream from 'event-stream';
// @ts-ignore
import JSONStream from 'JSONStream';
import { PassThrough } from 'stream';
import { getUserRole } from '@/utils/report/getBQLabel';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class PubReportCtrl implements DashboardAPI.DashboardCtrlInterface {
  @validateBody(PubDashbaordScheme)
  async getDashboardList(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];

    const { curUser = {} } = ctx.state;
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const seller_id = curUser.seller_id || [];
    formData.seller_id = Array.isArray(seller_id) ? seller_id : [seller_id];

    if (!formData.seller_id.length) {
      result = getCtxBackResult('SUCCESS', { total: 0, data: [] });
      ctx.body = result;
      return;
    }
    const userType = ctx.state.curUser?.type;
    const list = await dashboardService.getNewDashboardList(
      formData,
      { tag: api_url, tenantId: formData.tnt_id, userRole: getUserRole(userType) },
      { reportType: 'publisher' }
    );
    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(DownloadPubAndAdvDashboardScheme)
  async downloadAllReport(ctx: Context) {
    const result = { ...RESULT };
    const {
      curUser = {},
      api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0]
    } = ctx.state;

    const seller_id = curUser.seller_id || [];

    const formData = {
      ...ctx.request.body,
      tnt_id: ctx.session!.tnt_id,
      seller_id: seller_id.length ? seller_id : [seller_id]
    };

    if (!formData.seller_id.length) {
      ctx.body = [];
      return;
    }

    const {
      metrics,
      split_time,
      start_date,
      end_date,
      cur_time_zone,
      tnt_id,
      hour
    } = formData;

    const labels = {
      tag: api_url,
      tenantId: tnt_id
    };

    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments, info } = await adaptGenerateBigQuerySQL(
      formData,
      // @ts-ignore
      labels
    );

    const sql = concatSQLFragments({
      ...fragments,
      // 最多查询10000条
      limit: transformLimitStr(tnt_id, '', true)
    });

    // 判断表类型
    const isDailyTable = info.isDaily;
    const isDemandTable = info.isDemand;

    const [data, today_hours] = await Promise.all([
      // 查询列表数据
      dashboardModel.newDownloadAllReport(sql, labels),
      // 对于非daily表单独查询今日小时数
      !isDailyTable
        ? dashboardModel.getNewHoursToday(
            tnt_id,
            fragments.from,
            cur_time_zone,
            labels
          )
        : 0
    ]);
    if (data) {
      ctx.body = data
        .on('error', (err: any) => {
          getLogger('error').error(`downloadPubDashboardList error: [${err?.message}]`);
          data.end();
          ctx.body = result;
        })
        .on('end', () => {
          data.end();
        })
        .pipe(
          eventStream.mapSync((data: any, index: number) => {
            const item = transformOneRowSQL(data, {
              metrics: metrics || [],
              context: {
                start_date,
                end_date,
                today_hours,
                isDemand: isDemandTable,
                hour,
                isToday: includeToday(start_date, end_date),
                split_time,
                timezone: cur_time_zone,
                isSupplyReport: true
              }
            });
            return item;
          })
        )
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}

export default new PubReportCtrl();
