/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:45:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 14:33:43
 * @Description:
 */

import { Context } from 'koa';
import { commonService, dashboardService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult, getCtxBackResult } from '@/utils/response';
import { CommonAPI } from '@/types/common';
import { validateBody } from '@/utils/validate-params';
import {
  WelComeDashboardScheme,
  UpdateMsgStatusScheme
} from '@/schema/report/dashboard';
import { getUserRole } from '@/utils/report/getBQLabel';
const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class CommonCtrl implements CommonAPI.CommonCtrl {
  async getBuyerIntegrationType(ctx: Context) {
    let result = { ...RESULT };
    const data = await commonService.getBuyerIntegrationType();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getSellerIntegrationType(ctx: Context) {
    let result = { ...RESULT };
    const data = await commonService.getSellerIntegrationType();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(WelComeDashboardScheme)
  async getWelcomeDashboardList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const list = await dashboardService.getNewDashboardList(
      formData,
      { tag: api_url, tenantId: formData.tnt_id, userRole: getUserRole(userType) },
      { isAll: true }
    );
    if (Array.isArray(list.data)) {
      result = getCtxBackResult('SUCCESS', list);
    } else if (typeof list === 'string') {
      result = getCtxBackResult('PARAMS_INVALID', list);
    }

    ctx.body = result;
  }

  async getNotificationList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const { user_id } = ctx.session!;
    const { cur_time_zone } = ctx.request.body;

    const list = await commonService.getNotificationList(
      tnt_id,
      user_id,
      cur_time_zone
    );

    if (Array.isArray(list)) {
      result = getCtxResult('SUCCESS', list);
    } else if (typeof list === 'string') {
      result = getCtxResult('PARAMS_INVALID', list);
    }

    ctx.body = result;
  }

  @validateBody(UpdateMsgStatusScheme)
  async updateNotificationStatus(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.user_id = ctx.session!.user_id;
    const res = await commonService.updateNotificationStatus(formData);
    if (res) {
      result = getCtxResult('SUCCESS', res);
    } else {
      result = getCtxResult('PARAMS_INVALID', res);
    }
    ctx.body = result;
  }

  async getBrandInfo(ctx: Context) {
    let result = { ...RESULT };
    const { cs_domain } = ctx.state;
    const data = await commonService.getBrandInfo({ cs_domain });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getFavicon(ctx: Context) {
    const { cs_domain } = ctx.state;
    const stream = await commonService.getFavicon({ cs_domain });
    ctx.set('content-type', 'image/png');
    ctx.body = stream;
  }

  async getLogo(ctx: Context) {
    const { cs_domain } = ctx.state;
    const stream = await commonService.getLogo({ cs_domain });
    ctx.set('content-type', 'image/png');
    ctx.body = stream;
  }
}

export default new CommonCtrl();
