import { FormInstance } from 'antd';
import { useCallback, useRef } from 'react';
import { ProfitType } from '@/constants/strategy/profit';
import { ProfitTab } from '@/constants/strategy/profit';

type UseProfitJumpProps = {
  searchFormRef: React.RefObject<FormInstance<any>>;
  onTabChange: (tab: string) => void;
};

export const useProfitJump = ({ searchFormRef, onTabChange }: UseProfitJumpProps) => {
  // 用于标记是否是通过跳转到Bundle页的
  const isJumpToBundleRef = useRef(false);

  // 跳转到Bundle页面并设置搜索条件
  const jumpToBundle = useCallback((params: any) => {
    // 切换到Bundle标签页
    onTabChange(ProfitTab.bundle);
    
    // 设置搜索表单的值
    const formValues = {
      buyer_id: [params.buyer_id],
      ...(params.type !== ProfitType.Demand && {
        seller_id: [params.seller_id, 0]
      })
    };
    
    searchFormRef.current?.setFieldsValue(formValues);
    
    // 标记为跳转状态，以便在切换完成后自动触发搜索
    isJumpToBundleRef.current = true;
  }, [onTabChange, searchFormRef]);

  // 检查是否需要触发自动搜索（用于Tab切换后）
  const checkAndTriggerSearch = useCallback(() => {
    if (isJumpToBundleRef.current) {
      isJumpToBundleRef.current = false;
      // 宏任务，确保在Tab切换后执行
      setTimeout(() => {
        searchFormRef.current?.submit();
      }, 10);
      return true;
    }
    return false;
  }, [searchFormRef]);

  return {
    jumpToBundle,
    checkAndTriggerSearch,
    isJumpToBundleRef
  };
}; 
