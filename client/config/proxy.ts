/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-11 09:56:46
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-18 13:01:36
 * @Description:
 */
/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    // localhost:8000/api/** -> http://127.0.0.1:3100/api/**
    '/api/': {
      // 要代理的地址
      target: 'https://127.0.0.1:4090',
      // target: 'http://a.console-t.rixengine.com',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
      // 添加以下配置来处理 HTTPS 证书问题
      secure: false,  // 如果是自签名证书需要设置为 false
      rejectUnauthorized: false,  // 忽略证书错误
    },
    '/backend-api/': {
      target: 'http://127.0.0.1:4090',
      changeOrigin: true
    },
    '/webroot/': {
      // 重定向到本地的 webroot 目录
      target: 'http://127.0.0.1:4090',
      changeOrigin: true
    }
  }
};
