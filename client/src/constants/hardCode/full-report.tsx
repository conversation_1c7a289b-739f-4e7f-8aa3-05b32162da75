/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-15 16:44:39
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:58:35
 * @Description:
 */
import { TopBarSearchItem } from '@/components/TopBar';
import { DashboardDefaultColumnKeys, DashboardDefaultDimension, DashBoardDefaultMetrics, DashboardSearchOption, AllDimensionOptions, AllMetricsOptions, MetricsOptions, DimensionsOptions } from '../data-report';
export const ExtraSearchOption: TopBarSearchItem[] = [
  {
    name: 'App Name',
    key: 'app_name',
    type: 'select',
    mode: 'multiple',
    value: [],
    options: []
  }
];
// 额外增加的维度，目前只用于1050, 1075
// export const ExtraDimensionOptions = [{ label: 'App Name', value: 'app_name' }];
export const ExtraDimensions = ['app_name', 'seller_schain_hop', 'seller_schain_complete'];

// !通过租户自定义的角色id来自定义的租户,目前只有1053下面的Data_Custom角色,id为34
export const customTenant = [1053, 0];
export const customRole: { [key: number]: number[] } = {
  1053: [34]
};

// 自定义整个租户dashboard
// 夏兰：1059和1071的租户已经暂停合作了。暂时不用维护自定义的功能了
export const customDashboard = [1050, 1059, 1057, 1071, 1075, 1052, 1077, 1093];
// 租户的自定义信息
export const customConfig: { [key: number | string]: any } = {
  1052: {
    SearchOption: [...DashboardSearchOption.map(item => item.key)],
    Dimensions: [...AllDimensionOptions.map(item => item.value)],
    Metrics: [...AllMetricsOptions.map(item => item.value)],
    DefaultColumnKeys: [...DashboardDefaultColumnKeys],
    DefaultMetrics: [...DashBoardDefaultMetrics],
    DefaultDimension: [...DashboardDefaultDimension]
  },
  1053: {
    SearchOption: [
      'date',
      'buyer_id',
      'seller_id',
      'app_bundle_id',
      // 'placement_id ',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'device_type',
      'columns',
      'metrics'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'device_type',
    ],
    Metrics: [
      'buyer_net_revenue',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      'total_seller_bid_floor',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_revenue',
      // 'seller_net_revenue',
      'buyer_net_ecpm',
    ],
    DefaultMetrics: ['request', 'response', 'impression', 'click', 'fill_rate', 'buyer_net_revenue', 'buyer_net_ecpm'],
    DefaultDimension: ['day']
  },
  1050: {
    SearchOption: [
      'date',
      'hour',
      'buyer_id',
      'seller_id',
      'app_bundle_id',
      // 'placement_id ',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'app_name',
      'columns',
      'metrics',
      'ad_domain',
      'partner_id',
      'instl',
      'device_type',
      'inventory'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'ad_domain',
      'device_type',
      'inventory'
    ],
    Metrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'profit',

      'request',
      'total_request',
      'block_request',
      'out_request',

      'response',
      'win',

      'impression',
      'seller_payment_impression',
      'click',
      'total_seller_bid_floor',
      // 'buyer_gross_revenue',
      // 'seller_gross_revenue',
      'ecpr',
      'adv_ecpr',

      'fill_rate',
      'click_rate',
      'profit_rate',
      'win_rate',
      // 'buyer_gross_ecpm',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      // 'impression_rate',
      'adv_config_qps',
      'pub_config_qps',
      'real_qps',
      'bid_price',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_revenue',
      // 'placement_id',
      // 'seller_net_revenue',
      'buyer_net_ecpm'
    ],
    DefaultMetrics: [
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'buyer_net_revenue',
      // 'seller_net_revenue',
      'buyer_net_ecpm',
      // 'impression_rate'
    ],
    DefaultDimension: ['day']
  },
  1059: {
    SearchOption: [
      'date',
      'hour',
      'buyer_id',
      'seller_id',
      'app_bundle_id',
      // 'placement_id ',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'columns',
      'metrics',
      'ad_domain',
      'partner_id',
      'instl',
      'device_type',
      'inventory'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'ad_domain',
      'device_type',
      'inventory'
    ],
    Metrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'profit',

      'request',
      'total_request',
      'block_request',
      'out_request',

      'response',
      'win',

      'impression',
      'seller_payment_impression',
      'click',
      'total_seller_bid_floor',
      'buyer_gross_revenue',
      'seller_gross_revenue',
      'ecpr',
      'adv_ecpr',
      'fill_rate',
      'click_rate',
      'profit_rate',
      'win_rate',
      'buyer_gross_ecpm',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      // 'impression_rate',
      'adv_config_qps',
      'pub_config_qps',
      'real_qps',
      'bid_price',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_revenue',
      'placement_id',
      // 'seller_net_revenue',
      'buyer_net_ecpm'
    ],
    DefaultMetrics: [
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'buyer_net_revenue',
      // 'seller_net_revenue',
      'buyer_net_ecpm',
      // 'impression_rate'
    ],
    DefaultDimension: ['day']
  },
  1057: {
    SearchOption: [
      'date',
      'hour',
      'buyer_id',
      'seller_id',
      'app_bundle_id',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'columns',
      'metrics',
      'partner_id',
      'instl',
      'device_type',
      'inventory'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'device_type',
      'inventory'
    ],
    Metrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'profit',
      'request',
      'total_request',
      'block_request',
      'out_request',
      'response',
      'win',
      'impression',
      'seller_payment_impression',
      'click',
      'total_seller_bid_floor',
      // 'buyer_gross_revenue',
      // 'seller_gross_revenue',
      'ecpr',
      'adv_ecpr',
      'fill_rate',
      'click_rate',
      'profit_rate',
      'win_rate',
      // 'buyer_gross_ecpm',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      // 'impression_rate',
      'adv_config_qps',
      'pub_config_qps',
      'real_qps',
      'bid_price',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_revenue',
      // 'placement_id',
      // 'seller_net_revenue',
      'buyer_net_ecpm'
    ],
    DefaultMetrics: [
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'buyer_net_revenue',
      // 'seller_net_revenue',
      'buyer_net_ecpm',
      // 'impression_rate'
    ],
    DefaultDimension: ['day']
  },
  1071: {
    SearchOption: [
      'date',
      'hour',
      'buyer_id',
      'seller_id',
      'app_bundle_id',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'columns',
      'metrics',
      'ad_domain',
      'partner_id',
      'instl',
      'device_type',
      'inventory'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'ad_domain',
      'device_type',
      'inventory'
    ],
    Metrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'profit',
      'request',
      'total_request',
      'block_request',
      'out_request',
      'response',
      'win',

      'impression',
      'seller_payment_impression',
      'click',
      'total_seller_bid_floor',
      'buyer_gross_revenue',
      // 'seller_gross_revenue',
      'ecpr',
      'adv_ecpr',
      'fill_rate',
      'click_rate',
      'profit_rate',
      'win_rate',
      // 'buyer_gross_ecpm',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      // 'impression_rate',
      'adv_config_qps',
      'pub_config_qps',
      'real_qps',
      'bid_price',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'click_rate',
      // 'impression_rate',
      'buyer_net_revenue',
      'placement_id',
      // 'seller_net_revenue',
      'buyer_net_ecpm'
    ],
    DefaultMetrics: [
      'request',
      'response',
      'impression',
      'click',
      'fill_rate',
      'buyer_net_revenue',
      // 'seller_net_revenue',
      'buyer_net_ecpm',
      // 'impression_rate'
    ],
    DefaultDimension: ['day']
  },
  1075: {
    SearchOption: [
      'date',
      'hour',
      'buyer_id',
      'seller_id',
      'adv_partner_id',
      'pub_partner_id',
      'res_crid',
      'res_cid',
      'app_bundle_id',
      'ad_format',
      'ad_size',
      'platform',
      'country',
      'region',
      'placement_id',
      'columns',
      'metrics',
      'ad_domain',
      'partner_id',
      'instl',
      'device_type',
      'inventory'
    ],
    Dimensions: [
      'day',
      'day_hour',
      'seller_id',
      'buyer_id',
      'app_bundle_id',
      'placement_id',
      'ad_format',
      'ad_size',
      'country',
      'platform',
      'region',
      'ad_domain',
      'device_type',
      'inventory',
      'res_crid',
      'seller_schain_hop',
      'seller_schain_complete'
    ],
    Metrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'profit',
      'request',
      'total_request',
      'block_request',
      'out_request',
      'response',
      'win',
      'impression',
      'seller_payment_impression',
      'click',
      'total_seller_bid_floor',
      'buyer_gross_revenue',
      'seller_gross_revenue',
      'ecpr',
      'adv_ecpr',
      'fill_rate',
      'click_rate',
      'profit_rate',
      'win_rate',
      // 'buyer_gross_ecpm',
      'buyer_net_ecpm',
      'seller_net_ecpm',
      // 'impression_rate',
      'adv_config_qps',
      'pub_config_qps',
      'real_qps',
      'bid_price',
      'avg_dsp_cost_time',
      'avg_response_cost_time'
    ],
    DefaultColumnKeys: [
      'day',
      'seller_id',
      'buyer_id',
      'ad_format',
      'ad_size',
      'country',
      'region',
      'buyer_net_revenue',
      'seller_net_revenue',
      'request',
      'total_request',
      'response',
      'win',
      'impression',
      'seller_payment_impression'
    ],
    DefaultMetrics: [
      'buyer_net_revenue',
      'seller_net_revenue',
      'request',
      'total_request',
      'response',
      'win',
      'impression',
      'seller_payment_impression'
    ],
    DefaultDimension: ['day', 'seller_id', 'buyer_id', 'ad_format', 'ad_size', 'country', 'region']
  },
  1077: {
    SearchOption: [...DashboardSearchOption.map(item => item.key)],
    Dimensions: [...AllDimensionOptions.map(item => item.value)],
    Metrics: [...MetricsOptions.map(item => item.value), 'cpc'],
    DefaultColumnKeys: [...DashboardDefaultColumnKeys],
    DefaultMetrics: [...DashBoardDefaultMetrics],
    DefaultDimension: [...DashboardDefaultDimension]
  },
  1093: {
    SearchOption: [...DashboardSearchOption.map(item => item.key)],
    Dimensions: [...AllDimensionOptions.map(item => item.value)],
    Metrics: [...MetricsOptions.map(item => item.value), 'buyer_gross_revenue', 'seller_gross_revenue'],
    DefaultColumnKeys: [...DashboardDefaultColumnKeys],
    DefaultMetrics: [...DashBoardDefaultMetrics],
    DefaultDimension: [...DashboardDefaultDimension]
  },
};

// 需要自定义名称的维度
export const customDimensionName: { [key: number]: { [key: string]: string } } = {
  1053: {
    placement_id: 'Unit Name (ID)'
  }
};
