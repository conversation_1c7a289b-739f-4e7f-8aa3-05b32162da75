/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 15:28:42
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 17:37:21
 * @Description:
 */

import { Context } from 'koa';
import { partnerService, userService } from '@/services';
import { getLogger } from '@/config/log4js';
import {
  AddPartnerSchema, CreatePartnerAccountSchema, GetPartnerAccountSchema, UpdatePartnerSchema
} from '@/schema/partner';
import { validateBody } from '@/utils/validate-params';
import { RESULT } from '@/constants';
import { getCtxResult } from '@/utils/response';

class PartnerCtrl {
  @validateBody(AddPartnerSchema)
  async addPartner(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const flag = await partnerService.isPartnerExists(formData);
    if (flag) {
      result = getCtxResult('PARTNER_NAME_EXISTS', []);
    } else {
      const data = await partnerService.addPartner(formData);
      if (data) {
        result = getCtxResult('SUCCESS', data);
        getLogger('app').info(`addPartner params=[${JSON.stringify(formData)}]`);
      }
    }
    ctx.body = result;
  }

  @validateBody(UpdatePartnerSchema)
  async updatePartner(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const flag = await partnerService.isPartnerExists(formData);
    if (flag) {
      result = getCtxResult('PARTNER_NAME_EXISTS', []);
    } else {
      const data = await partnerService.updatePartner(formData);
      if (data) {
        result = getCtxResult('SUCCESS', data);
        getLogger('app').info(`updatePartner params=[${JSON.stringify(formData)}]`);
      }
    }
    ctx.body = result;
  }

  async getPartnerList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = (ctx.session!);
    const data = await partnerService.getPartnerList(tnt_id, formData.cur_time_zone);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(GetPartnerAccountSchema)
  async getPartnerAccount(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = (ctx.session!);
    formData.tnt_id = tnt_id;
    const data = await partnerService.getPartnerAccount(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(CreatePartnerAccountSchema)
  async createPartnerAccount(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const {
      brand = '', host_prefix = '', pv_domain = '', user_email = ''
    } = ctx.state.curUser || {};
    formData.brand = brand || 'RixEngine';
    formData.host_prefix = host_prefix;
    formData.pv_domain = pv_domain;
    formData.user_email = user_email;
    const flag = await userService.isAccountNameExists(formData);
    if (flag?.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
    } else {
      const data = await partnerService.createAccount(formData);
      if (data) {
        result = getCtxResult('SUCCESS', data);
      }
    }
    ctx.body = result;
  }
}

export const partnerCtrl = new PartnerCtrl();
