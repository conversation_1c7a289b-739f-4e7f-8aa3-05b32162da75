import OperateRender, { OperateRenderItem } from '@/components/OperateRender';
import { ProfitTab, ProfitType } from '@/constants/strategy/profit';
import { PlusOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import { useCallback } from 'react';

type ButtonType = {
  label: string;
  type: 'primary' | 'default';
  size: 'small' | 'middle' | 'large';
  onClick: (params: any) => void;
  icon?: React.ReactNode;
  accessCode?: string;
};

type UseTableOperationsProps = {
  currentTab: string;
  onEdit: (params: any) => void;
  onCreate: (params: any) => void;
};

export const useTableOperations = ({ currentTab, onEdit, onCreate }: UseTableOperationsProps) => {
  // 获取操作按钮配置
  const getOperationButtons = useCallback((): OperateRenderItem[] => {
    const isBundleTab = currentTab === ProfitTab.bundle;
    
    return [
      {
        label: 'Add',
        icon: <PlusOutlined />,
        onClick: onCreate,
        hide(params) {
          return [ProfitType['Seller-Demand'], ProfitType.DemandBundle, ProfitType.DemandSellerBundle].includes(
            params.type
          );
        },
        accessCode: 'addProfitAuth'
      },
      {
        label: 'Edit',
        onClick: onEdit,
        accessCode: isBundleTab ? 'updateBundleProfit' : 'updateProfitAuth'
      }
    ];
  }, [currentTab, onCreate, onEdit]);

  // 获取操作列配置
  const getOperationColumn = useCallback(() => {
    const isBundleTab = currentTab === ProfitTab.bundle;
    const btnOptions = getOperationButtons();
    
    return {
      title: 'Operation',
      dataIndex: 'operate',
      width: isBundleTab ? 100 : 120,
      fixed: 'right',
      render: (txt: any, params: any) => <OperateRender btnOptions={btnOptions} params={params} isExpendTable={true} />
    } as ColumnProps<any>;
  }, [currentTab, getOperationButtons]);

  // 获取表格扩展按钮
  const getExpandButtons = useCallback((): ButtonType[] => {
    if (currentTab === ProfitTab.bundle) {
      return [
        {
          label: 'Add Bundle Profit',
          type: 'primary',
          size: 'small',
          onClick: () => onCreate({ type: ProfitType.DemandBundle }),
          icon: <PlusOutlined />,
          accessCode: 'addBundleProfit'
        }
      ];
    }
    return [];
  }, [currentTab, onCreate]);

  return {
    getOperationButtons,
    getOperationColumn,
    getExpandButtons
  };
}; 
