/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:45:32
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-10 10:27:36
 * @Description:ion.tnt_id
 */

import { StatusMap } from '@/constants/strategy';
import { pmsUserModel } from '@/models';
import { UserAPI } from '@/types/user';
import { array2Object } from '@/utils';

class UserService {
  async addOneUser(
    options: UserAPI.addUserParams,
    operator: number
  ): Promise<boolean> {
    return !!(await pmsUserModel.addOneUser(options, operator));
  }

  async getUserList(
    params: UserAPI.UserListItem
  ): Promise<UserAPI.UserListItem[]> {
    const data: any[] = await pmsUserModel.getUserList(params);
    if (data?.length) {
      // 查用户的权限
      const role_ids = data.map((v: any) => v.role_id);
      const op_user_id: any[] = [
        ...new Set(data.map((v: any) => v.op_user_id))
      ];
      const [opUsers, roles] = await Promise.all([
        pmsUserModel.getUserByIds(op_user_id, params.tnt_id),
        pmsUserModel.getRoleByIds(role_ids, params.tnt_id)
      ]);
      const opMap: any = array2Object(opUsers, 'user_id');
      const roleObj: any = array2Object(roles, 'role_id');
      return data.map(v => {
        const role = roleObj[v.role_id];
        const user = opMap[v.op_user_id];
        return {
          ...v,
          // role_id: role?.role_id || null,
          role_name: role?.role_name || '',
          op_user_name:
            user?.status === StatusMap.Deleted ? 'Unknown' : user?.account_name,
          op_status: user?.status || StatusMap.Deleted,
          permission: role?.permissions || []
        };
      });
    }
    return data;
  }

  async getUserListCount(tnt_id: number): Promise<number> {
    return await pmsUserModel.getUserListCount(tnt_id);
  }

  async editUser(params: UserAPI.EditUserParams): Promise<boolean> {
    if (Number(params.roleChange) === 1) {
      return await pmsUserModel.editUserTransaction(params);
    }
    return await pmsUserModel.editUser(params);
  }

  async deleteUser(params: UserAPI.DeleteUserParams): Promise<boolean> {
    return await pmsUserModel.deleteUser(params);
  }
}

export const pmsUserService = new UserService();
