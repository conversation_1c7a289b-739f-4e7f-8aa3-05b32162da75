/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-19 16:15:59
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:54:24
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getCapList } from '@/services/api';

export default function useCapList() {
  const { data, run, loading, error } = useCustomRequest(getCapList, {
    cacheKey: 'cap-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
