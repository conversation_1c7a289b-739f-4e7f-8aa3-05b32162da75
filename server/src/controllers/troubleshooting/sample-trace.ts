/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-22 11:31:25
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 12:20:11
 * @Description:
 */
import { Context } from 'koa';
import { sampleTraceService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { TroubleShootingAPI } from '@/types/troubleshooting';
import {
  addTraceTaskSchema,
  updateTraceTaskSchema
} from '@/schema/troubleshooting';
import { genEnCode, md5 } from '@/utils';
import { RESULT } from '@/constants';

class SampleTraceCtrl implements TroubleShootingAPI.SampleTraceCtrlInterface {
  async getSampleTraceTaskList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const { cur_time_zone } = ctx.request.body;
    const curUser = ctx.state.curUser || {};
    const list = await sampleTraceService.getSampleTraceTaskList(
      tnt_id,
      cur_time_zone,
      curUser,
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(addTraceTaskSchema)
  async addSampleTraceTask(ctx: Context) {
    let result = { ...RESULT };
    const fromData = ctx.request.body;
    const tag_id = md5(`${new Date().getTime()}_${genEnCode(8)}`);
    fromData.tnt_id = ctx.session!.tnt_id;
    fromData.op_id = ctx.session!.user_id;
    fromData.tag_id = tag_id;
    const data = await sampleTraceService.addSampleTraceTask(fromData);
    if (data) {
      result = getCtxResult('SUCCESS');
      ctx.body = result;
      return;
    }
    ctx.body = result;
  }

  async getSampleTraceList(ctx: Context) {
    let result = { ...RESULT };
    const fromData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    const { cur_time_zone } = ctx.request.body;
    fromData.tnt_id = tnt_id;
    fromData.cur_time_zone = cur_time_zone;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const list = await sampleTraceService.getSampleTraceList(fromData, api_url);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(updateTraceTaskSchema)
  async updateSampleTraceTask(ctx: Context) {
    let result = { ...RESULT };
    const fromData = ctx.request.body;
    const curUser = ctx.state.curUser || {};

    const data = await sampleTraceService.updateSampleTraceTask(fromData, curUser);
    if (data) {
      result = getCtxResult('SUCCESS');
    }
    ctx.body = result;
  }
}

export const sampleTraceCtrl = new SampleTraceCtrl();
