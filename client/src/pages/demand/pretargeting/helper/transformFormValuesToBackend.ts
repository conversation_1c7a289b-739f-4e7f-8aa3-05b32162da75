/**
 * 将表单数据转换为后端接口所需的格式
 *
 * @description 该函数将前端表单的键值对数据转换为后端API期望的格式：
 * - 提取基础信息（如 campaign_name）
 * - 将定向字段及其级别信息转换为 items 数组格式
 * - 每个定向字段需要有对应的 level 字段才会被处理
 *
 * @param formValues - 表单数据对象，包含字段值和对应的级别信息
 * @returns 转换后的数据对象，包含 baseInfo 和 items 数组
 *
 * @example
 * ```typescript
 * const formData = {
 *   campaign_name: 'Test Campaign',
 *   country: ['US', 'CN'],
 *   'country-level': 1,
 *   device: ['mobile'],
 *   'device-level': 2
 * };
 *
 * const result = transformFormValuesToBackend(formData);
 * // 返回:
 * // {
 * //   campaign_name: 'Test Campaign',
 * //   items: [
 * //     { level: 1, content: '["US","CN"]' },
 * //     { level: 2, content: '["mobile"]' }
 * //   ]
 * // }
 * ```
 */
export const transformFormValuesToBackend = (formValues: Record<string, any>) => {
  // 提取基础信息
  const baseInfo = {
    campaign_name: formValues.campaign_name?.trim()
  };

  const items: { level: number; content: string }[] = [];

  // 遍历表单数据，识别并处理定向字段
  Object.keys(formValues).forEach(key => {
    // 匹配不包含连字符的字段名（排除 level 字段）
    const baseFieldMatch = key.match(/^([^-]+)$/);

    if (baseFieldMatch) {
      const baseField = baseFieldMatch[1];
      const levelKey = `${baseField}-level`;

      // 获取字段值和对应的级别
      const level = formValues[levelKey];
      const content = formValues[key];
      // 除了 undefined 和 null，还有空数组的情况
      if (
        level === undefined ||
        level === null ||
        content === undefined ||
        content === null ||
        content === '' ||
        content === 0 ||
        content.length === 0
      ) {
        return;
      }

      items.push({
        level: Number(level),
        content: content.toString()
      });
    }
  });

  return {
    baseInfo,
    items
  };
};
