import useUrlState, { type Options as UseUrlStateOptionsType } from '@ahooksjs/use-url-state';
import { useCallback } from 'react';

export const ProfitTabOptions = [
  { label: 'Advertiser Profit', value: 'advertiser' },
  { label: 'Publisher Profit', value: 'publisher' },
  { label: 'Bundle Profit', value: 'bundle' }
];
export const ProfitTab = {
  publisher: 'publisher',
  advertiser: 'advertiser',
  bundle: 'bundle'
} as const;

const CurrentTabOptions = ['advertiser', 'publisher', 'bundle'] as const;
const DefaultCurrentTab = CurrentTabOptions[0];
const UrlStateOptions: UseUrlStateOptionsType = {
  navigateMode: 'replace'
};

export type CurrentTab = typeof CurrentTabOptions[number];

/**
 * 自定义 hook 管理 tab 的 url 状态
 * @param initialState 初始状态
 * @returns [当前 tab, 设置当前 tab]
 */
export const useCurrentTabState = (initialState: {
  currentTab: CurrentTab;
}): [CurrentTab, (tab: CurrentTab) => void] => {
  const [urlState, setUrlState] = useUrlState(initialState, UrlStateOptions);

  // 校验是否是否合法，不合法返回默认 advertiser
  const currentTab: CurrentTab = CurrentTabOptions.includes(urlState.currentTab)
    ? urlState.currentTab
    : DefaultCurrentTab;

  const setCurrentTab = useCallback(
    (tab: CurrentTab) => {
      setUrlState({ currentTab: tab });
    },
    [setUrlState]
  );

  return [currentTab, setCurrentTab];
};
