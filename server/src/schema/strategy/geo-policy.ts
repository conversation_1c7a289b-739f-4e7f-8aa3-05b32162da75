import { GeoPolicyAPI } from '@/types/geo-policy';
import Joi from 'joi';

// ==== 新增关联 ====
const addGeoPolicyKeyRelationSchema = {
  policy_key_id: Joi.number().integer().required(),
  seller_id: Joi.number().integer().required()
};

export const AddGeoPolicyKeyRelationSchema =
  Joi.object<GeoPolicyAPI.AddGeoPolicyKeyRelationSchema>({
    ...addGeoPolicyKeyRelationSchema
  }).required();

export const UpdateGeoPolicyKeyRelationSchema =
  Joi.object<GeoPolicyAPI.UpdateGeoPolicyKeyRelationSchema>({
    id: Joi.number().integer().required(),
    status: Joi.number().integer().required(),
    ...addGeoPolicyKeyRelationSchema
  }).required();

// ==== 新增 policy key ====
const addGeoPolicyKeySchema = {
  policy_key: Joi.string().required(),
  remark: Joi.string().required(),
  is_default: Joi.number().integer().required()
};

export const AddGeoPolicyKeySchema =
  Joi.object<GeoPolicyAPI.AddGeoPolicyKeySchema>({
    ...addGeoPolicyKeySchema
  }).required();

export const UpdateGeoPolicyKeySchema =
  Joi.object<GeoPolicyAPI.UpdateGeoPolicyKeySchema>({
    id: Joi.number().integer().required(),
    status: Joi.number().integer().required(),
    ...addGeoPolicyKeySchema
  }).required();
