/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-04 18:31:34
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 10:51:30
 * @Description:
 */

// ?libs
import { useEffect, useRef, useState } from 'react';
import { useModel, useSearchParams } from 'umi';
import moment, { Moment } from 'moment';

// ?types
import { SorterResult } from 'antd/lib/table/interface';
import type { SearchResultItem, TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';

// ?components
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';

// ?constants
import {
  BreadOptions,
  SearchOptions,
  DefaultColumnKeys,
  DefaultMetrics,
  AllColumns,
  ABTestTypeOptions,
  FormatExportValueMap,
  FixedDimension
} from '@/constants/data-report/abtest-report';

// ?utils
import { fetchData, downloadCsv } from '@/utils';

// ?api
import { getABTestReportList } from '@/services/api';
import { checkMetrics } from '@/utils/report';
import { DateType } from '@/constants/data-report';
import { formatExportData } from '@/utils/export-file';

const getDefaultAbTestValues = (type: number, customTntId: number) => {
  if (customTntId === 1046) {
    return ABTestTypeOptions.find(item => item.label === 'op_bidfloor')!;
  }
  return ABTestTypeOptions.find(item => item.value === type) || ABTestTypeOptions[0];
};

export default function Advertiser() {
  const { dataSource: ABTestList, reload: reloadABtest } = useModel('useABTestList');
  const { fetchAdSize, adSizeMapByValue } = useModel('useAdSizeOptions');
  const [searchOptions, setSearchOptions] = useState<TopBarSearchItem[]>(SearchOptions);
  const [dataSource, setDataSource] = useState<API.BackResult<DashboardAPI.ABtestReportItem>>({ data: [], total: 0 });
  const [columnKeys, setColumnKeys] = useState(DefaultColumnKeys);
  const [columns, setColumns] = useState<ColumnProps<DashboardAPI.ABtestReportItem>[]>(AllColumns);
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [sortedInfo, setSortedInfo] = useState<SorterResult<DashboardAPI.ABtestReportItem>>({
    columnKey: 'buyer_net_revenue',
    order: 'descend'
  });
  const { initialState } = useModel('@@initialState');
  const customTntId = useRef<number>(0);
  useEffect(() => {
    // 如果当前用户的租户为1046，只要 BidFloor
    customTntId.current = initialState?.currentUser?.tnt_id || 0;
    if ([1046].includes(customTntId.current)) {
      customTntId.current = 1046;
      setSearchOptions(previous => {
        // 修改key为 test_tag_a 的 options，然后返回新的 searchOptions
        return previous.map(option => {
          if (option.key === 'test_tag_a') {
            return {
              ...option,
              options: [
                {
                  label: 'Bid Floor',
                  value: 'op_bidfloor'
                }
              ]
            };
          }
          return option;
        });
      });
    }
  }, [initialState]);
  const [allColumns, setAllColumns] = useState<ColumnProps<DashboardAPI.ABtestReportItem>[]>(AllColumns);
  const [totalCount, setTotalCount] = useState<number>(0);
  // const { state } = useLocation() as { state: { row: StrategyAPI.ABTestListItem } };
  const [searchParams] = useSearchParams();
  useEffect(() => {
    handleFillColumns();
    if (!ABTestList || !ABTestList.length) {
      reloadABtest();
    }
    fetchAdSize();
  }, []);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (Array.isArray(ABTestList) && Object.keys(adSizeMapByValue).length > 0) {
      handleSearchOptions();
    }
  }, [ABTestList, adSizeMapByValue]);
  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<DashboardAPI.ABtestReportItem>[] = AllColumns.map(v => {
        return {
          ...v,
          sortOrder: v.sorter && sortedInfo.columnKey === v.dataIndex ? sortedInfo.order : null
        };
      });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);
  // 默认的值
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));

    const index = options.findIndex(item => item.key === 'date');
    const abTestType = Number(searchParams.get('type'));
    const abTestId = Number(searchParams.get('id'));
    const { label: defaultAbTestType, value: defaultAbTestId } = getDefaultAbTestValues(
      abTestType,
      customTntId.current
    );
    if (index !== -1) {
      const lastDay = moment().format('YYYYMMDD');
      const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
      options[index].value = [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')];
      const params = {
        start: 0,
        end: 50,
        start_date: lastWeek,
        end_date: lastDay,
        columns: ['test_tag_a', 'test_tag_b']
      };
      setDefaultParams(params);
      const tmp = {
        metrics: DefaultMetrics,
        date: [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        test_tag_a: [defaultAbTestType],
        group: Number(abTestId) || ''
      };

      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
      if (!!abTestId && !!abTestType) {
        const curParams = { ...params, ...tmp };
        handleGroupParams(Number(abTestId), curParams);
        getTableData(curParams);
      }
    }
    const gIndex = options.findIndex(item => item.key === 'group');
    if (gIndex !== -1) {
      const gOptions = ABTestList?.filter(item => item.type === defaultAbTestId)?.map(
        (item: StrategyAPI.ABTestListItem) => {
          const seller_name = item.seller_name ? `${item.seller_name}` : '';
          const buyer_name = item.buyer_name ? `${item.buyer_name}` : '';
          const country = item.country ? `${item.country}` : '';
          const ad_format = item.ad_format ? `${AdFormatToLabel[item.ad_format]}` : '';
          const ad_size = item.ad_size ? `${adSizeMapByValue[item.ad_size]}` : '';
          return {
            label: [seller_name, buyer_name, country, ad_format, ad_size].filter(Boolean).join('+'),
            value: item.id
          };
        }
      );

      options[gIndex].options = gOptions;
    }
    const typeIndex = options.findIndex(item => item.key === 'test_tag_a');
    if (typeIndex !== -1) {
      let option = options[typeIndex].options || [];
      if (customTntId.current === 1046) {
        option = option.filter(item => item.value === 'op_bidfloor');
      } else if (customTntId.current !== 1052) {
        option = option.filter(item => item.value !== 'op_banner_transfer_format');
      }
      options[typeIndex].options = option;
    }

    setSearchOptions(options);
  };

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter(v => columnKeys.includes((v.key as string) || (v.dataIndex as string)));
      setColumns(tmp);
    }
  };

  const handleExport = async (
    columns: ColumnProps<DashboardAPI.ABtestReportItem>[],
    sourceData?: API.BackResult<DashboardAPI.ABtestReportItem>
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map(item => {
      return {
        label: item.title,
        value: item.dataIndex
      };
    });
    const data = await formatExportData(sourceData?.data || dataSource.data, FormatExportValueMap);

    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(item => item.key === 'columns' && Array.isArray(item.value) && item.value.length > 0);
    const metrics = val.find(item => item.key === 'metrics' && Array.isArray(item.value) && item.value.length > 0);

    // 目前仅针对 day 维度，后续可能需要根据维度进行处理
    // const is_hour = dimension?.value?.findIndex((v: any) => v === 'day_hour') > -1;
    const is_day = dimension?.value.findIndex((v: any) => v === 'day') > -1;
    const dim: any[] = dimension?.value.filter((d: string) => !['day', 'day_hour'].includes(d)) || [];
    const mts = (metrics && metrics?.value.filter((v: string) => v !== 'month' && v !== 'day')) || [];

    const column_keys = [...dim, ...mts];
    // if (is_day && is_hour) {
    //   column_keys.unshift('day_hour');
    // } else if ((is_day && !is_hour) || (!is_day && is_hour)) {
    //   is_day && column_keys.unshift('day');
    //   is_hour && column_keys.unshift('day_hour');
    // }

    if (is_day) {
      column_keys.unshift('day');
    }

    setColumnKeys([...column_keys, ...FixedDimension]);
  };

  const handleGroupParams = (id: number, params: any) => {
    const groups = ABTestList?.filter((v: StrategyAPI.ABTestListItem) => {
      return v.id === id;
    });
    const seller_ids = [
      ...new Set(
        groups?.map((v: StrategyAPI.ABTestListItem) => v.seller_id).filter(id => id !== null && id !== undefined)
      )
    ];
    const buyer_ids = groups?.map((v: StrategyAPI.ABTestListItem) => v.buyer_id).filter(Boolean);
    const countries = groups?.map((v: StrategyAPI.ABTestListItem) => v.country).filter(Boolean);
    const ad_formats = groups?.map((v: StrategyAPI.ABTestListItem) => v.ad_format).filter(Boolean);
    const ad_sizes = groups?.map((v: StrategyAPI.ABTestListItem) => v.ad_size).filter(Boolean);
    console.log(groups);
    params['seller_id'] = seller_ids;
    params['buyer_id'] = buyer_ids;
    params['country'] = countries;
    params['ad_format'] = ad_formats;
    params['ad_size'] = ad_sizes;
  };
  const handleSearchChange = (start: number, end: number, val: SearchResultItem[], isPaing?: boolean) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };

  const handleGetSearchParams = (start: number, end: number, val: SearchResultItem[]) => {
    const tmp = val.filter(
      item => (Array.isArray(item.value) && item.value.length > 0) || (!Array.isArray(item.value) && item.value)
    );

    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach(item => {
      if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map(v => v.format('YYYYMMDD'));
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else if (item.key === 'group') {
        handleGroupParams(item.value, params);
      } else {
        params[item.key] = item.value;
      }
    });
    const additionalColumns = ['test_tag_a', 'test_tag_b'];
    params['columns'] = Array.isArray(params['columns'])
      ? [...params['columns'], ...additionalColumns]
      : [...additionalColumns];

    if (params['columns']?.includes('day')) {
      params['split_time'] = DateType.Day;
    }

    return params;
  };

  const onSuccess = (
    data: API.BackResult<DashboardAPI.ABtestReportItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.ABtestReportItem>[]
  ) => {
    data.total === -1 ? (data.total = totalCount) : setTotalCount(data.total);
    const baseTrafficRatio =
      100 -
      data?.data.reduce((prev, next) => {
        const { test_tag_b } = next;
        const content = test_tag_b.split('::');
        if (content[0] !== 'base') {
          return prev + +content[0];
        } else {
          return prev;
        }
      }, 0);

    const tmp = data.data.map(item => {
      const { test_tag_b } = item;
      const content = test_tag_b.split('::');
      if (content[0] === 'base') {
        return {
          ...item,
          test_tag_b: item.test_tag_b.replace('base', `${baseTrafficRatio}`)
        };
      }

      return item;
    });

    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.ABtestReportItem>[]
  ) => {
    let { order, order_key } = getOrderAndKey();
    const { columns: tmpColumns } = value;
    if (!tmpColumns?.includes('day') && order_key.includes('date')) {
      order_key = ['buyer_net_revenue'];
    }
    const lastDay = moment().format('YYYYMMDD');
    const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time: 0,
      start_date: lastWeek,
      end_date: lastDay,
      order_key,
      order,
      ...value
    };
    if (!checkMetrics(params.metrics)) {
      return;
    }
    fetchData({
      setLoading,
      request: getABTestReportList,
      params,
      onSuccess: data => onSuccess(data, isDownloadAll, columns)
    });
  };
  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate > moment().endOf('day').subtract(1, 'day') ||
      currentDate < moment().subtract(14, 'months').subtract(1, 'd') ||
      currentDate < moment('20230412')
    );
  };
  const getOrderAndKey = () => {
    let order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    if (!columnKeys.includes('month') && !columnKeys.includes('day')) {
      order_key = ['buyer_net_revenue'];
    }
    return {
      order,
      order_key
    };
  };

  const handleSortChange = (page: number, size: number, search: SearchResultItem[], sorter: any) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });
    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    params.isPaing = !!totalCount;
    getTableData(params);
  };

  const handleSearchValueChange = (changeValue: any, allChangeValue: any) => {
    if (changeValue.test_tag_a) {
      const type = ABTestTypeOptions.find(item => item.label === changeValue.test_tag_a)?.value;
      const index = searchOptions.findIndex(item => item.key === 'group');
      if (index !== -1) {
        const gOptions = ABTestList?.filter((item: StrategyAPI.ABTestListItem) => item.type === type);
        searchOptions[index].options = gOptions?.map((item: StrategyAPI.ABTestListItem) => {
          const seller_name = item.seller_name ? `${item.seller_name}` : '';
          const buyer_name = item.buyer_name ? `${item.buyer_name}` : '';
          const country = item.country ? `${item.country}` : '';
          const ad_format = item.ad_format ? `${AdFormatToLabel[item.ad_format]}` : '';
          const ad_size = item.ad_size ? `${adSizeMapByValue[item.ad_size]}` : '';
          return {
            label: [seller_name, buyer_name, country, ad_format, ad_size].filter(Boolean).join('+'),
            value: item.id
          };
        });
        setSearchOptions([...searchOptions]);
      }
    }
  };
  return (
    <PageContainer flexDirection="column" options={BreadOptions}>
      <BackTable<DashboardAPI.ABtestReportItem>
        pageTitle="A/B Test Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        getTableData={getTableData}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        maxRange={93}
        defaultDates={defaultSearchValue.date}
        dateRangeKeys={['Yesterday', '3 Days', '7 Days']}
        handleSearchValueChange={handleSearchValueChange}
      />
    </PageContainer>
  );
}
