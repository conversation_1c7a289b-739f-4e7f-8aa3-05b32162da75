{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "npm: dev - client (Mac/Linux)",
      "type": "shell",
      "args": [
        "8000"
      ],
      "command": ".vscode/check-dev.sh",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "isBackground": true,
      "problemMatcher": [],
      "detail": "cross-env UMI_ENV=dev max dev"
    },
    {
      "label": "npm: dev - client (Windows)",
      "type": "shell",
      "args": [
        "8000"
      ],
      "command": ".vscode/check-dev.cmd",
      "group": {
        "kind": "build",
        "isDefault": true
      },
      "problemMatcher": [],
      "detail": "cross-env UMI_ENV=dev max dev"
    },
  ]
}
