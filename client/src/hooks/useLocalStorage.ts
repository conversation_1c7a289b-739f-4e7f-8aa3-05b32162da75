import { useState, useEffect, useCallback } from 'react';
import { StorageUtils } from '../utils/storage';

/**
 * 使用 localStorage 的 hook
 * @param key 键
 * @param initialValue 初始值
 * @returns [存储的值, 设置值的函数, 错误信息]
 */
function useLocalStorage<T>(
  key: string,
  initialValue: T
): readonly [T, (value: T | ((prev: T) => T)) => void, string | null];
function useLocalStorage<T>(
  key: string
): readonly [T | undefined, (value: T | undefined | ((prev: T | undefined) => T | undefined)) => void, string | null];
function useLocalStorage<T>(key: string, initialValue?: T) {
  // 获取初始值
  const getInitialValue = () => {
    try {
      const storedValue = StorageUtils.get<T>(key);
      if (storedValue !== null) {
        return storedValue;
      }
      return initialValue;
    } catch (error) {
      console.warn(`[useLocalStorage] Warning: Error getting initial value for key "${key}"`, error);
      return initialValue;
    }
  };

  const [value, setValue] = useState<T | undefined>(getInitialValue);
  const [error, setError] = useState<string | null>(null);

  // 更新值的函数
  const updateValue = useCallback(
    (newValue: T | ((prev: T | undefined) => T | undefined)) => {
      try {
        // 处理函数式更新
        const valueToStore = newValue instanceof Function ? newValue(value) : newValue;

        // 检查特殊类型
        if (valueToStore === undefined) {
          console.warn('[useLocalStorage] Warning: Storing undefined values is not recommended, use null instead');
        }

        if (typeof valueToStore === 'function' || typeof valueToStore === 'symbol') {
          const errorMsg = `Cannot store value of type ${typeof valueToStore}`;
          console.warn(`[useLocalStorage] Warning: ${errorMsg}`);
          setError(errorMsg);
          return;
        }

        // 更新状态
        setValue(valueToStore);
        setError(null);
      } catch (error) {
        const errorMsg = 'Error updating value';
        console.warn(`[useLocalStorage] Warning: ${errorMsg}`, error);
        setError(errorMsg);
      }
    },
    [value]
  );

  // 当值改变时，更新 localStorage
  useEffect(() => {
    try {
      if (value === undefined) {
        StorageUtils.remove(key);
      } else {
        const success = StorageUtils.set(key, value);
        if (!success) {
          setError('Storage operation failed');
        }
      }
    } catch (error) {
      const errorMsg = 'Error syncing to localStorage';
      console.warn(`[useLocalStorage] Warning: ${errorMsg}`, error);
      setError(errorMsg);
    }
  }, [key, value]);

  // 监听其他标签页的更改
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key) {
        try {
          if (e.newValue === null) {
            setValue(undefined);
          } else {
            setValue(StorageUtils.deserialize(e.newValue));
          }
          setError(null);
        } catch (error) {
          const errorMsg = 'Error handling storage event';
          console.warn(`[useLocalStorage] Warning: ${errorMsg}`, error);
          setError(errorMsg);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [value, updateValue, error] as const;
}

export default useLocalStorage;
