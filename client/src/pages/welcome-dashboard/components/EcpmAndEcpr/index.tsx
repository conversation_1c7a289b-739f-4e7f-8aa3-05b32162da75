/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-14 17:07:51
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-19 10:25:52
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { Line } from '@ant-design/charts';
import type { Datum, LineConfig } from '@ant-design/charts';
import Card from '../Card';
import { fetchData } from '@/utils';
import { getTopAdFormatEcpmAndEcpr } from '@/services/ai-board';

const EcpmAndEcpr: React.FC = ({}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<BoardAPI.EcpmAndEcprItem[]>([]);
  const ecpmConfig: LineConfig = {
    theme: 'custom-theme',
    autoFit: true,
    data: totalData,
    xField: 'date',
    yField: 'ecpm',
    seriesField: 'ad_format',
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: v => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, s => `${s},`)
      }
    },
    legend: {
      position: 'bottom',
      offsetY: 10
    },
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.ad_format,
          value: `${(+datum.ecpm).toFixed(2)}`
        };
      }
    }
  };
  const ecprConfig: LineConfig = {
    theme: 'custom-theme',
    autoFit: true,
    data: totalData,
    xField: 'date',
    yField: 'ecpr',
    seriesField: 'ad_format',
    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: v => `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, s => `${s},`)
      }
    },
    legend: {
      position: 'bottom',
      offsetY: 10
    },
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.ad_format,
          value: `${(+datum.ecpr).toFixed(2)}`
        };
      }
    }
  };
  useEffect(() => {
    fetchData({
      setLoading,
      request: getTopAdFormatEcpmAndEcpr,
      onSuccess: res => {
        if (Array.isArray(res)) {
          setTotalData(res);
        } else {
          setTotalData([]);
        }
      }
    });
  }, []);

  const isExistData = totalData.length > 0;

  return (
    <div className={styles['ecpm-ecpr-container']}>
      <Card headline="Ecpm - Ad Format" className={styles['ecpm-container-card']} loading={loading}>
        <div className={styles['ecpm-container']}>{isExistData && <Line {...ecpmConfig} />}</div>
      </Card>
      <Card headline="Ecpr - Ad Format" className={styles['ecpr-container-card']} loading={loading}>
        <div className={styles['ecpr-container']}>{isExistData && <Line {...ecprConfig} />}</div>
      </Card>
    </div>
  );
};

export default EcpmAndEcpr;
