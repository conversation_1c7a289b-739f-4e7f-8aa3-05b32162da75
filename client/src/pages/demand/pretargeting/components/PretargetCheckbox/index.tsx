import { Checkbox } from 'antd';
import { type CheckboxGroupProps } from 'antd/lib/checkbox';
import classnames from 'classnames';
import styles from './index.less';

type PretargetCheckboxProps = CheckboxGroupProps;

const PretargetCheckbox = ({ className, ...props }: PretargetCheckboxProps) => {
  return <Checkbox.Group {...props} className={classnames(styles['checkbox-container'], className)} />;
};

export default PretargetCheckbox;
