import NormalTitle from '@/components/NormalTitle';
import PageContainer from '@/components/RightPageContainer';
import { PretargetBreadOptions } from '@/constants/demand/pretargeting';
import { getPretargetCampaign } from '@/services/demand';
import { withScopeState } from '@/stores/scope';
import { useLocation, useModel, useNavigate, useRequest, useSearchParams } from '@umijs/max';
import { Empty } from 'antd';
import React, { useEffect, useState } from 'react';
import PretargetCopyModal from './components/PretargetCopyModal';
import PretargetingItem from './components/PretargetingItem';
import PretargetModal from './components/PretargetModal';
import PretargetCreateButton from './components/PretargetModal/PretargetCreateButton';
import { PretargetingDataItem, transformPretargetListData } from './helper/transformPretargetListData';
import styles from './index.less';
import { initialPretargetingState, usePretargetingState, usePretargetingStore } from './store';

const Pretargeting: React.FC = () => {
  const [campaignList, setCampaignList] = useState<PretargetingDataItem[]>([]);
  const [searchParams] = useSearchParams();
  const id = searchParams.get('id');
  const {
    dataSource: supplyList,
    supplyOptions,
    supplyMapByValue,
    reload: reloadSupply,
    loading: supplyLoading
  } = useModel('useSupplyListWithTesting');
  const {
    dataSource: demandList,
    options: demandOptions,
    reload: reloadDemand,
    loading: demandLoading
  } = useModel('useDemandListWithTesting');
  const { adSizeOptions, adSizeMapByValue, fetchAdSize } = useModel('useAdSizeOptions');
  const state = usePretargetingState();
  const { currentDemand } = usePretargetingStore();
  const navigate = useNavigate();
  const location = useLocation();
  // 获取 pt 数据
  const { run: fetchPretargetData, loading } = useRequest(
    () => {
      if (!id) return;
      return getPretargetCampaign({ op: 0, buyer_id: id });
    },
    {
      manual: true,
      onSuccess: (data: DemandAPI.PretargetCampaignItem[]) => {
        setCampaignList(transformPretargetListData(data));
        if (state.onePretargetingData) {
          state.pretargetModalOp = 1;
          state.pretargetModalVisible = true;
          // 清除 state
          // 同时保持 url searchParams 不变
          navigate(location, { replace: true });
        }
      }
    }
  );

  useEffect(() => {
    if (id) {
      fetchPretargetData();
    }
    fetchAdSize();
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
  }, [id]);

  useEffect(() => {
    if (!id || !demandList || !demandList.length) return;

    for (const item of demandList) {
      if (item.buyer_id === +id) {
        state.currentDemand = item;
        break;
      }
    }
  }, [demandList, id]);

  // 标题
  const pretargetingTitle = currentDemand ? `${currentDemand.buyer_name} (${currentDemand.buyer_id})` : '-';

  return (
    <PageContainer
      options={PretargetBreadOptions}
      loading={loading || supplyLoading || demandLoading}
      isBack
      handleGoBack={() => history.go(-1)}
    >
      <div className={styles['pretargeting-container']}>
        <NormalTitle blackName="Pretargeting" grayName={pretargetingTitle} isTitle={true} bottom={20} />
        <PretargetCreateButton />
        <PretargetModal
          fetchData={fetchPretargetData}
          loading={loading}
          supplyOptions={supplyOptions}
          adSizeOptions={adSizeOptions}
        />
        <PretargetCopyModal demandOptions={demandOptions} />
        {campaignList.map(item => {
          return (
            <PretargetingItem
              key={item.campaign_id}
              fetchData={fetchPretargetData}
              handleDeleteItem={fetchPretargetData}
              supplyMapByValue={supplyMapByValue}
              adSizeMapByValue={adSizeMapByValue}
              currentItem={item}
            />
          );
        })}
        {!campaignList.length && <Empty style={{ paddingTop: 100 }} />}
      </div>
    </PageContainer>
  );
};

export default withScopeState(Pretargeting, initialPretargetingState);
