import { StatusMap, UserType } from '@/constants';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { PmpAPI } from '@/types/pmp';
import { buildSQLWhereClause } from '@rixfe/rix-tools';
// 系统内部用户
const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];

class PmpModel {
  async isExistsDeal(params: PmpAPI.IsExistsDeal) {
    const { name, tnt_id, deal_id, id, buyer_id, pmp_id, bidfloor } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      // ['name = ?', name],
      // ['deal_id = ?', deal_id],
      // ['buyer_id = ?', buyer_id],
      // ['pmp_id = ?', pmp_id],
      // ['bidfloor = ?', bidfloor],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select id,buyer_id,pmp_id,bidfloor from pmp_deal ${clause} and (name=? or deal_id=? or (buyer_id=? and pmp_id=? and bidfloor=?))`;
    return await dbUtil.query(sql, [
      ...values,
      name,
      deal_id,
      buyer_id,
      pmp_id,
      bidfloor
    ]);
  }

  async getPmpDealList(tnt_id: number, cur_time_zone: string) {
    const sql = `
      select
        pdl.id,
        pdl.name,
        pdl.pmp_id,
        pdl.buyer_id,
        pdl.deal_id,
        pdl.bidfloor,
        pdl.auction_type,
        pdl.status,
        pdl.op_id,
        pdl.tnt_id,
        ${getTzTimeSql('pdl.update_time', cur_time_zone)} as update_time,
        byr.buyer_name,
        ptl.name as pmp_internal_name,
        u.status as u_status,
        (case when u.type in (?) or u.type is null then "System" else u.account_name end) as op_name
      from pmp_deal as pdl
      left join (select buyer_name, buyer_id from buyer) as byr on byr.buyer_id=pdl.buyer_id
      left join (select id, name from pmp_internal) as ptl on ptl.id=pdl.pmp_id
      left join (select user_id, account_name, status, type from user) as u on u.user_id=pdl.op_id
      where pdl.tnt_id=?
      order by pdl.update_time desc
    `;
    return await dbUtil.query(sql, [SystemType, tnt_id]);
  }

  async addDeal(params: PmpAPI.AddDealParams) {
    const {
      name,
      pmp_id,
      buyer_id,
      deal_id,
      bidfloor,
      auction_type,
      op_id,
      tnt_id
    } = params;

    const sql = `insert into pmp_deal(name, pmp_id, buyer_id, deal_id, bidfloor, auction_type, status, op_id, tnt_id) values ? on duplicate key update bidfloor=?, auction_type=?, status=?, op_id=?`;

    return dbUtil.query(sql, [
      [
        [
          name,
          pmp_id,
          buyer_id,
          deal_id,
          bidfloor || 0,
          auction_type,
          StatusMap.Active,
          op_id,
          tnt_id
        ]
      ],
      bidfloor || 0,
      auction_type || 0,
      StatusMap.Active,
      op_id
    ]);
  }

  async updateDeal(params: PmpAPI.UpdateDealParams) {
    const { id, name, deal_id, bidfloor, auction_type, status, op_id, tnt_id } =
      params;
    const sql = `
      update pmp_deal set
          name=?,
          deal_id=?,
          bidfloor=?,
          auction_type=?,
          status=?,
          op_id=?
      where tnt_id=? and id=?
    `;
    const values = [
      name,
      deal_id,
      bidfloor,
      auction_type,
      status,
      op_id,
      tnt_id,
      id
    ];
    return await dbUtil.query(sql, values);
  }

  async isExistsInventory(params: PmpAPI.isExistsInventory) {
    const { name, tnt_id, id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['name = ?', name],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select id from pmp_internal ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async getPmpInventoryList(tnt_id: number, cur_time_zone: string) {
    const sql = `
      select
        ptl.id,
        ptl.name,
        coalesce(ptl.seller_id, '') as seller_id,
        coalesce(ptl.inventory_type, '') as inventory_type,
        coalesce(ptl.bundle, '') as bundle,
        coalesce(ptl.country, '') as country,
        coalesce(ptl.ad_format, '') as ad_format,
        coalesce(ptl.ad_size, '') as ad_size,
        coalesce(ptl.seller_deal_id, '') as seller_deal_id,
        coalesce(ptl.remark, '') as remark,
        ptl.status,
        ptl.op_id,
        ptl.tnt_id,
        ${getTzTimeSql('ptl.update_time', cur_time_zone)} as update_time,
        u.status as u_status,
        (case when u.type in (?) or u.type is null then "System" else u.account_name end) as op_name
      from pmp_internal as ptl
      left join (select user_id, account_name, status, type from user) as u on u.user_id=ptl.op_id
      where ptl.tnt_id=?
      order by ptl.update_time desc
    `;
    return await dbUtil.query(sql, [SystemType, tnt_id]);
  }

  async addInventory(params: PmpAPI.addInventoryParams) {
    const {
      name,
      seller_id,
      inventory_type,
      bundle,
      ad_format,
      country,
      ad_size,
      seller_deal_id,
      remark,
      op_id,
      tnt_id
    } = params;
    const sql = `insert into pmp_internal
    (name, seller_id, inventory_type, bundle, ad_format, country, ad_size, seller_deal_id, remark, op_id, tnt_id)
    values (?,?,?,?,?,?,?,?,?,?,?)`;
    const values = [
      name,
      seller_id?.join(',') || '',
      inventory_type?.join(',') || '',
      bundle?.join(',') || '',
      ad_format?.join(',') || '',
      country?.join(',') || '',
      ad_size?.join(',') || '',
      seller_deal_id || '',
      remark || '',
      op_id,
      tnt_id
    ];
    return !!(await dbUtil.query(sql, values));
  }

  async updateInventory(params: PmpAPI.updateInventoryParams) {
    const {
      id,
      name,
      seller_id,
      inventory_type,
      bundle,
      ad_format,
      country,
      ad_size,
      seller_deal_id,
      remark,
      status,
      op_id,
      tnt_id
    } = params;
    const sql = `
      update pmp_internal set
          name=?,
          seller_id=?,
          inventory_type=?,
          bundle=?,
          ad_format=?,
          country=?,
          ad_size=?,
          seller_deal_id=?,
          remark=?,
          status=?,
          op_id=?
      where tnt_id=? and id=?
    `;
    const values = [
      name,
      seller_id?.join(',') || '',
      inventory_type?.join(',') || '',
      bundle?.join(',') || '',
      ad_format?.join(',') || '',
      country?.join(',') || '',
      ad_size?.join(',') || '',
      seller_deal_id || '',
      remark || '',
      status,
      op_id,
      tnt_id,
      id
    ];
    return await dbUtil.query(sql, values);
  }
}

export const pmpModel = new PmpModel();
