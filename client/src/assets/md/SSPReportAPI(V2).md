# SSP Report API V2

## Definition

### Endpoint

---

| Name | Endpoint Address                       |
| ---- | -------------------------------------- |
| API  | https://{host}.rpt.{domain}/ssp/api/v2 |

### Request Details

---

- Request Method: POST | GET
- Request Authorization: Please contact your account manager and add to request header.

| Name            | Description             |
| --------------- | ----------------------- |
| x-userid        | user id, alias sid      |
| x-authorization | user token, alias token |

- Default Timezone: UTC+0

### Request Limit

---

- Maximum number of requests per day：50.
- Request Day: last 60 day to current day.
- Request Date interval: within 15 days.

## Data Schema

---

### Request Data Schema

---

| Name | Type | Required | Description | Available Values | Default |
| :-: | :-: | :-: | :-- | :-- | :-: |
| start_date | YYYY-MM-DD | Yes | Start date,format YYYY-MM-DD. | 2023-04-01 |  |
| end_date | YYYY-MM-DD | Yes | End date,format YYYY-MM-DD. | 2023-04-15 |  |
| dimensions | array\<string\> | Yes | Dimensions of report | day, region | day |
| timezone | string | No | Timezone | From UTC-12 to UTC+12, eg:UTC-12,UTC-11,UTC-1,UTC+0,UTC+1,UTC+2... | UTC+0 |

### Response Data Schema

---

|   Attribute   |  Type   | Description            |
| :-----------: | :-----: | :--------------------- |
|      day      |  date   | Date,format YYYY-MM-DD |
|  net_revenue  |  float  | Estimated net revenue  |
|    request    | integer | Request count          |
|   response    | integer | Response count         |
|  impression   | integer | Impress count          |
|    region     | string  | Server Region          |

## ReportAPI Method

### API

---

#### Request Schema

---

- Request body see <a href='#Request Data Schema'>Request Data Schema</a>
- example: '{"start_date": "YYYY-MM-DD", "end_date": "YYYY-MM-DD"}'

#### Response Schema

---

| Attribute | Type | Description |
| :-: | :-: | :-- |
| status.code | integer | Server response status, when it is zero, indicates that the server responded correctly to the request. When it is not zero, you can find specific error messages in status.msg. |
| status.msg | string | Server response description. |
| timestamp | string | UTC time, which is used to indicate the time when the server responds to the current request. |
| data | array or null | Server response content. |

- status code see <a href='#Appendix'>Appendix</a>
- data details see <a href='#Response Data Schema'>Response Data Schema</a>

#### Example

```powershell
### POST
curl -H "x-userid: {YOUR_USERID}"  -H "x-authorization: {YOUR_AUTHORIZATION}" -H "Content-Type: application/json" \
-d '{"start_date":"2023-03-01", "end_date":"2023-03-02", "dimensions": ["day"]}' \
-X POST "https://{host}.rpt.{domain}/ssp/api/v2"

### GET
curl -X GET "https://{host}.rpt.{domain}/ssp/api/v2?x-userid={YOUR_USERID}&x-authorization={YOUR_AUTHORIZATION}&start_date=2023-03-01&end_date=2023-03-02&dimensions[]=day"
```

```json
{
  "status": {
    "code": 0,
    "msg": "success"
  },
  "timestamp": "Thu Dec 26 06:32:50 +0000 2023",
  "data": {
    "total": 2,
    "data": [
      {
        "day": "2023-03-02",
        "request": 99999,
        "response": 888888,
        "impression": 999999,
        "net_revenue": 666.66
      },
      {
        "day": "2023-03-01",
        "request": 66666,
        "response": 888888,
        "impression": 999999,
        "net_revenue": 666.66
      }
    ]
  }
}
```

---

## Appendix

### Status Code

---

| Error Code | Error Message                                                   |
| :--------: | :-------------------------------------------------------------- |
|     0      | success                                                         |
|    1000    | system error                                                    |
|    1001    | invalid params.                                                 |
|    2000    | token authorized failed.                                        |
|    2002    | The number of requests for the day has reached the upper limit. |
|    2003    | request timeout.                                                |
|    2004    | user status is not active. |
