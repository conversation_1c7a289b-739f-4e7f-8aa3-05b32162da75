/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:59:11
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import { DemandAndSupplyStatusMap, StatusOptions } from '@/constants';
import { addCap, updateCap } from '@/services/api';
import { fetchData, isValidBundle } from '@/utils';
import InputNumber from '@/components/Input/InputNumber';
import Select from '@/components/Select/NormalSelect';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { handleFilterSelect } from '@/utils';
import { CapTypeOptions, CapType } from '@/constants/strategy/cap';
import NormalInput from '@/components/Input/NormalInput';

type AddCapModelProps = {
  cap?: StrategyAPI.CapListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadCap: () => void;
  demandList: DemandAPI.DemandListItem[];
  supplyList: SupplyAPI.SupplyListItem[];
};

const DefaultFormData = {
  buyer_id: undefined,
  seller_id: undefined,
  rev_cap: 0,
  status: 1,
  type: CapType['Adv + Pub'],
  bundle: ''
};

const AddCapModel: React.FC<AddCapModelProps> = ({
  cap,
  handleClose,
  visible,
  isEdit,
  reloadCap,
  demandList,
  supplyList
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState(CapType['Adv + Pub']);

  useEffect(() => {
    if (cap && isEdit && visible) {
      setType(cap.type);
      form.setFieldsValue(cap);
    } else {
      form.resetFields();
    }
  }, [cap, isEdit, visible]);
  const handleConfirm = () => {
    form.submit();
  };
  const handleFinish = (values: any) => {
    const params = {
      buyer_id: 0,
      seller_id: 0,
      bundle: '',
      ...values
    };
    if (isEdit) {
      handleEditCap({ ...params, buyer_id: cap?.buyer_id || 0, id: cap?.id, ori_data: cap });
    } else {
      handleAddCap(params);
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadCap();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleAddCap = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addCap, params, onSuccess });
  };

  const handleEditCap = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateCap, params });
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    setType(allValue.type);
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Cap` : `Create Cap`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
      >
        <Form.Item
          name="type"
          label="Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[{ required: true, message: 'Please Select Type' }]}
        >
          <Select options={CapTypeOptions} allowClear={false} disabled={isEdit} />
        </Form.Item>
        {
          [CapType.Pub, CapType['Adv + Pub'], CapType['Adv + Pub + Bundle']].includes(type) && (
            <Form.Item
              name="seller_id"
              label="Publisher:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Select Publisher'
                }
              ]}
            >
              <Select
                showSearch
                optionFilterProp="children"
                filterOption={handleFilterSelect}
                disabled={isEdit}
                options={supplyList?.map(v => ({ value: v.seller_id, label: `${v.seller_name}(${v.seller_id})`, disabled: v.status === DemandAndSupplyStatusMap.Testing }))}
              />
            </Form.Item>
          )
        }
        {
          type !== CapType.Pub && (
            <Form.Item
              name="buyer_id"
              label="Advertiser:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[{ required: true, message: 'Please Select Advertiser' }]}
            >
              <Select
                showSearch
                optionFilterProp="children"
                filterOption={handleFilterSelect}
                disabled={isEdit}
                options={demandList?.map(v => ({ value: v.buyer_id, label: `${v.buyer_name}(${v.buyer_id})` }))}
              />
            </Form.Item>
          )
        }
        {[CapType['Adv + Bundle'], CapType['Adv + Pub + Bundle']].includes(type) && (
          <Form.Item
            name="bundle"
            label="Bundle:"
            validateTrigger={['onChange', 'onBlur']}
            getValueFromEvent={(e: any) => e.target.value.replace(/\s+/g, '')}
            tooltip="Valid after an hour"
            rules={[
              {
                required: true,
                message: 'Please Input Bundle'
              },
              {
                validator: (rule: any, value: any) => {
                  if (!value || isValidBundle(value)) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('invalid bundle'));
                }
              }
            ]}
          >
            <NormalInput placeholder='Please input Bundle' disabled={isEdit}/>
          </Form.Item>
        )}
        <Form.Item
          name="rev_cap"
          label="Daily Revenue Cap:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Input Daily Revenue Cap'
            },
            {
              validator: (rule, value) => {
                const maxValue = 10000000;
                const minValue = 0;
                if (value > maxValue) {
                  return Promise.reject(`Revenue Cap maximum value is ${maxValue}`);
                }
                if (value <= 0) {
                  return Promise.reject(`Revenue Cap must greater than ${minValue}`);
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <InputNumber
            style={{ width: '150px' }}
            min={0} controls
            type="number"
            addonAfter="$"
            formatter={(value) => `${value}`.includes('.') ? (+(value || 0)).toFixed(2) : `${value}`}
          />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddCapModel;
