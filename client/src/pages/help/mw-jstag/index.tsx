/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-31 11:42:46
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-31 11:51:50
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-14 18:42:13
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-23 15:35:21
 * @Description:
 */
import React from 'react';
import MdPage from '../components/MdPage';
// @ts-ignore
import GSContent from '@/assets/md/MW-JSTag.md';

const MaxJsTagDoc: React.FC = () => {
  return <MdPage enMdPath={GSContent}></MdPage>;
};

export default MaxJsTagDoc;
