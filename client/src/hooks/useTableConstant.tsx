/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:46:01
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-01 14:31:27
 * @Description:
 */
import React, { useState, useEffect } from 'react';

type PropsType<T, K> = {
  reload: () => void;
  key: K;
  dataSource: T[];
  loading: boolean;
}

function useTableConstant<T extends object>({ reload, key, dataSource, loading }: PropsType<T, keyof T>) {
  const [mgHeight, setMgHeight] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRow, setSelectedRow] = useState<T | undefined>(undefined);

  const reset = () => {
    setSelectedRowKeys([]);
    setSelectedRow(undefined);
  };
  // 路由改变刷新页面
  useEffect(() => {
    reload();
  }, []);

  useEffect(() => {
    reset();
  }, [loading]);

  const handleRowClick = (record: T) => {
    const curSelectedId: any = record[key];
    if (!selectedRowKeys.length || !selectedRowKeys.includes(curSelectedId)) {
      setSelectedRowKeys([curSelectedId]);
      setSelectedRow(record);
    }
  };

  const handleRowChange = (curSelectedRowKeys: any) => {
    setSelectedRowKeys(curSelectedRowKeys);
    if (!curSelectedRowKeys.length) {
      setSelectedRow(undefined);
    } else if (curSelectedRowKeys.length === 1) {
      const record = dataSource.find((el: T) => el[key] === curSelectedRowKeys[0]);
      setSelectedRow(record);
    } else {
      setSelectedRow(undefined);
    }
  };

  return {
    mgHeight,
    setMgHeight,
    selectedRowKeys,
    setSelectedRowKeys,
    selectedRow,
    setSelectedRow,
    reload,
    reset,
    handleRowClick,
    handleRowChange
  };
}

export default useTableConstant;
