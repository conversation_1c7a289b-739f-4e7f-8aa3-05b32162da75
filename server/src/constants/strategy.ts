/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 17:55:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-06 18:46:00
 * @Description:
 */
export const CapType = {
  Pub: 1,
  Adv: 2,
  'Adv + Pub': 3,
  'Adv + Bundle': 4,
  'Adv + Pub + Bundle': 5
};

export const StatusMap = {
  Active: 1,
  Paused: 2,
  Deleted: 3
};

export const QpsLevel = {
  supply: 1,
  demand: 2,
  'demand + supply': 3,
  'demand + supply-app': 4,
  'demand + supply-placement': 5,
  'demand + supply + ad_format': 6,
  'demand + supply + country': 7,
  'demand + ad_format': 8,
  'demand + country': 9,
  'demand + bundle': 10,
  'supply + bundle,': 51,
  'supply + country': 52,
  'supply + ad_format': 53
};

export const ProfitType = {
  Seller: 1,
  Demand: 2,
  'Seller-Demand': 3
};

export const RegionType = {
  ALL: 0,
  USE: 1,
  APAC: 2
};

export const RegionTypeMap = {
  0: 'ALL',
  1: 'USE',
  2: 'APAC'
};

export const FloorType = {
  'Ad Format + Country': 1,
  'Supply + Ad Format + Country': 2,
  'Supply-Placement + Country': 3,
  'Supply + Demand + Ad Format + Country': 4,
  'Supply-Placement + Demand + Country': 5,
  '(SSP)Supply-Placement + Country': 99
};

export const FloorTypeMap = {
  1: 'Ad Format + Country',
  2: 'Supply + Ad Format + Country',
  3: 'Supply-Placement + Country',
  4: 'Supply + Demand + Ad Format + Country',
  5: 'Supply-Placement + Demand + Country',
  99: '(SSP)Supply-Placement + Country'
};

export const ABTestTypeDesc: { [key: number]: string } = {
  1: 'Profit',
  2: 'Bid Floor'
};
