/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 16:19:56
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-04 21:48:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-03-09 21:28:34
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-03-09 20:47:14
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-09 17:03:59
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import { default as HoverToolip, default as HoverToolTip } from '@/components/ToolTip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { StatusDesc, StatusOptions } from '@/constants';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { ColumnProps } from 'antd/lib/table';

export const BlAndWlSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Advertiser'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    mode: 'multiple',
    value: [],
    options: [],
    placeholder: 'Please Select Publisher'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const BlAndWlBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'BL & WL'
  }
];

export const ListTypeOptions = [
  {
    label: 'White List',
    options: [
      { label: 'Bundle(Domain) White List', value: 1 },
      { label: 'Ad Format White List', value: 3 },
      { label: 'Country White List', value: 5 },
      { label: 'Ad Size White List', value: 7 }
    ]
  },
  {
    label: 'Black List',
    options: [
      { label: 'Bundle(Domain) Black List', value: 2 },
      { label: 'Ad Format Black List', value: 4 },
      { label: 'Country Black List', value: 6 },
      { label: 'Ad Size Black List', value: 8 }
    ]
  }
];

export const ListTypeToLabel: API.StringToStringType = {
  1: 'Bundle(Domain) White List',
  2: 'Bundle(Domain) Black List',
  3: 'Ad Format White List',
  4: 'Ad Format Black List',
  5: 'Country White List',
  6: 'Country Black List',
  7: 'Ad Size White List',
  8: 'Ad Size Black List'
};

export const ListType = {
  'Bundle(Domain) White List': 1,
  'Bundle(Domain) Black List': 2,
  'Ad Format White List': 3,
  'Ad Format Black List': 4,
  'Country White List': 5,
  'Country Black List': 6,
  'Ad Size White List': 7,
  'Ad Size Black List': 8
};

export const BlAndWlColumns: ColumnProps<StrategyAPI.BlAndWlListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 160,
    ellipsis: { showTitle: false },
    fixed: 'left',
    render: (txt, params) => {
      return (
        <HoverToolTip title={txt ? `${txt}(${params.buyer_id})` : 'All Advertisers'}>
          <span>{txt ? `${txt}(${params.buyer_id})` : params.buyer_id === 0 ? 'All Advertisers' : '-'}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 160,
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={txt ? `${txt}(${params.seller_id})` : params.seller_id === 0 ? 'All Publishers' : '-'}>
        <span>{txt ? `${txt}(${params.seller_id})` : params.seller_id === 0 ? 'All Publishers' : '-'}</span>
      </HoverToolTip>
    )
  },

  {
    title: 'Type',
    dataIndex: 'type',
    width: 220,
    render: txt => <>{ListTypeToLabel[txt]}</>
  },
  {
    title: 'Content',
    width: 200,
    ellipsis: { showTitle: false },
    dataIndex: 'content'
  },

  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <HoverToolip>
          <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
            {row['account_status'] === 3 ? 'Unknown' : _}
          </span>
        </HoverToolip>

        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 180,
    render: txt => <>{txt}</>
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 85,
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  }
];
