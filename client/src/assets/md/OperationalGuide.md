**运营指南旨在从核心问题上给初期运营者提供运营建议，协助运营者提高运营效率，提升平台流水规模。**

![image-20231027172835306](https://static.rixengine.com/a/platform/help/operational-guide/1.png)

# 1 前期 live test 问题

- 首次接入的上游/下游的量级保持在**200QPS**以内，通常按**100QPS**进行 live test ， live test 期间需核对数据。 live test 之后应定期核对数据，若发现误差超过**5%**，应先排查原因并适当减少 QPS，以减少平台因数据误差而产生的损失。
-  **下游反馈接入链路不通， 平台有response返回给下游但是下游没有收到。** 可以针对下游开启testing模式，让下游手动请求测试一下链路。 **注意：需要同步下游测试模式的展示不结算。**

# 2 数据核对问题

- 需特别留意合同中关于数据差异处理的约定，以明确哪方数据为准。一般月度结算时5%以内的误差以某一方的数据为准，超过5%的差异双方各承担一半， 具体以合同约定为准。

## 2.1数据核对

### 2.1.1上游核对内容

- 与上游核对数据的主要指标是 **Advertiser Net Revenue、Request 和 Impression**，核对误差通常不能超过**5%**。如果有超过**5%**的数据差异，先确认双方的数据归因方式是否一致。需要注意的是，当收入很少时，可能导致较大的误差百分比，这种情况可跟上游重点核对**Request**和**Impression**的数据差异。

### 2.1.2下游核对内容

- 与下游核对数据的主要指标是 **Publisher Net Revenue、Total Request 和 Impression**，核对误差通常不能超过**5%**。如果有超过**5%**的数据差异，先确认双方的数据归因方式是否一致。需要注意的是，当收入很少时，可能导致较大的误差百分比，这种情况可跟下游重点核对**Request**和**Impression**的数据差异。

### 2.1.3核对周期

- 上下游对接初期，需要每天核对数据。 对接稳定后（历史数据误差小于**5%**），建议可以一周核对一次。若误差较大，则建议适当增加核对频次，可从细维度数据排查差异原因，比如**Country/Ad Format/Ad Size/Bundle**。

# 3 结算风险

## 3.1核减问题

- 应特别留意上游是否对**流量质量IVT**有特殊要求。对于有特殊要求的上游合作方，在发送流量时务必重点关注**IVT指标**，以避免结算核减。
-  非Google预算一般很少会核减（如果有这类上游反馈了IVT高的流量，需要及时屏蔽）。

# 4 运营技巧

- Top收入的Bundle、eCPR高的下游，可以跟下游沟通增加流量、提高QPS。
-  根据不同上游的预算分布来授权相应的流量，尽可能地让授权的流量和预算匹配。
- 不同季节有不同广告主预算 , 可以根据预算变化及时优化流量配置。

------

**更多运营常见问题，请参照<a href='/help/faq' targe='_blank'>FAQ</a>**
