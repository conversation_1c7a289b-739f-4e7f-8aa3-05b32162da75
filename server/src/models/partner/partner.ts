/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 14:29:44
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-17 15:26:32
 * @Description:
 */
import dbUtils, { PoolConnection } from '@/db/mysql';
import { PartnerAPI } from '@/types/partner';
import { getLogger } from '@/config/log4js';
import { PartnerType } from '@/constants/partner';
import { StatusType, RoleTypeToNumber } from '@/constants/permission';
import { UserType } from '@/constants';

class PartnerModel {
  async addPartner(params: PartnerAPI.AddPartnerParams) {
    const { partner_name, dp_email = '', sp_email = '', type, tnt_id } = params;
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const sql = `insert into partner (partner_name, tnt_id) values(?, ?)`;
      const rows = await query({ sql, values: [partner_name, tnt_id] });
      const partner_id = rows.insertId;
      const dSql = `insert into buyer_parent (dp_name, partner_id,email,tnt_id) values(?,?,?,?)`;
      const sSql = `insert into seller_parent (sp_name, partner_id,email,tnt_id) values(?,?,?,?)`;
      const arr = [];
      if (
        type === PartnerType.Demand ||
        type === PartnerType['Supply & Demand']
      ) {
        arr.push({
          sql: dSql,
          values: [partner_name, partner_id, dp_email, tnt_id]
        });
      }
      if (
        type === PartnerType.Supply ||
        type === PartnerType['Supply & Demand']
      ) {
        arr.push({
          sql: sSql,
          values: [partner_name, partner_id, sp_email, tnt_id]
        });
      }
      await Promise.all(arr.map(v => query(v)));
      // 更新publisher_id
      const uSql = `update seller_parent set publisher_id = sp_id where partner_id=? and tnt_id=?`;
      await query({ sql: uSql, values: [partner_id, tnt_id] });
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`addPartner failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async updatePartner(params: PartnerAPI.UpdatePartnerParams) {
    const {
      partner_id,
      partner_name,
      type,
      dp_email = '',
      sp_email = '',
      tnt_id
    } = params;
    const arr = [];
    const sql = `update partner set partner_name=? where tnt_id=? and partner_id=?`;
    arr.push({ sql, values: [partner_name, tnt_id, partner_id] });
    const dSql = `insert into buyer_parent (dp_name, partner_id,email,tnt_id) values ?
      on duplicate key update dp_name=?, status=?, email=?`;
    const sSql = `insert into seller_parent (sp_name, partner_id,email,tnt_id) values ?
      on duplicate key update sp_name=?, status=?, email=?`;
    const values = [
      // dSql
      [
        [[partner_name, partner_id, dp_email, tnt_id]],
        partner_name,
        StatusType.Active,
        dp_email
      ],
      // sSql
      [
        [[partner_name, partner_id, sp_email, tnt_id]],
        partner_name,
        StatusType.Active,
        sp_email
      ]
    ];
    if (
      type === PartnerType.Demand ||
      type === PartnerType['Supply & Demand']
    ) {
      arr.push({
        sql: dSql,
        values: values[0]
      });
    }
    if (
      type === PartnerType.Supply ||
      type === PartnerType['Supply & Demand']
    ) {
      arr.push({
        sql: sSql,
        values: values[1]
      });
    }
    return !!(await dbUtils.execWaterfallTransaction(arr));
  }

  async getPartnerList(tnt_id: number) {
    const sql = `
      select
        pt.partner_id,
        pt.partner_name,
        pt.update_time,
        coalesce(spt.sp_id, 0) as sp_id,
        coalesce(spt.publisher_id, '') as sp_publisher_id,
        coalesce(spt.sp_name, '') as sp_name,
        coalesce(spt.email, '') as sp_email,
        coalesce(bpt.dp_id, 0) as dp_id,
        coalesce(bpt.dp_name, '') as dp_name,
        coalesce(bpt.email, '') as dp_email
      from partner as pt
      left join seller_parent as spt on pt.partner_id=spt.partner_id
      left join buyer_parent as bpt on pt.partner_id=bpt.partner_id
      where pt.tnt_id=?
      order by pt.update_time desc
    `;
    return await dbUtils.query(sql, [tnt_id]);
  }

  async isPartnerExists(tnt_id: number, partner_name: string) {
    const sql = `select partner_id from partner where tnt_id=? and partner_name=? limit 1`;
    return await dbUtils.query(sql, [tnt_id, partner_name]);
  }

  // 获取partner的账号
  async getBuyerPartnerAccount(params: PartnerAPI.QueryPartnerAccountParams) {
    const { partner_id, tnt_id } = params;
    const sql = `
      select
        bp.partner_id,
        bp.token,
        u.user_id,
        u.account_name,
        u.status,
        u.api_status,
        ${RoleTypeToNumber['Demand Partner']} as role_type
      from buyer_parent as bp
      left join user as u on u.user_id=bp.user_id
      where bp.partner_id=? and bp.tnt_id=? and u.type=?
    `;
    return await dbUtils.query(sql, [partner_id, tnt_id, UserType.Partner]);
  }

  // 获取下游partner的账号
  async getSellerPartnerAccount(params: PartnerAPI.QueryPartnerAccountParams) {
    const { partner_id, tnt_id } = params;
    const sql = `
      select
        sp.partner_id,
        sp.token,
        u.user_id,
        u.account_name,
        u.status,
        u.api_status,
        ${RoleTypeToNumber['Supply Partner']} as role_type
      from seller_parent as sp
      left join user as u on u.user_id=sp.user_id
      where sp.partner_id=? and sp.tnt_id=? and u.type=?
    `;
    return await dbUtils.query(sql, [partner_id, tnt_id, UserType.Partner]);
  }

  // 创建partner账号
  async createAccount(params: PartnerAPI.CreatePartnerAccountParams) {
    const {
      account_name,
      tnt_id,
      password,
      token,
      cur_user_id,
      p_token,
      partner_id,
      role_type
    } = params;
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const sql = `insert into user (account_name, password, type, token, role, tnt_id) values ?`;
      const values = [
        [[account_name, password, UserType.Partner, token, 2, tnt_id]]
      ];
      const rows = await query({ sql, values });
      const user_id = rows.insertId;
      const role_id = role_type;

      const uSql = `insert into user_role_rl (user_id,role_id,op_user_id,tnt_id) values ?`;
      const uValue = [[[user_id, role_id, cur_user_id, tnt_id]]];

      const partner_table =
        role_type === RoleTypeToNumber['Demand Partner']
          ? 'buyer_parent'
          : 'seller_parent';
      const pSql = `update ${partner_table} set user_id=?, token=? where partner_id=? and tnt_id=?`;
      const pValue = [user_id, p_token, partner_id, tnt_id];

      await Promise.all([
        query({ sql: uSql, values: uValue }),
        query({ sql: pSql, values: pValue })
      ]);
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`addPartner failed ${e.message}`);
      console.log('xx报错的', e);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }
}

export const partnerModel = new PartnerModel();
