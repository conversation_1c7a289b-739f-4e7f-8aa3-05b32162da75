/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-04 13:55:02
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-10 14:19:04
 * @Description:
 */

import { Context, Next } from 'koa';
import { getLogger } from '@/config/log4js';
import { getRedisByKey, setRedisByKey } from '@/db/redis';
import { md5, getConfig } from '@/utils';
import { boardModel } from '@/models';
import { getCtxResult } from '@/utils/response';
import { getUserRole } from '@/utils/report/getBQLabel';

const { redisConfig } = getConfig();
const { platform_key: redis_ } = redisConfig;

// 检查图表是否有缓存数据
export async function checkChartCache(ctx: Context, next: Next) {
  const cache_key = `${redis_}${md5(
    `RIXENGINE_REDIS_CACHE_bdLhLhz4wfQD_CUR_HOUR`
  )}`;
  const cache_data: any = await getRedisByKey(cache_key);
  console.log('checkChartCache', cache_data ? '已缓存cur_hour' : '无缓存');
  if (cache_data) {
    ctx.request.body.cache_cur_hour = cache_data.cur_hour;
    getLogger('app').info(`checkChartCache , cache cur_hour: ${cache_data.cur_hour}`);
  } else {
    const { tnt_id } = ctx.session!;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type; 
    const cur_hour = await boardModel.getTodayHours(tnt_id, {
      tag: api_url,
      tenantId: tnt_id,
      userRole: getUserRole(userType)
    });
    if (!cur_hour) {
      getLogger('app').info(`checkChartCache , cur_hour is null`);
      ctx.body = getCtxResult('SUCCESS', []);
      return;
    }
    // 缓存当前小时
    await setRedisByKey(cache_key, { cur_hour }, 60 * 10);
    ctx.request.body.cur_hour = cur_hour;
  }
  await next();
}
