.content-label {
  color: #5e6466;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  .label {
    text-align: left;
    margin-right: 20px;
    &::before {
      display: inline-block;
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }
  }
  .icon {
    color: var(--primary-color);
    font-size: 18px;
  }
}

.dates-picker-container {
  :global {
    .ant-picker-header {
      .ant-picker-header-prev-btn,
      .ant-picker-header-super-prev-btn,
      .ant-picker-header-next-btn,
      .ant-picker-header-super-next-btn {
        color: #5e6466;
      }
      .ant-picker-header-view {
        color: var(--text-color);
      }
    }
    .ant-picker-cell-today {
      .ant-picker-cell-inner {
        &:before {
          cursor: pointer;
          border: none;
          top: 24px;
          left: 50%;
          width: 4px;
          height: 4px;
          border-radius: 4px;
          background: var(--primary-1);
          transform: translateX(-50%);
        }
      }
    }
    .ant-picker-cell.ant-picker-cell-in-view {
      .selected-date {
        cursor: pointer;
        border: none;
        border-radius: 4px;
        background: var(--primary-color);
        color: #fff;
      }
      .no-selected-date {
        cursor: pointer;
        border: none;
        border-radius: 4px;
        background: #fff;
        color: var(--text-color);
      }
      .ant-picker-cell-inner {
        border-radius: 6px;
      }
    }

    .ant-picker-content {
      th {
        color: #8d9799;
      }
      tr {
        height: 36px;
      }
      .ant-picker-cell-disabled {
        color: #d7dadb;
        &::before {
          height: 32px;
        }
        .no-selected-date {
          color: #d7dadb;
          background: #f5f7f8;
          cursor: not-allowed;
          border: none;
          border-radius: 4px;
        }
      }
    }
  }
}
