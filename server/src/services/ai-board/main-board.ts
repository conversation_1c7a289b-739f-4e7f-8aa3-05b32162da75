/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-12-13 19:04:15
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-07 15:36:36
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { AdFormatMap } from '@/constants';
import { AlertRuleType, TrafficTypeDesc, YesNoMap } from '@/constants/ai-board';
import { Country } from '@/constants/country';
import { DateType } from '@/constants/report/dashboard';
import { AdFormatTypeDesc } from '@/constants/troubleshooting';
import { boardModel, commonModel, demandModel, supplyModel } from '@/models';
import { BoardAPI } from '@/types/ai-board';
import { formatNumberToUnit, md5 } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  LabelGenerationParams,
  transformSQLResult
} from '@rixfe/rix-tools';
import moment from 'moment-timezone';

interface BoardFormData {
  cur_hour: string;
  cur_role_id: number;
  cur_time_zone: string;
  cur_user_id: number;
  tnt_id: number;
  cache_cur_hour?: string;
}

class BoardService implements BoardAPI.BoardService {
  async getOverview(formData: BoardFormData, label: LabelGenerationParams) {
    const {
      cache_cur_hour,
      cur_hour,
      cur_time_zone = 'Etc/UTC',
      ...restParams
    } = formData;

    const cur_new_hour = cur_hour || cache_cur_hour || '';
    const curUtcMoment = moment.tz(cur_new_hour, 'Etc/Utc');
    const endDate = curUtcMoment
      .clone()
      .tz(cur_time_zone)
      .format('YYYY-MM-DD HH:00:00');
    const startDate = moment(endDate)
      .startOf('day')
      .format('YYYY-MM-DD 00:00:00');
    const t_start_hour = moment(startDate).utc().format('YYYY-MM-DD HH:00:00');
    const t_end_hour = cur_new_hour;

    const y_start_hour = moment(t_start_hour)
      .subtract(1, 'days')
      .format('YYYY-MM-DD HH:00:00');
    const y_end_hour = moment(t_end_hour)
      .subtract(1, 'days')
      .format('YYYY-MM-DD HH:00:00');

    const getQuerySql = async (
      start_date: string,
      end_date: string,
      params: {
        cur_time_zone: string;
        restParams: any;
        label: LabelGenerationParams;
      }
    ) => {
      const { restParams, cur_time_zone, label } = params;
      await updateBQConfigAdapter();
      const { fragments } = await adaptGenerateBigQuerySQL(
        {
          ...restParams,
          start_date,
          end_date,
          tz_start_date: start_date,
          tz_end_date: end_date,
          cur_time_zone,
          split_time: DateType.Hour,
          columns: ['day_hour'],
          metrics: ['profit', 'request', 'ecpr'],
          order: 'asc',
          order_key: ['date']
        },
        { api_url: label.tag }
      );
      return concatSQLFragments({
        ...fragments,
        limit: ''
      });
    };

    // 获取今日和昨日的 SQL
    const today_sql = await getQuerySql(t_start_hour, t_end_hour, {
      cur_time_zone,
      restParams,
      label
    });
    const yesterday_sql = await getQuerySql(y_start_hour, y_end_hour, {
      cur_time_zone,
      restParams,
      label
    });

    const datas = await boardModel.getNewOverview(
      { today: today_sql, yesterday: yesterday_sql },
      label
    );

    // 获取 SQL 结果 并处理
    const [today_data, yesterday_data] = datas.map(data => {
      return transformSQLResult(data, {
        rowProcessingOptions: {
          metrics: ['profit', 'request', 'ecpr'],
          context: {
            start_date: t_start_hour,
            end_date: t_end_hour,
            split_time: DateType.Hour,
            timezone: cur_time_zone
          }
        }
      });
    });
    const todayMap = today_data.reduce((prev: any, next: any) => {
      if (!prev) {
        return {
          request: +next.request || 0,
          revenue: +next.buyer_net_revenue || 0,
          profit: +next.profit || 0
        };
      }
      return {
        request: prev.request + (+next.request || 0),
        revenue: prev.revenue + (+next.buyer_net_revenue || 0),
        profit: prev.profit + (+next.profit || 0)
      };
    }, null);
    const yesterdayMap = yesterday_data.reduce((prev: any, next: any) => {
      if (!prev) {
        return {
          request: +next.request || 0,
          revenue: +next.buyer_net_revenue || 0,
          profit: +next.profit || 0
        };
      }
      return {
        request: prev.request + (+next.request || 0),
        revenue: prev.revenue + (+next.buyer_net_revenue || 0),
        profit: prev.profit + (+next.profit || 0)
      };
    }, null);
    const result: BoardAPI.OverviewItem[] = [];
    if (todayMap && yesterdayMap) {
      const todayRev: number = todayMap.revenue;
      const todayProfit: number = todayMap.profit;
      const todayReq: number = todayMap.request;
      const todayEcpr: number =
        todayReq > 0 ? (todayRev * 1000000) / todayReq : 0;
      const yesRev: number = yesterdayMap.revenue || 0;
      const yesProfit: number = yesterdayMap.profit || 0;
      const yesReq: number = yesterdayMap.request || 0;
      const yesEcpr: number = yesterdayMap.request
        ? (yesterdayMap.revenue * 1000000) / yesterdayMap.request
        : 0;
      const revenue_increase: number =
        yesRev > 0 ? +(((todayRev - yesRev) / yesRev) * 100).toFixed(2) : 0;
      const profit_increase: number =
        yesProfit > 0
          ? +(((todayProfit - yesProfit) / yesProfit) * 100).toFixed(2)
          : 0;
      const request_increase: number =
        yesReq > 0 ? +(((todayReq - yesReq) / yesReq) * 100).toFixed(2) : 0;

      const ecpr_increase: number =
        yesEcpr > 0 ? +(((todayEcpr - yesEcpr) / yesEcpr) * 100).toFixed(2) : 0;
      result.push({
        revenue: formatNumberToUnit(todayRev),
        revenue_increase,
        profit: formatNumberToUnit(todayProfit),
        profit_increase,
        request: formatNumberToUnit(todayReq),
        request_increase,
        ecpr: formatNumberToUnit(todayEcpr),
        ecpr_increase,
        hours_data: today_data.map(({ buyer_net_revenue, ...rest }) => ({
          ...rest,
          revenue: +buyer_net_revenue,
          ecpr: +rest.ecpr
        })),
        update_time: moment
          .tz(cur_new_hour, 'Etc/Utc')
          .clone()
          .tz(cur_time_zone)
          .format('YYYY-MM-DD HH:mm:ss')
      });
    }
    return Promise.resolve(result);
  }

  async getTopCountry(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams
  ): Promise<{ profit: any; data: BoardAPI.TopCountryAdFormatItem[] }> {
    const topCountryData = await boardModel.getTopCountry(
      cur_hour,
      cur_time_zone,
      tnt_id,
      label
    );
    if (topCountryData?.length) {
      const topCtyObj: any = {}; // 按国家维度分组
      const topCtyAdObj: any = {}; // 国家+ad_format维度分组

      topCountryData.forEach(v => {
        const cty_format_key = `${v.country}_${v.ad_format}`;

        if (!topCtyObj[v.country]) {
          topCtyObj[v.country] = {
            country: v.country,
            revenue: v.revenue,
            sl_revenue: v.sl_revenue
          };
        } else {
          topCtyObj[v.country] = {
            country: v.country,
            revenue: topCtyObj[v.country].revenue + v.revenue,
            sl_revenue: topCtyObj[v.country].sl_revenue + v.sl_revenue
          };
        }

        if (!topCtyAdObj[cty_format_key]) {
          topCtyAdObj[cty_format_key] = {
            country: v.country,
            ad_format: v.ad_format,
            revenue: v.revenue
          };
        } else {
          topCtyAdObj[cty_format_key] = {
            country: v.country,
            ad_format: v.ad_format,
            revenue: topCtyAdObj[cty_format_key].revenue + v.revenue
          };
        }
      });
      // rev前五国家单独列出，其他国家合并为其他
      const topCountryResult: BoardAPI.TopCountryAdFormatItem[] =
        Object.values<BoardAPI.TopCountryAdFormatItem>(topCtyObj).reduce(
          (prev: any, next: any) => {
            if (prev.length < 5) {
              prev.push({
                country: next.country,
                revenue: +next.revenue,
                sl_revenue: +next.sl_revenue
              });
            } else if (!prev[5]) {
              prev[5] = next;
            } else {
              prev[5] = {
                country: 'Other',
                revenue: +prev[5].revenue + +next.revenue,
                sl_revenue: +prev[5].sl_revenue + +next.sl_revenue
              };
            }
            return prev;
          },
          []
        );

      const top_countries = topCountryResult
        .map(item => item.country)
        .filter(item => item !== 'Other');

      const topAdFormatInCtyObj: any = {};

      Object.keys(topCtyAdObj).forEach(v => {
        const item = topCtyAdObj[v];
        if (
          !topAdFormatInCtyObj[item.country] &&
          top_countries.includes(item.country)
        ) {
          topAdFormatInCtyObj[item.country] = [
            {
              ad_format: item.ad_format,
              revenue: item.revenue
            }
          ];
        } else if (
          !topAdFormatInCtyObj[item.country] &&
          !top_countries.includes(item.country)
        ) {
          if (topAdFormatInCtyObj.Other) {
            topAdFormatInCtyObj.Other.push({
              ad_format: item.ad_format,
              revenue: item.revenue
            });
          } else {
            topAdFormatInCtyObj.Other = [
              {
                ad_format: item.ad_format,
                revenue: item.revenue
              }
            ];
          }
          // 合并topAdFormatInCtyObj['Other']里ad_format相同的项
          const otherObj: any = {};
          topAdFormatInCtyObj.Other.forEach((v: any) => {
            if (!otherObj[v.ad_format]) {
              otherObj[v.ad_format] = v;
            } else {
              otherObj[v.ad_format] = {
                ...v,
                revenue: otherObj[v.ad_format].revenue + v.revenue
              };
            }
          });
          topAdFormatInCtyObj.Other = Object.values(otherObj).map((v: any) => ({
            ...v,
            revenue: +v.revenue
          }));
        } else {
          topAdFormatInCtyObj[item.country].push({
            // country: item.country,
            ad_format: item.ad_format,
            revenue: item.revenue
          });
        }
      });

      // 最终数据
      topCountryResult.forEach(item => {
        item.revenue = +item.revenue;
        item.sl_revenue = +item.sl_revenue;
        // 每个国家的top ad format
        if (topAdFormatInCtyObj[item.country]) {
          item.top_ad_formats = topAdFormatInCtyObj[item.country].map(
            (v: any) => {
              // 每个国家的top ad format的top ad size
              const top_ad_sizes = topCountryData
                .filter(
                  (ad_size: any) =>
                    ad_size.ad_format === v.ad_format &&
                    ad_size.country === item.country
                )
                .slice(0, 3);
              return {
                ad_format: AdFormatMap[v.ad_format],
                revenue: +v.revenue,
                top_ad_sizes
              };
            }
          );
        }
      });
      getLogger('app').info(
        `topCountryData: [${JSON.stringify(topCountryResult, null, 2)}]`
      );
      const profit = topCountryResult.reduce((prev: any, next: any) => {
        const p = next.revenue - next.sl_revenue;
        return prev + p;
      }, 0);
      const result = {
        profit: +profit,
        data: topCountryResult
      };
      return Promise.resolve(result);
    }
    return Promise.resolve({ profit: 0, data: [] });
  }

  async getSupplyDemand(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams
  ): Promise<{
    supplyData: Partial<BoardAPI.SupplyDemandItem>[];
    demandData: Partial<BoardAPI.SupplyDemandItem>[];
  }> {
    const [supplyDemandData, demandList, supplyList] = await Promise.all([
      boardModel.getSellerDemand(cur_hour, cur_time_zone, tnt_id, label),
      demandModel.getDemandList(tnt_id, cur_time_zone, true),
      supplyModel.getSupplyList(tnt_id, cur_time_zone, undefined, true)
    ]);

    // 上下游id -> name 映射
    const demandIdMap: { [key: string]: string } = {};
    const supplyIdMap: { [key: string]: string } = {};
    demandList?.forEach(v => {
      demandIdMap[v.buyer_id] = `${v.buyer_name}`;
    });
    supplyList?.forEach(v => {
      supplyIdMap[v.seller_id] = `${v.seller_name}`;
    });

    // 上下游收入映射 id -> revenue
    const supplyRevObj: { [key: string]: number } = {};
    const demandRevObj: { [key: string]: number } = {};
    // 下游ad_format映射 id -> [{ad_format,revenue},...]
    const supplyAdFormatObj: {
      [key: string]: BoardAPI.SupplyDemandItem['top_ad_formats'];
    } = {};
    // 上游country映射 id -> [{country,revenue},...]
    const demandCountryObj: {
      [key: string]: BoardAPI.SupplyDemandItem['top_countries'];
    } = {};
    supplyDemandData.forEach(v => {
      v.buyer_name = demandIdMap[v.buyer_id] || `${v.buyer_id}`;
      v.seller_name = supplyIdMap[v.seller_id] || `${v.seller_id}`;
      v.ad_format = AdFormatMap[v.ad_format];

      if (!supplyRevObj[v.seller_id]) {
        supplyRevObj[v.seller_id] = v.revenue;
      } else {
        supplyRevObj[v.seller_id] = +supplyRevObj[v.seller_id] + +v.revenue;
      }

      if (!demandRevObj[v.buyer_id]) {
        demandRevObj[v.buyer_id] = v.revenue;
      } else {
        demandRevObj[v.buyer_id] = +demandRevObj[v.buyer_id] + +v.revenue;
      }

      if (!supplyAdFormatObj[v.seller_id]) {
        supplyAdFormatObj[v.seller_id] = [
          {
            ad_format: v.ad_format,
            revenue: v.revenue
          }
        ];
      } else {
        const item = supplyAdFormatObj[v.seller_id]?.find(
          item => item.ad_format === v.ad_format
        );
        if (item) {
          item.revenue = (item.revenue || 0) + v.revenue;
        } else {
          supplyAdFormatObj[v.seller_id]?.push({
            ad_format: v.ad_format,
            revenue: v.revenue
          });
        }
      }

      if (!demandCountryObj[v.buyer_id]) {
        demandCountryObj[v.buyer_id] = [
          {
            country: v.country,
            revenue: v.revenue
          }
        ];
      } else {
        const item = demandCountryObj[v.buyer_id]?.find(
          item => item.country === v.country
        );
        if (item) {
          item.revenue = (item.revenue || 0) + v.revenue;
        } else {
          demandCountryObj[v.buyer_id]?.push({
            country: v.country,
            revenue: v.revenue
          });
        }
      }
    });

    const supplyData = Object.keys(supplyRevObj)
      .map(v => {
        const top_ad_formats = supplyAdFormatObj[v] || [];
        top_ad_formats.forEach(item => {
          item.revenue = +item.revenue;
        });
        return {
          seller_id: v,
          seller_name: supplyIdMap[`${v}`] || v,
          revenue: +supplyRevObj[v],
          top_ad_formats
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    const demandData = Object.keys(demandRevObj)
      .map(v => {
        let top_countries = demandCountryObj[v] || [];
        top_countries = top_countries.slice(0, 3).map(item => ({
          ...item,
          revenue: +item.revenue
        }));
        return {
          buyer_id: v,
          buyer_name: demandIdMap[`${v}`] || v,
          revenue: +demandRevObj[v],
          top_countries
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);
    const result = {
      supplyData,
      demandData
    };
    return Promise.resolve(result);
  }

  async getTopAdFormatEcpmAndEcpr(
    cur_hour: string,
    cur_time_zone: string,
    tnt_id: number,
    label: LabelGenerationParams
  ): Promise<BoardAPI.EcpmAndEcprItem[]> {
    const data = await boardModel.getTopAdFormatEcpmAndEcpr(
      cur_hour,
      cur_time_zone,
      tnt_id,
      label
    );
    const adFormatObj: any = {};
    data.forEach(v => {
      if (!adFormatObj[v.ad_format]) {
        adFormatObj[v.ad_format] = {
          ad_format: v.ad_format,
          revenue: v.revenue
        };
      } else {
        adFormatObj[v.ad_format] = {
          ad_format: v.ad_format,
          revenue: adFormatObj[v.ad_format].revenue + v.revenue
        };
      }
    });
    const top2AdFormat =
      Object.keys(adFormatObj)
        .sort((a, b) => adFormatObj[b].revenue - adFormatObj[a].revenue)
        ?.slice(0, 2) || [];

    const result = data
      .filter(v => top2AdFormat.includes(`${v.ad_format}`))
      ?.map(v => ({
        ...v,
        ad_format: AdFormatMap[v.ad_format]
      }));
    return result;
  }

  async getBudgetAndTraffic(
    cur_hour: string,
    tnt_id: number,
    cur_time_zone: string,
    labels: LabelGenerationParams
  ): Promise<{
    data: BoardAPI.BudgetAndTrafficItem[];
    update_time: string;
  }> {
    const [topCountryData, trafficReqList] = await Promise.all([
      boardModel.getBudgetAndTraffic({
        cur_hour,
        cur_time_zone,
        tnt_id,
        labels
      }),
      boardModel.getTffReqLog(tnt_id, cur_time_zone)
    ]);
    const top5Obj: any = {};
    const topEObj: any = {};
    const top5Len: any = {};
    const topRev: any = {};
    topCountryData.forEach(v => {
      if (!top5Len[v.country]) {
        top5Len[v.country] = { revenue: v.revenue, country: v.country };
      } else {
        top5Len[v.country] = {
          revenue: top5Len[v.country].revenue + v.revenue,
          country: v.country
        };
      }
      const key = `${v.ad_format}_${v.country}`;
      if (!topRev[key]) {
        topRev[key] = v;
      } else {
        topRev[key] = { ...v, revenue: v.revenue + topRev[key].revenue };
      }
    });

    // 收入符合的
    const revKeys = Object.keys(topRev)
      .map(v => topRev[v])
      .filter(v => v.revenue > 100)
      .map(v => `${v.ad_format}_${v.country}`);
    const arr: any[] = [];
    Object.keys(top5Len).forEach(v => arr.push(top5Len[v]));
    arr.sort((a, b) => b.revenue - a.revenue);
    // 总收入前5的国家
    const top5C = arr.slice(0, 5).map(v => v.country);
    // 总收入前20国家
    const top20C = arr.slice(0, 20).map(v => v.country);

    topCountryData.forEach((v: any) => {
      const key = `${v.ad_format}_${v.country}`;
      // country + ad_format 收入 > 100
      if (revKeys.includes(key)) {
        // 总收入前5国家
        if (top5C.includes(v.country)) {
          if (!top5Obj[key]) {
            top5Obj[key] = {
              ...v,
              isRevenue: true,
              ecpr: +v.ecpr,
              buyer_options: [v],
              revenue: topRev[key].revenue
            };
          } else if (top5Obj[key].buyer_options.length < 3) {
            top5Obj[key].buyer_options.push(v);
          }
        }
        // 总收入前20加ecpr > 4
        if (+v.ecpr > 4 && top20C.includes(v.country)) {
          if (!topEObj[key]) {
            topEObj[key] = {
              ...v,
              ecpr: +v.ecpr,
              isRevenue: false,
              buyer_options: [v],
              revenue: topRev[key].revenue
            };
          } else if (topEObj[key].buyer_options.length < 3) {
            topEObj[key].buyer_options.push(v);
          }
        }
      }
    });

    // 收入前五的国家
    const top5Cty: any[] = [];
    // const topEcpr: any[] = [];
    Object.keys(top5Obj).forEach(v => {
      top5Cty.push(top5Obj[v]);
    });
    // Object.keys(topEObj).forEach(v => {
    //   topEcpr.push(topEObj[v]);
    // });
    // const result = [...top5Cty, ...topEcpr];
    const result = [...top5Cty];
    result.forEach(item => {
      const bids = item.buyer_options.map((item: any) => item.buyer_id);
      const timeStr = moment().tz('Etc/UTC').format('YYYYMMDD');
      const key = md5(
        `${tnt_id}_${bids.join(',')}_${item.ad_format}_${
          item.country
        }_${timeStr}`
      );
      item.key = key;
      const index = trafficReqList?.findIndex((v: any) => v.mixed_key === key);
      item.isReuqested = index > -1;
    });
    return {
      data: result,
      update_time: moment
        .tz(cur_hour, 'Etc/Utc')
        .clone()
        .tz(cur_time_zone)
        .subtract(1, 'days')
        .format('YYYY-MM-DD')
    };
  }

  async trafficRequest(params: BoardAPI.requestTrafficParams) {
    const { tnt_id, ad_format, country, ext_1, ext_2, user_id, buyer_ids } =
      params;
    const timeStr = moment().tz('Etc/UTC').format('YYYYMMDD');
    const result = await Promise.all([
      commonModel.getTenantByExceptId(tnt_id),
      commonModel.getDemandByIds(buyer_ids, tnt_id)
    ]);
    const data: any[] = result[0] || [];
    const buyers: any[] = result[1] || [];
    const buyerObj: any = {};
    buyers.forEach(v => {
      buyerObj[v.buyer_id] = `${v.buyer_name}(${v.buyer_id})`;
    });
    // 固定5
    const rule_id = AlertRuleType.Trading;
    const ids = data.map(v => v.tnt_id);
    const title = '【TRA】Find Traffic';
    const adsStr = +ext_1 === YesNoMap.Yes ? '*Need to add Ads.txt.' : '';
    const trafficTypeStr = TrafficTypeDesc[ext_2] || '';
    const ad_format_desc = AdFormatTypeDesc[ad_format] || '';
    const country_desc = Country[country];

    // const buyerStr = buyer_ids.map(v => buyerObj[v] || v).join(',');
    // const txtStr = +ext_1 === YesNoMap.Yes ? 'Ads.txt' : '';

    // const msg = `${buyerStr};${country_desc};${ad_format_desc};${txtStr}`;
    const msg = `Your ${country_desc}[${ad_format_desc}] revenue is good, would you like to request more inventory from RIX?`;
    const key = md5(
      `${tnt_id}_${buyer_ids.join(',')}_${ad_format}_${country}_${timeStr}`
    );
    const content = `RIX has ${country_desc}[${ad_format_desc}, ${trafficTypeStr}] budget, but lack of traffic. ${adsStr}
    If you want to cooperate, please contact your account manager. 
    Code: [${key}]`;
    const options = {
      content,
      title,
      rule_id,
      tnt_id,
      tnt_ids: ids,
      ext_1,
      ext_2,
      user_id,
      msg,
      key
    };

    return await boardModel.trafficRequest(options);
  }

  async getTffReqLog(tnt_id: number, cur_time_zone: string) {
    return await boardModel.getTffReqLog(tnt_id, cur_time_zone);
  }
}

export const boardService = new BoardService();
