/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-01 14:58:53
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-02 17:59:59
 * @Description:
 */
import { Context } from 'koa';
// @ts-ignore
import JSONStream from 'JSONStream';
import { PassThrough } from 'stream';
import { humanService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import {
  HumanReportScheme,
  DownloadHumanReportSchema
} from '@/schema/report/human-report';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

class HumanController {
  @validateBody(HumanReportScheme)
  async getHumanReport(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];

    const params = humanService.getReportParams(formData);
    const report = await humanService.getHumanReport(params, api_url);
    if (report) {
      result = getCtxResult('SUCCESS', report);
    }
    ctx.body = result;
  }

  @validateBody(DownloadHumanReportSchema)
  async downloadHumanReport(ctx: Context) {
    const result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const params = humanService.getReportParams(formData);
    const stream = await humanService.downloadHumanReport(params, api_url);
    if (stream) {
      ctx.body = stream
        .on('error', (err) => {
          getLogger('error').error(`downloadHumanReport error: [${err?.message}]`);
          stream.end();
          ctx.body = result;
        })
        .on('end', () => {
          stream.end();
        })
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}
export const humanController = new HumanController();
