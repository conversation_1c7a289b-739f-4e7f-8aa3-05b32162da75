/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-04 16:02:27
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-04 16:02:27
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getTffReqLog } from '@/services/ai-board';

export default function useTrafficReqLog() {
  const { data, run, loading, error } = useCustomRequest(getTffReqLog, {
    cacheKey: 'traffic-request-log-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
