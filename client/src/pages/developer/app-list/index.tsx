/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 10:40:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-25 11:59:54
 * @Description:
 */
import React, { useState, useEffect, useMemo } from 'react';
import { useModel } from '@umijs/max';
import styles from './index.less';

import PageContainer from '@/components/RightPageContainer';
import AddAppModel from '../components/AddAppDrawer';
import AppPlacement from '../components/AppPlacement';
import AppLeft from '../components/AppLeft';
import { AppBreadOptions, IntegrationType } from '@/constants/app-list';

import { getSupplyAppPlacement } from '@/services/api';
import { fetchData } from '@/utils';
import { ChannelTypeMap } from '@/constants/supply';

const Page: React.FC = () => {
  const {
    reload: reloadDashboardSupply,
    dataSource: supplyList,
    loading: dashboardSupplyLoading
  } = useModel('useDashboardSupplyList');

  const [filterList, setFilterList] = useState<AppAPI.AppListItem[]>([]);
  const [currentApp, setCurrentApp] = useState<AppAPI.AppListItem | undefined>();
  const [supplyIndex, setSupplyIndex] = useState<number | undefined>();
  const [supply, setSupply] = useState<SupplyAPI.SupplyListItem | undefined>(undefined);
  const [isSDK, setIsSDK] = useState(false);
  const [isJstag, setIsJstag] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isSite, setIsSite] = useState(false);
  const [isVastTag, setIsVastTag] = useState(false);
  const [visible, setVisible] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [appList, setAppList] = useState<AppAPI.AppListItem[]>([]);
  const [itemColor, setItemColor] = useState('');

  useEffect(() => {
    reloadDashboardSupply();
  }, []);

  useEffect(() => {
    if (supply) {
      setIsSDK(supply.integration_type_desc.toLowerCase().includes('sdk'));
      setIsJstag(supply.integration_type_desc.toLowerCase() === 'max-jstag');
      setIsIOS(supply.integration_type === IntegrationType['iOS SDK']);
      setIsSite(supply.channel_type === ChannelTypeMap['Site']);
      setIsVastTag(supply.integration_type === IntegrationType['VastTag']);

      handleGetAppList();
    } else {
      setIsSDK(false);
    }
  }, [supply]);

  useEffect(() => {
    // 默认第一条
    if (supplyList?.length) {
      const supply = supplyList[0];
      setSupply(supply);
      setSupplyIndex(supply.seller_id);
    }
  }, [supplyList]);

  useEffect(() => {
    if (filterList && filterList.length) {
      // 判断当前选择的app在不在里面，再就不用改
      const item = filterList.find(item => item.app_id === currentApp?.app_id);
      if (!item) {
        setCurrentApp(filterList[0]);
      } else {
        setCurrentApp(item);
      }
    } else {
      setCurrentApp(undefined);
    }
  }, [filterList]);

  useEffect(() => {
    if (appList && appList.length) {
      // 不改变当前选择的app
      setFilterList(appList);
    } else {
      setFilterList([]);
      setCurrentApp(undefined);
    }
  }, [appList]);

  const handleSearch = (val: string) => {
    if (val) {
      const list = appList.filter((item: AppAPI.AppListItem) => {
        return (
          item.app_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 ||
          item.app_id.toString().indexOf(val) !== -1 ||
          item.bundle.indexOf(val.toLowerCase()) !== -1
        );
      });
      setFilterList(list);
    } else {
      setFilterList(appList);
    }
  };

  const handleChangeCurrentApp = (current: AppAPI.AppListItem | undefined, itemColor: string) => {
    setCurrentApp(current);
    setItemColor(itemColor);
  };

  const handleCloseModel = () => {
    setVisible(false);
  };

  const handleOpenModel = () => {
    setVisible(true);
  };

  const handleSupplyChange = (value: number) => {
    setSupplyIndex(value);
    const supply = supplyList.find((item: SupplyAPI.SupplyListItem) => item.seller_id === value);

    setSupply(supply);
    setItemColor(`linear-gradient(163deg,#0A5CF4,#25A1FF)`);
  };

  const handleGetAppList = (isEdit?: boolean) => {
    const onSuccess = (data: any) => {
      setAppList(data);
      // 排除placement情况
      if (!isEdit) {
        const item = data && data[0];
        setCurrentApp(item);
      }
    };
    fetchData({
      setLoading: setDataLoading,
      request: getSupplyAppPlacement,
      params: { seller_id: supply?.seller_id, developer: 1 },
      onSuccess
    });
  };

  return (
    <PageContainer options={AppBreadOptions}>
      <div className={styles['main']}>
        {useMemo(() => {
          return (
            <AppLeft
              supply={supply}
              supplyIndex={supplyIndex}
              supplyList={supplyList}
              supplyLoading={dashboardSupplyLoading}
              dataLoading={dataLoading}
              filterList={filterList}
              currentApp={currentApp}
              handleSupplyChange={handleSupplyChange}
              handleOpenModel={handleOpenModel}
              handleSearch={handleSearch}
              handleChangeCurrentApp={handleChangeCurrentApp as any}
              isDeveloper={true}
              maxHeight="calc(100vh - 250px)"
              auth="addAppAuth"
              isSite={isSite}
            />
          );
        }, [supply, supplyList, dataLoading, filterList, currentApp])}
        <AppPlacement
          loading={dataLoading}
          dataSource={currentApp?.adslots || []}
          rowKey={'plm_id'}
          isSDK={isSDK}
          isJstag={isJstag}
          isIOS={isIOS}
          isVastTag={isVastTag}
          supply={supply}
          disabled={!currentApp}
          currentApp={currentApp}
          fetchAppList={handleGetAppList}
          itemColor={itemColor || `linear-gradient(163deg,#0A5CF4,#25A1FF)`}
          isSite={isSite}
        />
      </div>
      <AddAppModel
        visible={visible}
        isEdit={false}
        handleClose={handleCloseModel}
        supply={supply}
        fetchAppList={handleGetAppList}
        isIOS={isIOS}
        isSite={isSite}
        isVastTag={isVastTag}
      />
    </PageContainer>
  );
};

export default Page;
