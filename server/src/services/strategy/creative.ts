/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-20 14:19:35
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-20 16:41:33
 * @Description:
 */
import { Context } from 'koa';
import { CreativeAPI } from '@/types/creative';
import { creativeModel } from '@/models';
import { UserType } from '@/constants';
class CreativeService implements CreativeAPI.Creative {
  async getCreativeList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<CreativeAPI.CreativeListItem[]> {
    let list = await creativeModel.getCreativeList(tnt_id, cur_time_zone);
    list = list.map((item: any) => {
      [UserType.Rix_Admin, UserType.Rix_Data_Analyst].includes(
        item.account_type
      ) && (item.op_name = 'System');
      item.account_status === 3 && (item.op_name = 'UnKnow');
      delete item.account_type;
      return item;
    });
    return list;
  }

  async addCreative(params: CreativeAPI.AddCreativeParams): Promise<boolean> {
    return await creativeModel.addCreative(params);
  }

  async updateCreative(
    params: CreativeAPI.updateCreativeParams
  ): Promise<boolean> {
    return await creativeModel.updateCreative(params);
  }
}
export const creativeService = new CreativeService();
