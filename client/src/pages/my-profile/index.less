.container {
  background-color: #fff;
  border-radius: 6px;
  margin: 0 20px;
  padding: 28px 36px;

  .top {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 16px;
    .tab {
      margin-right: 16px;
    }
    .top-right {
      display: flex;
      align-items: center;
    }
    .top-left {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      flex: 1;
      .ant-avatar {
        margin-right: 16px;
      }
    }
    h3 {
      margin-bottom: 0px;
      color: var(--text-color);
      font-weight: 700;
      font-size: 16px;
      padding-left: 14px;
    }
    svg {
      color: #8d9799;
    }
    :global {
      .ant-radio-button-wrapper {
        height: 32px;
        box-shadow: none;
        border: none;
        font-weight: bold;
        color: #5e6466;
        &:hover {
          color: var(--primary-color);
        }
        span {
          line-height: 32px;
        }
        span:last-child {
          white-space: nowrap;
        }
        .ant-radio-button {
          border-radius: 6px;
        }
        background-color: #fff;
      }
      .ant-radio-button-wrapper-checked {
        color: var(--primary-color);
      }
      .ant-radio-group {
        background-color: #fff;
      }
      .ant-radio-button-checked {
        background-color: var(--background-color);
      }
    }
  }

  .copy-container {
    background: #edeff0;
    border-radius: 6px;
    height: 32px;
    max-width: 418px;
    display: flex;
    align-items: center;
    > span {
      color: #8d9799;
      padding-left: 12px;
      flex: 1;
      cursor: not-allowed;
    }
    :global {
      .ant-typography {
        margin-bottom: 0px;
        height: 32px;
        line-height: 32px;
        background: var(--primary-color);
        border-radius: 0px 6px 6px 0px;
        width: 51px;
        display: flex;
        .ant-typography-copy {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          text-align: center;
          // margin-left: 8px;
          margin: auto;
        }
      }
    }
  }
  .delete-btn {
    svg {
      vertical-align: bottom;
    }
  }

  :global {
    .ant-layout {
      min-height: 0 !important;
    }
    .ant-form-item-label > label {
      color: #5e6466;
    }
    .ant-input[disabled] {
      background: #edeff0;
    }
    .ant-typography-copy-success,
    .ant-typography-copy-success:hover,
    .ant-typography-copy-success:focus {
      svg {
        color: #fff;
      }
    }
  }
}
.top-dropdown {
  :global {
    .ant-dropdown-menu {
      border-radius: 6px;
      padding: 12px;
    }
    .ant-dropdown-menu-item {
      border-radius: 6px;
      &:hover {
        background-color: #edeff0;
      }
    }
  }
}
