/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:11:59
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 19:20:46
 * @Description:
 */
import { Context } from 'koa';
import { capService } from '@/services';
import { CapAPI } from '@/types/cap';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import { AddCapSchema, UpdateCapSchema } from '@/schema/strategy/cap';
import { RESULT } from '@/constants';

class CapCtrl implements CapAPI.CapCtrlInterface {
  async getCapList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await capService.getCapList(
      ctx.session!.tnt_id,
      cur_time_zone
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddCapSchema)
  async addCap(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const data = await capService.isCapExists(formData);
    if (data && data.length) {
      result = getCtxResult('CAP_EXISTS', data);
    } else {
      const list = await capService.addCap(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(`addCap success formData=[${JSON.stringify(formData)}]`);
      }
    }
    ctx.body = result;
  }

  @validateBody(UpdateCapSchema)
  async updateCap(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    // 更新不用去重
    const list = await capService.updateCap(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updateCap success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }
}

export default new CapCtrl();
