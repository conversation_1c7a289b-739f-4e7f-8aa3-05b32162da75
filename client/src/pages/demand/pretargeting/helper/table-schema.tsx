import { demandCampaign } from '@/constants/demand/demand-campaign';

// 用于展示 table 数据的 column 配置
// 定义 column 的结构类型 需要添加映射管理

export type TableColumnSchema = {
  title: string;
  name: string;
  key: string;
  values: any;
  isDisabled?: boolean;
  minWidth?: number;
  tooltip?: string;
  optionMapping?: Record<string | number, string>;
};

// 定义原始数据项的类型
export interface RawPretargetingItem {
  campaign_id: number;
  campaign_name: string;
  buyer_id: number;
  status: number;
  pt_flag: string;
  op_id: number;
  update_time: string;
  op_name: string;
  [key: string]: any; // 用于其他动态字段
}

// 定义转换后的目标字段类型
export interface TargetingField {
  value: any;
  level: number;
  status: string;
}

// 定义转换后的数据结构
export interface TransformedPretargetingData {
  baseInfo: {
    campaign_id: number;
    campaign_name: string;
    buyer_id: number;
    status: number;
    pt_flag: string;
    op_id: number;
    update_time: string;
    op_name: string;
  };
  targetingFields: {
    [key: string]: TargetingField;
  };
}

// 将值转换为映射值
const transformValueToLabel = (value: any, optionMapping?: Record<string | number, string>): any => {
  if (!optionMapping) return value;

  if (Array.isArray(value)) {
    return value.map(v => optionMapping[v] || v);
  }

  return optionMapping[value] || value;
};

// 转换函数：将原始数据转换为目标格式
export const transformPretargetingData = (rawData: RawPretargetingItem): TransformedPretargetingData => {
  // 提取基础信息
  const baseInfo = {
    campaign_id: rawData.campaign_id,
    campaign_name: rawData.campaign_name,
    buyer_id: rawData.buyer_id,
    status: rawData.status,
    pt_flag: rawData.pt_flag,
    op_id: rawData.op_id,
    update_time: rawData.update_time,
    op_name: rawData.op_name
  };

  const targetingFields: { [key: string]: TargetingField } = {};

  // 遍历原始数据的所有键
  Object.keys(rawData).forEach(key => {
    // 检查是否是targeting字段（通过检查是否有对应的level和status字段）
    const baseFieldMatch = key.match(/^([^-]+)$/);
    if (baseFieldMatch) {
      const baseField = baseFieldMatch[1];
      const levelKey = `${baseField}-level`;
      const statusKey = `${baseField}-status`;

      // 如果存在对应的level和status字段，则这是一个targeting字段
      if (rawData[levelKey] !== undefined && rawData[statusKey] !== undefined) {
        targetingFields[baseField] = {
          value: rawData[baseField],
          level: Number(rawData[levelKey]),
          status: rawData[statusKey]
        };
      }
    }
  });

  return {
    baseInfo,
    targetingFields
  };
};

// 将转换后的数据应用到schema
export const applyDataToSchema = (
  schema: TableColumnSchema[],
  transformedData: TransformedPretargetingData
): TableColumnSchema[] => {
  return schema.map(column => {
    const fieldData = transformedData.targetingFields[column.key];
    if (fieldData) {
      // 转换值为映射值
      const displayValues = transformValueToLabel(fieldData.value, column.optionMapping);

      return {
        ...column,
        values: displayValues,
        isDisabled: fieldData.status === 'Block'
      };
    }
    return column;
  });
};

// 根据入参生成 schema
type GenerateTableColumnSchemaParams = {
  adSizeMapByValue?: Record<string | number, string>;
  supplyMapByValue?: Record<string | number, string>;
};

export const generateTableColumnSchema = (params: GenerateTableColumnSchemaParams): TableColumnSchema[] => {
  return [
    {
      title: 'Publisher',
      name: 'seller',
      key: 'seller',
      values: [],
      optionMapping: params.supplyMapByValue || ({} as Record<string | number, string>),
      isDisabled: false,
      minWidth: 160
    },
    {
      title: 'Delivery Time Slot',
      name: 'time_slot',
      key: 'time_slot',
      values: [],
      optionMapping: demandCampaign.TimeSlot,
      minWidth: 200,
      tooltip: '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported.'
    },
    {
      title: 'Country',
      name: 'country',
      key: 'country',
      isDisabled: false,
      values: [],
      optionMapping: demandCampaign.Country,
      minWidth: 150
    },
    {
      title: 'Device',
      name: 'platform',
      key: 'platform',
      values: [],
      optionMapping: demandCampaign.Platform,
      minWidth: 160
    },
    {
      title: 'OS',
      name: 'mobile_os',
      key: 'mobile_os',
      values: [],
      optionMapping: demandCampaign.MoblieOS,
      minWidth: 160
    },
    {
      title: 'Ad Format',
      name: 'ad_platform',
      key: 'ad_platform',
      values: [],
      optionMapping: demandCampaign.AdPlatform,
      minWidth: 160
    },
    {
      title: 'Device Brand',
      name: 'brand',
      key: 'brand',
      isDisabled: false,
      values: [],
      optionMapping: demandCampaign.DeviceBrand,
      minWidth: 160
    },
    {
      title: 'Ad Size',
      name: 'ad_size',
      key: 'ad_size',
      isDisabled: false,
      values: [],
      optionMapping: params.adSizeMapByValue || ({} as Record<string | number, string>),
      minWidth: 160
    },
    {
      title: 'Category',
      name: 'category',
      key: 'category',
      isDisabled: false,
      values: [],
      optionMapping: demandCampaign.Category,
      minWidth: 180
    },
    {
      title: 'Inventory',
      name: 'mobile_inventory',
      key: 'mobile_inventory',
      values: [],
      optionMapping: demandCampaign.MoblieInventory,
      minWidth: 100
    },
    {
      title: 'Network',
      name: 'network',
      key: 'network',
      isDisabled: false,
      values: [],
      optionMapping: demandCampaign.Network,
      minWidth: 160
    },
    {
      title: 'Bundle(Domain)',
      name: 'mobile_app',
      key: 'mobile_app',
      isDisabled: false,
      values: [],
      minWidth: 240
    }
  ];
};
