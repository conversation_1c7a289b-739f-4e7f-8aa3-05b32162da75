/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-07 10:47:56
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-13 11:51:32
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Button, Form, message, Radio } from 'antd';
import { AtcBreadOptions, AtcModelDesc, AtcModelOptions } from '@/constants/strategy/atc';
import { fetchData } from '@/utils';
import { updateAtcModel, getAtcModelList } from '@/services/strategy';
import PageContainer from '@/components/RightPageContainer';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalModal from '@/components/Modal/NormalModal';

const Page: React.FC = () => {
  const [allowSubmit, setAllowSubmit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [model, setModel] = useState(3);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchData({
      setLoading,
      request: getAtcModelList,
      onSuccess: res => {
        setModel(res[0]?.model);
        form.setFieldsValue({ model: res[0]?.model });
      }
    });
  }, []);
  const handleSubmit = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success('Update Success');
    setAllowSubmit(false);
    // setModel(form.getFieldValue('model'));
  };
  const handleFinish = (values: any) => {
    const curModel = values.model;
    setModel(model);
    NormalModal.confirm({
      title: 'Switch ATC Model',
      content: (
        <p>
          Are you sure you want to switch to <span style={{ fontWeight: 'bold' }}>{AtcModelDesc[curModel]} </span>
          model? It is not recommended to switch between different models frequently
        </p>
      ),
      onOk: () => {
        fetchData({
          setLoading,
          request: updateAtcModel,
          params: {
            ...values,
            ori_data: {
              model
            }
          },
          onSuccess
        });
      }
    });
  };

  const handleValueChange = (values: any) => {
    setAllowSubmit(values.model && values.model !== model);
  };

  return (
    <PageContainer options={AtcBreadOptions}>
      <div className={styles['container']}>
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 80 } }}
          wrapperCol={{ span: 24 }}
          onFinish={handleFinish}
          onValuesChange={handleValueChange}
        >
          <Form.Item
            name="model"
            label="Model:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please select atc model'
              }
            ]}
          >
            <NormalSelect style={{ maxWidth: 418 }} options={AtcModelOptions} placeholder="Please Select Atc Model" />
          </Form.Item>

          <Form.Item>
            <div className={styles['footer']}>
              <Button type="primary" onClick={handleSubmit} loading={loading} disabled={!allowSubmit}>
                Submit
              </Button>
            </div>
          </Form.Item>
        </Form>
      </div>
    </PageContainer>
  );
};

export default Page;
