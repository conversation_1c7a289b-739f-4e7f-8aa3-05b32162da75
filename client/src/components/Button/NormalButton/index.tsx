/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: chenmu<PERSON>@algorix.co
 * @LastEditTime: 2023-01-12 11:42:27
 * @Description:
 */
import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import classnames from 'classnames';
import styles from './index.less';

function NormalButton(props: ButtonProps): JSX.Element {
  return <Button {...props} className={classnames(styles['normal-button'], props.className)} />;
}

export default NormalButton;
