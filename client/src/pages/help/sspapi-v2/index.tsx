/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-10-13 14:28:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-23 15:49:16
 * @Description:
 */
import React from 'react';
import MdPage from '../components/MdPage';
// @ts-ignore
import GSContent from '@/assets/md/SSPReportAPI(V2).md';
const SSPApiDoc: React.FC = () => {
  return <MdPage enMdPath={GSContent} isApi></MdPage>;
};

export default SSPApiDoc;
