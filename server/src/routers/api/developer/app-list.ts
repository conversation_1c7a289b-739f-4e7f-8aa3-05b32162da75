/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:38:24
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 11:57:09
 * @Description:
 */

import Router from 'koa-router';
import { appController } from '@/controllers';

const router = new Router({ prefix: '/app' });

const routers = router
  .post('/addApp', appController.addApp)
  .post('/addPlacement', appController.addPlacement)
  .post('/updateApp', appController.updateApp)
  .post('/updatePlacement', appController.updatePlacement)
  .post('/getAppList', appController.getAppList)
  .post('/getAllPlacementList', appController.getAllPlacementList);

export default routers;
