/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 16:00:46
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:50:39
 * @Description:
 */
import Router from 'koa-router';
import { roleController } from '@/controllers';

const {
  addRole,
  updateRole,
  getAllRole,
  editRolePms: editRole,
  deleteRole
} = roleController;
const router = new Router({ prefix: '/role' });

const routers = router
  .post('/addRole', addRole)
  .post('/updateRole', updateRole)
  .post('/getAllRole', getAllRole)
  .post('/editRole', editRole)
  .post('/deleteRole', deleteRole);

export default routers;
