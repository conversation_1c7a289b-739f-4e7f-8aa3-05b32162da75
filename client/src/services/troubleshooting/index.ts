/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-22 11:12:36
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-13 18:35:26
 * @Description:
 */
import { request } from '@umijs/max';

export async function getSampleTraceTaskList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<TroubleShootingAPI.TaskListItem>>('/api/sample/getSampleTraceTaskList', {
    data: body || {},
    ...(options || {})
  });
}

export async function addSampleTraceTask(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sample/addSampleTraceTask', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getSampleTraceList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<TroubleShootingAPI.TraceListItem>>('/api/sample/getSampleTraceList', {
    data: body || {},
    ...(options || {})
  });
}

export async function updateSampleTraceTask(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/sample/updateSampleTraceTask', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}
