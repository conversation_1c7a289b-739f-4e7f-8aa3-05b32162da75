/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 19:28:40
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 11:27:55
 * @Description: e,
 */
import React, { useState, useEffect } from 'react';
import { Form, Input, Radio, Checkbox, Switch, message, Row, Col } from 'antd';
import {
  SupportMimeTypeOptions,
  AllAPIOptions,
  AdPositionOptions,
  PlacementTypeOptions,
  AdSizes,
  PlacementType,
  PlacementTypeFlip,
  AssetsOptions,
  ProtocolsOptions,
  minDurationOptions,
  maxDurationOptions,
  PlDefaultFormData as DefaultFormData
} from '@/constants/app-list';
import styles from './index.less';
import Select from '@/components/Select/NormalSelect';
import { addPlacement, updatePlacement, getFloorList } from '@/services/api';
import { fetchData } from '@/utils';
import NormalRadio from '@/components/Radio/NormalRadio';
import InputNumber from '@/components/Input/InputNumber';
import OldBundleTableEdit from '@/components/OldBundleTableEdit';
import { AdFormatType } from '@/constants/global-mapping/ad-format';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { useModel } from '@umijs/max';
import { SSPFloorType } from '@/constants/strategy/floor';

type AddPlacementProps = {
  visible: boolean;
  isEdit: boolean; // 是新增还是编辑
  handleClose: () => void; // 关闭弹窗
  isSDK: boolean;
  currentApp: AppAPI.AppListItem | undefined;
  fetchAppList: (flag?: boolean) => void;
  placement: AppAPI.PlacementListItem | undefined;
  isJstag: boolean;
  isIOS?: boolean;
  isSite?: boolean;
  isVastTag?: boolean;
};
const getSizeOption = (
  type: 'Banner' | 'Medium Rectangle' | 'Interstitial' | 'Rewarded Video',
  isVastTag?: boolean
) => {
  if (!AdSizes[type]) {
    return [];
  }

  const adSizes = AdSizes[type].map(item => {
    return {
      label: item.replace('|', ' * '),
      value: item
    };
  });
  if (isVastTag) {
    adSizes.push({ label: '1920 * 1080', value: '1920|1080' }, { label: '1080 * 1920', value: '1080|1920' });
  }
  return adSizes;
};

const getAPIOptions = (type: 'Banner' | 'Video') => {
  const values = type === 'Banner' ? [5, 6] : [1, 2, 5, 6];
  return AllAPIOptions.filter(opt => values.indexOf(opt.value) > -1);
};

const getFormValue = (placement: AppAPI.PlacementListItem) => {
  const support_type = [];
  if (placement.support_html) {
    support_type.push('html');
  }
  if (placement.support_video) {
    support_type.push('video');
  }
  const params = {
    plm_name: placement.plm_name,
    placement_type: placement.placement_type,
    ad_size: `${placement.ad_width}|${placement.ad_height}`,
    support_type,
    skip: !!placement.skip,
    banner_api: placement.banner_api
      .split(',')
      .map(val => +val)
      .filter(v => v),
    pos: placement.pos,
    mute: !!placement.mute,
    assets: placement.assets.split(',').map(val => +val),
    protocols: placement.protocols.split(',').map(val => +val),
    minduration: placement.minduration,
    maxduration: placement.maxduration,
    companionad: !!placement.companionad,
    video_api: placement.video_api
      .split(',')
      .map(val => +val)
      .filter(v => v),
    bid_floor: placement.bid_floor
    // price: placement.price,
  };
  return params;
};

const BannerAPIOptions = getAPIOptions('Banner');
const VideoAPIOptions = getAPIOptions('Video');

function AddPlacementModel({
  visible,
  isEdit,
  handleClose,
  isSDK,
  currentApp,
  fetchAppList,
  placement,
  isJstag,
  isIOS,
  isSite,
  isVastTag
}: AddPlacementProps): JSX.Element {
  const [form] = Form.useForm();
  const [placementType, setPlacementType] = useState(0);
  const [supportType, setSupportType] = useState<string[]>([]);
  const [isBanner, setIsBanner] = useState(true);
  const [isVideo, setIsVideo] = useState(false);
  const [isSkip, setIsSkip] = useState(true);
  const [loading, setLoading] = useState(false);
  const [floorLoading, setFloorLoading] = useState(false);
  const [isChangeFloor, setIsChangeFloor] = useState(false);
  const [defaultContentList, setDefaultContentList] = useState<string[]>([]);
  const [defaultFloorList, setDefaultFloorList] = useState<{ [key: string]: number }>({});
  const { initialState } = useModel('@@initialState');
  const [adFormatOptions, setAdFormatOptions] = useState<{ label: string; value: number }[]>(PlacementTypeOptions);
  useEffect(() => {
    if (
      (placementType === PlacementType.Interstitial &&
        supportType.length === 1 &&
        supportType.indexOf('html') !== -1) ||
      placementType === PlacementType.Banner ||
      placementType === PlacementType['Medium Rectangle'] ||
      isJstag ||
      isSite
    ) {
      setIsBanner(true);
    } else {
      setIsBanner(false);
    }
    if (
      (placementType === PlacementType.Interstitial && supportType.indexOf('video') !== -1) ||
      placementType === PlacementType['Rewarded Video']
    ) {
      setIsVideo(true);
    } else {
      setIsVideo(false);
    }
  }, [placementType, supportType]);

  useEffect(() => {
    let options: {
      value: number;
      label: string;
    }[] = [];
    if (isJstag || isSite) {
      options = PlacementTypeOptions.filter(item => item.value < 4);
    } else if (isVastTag) {
      options = PlacementTypeOptions.filter(item =>
        [PlacementType.Interstitial, PlacementType['Rewarded Video']].includes(item.value)
      );
      setSupportType(['video']);
      form.setFieldValue('support_type', ['video']);
    } else {
      options = PlacementTypeOptions;
    }

    setAdFormatOptions(options);
    if (visible && !isEdit) {
      form.setFieldsValue(DefaultFormData);
      setDefaultContentList([]);
      setDefaultFloorList({});
      setPlacementType(options[0].value);
      form.setFieldValue('placement_type', options[0].value);
    }
    if (visible && isEdit) {
      const floorParams = { plm_id: placement?.plm_id, type: SSPFloorType['(SSP) Pub + Ad Unit + Country'] };
      fetchData({
        setLoading: setFloorLoading,
        request: getFloorList,
        params: floorParams,
        onSuccess: getFloorSuccess
      });
      const params = getFormValue(placement!);

      setPlacementType(params.placement_type);
      setSupportType(params.support_type);
      form.setFieldsValue(params);
    }
  }, [visible, isEdit]);
  useEffect(() => {
    console.log('defaultFloorList', defaultFloorList);
  }, [defaultFloorList]);
  const getFloorSuccess = (res: any) => {
    const cList: string[] = [];
    const bList: { [key: string]: number } = {};
    res?.forEach((item: any) => {
      cList.push(item.country);
      bList[item.country] = item.bid_floor;
    });
    setDefaultContentList(cList);
    setDefaultFloorList(bList);
  };
  const handleConfirm = () => {
    form?.submit();
  };

  const handleFinish = (values: any) => {
    const sizeArr = values.ad_size ? values.ad_size.split('|') : [];
    const banner_api = values.banner_api ? values.banner_api.join(',') : '';
    const protocols = values.protocols ? values.protocols.join(',') : '';
    const video_api = values.video_api ? values.video_api.join(',') : '';
    const assets = values.assets ? values.assets.join(',') : '';
    const support_html = values.support_type && values.support_type.indexOf('html') !== -1 ? 1 : 0;
    const support_video = values.support_type && values.support_type.indexOf('video') !== -1 ? 1 : 0;
    let ad_format = AdFormatType.Banner;
    if (isVideo) {
      if (placementType === PlacementType['Rewarded Video']) {
        ad_format = AdFormatType['Reward Video'];
      } else {
        ad_format = AdFormatType.Video;
      }
    }
    if (placementType === PlacementType.Native) {
      ad_format = AdFormatType.Native;
    }

    let content;
    if (
      (values.content && Array.isArray(Object.keys(values.content)) && Object.keys(values.content).length) ||
      isChangeFloor
    ) {
      content = values.content;
    } else {
      content = defaultFloorList;
    }

    const params = {
      ...values,
      plm_name: values.plm_name?.trim(),
      ad_width: +sizeArr[0],
      ad_height: +sizeArr[1],
      banner_api,
      protocols,
      video_api,
      assets,
      support_html,
      support_video,
      ad_format,
      app_id: currentApp?.app_id,
      seller_id: currentApp?.seller_id,
      isSDK,
      op_id: initialState?.currentUser?.user_id,
      isChangeFloor,
      isIOS,
      content,
      isVastTag
    };
    if ([PlacementType['Interstitial']].includes(placementType) && (isVastTag || !isSkip)) {
      params.minduration = 5;
    }
    delete params.ad_size;

    Object.keys(params).forEach(item => {
      if (!params[item] && params[item] !== 0 && typeof params[item] !== 'boolean') {
        delete params[item];
      }
    });

    if (isEdit) {
      params.plm_id = placement?.plm_id;
      params.ori_data = { ...placement, content: { ...defaultFloorList }};
      handleUpdatePlacement(params);
    } else {
      handleAddPlacement(params);
    }
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.placement_type) {
      setPlacementType(changeValue.placement_type);
      form.setFieldValue('ad_size', undefined);
      (isJstag || isSite) && form.setFieldValue('support_type', ['html']);
      isVastTag && form.setFieldValue('support_type', ['video']);
    }
    if (changeValue.support_type) {
      setSupportType(changeValue.support_type);
    }
  };

  const onSuccess = () => {
    message.success('success');
    fetchAppList(true);
    handleCloseDrawer();
  };

  const handleCloseDrawer = () => {
    handleClose();
    handleReset();
  };
  const handleReset = () => {
    console.log('handleReset');
    form.resetFields();
    form.setFieldValue('content', {});
    setDefaultContentList([]);
    setDefaultFloorList({});
    setPlacementType(PlacementType.Banner);
    setSupportType([]);
    setIsBanner(true);
    setIsVideo(false);
    setIsChangeFloor(false);
  };

  const handleAddPlacement = (params: AppAPI.AddPlacementParams) => {
    fetchData({ setLoading, request: addPlacement, params, onSuccess });
  };
  const handleUpdatePlacement = (params: AppAPI.UpdatePlacementParams) => {
    console.log('handleUpdatePlacement', params);
    fetchData({ setLoading, request: updatePlacement, params, onSuccess });
  };

  const handleContentChange = (val: string[]) => {};
  const handleAddSSPBidFloor = (params: any) => {
    console.log('handleAddSSPBidFloor', params);
    setIsChangeFloor(true);
    form.setFieldValue('content', params);
  };

  const handleSkip = (checked: boolean) => {
    console.log('handleSkip', checked);
    setIsSkip(checked);
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Ad Unit` : `Create Ad Unit`}
      grayName={`${currentApp?.app_name}`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleCloseDrawer}
      width={910}
      maskClosable={false}
      loading={loading}
    >
      <div className={styles['container']}>
        <div style={{ maxHeight: 'calc(100vh - 140px)', overflowY: 'auto' }}>
          <Form
            labelCol={{ span: 24 }}
            initialValues={DefaultFormData}
            onFinish={handleFinish}
            onFinishFailed={err => console.log(err)}
            autoComplete="off"
            form={form}
            onValuesChange={handleValueChange}
            layout="vertical"
          >
            <Form.Item
              label="Unit Name:"
              name="plm_name"
              rules={[{ required: true, message: 'Please input unit name!' }]}
              className={styles['form-label-default-padding']}
            >
              <Input style={{ width: 360 }} placeholder="Please input unit name" />
            </Form.Item>
            <Form.Item
              label="Ad Format:"
              name="placement_type"
              rules={[{ required: true, message: 'Please input bundle!' }]}
              className={`${styles['form-label-default-padding']} ${styles['ad-format-item']}`}
            >
              <NormalRadio
                // options={isJstag || isSite ? PlacementTypeOptions.filter(item => item.value < 4) : PlacementTypeOptions}
                options={adFormatOptions}
                disabled={isEdit}
              />
            </Form.Item>
            {placementType !== PlacementType.Native && (
              <Form.Item
                label="Ad Size:"
                name="ad_size"
                rules={[{ required: true, message: 'Please select Ad size!' }]}
                className={styles['form-label-default-padding']}
              >
                <Select
                  options={[...getSizeOption(PlacementTypeFlip[placementType] as any, isVastTag)]}
                  style={{ width: 210 }}
                  placeholder="Please select Ad size"
                />
              </Form.Item>
            )}
            {placementType === PlacementType.Interstitial && (
              <Form.Item
                label="Support MIME Types:"
                name="support_type"
                rules={[{ required: true, message: 'Please select Support MIME types!' }]}
                className={`${styles['form-label-default-padding']} ${styles['form-label-checkbox']}`}
                tooltip={
                  isSite ? undefined : (
                    <span>
                      {'If the video option is selected, it is a video type, otherwise it is a banner type.'}
                      <br></br>
                      {isJstag ? 'Max-JSTag Publisher dose not support Video Ad Format.' : ''}
                    </span>
                  )
                }
              >
                <Checkbox.Group
                  options={
                    isJstag || isSite
                      ? SupportMimeTypeOptions.map(item =>
                        item.value === 'video' ? { ...item, disabled: true } : item
                      )
                      : isVastTag
                        ? SupportMimeTypeOptions.map(item => (item.value === 'html' ? { ...item, disabled: true } : item))
                        : SupportMimeTypeOptions
                  }
                />
              </Form.Item>
            )}
            {isVideo && placementType !== PlacementType['Rewarded Video'] && !isIOS && !isVastTag && (
              <Form.Item
                label="Allow Skip:"
                name="skip"
                rules={[{ required: true, message: 'Please select!' }]}
                className={styles['form-label-default-padding']}
                valuePropName="checked"
                validateTrigger={['onChange', 'onBlur']}
              >
                <Switch onChange={handleSkip} />
              </Form.Item>
            )}
            {placementType === PlacementType.Interstitial && isVideo && isSDK && (
              <Form.Item
                label="Mute:"
                name="mute"
                rules={[{ required: true, message: 'Please select!' }]}
                className={styles['form-label-default-padding']}
                valuePropName="checked"
                validateTrigger={['onChange', 'onBlur']}
              >
                <Switch />
              </Form.Item>
            )}
            {placementType === PlacementType.Native && (
              <Form.Item
                name="assets"
                label="Assets:"
                rules={[{ required: true, message: 'Please select!' }]}
                className={`${styles['form-label-default-padding']} ${styles['form-label-checkbox']}`}
              >
                <Checkbox.Group options={AssetsOptions} />
              </Form.Item>
            )}
            <Form.Item
              label="Bid Floor:"
              className={styles['form-label-default-padding']}
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                { required: true, message: 'Please input bid floor!' },
                () => ({
                  validator(_, value) {
                    const reg = /^\d*(\.\d*)?(\d+)?$/;
                    if (!value || reg.test(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Please input number'));
                  }
                })
              ]}
              name="bid_floor"
            >
              <Row>
                <Col span={6}>
                  <Form.Item name="bid_floor" noStyle>
                    <InputNumber style={{ width: '150px' }} addonAfter="$" min={0} precision={4} max={10000} />
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <span style={{ lineHeight: '32px', paddingLeft: '8px', color: '#5E6466' }}>&nbsp;&nbsp;Global</span>
                </Col>
              </Row>
            </Form.Item>
            <Form.Item name="content" label="Specific Country Bid Floor:">
              <OldBundleTableEdit
                editTitle="Edit Content"
                editTips="Enter the content(one per line)"
                deleteAllTitle="Delete All Content"
                deleteAllTips="Are you sure to delete all content?"
                onValueChange={handleContentChange}
                defaultList={defaultContentList}
                open={visible}
                contentMaxHeight={isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'}
                isBidFloor={true}
                handleAddFloor={handleAddSSPBidFloor}
                defaultFloorMap={defaultFloorList}
                loading={floorLoading}
                editSingle={true}
              />
            </Form.Item>
            {/* 暂时不需要 */}
            {/* <Form.Item
              label="Price:"
              name="price"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                { required: true, message: 'Please input price!' },
                () => ({
                  validator(_, value) {
                    const reg = /^\d*(\.\d*)?(\d+)?$/;
                    if (!value || reg.test(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Please input number'));
                  }
                })
              ]}
              className={styles['form-label-default-padding']}
            >
              <Row>
                <Col span={6}>
                  <Form.Item name="price" noStyle>
                    <InputNumber style={{ width: 150 }} addonAfter="$" min={0} controls precision={4} type="number" max={10000}/>
                  </Form.Item>
                </Col>
                <Col span={3}>
                  <span style={{ lineHeight: '32px', paddingLeft: '8px', color: '#5E6466' }}>&nbsp;&nbsp;Global</span>
                </Col>
              </Row>
            </Form.Item> */}

            {placementType !== PlacementType.Native && placementType > 0 && (
              <>
                {isVideo && (
                  <>
                    {!isSDK && !isIOS && (
                      <Form.Item
                        label="Protocols:"
                        name="protocols"
                        rules={[{ required: true, message: 'Please select!' }]}
                        className={`${styles['form-label-default-padding']} ${styles['form-label-checkbox']}`}
                      >
                        <Checkbox.Group options={ProtocolsOptions} className={styles['checkbox-flex-3']} />
                      </Form.Item>
                    )}
                    {!isIOS && (
                      <>
                        {((isSkip && !isVastTag) || placementType === PlacementType['Rewarded Video']) && (
                          <Form.Item
                            label={
                              placementType === PlacementType.Interstitial ? 'Show Skip Button After:' : 'Min Duration:'
                            }
                            name="minduration"
                            rules={[{ required: true, message: 'Please select!' }]}
                            className={styles['form-label-default-padding']}
                          >
                            <Select options={minDurationOptions} style={{ width: 210 }} placeholder="Please Select" />
                          </Form.Item>
                        )}

                        <Form.Item
                          label="Max Duration:"
                          name="maxduration"
                          rules={[{ required: true, message: 'Please select!' }]}
                          className={styles['form-label-default-padding']}
                        >
                          <Select options={maxDurationOptions} style={{ width: 210 }} placeholder="Please Select" />
                        </Form.Item>
                      </>
                    )}

                    {!isSDK && !isIOS && (
                      <>
                        <Form.Item
                          name="companionad"
                          label="Support Companion AD:"
                          className={styles['form-label-default-padding']}
                          valuePropName="checked"
                        >
                          <Switch />
                        </Form.Item>
                        {!isIOS && (
                          <Form.Item
                            name="video_api"
                            label="Supported Video API Frameworks:"
                            className={`${styles['form-label-default-padding']} ${styles['form-label-checkbox']}`}
                          >
                            <Checkbox.Group options={VideoAPIOptions} />
                          </Form.Item>
                        )}
                      </>
                    )}
                  </>
                )}
                {isBanner && (
                  <>
                    {placementType !== PlacementType.Interstitial && (
                      <Form.Item
                        label="Ad Position:"
                        name="pos"
                        rules={[{ required: false, message: 'Please select Ad Position!' }]}
                        className={styles['form-label-default-padding']}
                      >
                        <NormalRadio>
                          {AdPositionOptions.map((item, index) => (
                            <Radio value={item.value} key={index}>
                              {item.label}
                            </Radio>
                          ))}
                        </NormalRadio>
                      </Form.Item>
                    )}
                    {!isSDK && !isIOS && (
                      <Form.Item
                        label="Supported Banner API Frameworks:"
                        name="banner_api"
                        rules={[{ required: false, message: 'Please select!' }]}
                        className={`${styles['form-label-default-padding']} ${styles['form-label-checkbox']}`}
                      >
                        <Checkbox.Group options={BannerAPIOptions} />
                      </Form.Item>
                    )}
                  </>
                )}
              </>
            )}
          </Form>
        </div>
        <div className={styles['img-right']}>
          <span>Overview</span>
          <img src={`/img/${PlacementTypeFlip[placementType]}.png`} />
          <span>{PlacementTypeFlip[placementType]}</span>
        </div>
      </div>
    </NormalDrawer>
  );
}

export default AddPlacementModel;
