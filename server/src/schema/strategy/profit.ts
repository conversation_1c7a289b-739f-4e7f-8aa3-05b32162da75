/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-03-22 19:23:23
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:26:43
 * @Description:
 */
import { ProfitMaxValue, SpecialProfitMinValue } from '@/constants';
import Joi from 'joi';

const addProfitSchema = {
  buyer_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer())
  ),
  seller_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer())
  ),
  profit_ratio: Joi.number()
    .integer()
    .required()
    .min(SpecialProfitMinValue)
    .max(ProfitMaxValue),
  // 1 for seller, 2 for buyer, 3 for buyer + seller
  type: Joi.number().integer().required().valid(1, 2, 3)
};

export const GetProfitListSchema = Joi.object({
  type: Joi.string()
    .allow('')
    .default('advertiser')
    .valid('advertiser', 'publisher', 'bundle')
});

export const AddProfitSchema = Joi.object({ ...addProfitSchema });

export const AddBundleProfitSchema = Joi.object({
  buyer_id: Joi.number().integer().required(),
  profit_ratio: Joi.number()
    .integer()
    .required()
    .min(SpecialProfitMinValue)
    .max(ProfitMaxValue),
  // 4 for demand-bundle, 5 for demand-seller-bundle
  type: Joi.number().integer().required().valid(4, 5),
  seller_id: Joi.number().integer(), // seller_id 是数字，可选
  // bundle 是字符串或字符串数组
  bundle: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string()))
});

export const UpdateBundleProfitSchema = Joi.object({
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required(),
  buyer_id: Joi.number().integer().required(),
  profit_ratio: Joi.number()
    .integer()
    .required()
    .min(SpecialProfitMinValue)
    .max(ProfitMaxValue),
  // 4 for demand-bundle, 5 for demand-seller-bundle
  type: Joi.number().integer().required().valid(4, 5),
  seller_id: Joi.number().integer(), // seller_id 是数字，可选
  bundle: Joi.string().required()
});

export const UpdateProfitSchema = Joi.object({
  ...addProfitSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
