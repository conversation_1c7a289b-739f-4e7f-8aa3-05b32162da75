import { Context } from 'koa';

/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-28 20:34:09
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 17:02:49
 * @Description:
 */
export declare namespace PermissionAPI {
  type MenuItem = {
    id: number;
    title: string;
    path: string;
    pid: number;
    remark: string;
    create_time: string;
    update_time: string;
    is_hide: number;
    icon: string;
    component: string;
    sort: number;
    access: string;
    menu_render: number;
    interfaces: number[];
    icon_tmp: string;
    status: number;
    // status_desc: string;
  };
  type RoleItem = {
    role_id: number;
    id: number;
    role_name: string;
    type: number;
    type_desc: string;
    permission: any[];
    status: number;
  };
  type OperationItem = {
    id: number;
    op_name: string;
    auth_sign: number | string;
    remark: string;
    // status_desc: string;
    status: number;
  };
  type PermissionItem = {
    id: number;
    pms_name: string;
    type: number;
    menu_list: { rsc_id: number; pms_id: number; type: number }[];
    op_list: { rsc_id: number; pms_id: number; type: number }[];
    op_type: number;
    bl_type: number;
    remark: string;
    type_desc: string;
    // status_desc: string;
    status: number;
    pms_id: number;
    pl_id: number; //permission_rl 表的id
  };
  type InterfaceItem = {
    id: number;
    itf_name: string;
    path: string;
    type: number;
    op_type: number;
    create_time: string;
    update_time: string;
    type_desc: string;
    op_type_desc: string;
    // status_desc: string;
    status: number;
  };

  type DelRoleParams = {
    id: number;
    tnt_id: number;
  };

  type AddRoleParams = {
    role_name: string;
    role_type: number;
    tnt_id: number;
  };
  type UpdateRoleNameParams = {
    id: number;
    role_name: string;
    tnt_id: number;
  };
  type RolePmsItem = {
    rsc_id: number;
    type: number;
  };
  type EditRolePmsParams = {
    id: number;
    permissions: RolePmsItem[];
    tnt_id: number;
    pms_change: boolean;
  };
  interface RoleModel {
    addRole(options: AddRoleParams): Promise<boolean>;
    updateRole(options: UpdateRoleNameParams): Promise<boolean>;
    deleteRole(id: number, tnt_id: number): Promise<any>;
    editRolePms(sqls: string[]): Promise<any>;
    getAllRole(tnt_id: number): Promise<any>;
    isRoleExist(role_name: string, type: number, tnt_id: number): Promise<any>;
    getAllUserByRole(role_id: number, tnt_id: number): Promise<any>;
    getAllUserByRoleList(roles: string[]): Promise<any>;
  }

  interface RoleServices {
    addRole(options: AddRoleParams): Promise<boolean>;
    updateRole(options: UpdateRoleNameParams): Promise<boolean>;
    editRolePms(options: any, operator?: any): Promise<boolean>;
    getAllRole(tnt_id: number): Promise<any>;
    isRoleExist(role_name: string, type: number, tnt_id: number): Promise<any>;
    deleteRole(options: DelRoleParams): Promise<any>;
  }

  interface RoleCtrl {
    addRole(ctx: Context): void;
    updateRole(ctx: Context): void;
    editRolePms(ctx: Context): void;
    getAllRole(ctx: Context): void;
    deleteRole(ctx: Context): void;
  }
}
