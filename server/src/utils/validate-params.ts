/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-19 10:37:11
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-21 16:02:23
 * @Description:
 */
import Jo<PERSON> from 'joi';
import { Context, Next } from 'koa';
import moment from 'moment';
import { getLogger } from '@/config/log4js';
import { getCtxResult } from './response';
import { APIRESULT } from '@/constants/backend-api/common';
import { Code, Message } from '@/codes';
import { sendLarkAlertCard } from './alert';
import { getUserIp } from '.';

// （平台接口）验证参数 验证post/get参数
export const validateBody =
  (schema: Joi.Schema, allowUnknown = true, isAsync = false) =>
  (target: any, methodName: string, desc: PropertyDescriptor) => {
    const curMethod = desc.value; // 原方法
    // eslint-disable-next-line no-param-reassign
    desc.value = async (ctx: Context, next: Next) => {
      const method = ctx.request.method.toLowerCase();
      const params = method === 'post' ? ctx.request.body : ctx.request.query;

      // 只有在用户已登录的情况下才添加 tnt_id
      if (!params?.tnt_id && ctx.session?.tnt_id) {
        params.tnt_id = ctx.session.tnt_id;
      }

      // 允许多余字段
      try {
        const result = isAsync
          ? await schema.validateAsync(params || {}, { allowUnknown })
          : schema.validate(params || {}, { allowUnknown });
        if (result.error || !params) {
          throw new Error(result.error?.message || 'Params invalid');
        }
        if (method === 'post') {
          ctx.request.body = {
            ...ctx.request.body,
            ...result.value
          };
        } else {
          ctx.request.query = {
            ...ctx.request.query,
            ...result.value
          };
        }
        // 执行原方法
        await curMethod.call(target, ctx, next);
      } catch (error: any) {
        const { cur_log_id, curUser } = ctx.state;
        const { user_id = 0, account_name = '', tnt_id = 0 } = curUser || {};
        const ip = getUserIp(ctx);
        const msg = {
          'Log ID': cur_log_id,
          IP: ip,
          'API URL': ctx.request.originalUrl,
          'Page URL': ctx.request.header.referer,
          Operator: `${account_name}(${user_id})`,
          'Tenant ID': tnt_id,
          'Session ID': ctx.session?.session_id,
          'User Agent': ctx.request.headers['user-agent'],
          'x-time-zone': ctx.request.headers['x-time-zone'],
          Params: ctx.request.body,
          Error: error
        };
        await sendLarkAlertCard({
          title: 'SAAS STD Platform Valid Params Error',
          data: msg
        });
        getLogger('error').error(
          `Params Error,log_id=${cur_log_id},msg=${JSON.stringify(msg)}`
        );
        const res = getCtxResult('ERROR_SYS');
        res.message =
          process.env.NODE_ENV === 'prod'
            ? res.message
            : error?.message || res.message;
        ctx.body = res;
      }
    };
  };

// api接口 验证参数
export const validateBeApiBody =
  (schema: Joi.Schema, allowUnknown = true) =>
  (target: any, methodName: string, desc: PropertyDescriptor) => {
    const curMethod = desc.value; // 原方法
    // eslint-disable-next-line no-param-reassign
    desc.value = async (ctx: Context, next: Next) => {
      const method = ctx.request.method.toLowerCase();
      const params = method === 'post' ? ctx.request.body : ctx.request.query;

      // 只有在用户已登录的情况下才添加 tnt_id
      if (!params?.tnt_id && ctx.session?.tnt_id) {
        params.tnt_id = ctx.session.tnt_id;
      }

      // 允许多余字段
      const result = schema.validate(params || {}, { allowUnknown });
      if (result.error || !params) {
        const {
          user_id = 0,
          account_name = '',
          tnt_id = 0,
          cur_log_id
        } = ctx.state!;
        const body = { ...APIRESULT };
        body.data = null;
        body.status = {
          code: Code.PARAMS_INVALID,
          msg: result.error?.message
            ? `Params invalid:${result.error?.message}`
            : Message.PARAMS_INVALID
        };
        body.timestamp = moment
          .utc(new Date())
          .format('ddd MMM D HH:mm:ss Z YYYY');
        const ua = ctx.request.header['user-agent'] || '';
        const ip = getUserIp(ctx);
        const msg = {
          'Log ID': cur_log_id,
          IP: ip,
          'API URL': ctx.request.originalUrl,
          'Page URL': ctx.request.header.referer,
          Operator: `${account_name}(${user_id})`,
          'Tenant ID': tnt_id,
          'Session ID': ctx.session?.session_id,
          'User Agent': ctx.request.headers['user-agent'],
          'x-time-zone': ctx.request.headers['x-time-zone'],
          Params: ctx.request.body,
          Error: result.error
        };
        getLogger('error').error(`Params Error,msg=[${JSON.stringify(msg)}]`);
        sendLarkAlertCard({
          title: 'SAAS STD Platform Valid Params Error',
          data: msg
        });
        ctx.body = body;
        return;
      }
      if (method === 'post') {
        ctx.request.body = {
          ...ctx.request.body,
          ...result.value
        };
      } else {
        ctx.request.query = {
          ...ctx.request.query,
          ...result.value
        };
      }
      // 执行原方法
      await curMethod.call(target, ctx, next);
    };
  };
