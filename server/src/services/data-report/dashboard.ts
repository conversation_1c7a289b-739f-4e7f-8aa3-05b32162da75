/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:29:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-09 14:49:47
 * @Description:
 */
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import {
  AdFormatToLabel,
  Country,
  MoblieOS,
  RegionLabelMap
} from '@/constants/report/common-report';
import {
  ExportTypeMap,
  StatusTypeMap
} from '@/constants/report/exported-report';
import { QpsLevel, RegionType } from '@/constants/strategy';
import {
  appModel,
  dashboardModel,
  demandModel,
  exportLogModel,
  supplyModel
} from '@/models';
import { AppListAPI } from '@/types/app-list';
import { DashboardAPI } from '@/types/dashboard';
import { QpsAPI } from '@/types/qps';
import { createDirectory } from '@/utils';
import { transformLimitStr, updateBQConfigAdapter } from '@/utils/report/slim';
import { getDownTaskInfo } from '@/utils/report';
import {
  LabelGenerationParams,
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  includeToday,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';
import { DeviceTypeMapDesc, SchainMap } from '@/constants/report/dashboard';

export interface DashboardListOptions {
  /**
   * 报表类型
   */
  reportType?: 'advertiser' | 'publisher' | 'dashboard';
  /**
   * 是否分页，用来查总数缓存
   */
  isPaging?: boolean;
  /**
   * 是否查询全部数据
   */
  isAll?: boolean;
}

class DashboardService implements DashboardAPI.Dashboard {
  async getNewDashboardList(
    formData: DashboardAPI.GetListParams,
    labels: LabelGenerationParams,
    options?: DashboardListOptions
  ) {
    const { reportType = 'dashboard', isAll = false } = options || {};

    // formData.tnt_id = 1052;
    // formData.buyer_id = [32156];

    await updateBQConfigAdapter();
    // @ts-ignore
    // 生成SQL片段
    const { fragments, info } = await adaptGenerateBigQuerySQL(formData, {
      ...labels
    });

    const sql = concatSQLFragments({
      ...fragments,
      select: [...fragments.select, 'count(*) over() as total'],
      limit: transformLimitStr(formData.tnt_id, fragments.limit, isAll)
    });

    // 判断表类型
    const isDailyTable = info.isDaily;
    const isDemandTable = info.isDemand;

    const [data, today_hours] = await Promise.all([
      // 查询列表数据
      dashboardModel.getNewDashboardList(sql, labels),
      // 对于非daily表单独查询今日小时数
      !isDailyTable
        ? dashboardModel.getNewHoursToday(
            formData.tnt_id,
            fragments.from,
            formData.cur_time_zone,
            labels
          )
        : 0
    ]);

    // 处理查询结果
    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        metrics: formData.metrics || [],
        context: {
          start_date: formData.start_date,
          end_date: formData.end_date,
          today_hours,
          isDemand: isDemandTable,
          hour: formData.hour,
          isToday: includeToday(formData.start_date, formData.end_date),
          split_time: formData.split_time,
          timezone: formData.cur_time_zone,
          isDemandReport: reportType === 'advertiser',
          isSupplyReport: reportType === 'publisher'
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return {
      total:
        (reportType === 'advertiser' || reportType === 'publisher') &&
        total > 10000
          ? 10000
          : total,
      data: result.length > 0 ? result : []
    };
  }

  // 下载全部报表
  async downloadAllReport(
    options: DashboardAPI.GetListParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const {
        columns,
        metrics,
        split_time,
        start_date,
        end_date,
        cur_time_zone,
        tnt_id,
        cur_condition_str,
        role_id,
        hour
      } = options;

      await updateBQConfigAdapter();
      // @ts-ignore
      // 生成SQL片段
      const { fragments, info } = await adaptGenerateBigQuerySQL(options, {
        ...labels
      });

      const sql = concatSQLFragments({
        ...fragments,
        // 最多查询50000条
        limit: transformLimitStr(options.tnt_id, '', true)
      });

      // 判断表类型
      const isDailyTable = info.isDaily;
      const isDemandTable = info.isDemand;

      const [data, today_hours] = await Promise.all([
        // 查询列表数据
        dashboardModel.newDownloadAllReport(sql, labels),
        // 对于非daily表单独查询今日小时数
        !isDailyTable
          ? dashboardModel.getNewHoursToday(
              options.tnt_id,
              fragments.from,
              options.cur_time_zone,
              labels
            )
          : 0
      ]);

      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } =
        getDownTaskInfo(
          cur_time_zone,
          tnt_id,
          columns || [],
          metrics,
          'Full_Reporting',
          role_id
        );

      const exportLogRow = await exportLogModel.createExportLog(
        user_id,
        cur_condition_str,
        ExportTypeMap['Full Reporting'],
        csvName,
        requestPath
      );
      if (exportLogRow?.insertId) {
        const result = {
          code: Code.SUCCESS,
          name: csvName,
          type: ExportTypeMap['Full Reporting']
        };
        resolve(result);
      }

      // 租户 1050 额外的维度
      let appList: AppListAPI.AppListItem[] = [];
      if (tnt_id === 1050) {
        appList = await appModel.getAppList(tnt_id);
      }

      let placementList: AppListAPI.PlacementListItem[] = [];
      // 租户 1053 iion下的Data_Custom角色 id = 34
      if (tnt_id === 1053 && [34].includes(role_id || 0)) {
        placementList = await appModel.getAllPlacementList(tnt_id);
      }
      if (data) {
        const [supplyList, demandList, qpsList] = await Promise.all([
          supplyModel.getSupplyList(tnt_id, cur_time_zone, undefined, true),
          demandModel.getDemandList(tnt_id, cur_time_zone, true),
          this.getSupplyAndDemandConfigQps(tnt_id)
        ]);

        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          console.log('开始写入', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
        });

        writeStream.on('error', async err => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });

        data
          .on('error', async (err: any) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((data: any, index: number) => {
              const item = transformOneRowSQL(data, {
                metrics: metrics || [],
                context: {
                  start_date,
                  end_date,
                  today_hours,
                  isDemand: isDemandTable,
                  hour,
                  isToday: includeToday(start_date, end_date),
                  split_time,
                  timezone: cur_time_zone
                }
              });
              // ecpr,adv_ecpr 转为数字（sql中计算了，结果被bq处理成了字符串）
              item.ecpr = +item.ecpr;
              item.adv_ecpr = +item.adv_ecpr;
              if (item.buyer_id && Array.isArray(demandList)) {
                item.buyer = `${item.buyer_id}`;
                const buyer = demandList.find(
                  (v: any) => v.buyer_id === +item.buyer_id
                );
                if (buyer) {
                  item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
                }
              }
              if (item.seller_id && Array.isArray(supplyList)) {
                item.seller = `${item.seller_id}`;
                const seller = supplyList.find(
                  (v: { seller_id: number }) => v.seller_id === +item.seller_id
                );
                if (seller) {
                  item.seller = `${seller.seller_name}(${seller.seller_id})`;
                }
              }
              item.seller_schain_complete = SchainMap[item.seller_schain_complete] || '-';
              item.country = Country[item.country] || 'Unknown';
              item.ad_format = AdFormatToLabel[`${item.ad_format}`];
              item.ad_size = `${item.ad_width} * ${item.ad_height}`;
              item.platform = MoblieOS[item.platform];
              let adv_config_qps = 0;
              let pub_config_qps = 0;
              const advQps = qpsList.filter(
                (qps: QpsAPI.QpsListItem) =>
                  qps.level === QpsLevel.demand &&
                  item.buyer_id === qps.buyer_id
              );
              const pubQps = qpsList.filter(
                (qps: QpsAPI.QpsListItem) =>
                  qps.level === QpsLevel.supply &&
                  item.seller_id === qps.seller_id
              );
              advQps.forEach(
                (qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps)
              );
              pubQps.forEach(
                (qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps)
              );
              if (item.region === RegionLabelMap.USE) {
                adv_config_qps = pub_config_qps = 0;
                const useAdvQps = advQps.filter(
                  (item: QpsAPI.QpsListItem) => item.region === RegionType.USE
                );
                const usePubQps = pubQps.filter(
                  (item: QpsAPI.QpsListItem) => item.region === RegionType.USE
                );
                useAdvQps.forEach(
                  (qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps)
                );
                usePubQps.forEach(
                  (qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps)
                );
              }
              if (item.region === RegionLabelMap.APAC) {
                adv_config_qps = pub_config_qps = 0;
                const apacAdvQps = advQps.filter(
                  (item: QpsAPI.QpsListItem) => item.region === RegionType.APAC
                );
                const apacPubQps = pubQps.filter(
                  (item: QpsAPI.QpsListItem) => item.region === RegionType.APAC
                );
                apacAdvQps.forEach(
                  (qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps)
                );
                apacPubQps.forEach(
                  (qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps)
                );
              }

              if (item.device_type !== undefined) {
                item.device_type = DeviceTypeMapDesc[item.device_type as number];
              }

              item.adv_config_qps = adv_config_qps;
              item.pub_config_qps = pub_config_qps;
              // 租户 1050 额外的维度
              if (tnt_id === 1050) {
                const app = appList.find(v => v.bundle === item.app_bundle_id);
                item.app_name = app?.app_name || '-';
              }
              // 租户 1053 iion 下的 Data_Custom角色 id = 34
              if (
                tnt_id === 1053 &&
                [34].includes(role_id || 0) &&
                placementList?.length > 0
              ) {
                const placement = placementList.find(
                  (v: any) => `${v.plm_id}` === `${item.placement_id}`
                );
                if (placement) {
                  item.placement_id = `${placement.plm_name}(${item.placement_id})`;
                }
              }
              return item;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf-8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }

  async getConfigQps(tnt_id: number) {
    return await dashboardModel.getConfigQps(tnt_id);
  }

  async getSupplyAndDemandConfigQps(tnt_id: number) {
    return await dashboardModel.getSupplyAndDemandConfigQps(tnt_id);
  }
}

export default new DashboardService();
