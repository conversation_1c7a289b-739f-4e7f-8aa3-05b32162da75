/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-03 21:34:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-12 20:10:21
 * @Description:
 */

class AdvReportService {
  // 下载全部报表
  // async downloadAllReport(options: any, labels: LabelGenerationParams) {
  //   return new Promise(async (resolve, reject) => {
  // }
}

export default new AdvReportService();
