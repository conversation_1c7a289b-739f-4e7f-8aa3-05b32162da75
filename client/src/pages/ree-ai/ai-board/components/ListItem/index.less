.item-container {
  margin: 0 12px;
  padding: 16px 16px 12px 16px;
  background-color: #fff;
  border-radius: 6px;
  margin-bottom: 20px;
  margin-right: 0px;
  .item-title {
    color: var(--text-color);;
    line-height: 22px;

    b {
      font-weight: 600;
    }
  }
  .item-top {
    border-bottom: 1px solid #e2eaeb;
    padding-bottom: 12px;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-start;
    align-items: flex-start;
    .item-top-left {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .item-top-right {
      margin-left: auto;
    }
  }
  .item-bottom {
    display: flex;
    align-items: center;
    padding: 12px 16px 0 0;
    color: #5e6466 !important;
    .buyer-container {
      min-width: 150px;
      display: flex;
      align-items: center;
      gap: 20px;
      .buyer-names {
        cursor: pointer;
        width: 120px;
      }
    }
    .item-value {
      margin-left: 12px;
    }
    :global {
      .ant-select {
        min-width: 220px;
        color: #5e6466 !important;
      }
    }
  }
}
.themeGreen {
  border-bottom: 4px solid #1caf34;
  :global {
    .ant-btn-primary {
      background-color: #1baa33;
      border: none;
      &:hover {
        background: #148f29;
        border: none;
      }

      &:active {
        background: #15952b;
        border: none;
      }

      &:disabled {
        color: rgba(0, 0, 0, 0.25);
        border-color: #d9d9d9;
        background-color: #f5f5f5;
      }
    }
  }
}
.themeBlue {
  border-bottom: 4px solid #126bf0;
  :global {
    .ant-btn-primary {
      background-color: #126bf0;
      border: none;
      &:hover {
        background: #0f5fd6;
        border: none;
      }

      &:active {
        background: #105bcc;
        border: none;
      }
      &:disabled {
        color: rgba(0, 0, 0, 0.25);
        border-color: #d9d9d9;
        background-color: #f5f5f5;
      }
    }
  }
}
