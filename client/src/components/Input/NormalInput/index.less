.normal-input-radius-6 {
  border-radius: 6px;
}

:global {
  .ant-input-number-wrapper .ant-input-number-group-addon:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .ant-input:hover,
  .ant-input:focus {
    border-color: var(--primary-color);
  }

  .ant-input-search.ant-input-search-with-button > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
    border-color: var(--primary-color);
  }

  .ant-input-affix-wrapper:focus,
  .ant-input-affix-wrapper-focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-outline);
  }
}
