/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-26 14:42:11
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 14:42:13
 * @Description:
 */
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { GetAppInfoSchema } from '@/schema/app-ads';
import { appInfoService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class AppInfoCtrl {
  @validateBody(GetAppInfoSchema)
  async getAppInfo(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const data = await appInfoService.getAppInfo(formData);
    getLogger('app').info(`getAppInfo params=[${JSON.stringify(formData)}]`);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const appInfoCtrl = new AppInfoCtrl();
