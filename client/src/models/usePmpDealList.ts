import useCustomRequest from '@/hooks/useCustomRequest';
import { getPmpDealList } from '@/services/api';

interface PmpDealListProps {
  dataSource: StrategyAPI.PmpDealListItem[];
  reload: () => void;
  loading: boolean;
}

export default function usePmpDealList(): PmpDealListProps {
  const { data, run, loading, error } = useCustomRequest(getPmpDealList, {
    cacheKey: 'pmp-deal-all-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data || [],
    reload: run,
    loading
  };
}
