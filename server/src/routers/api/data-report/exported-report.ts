/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-17 18:40:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-28 10:11:43
 * @Description:
 */
import Router from 'koa-router';
import { exportLogCtrl } from '@/controllers';
import { parseDateParams } from '@/utils/report';
const router = new Router({ prefix: '/exported-report' });

const routers = router
  .get('/download/:name', exportLogCtrl.fileVisit)
  .post('/getExportedReportList', parseDateParams, exportLogCtrl.getExportLog)
  .post('/getExportedTaskStatus', exportLogCtrl.getExportTaskStatus);

export default routers;
