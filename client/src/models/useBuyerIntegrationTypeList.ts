/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-19 16:15:59
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:54:13
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getBuyerIntegrationType } from '@/services/api';

type IntegrationTypeProps = {
  dataSource: CommonAPI.IntegrationTypeItem[],
  reload: () => Promise<any>;
  loading: boolean
}

export default function useBuyerIntegrationTypeList(): IntegrationTypeProps {
  const { data, run, loading, error } = useCustomRequest(getBuyerIntegrationType, {
    cacheKey: 'buyer-integration-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
