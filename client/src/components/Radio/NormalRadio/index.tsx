/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:56:59
 * @Description:
 */
import type { RadioGroupProps } from 'antd';
import { Radio } from 'antd';
import { useState } from 'react';
import styles from './index.less';

function NormalRadio(props: RadioGroupProps & { nums?: number; bgColor?: string }): JSX.Element {
  const [btnNums, setBtnNums] = useState(props.nums || props.options?.length || 0);

  return (
    <Radio.Group
      className={`${styles['radio-button-container']} ${btnNums > 3 ? styles['more-radio-button-container'] : ''}`}
      optionType="button"
      {...props}
    />
  );
}

export default NormalRadio;
