/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-21 15:25:13
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-08 16:37:58
 * @Description:
 */

import { LabelGenerationParams } from '@rixfe/rix-tools';
import { Context } from 'koa';
export declare namespace ABTestAPI {
  type ABTestListItem = {
    id: number;
    type: number;
    seller_id: number;
    buyer_id: number;
    default_profit?: number;
    country: string;
    ad_format: number;
    ad_size: string;
    seller_name: string;
    buyer_name: string;
    content: string;
    op_id: number;
    account_name: string;
    account_status: number;
    create_time: string;
    update_time: string;
    expire_time: string | string[];
  };
  type CheckABTestParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    tnt_id: number;
    id?: number;
    country?: string;
    ad_format?: string;
    ad_size?: string;
  };

  type AddABTestParams = {
    tnt_id: number;
    seller_id: number;
    buyer_id: number;
    op_id: number;
    type: number;
    content: string;
    expire_time: string;
    country?: string;
    ad_format?: string;
    ad_size?: string;
  };
  type UpdateABTestParams = {
    tnt_id: number;
    op_id: number;
    id: number;
    status: number;
    content: string;
    expire_time: string;
    country?: string;
    ad_format?: string;
    ad_size?: string;
  };

  interface ABTestCtrl {
    getABTestList(ctx: Context): Promise<void>;
    addABTest(ctx: Context): Promise<void>;
    updateABTest(ctx: Context): Promise<void>;
  }

  interface ABTestService {
    getABTestList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<ABTestListItem[]>;
    addABTest(params: AddABTestParams): Promise<boolean>;
    updateABTest(params: UpdateABTestParams): Promise<boolean>;
  }

  interface ABTestModel {
    isABTestExist(params: CheckABTestParams): Promise<boolean>;
    isOtherGroupExist(params: CheckABTestParams): Promise<boolean>;
    getABTestList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<ABTestListItem[]>;
    addABTest(params: AddABTestParams): Promise<boolean>;
    updateABTest(params: UpdateABTestParams): Promise<boolean>;
  }

  // ABTestReport
  type sqlValue = {
    conditions: string;
    dimension: string;
    groupBy: string;
    order: string;
    limit: string;
    table?: string;
  };
  type ABTestReportListItem = {
    date: string;
    type: string;
    content: string;
    request: number;
    response: number;
    impression: number;
    fill_rate: number;
    win_rate: number;
    net_revenue: number;
    ecpr: number;
    profit_ecpr: number;
    real_qps: number;
    profit: number;
    profit_rate: number;
    avg_bid_floor: number;
    avg_bid_price: number;
    total_request: number;
  };
  type GetABTestReportListParams = {
    start_date: string;
    end_date: string;
    buyer_id?: number[];
    seller_id?: number[];
    start: number;
    end: number;
    isAll?: boolean;
    columns?: string[];
    metrics: string[];
    download?: boolean;
    order_key: string[];
    tnt_id: number;
    order: string;
    type: number;
    cur_time_zone: string;
    tz_start_date: string;
    tz_end_date: string;
    region?: number[];
    ad_size?: string[];
    test_tag_a?: string[];
    ad_format?: number[];
    country?: string[];
  };
  interface ABTestReportModel {
    // getABTestReportList(
    //   options: sqlValue,
    //   labels: LabelGenerationParams
    // ): Promise<ABTestReportListItem[]>;
    // countList(params: any, labels: LabelGenerationParams): Promise<any>;
  }

  interface ABTestReportService {
    getABTestReportList(
      params: GetABTestReportListParams,
      labels: LabelGenerationParams,
      isPaing?: boolean
    ): Promise<{
      total: number;
      data: Array<ABTestReportListItem>;
    }>;
    // initParams(params: GetABTestReportListParams): sqlValue;
  }
  interface ABTestReportCtrl {
    getABTestReportList(ctx: Context): Promise<void>;
  }
}
