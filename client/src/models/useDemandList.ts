/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:55:47
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDemandList } from '@/services/api';

export default function useDemandList() {
  const { data, run, loading, error } = useCustomRequest(getDemandList, {
    cacheKey: 'demand-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
