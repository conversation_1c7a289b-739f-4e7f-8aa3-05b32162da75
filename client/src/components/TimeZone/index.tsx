/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-08-23 11:32:17
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 14:34:50
 * @Description: 时区选择
 */
import React, { useState, useEffect } from 'react';
import { Dropdown } from 'antd';
import { useModel } from '@umijs/max';
import styles from './index.less';
import { TimeZoneOptions, TimeZoneMap } from '@/constants/base/time-zone';
import { CheckOutlined } from '@ant-design/icons';
import { TimeZoneManager } from '@/utils/iframe-manager/time-zone';

const TimeZoneDropdown: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const [timeZone, setTimeZone] = useState(initialState?.timeZone || 'Etc/UTC');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    const user_id = String(initialState?.currentUser?.user_id || '');
    
    // 使用TimeZoneManager初始化时区
    const result = TimeZoneManager.initializeTimeZone(user_id, false);
    if (result.success) {
      setTimeZone(result.timeZone);
    }
  }, [initialState?.currentUser?.user_id]);

  const handleClick = (value: string) => {
    const user_id = String(initialState?.currentUser?.user_id || '');
    
    // 使用TimeZoneManager设置时区
    const result = TimeZoneManager.setTimeZone(value, {
      userId: user_id,
      autoReload: true,
      debug: false
    });

    if (result.success) {
      setTimeZone(value);
      setOpen(false);
      setInitialState({
        ...initialState,
        isCollapsed: initialState?.isCollapsed || false,
        timeZone: value
      });
    } else {
      console.error('fail to set time zone:', result.error);
    }
  };

  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };

  return (
    <Dropdown
      placement="bottom"
      trigger={['click']}
      open={open}
      onOpenChange={onOpenChange}
      menu={{
        items: [],
        selectable: true,
        defaultSelectedKeys: [timeZone]
      }}
      dropdownRender={() => (
        <div className={styles['time-zone-container']}>
          {TimeZoneOptions.map((v, index) => {
            return (
              <div
                key={index}
                title={v.label}
                onClick={() => handleClick(v.value)}
                className={v.value === timeZone ? styles['active'] : ''}
              >
                <span className={styles['ellipsis']}>{v.label}</span>
                {v.value === timeZone && <CheckOutlined style={{ color: 'var(--primary-color)', fontWeight: 600 }} />}
              </div>
            );
          })}
        </div>
      )}
    >
      <div className={styles['time-zone']}>
        <span style={{ whiteSpace: 'nowrap' }}>{TimeZoneMap[timeZone]}</span>
      </div>
    </Dropdown>
  );
};

export default TimeZoneDropdown;
