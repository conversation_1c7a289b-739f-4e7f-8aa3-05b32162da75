import { isNumber } from '@/utils';

const profitRatioValidator = (min: number, max: number) => (_: any, value: number | string) => {
  if (value === '' || value === undefined || value === null) {
    return Promise.resolve();
  }
  const number = Number(value);
  if (isNumber(number) && !isNaN(number) && Number.isInteger(number) && number >= min && number <= max) {
    return Promise.resolve();
  }
  return Promise.reject(new Error(`Ratio must be integer between ${min} and ${max}`));
};

export const RatioRule: any = {
  validator: profitRatioValidator(0, 100)
};

export const NewProfitRatioRule: any = {
  validator: profitRatioValidator(-100, 100)
};

export const RevShareRatioRule: any = {
  validator: profitRatioValidator(1, 100)
};
