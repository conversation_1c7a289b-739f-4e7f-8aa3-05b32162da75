/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-03-22 15:28:54
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 16:57:14
 * @Description:
 */
import Joi from 'joi';

export const AddRoleSchema = Joi.object({
  role_name: Joi.string().required()
});

export const UpdateRoleSchema = Joi.object({
  role_name: Joi.string().required(),
  id: Joi.number().integer().required()
});

export const DeleteRoleSchema = Joi.object({
  id: Joi.number().integer().required()
});

const customIdValidator = (value: any, helpers: any) => {
  if ([1, 2, 5, 6, 7, 8].includes(value)) {
    return helpers.message('System Role and Rix Role cannot be modified');
  }
  return value;
};
export const EditRolePmsSchema = Joi.object({
  // 系统默认角色和内部角色不允许被修改，id不可以为1, 2, 5, 6, 7, 8
  id: Joi.number().integer().required().custom(customIdValidator),
  permissions: Joi.array().items(
    Joi.object({
      rsc_id: Joi.number().integer(),
      type: Joi.number().integer()
    })
  ),
  pms_change: Joi.boolean().required()
});
