:global {
  .ant-picker {
    &:hover,
    &:focus {
      border-color: var(--primary-color);
    }

    &.ant-picker-focused {
      border-color: var(--primary-color);
    }

    .ant-picker-active-bar {
      background-color: var(--primary-color);
    }
  }

  .ant-picker-time-panel-column > li.ant-picker-time-panel-cell-selected .ant-picker-time-panel-cell-inner {
    background-color: var(--primary-1) !important;
  }
}

.range-picker-container {
  color: red;
  :global {
    .ant-picker-header {
      .ant-picker-header-prev-btn,
      .ant-picker-header-super-prev-btn,
      .ant-picker-header-next-btn,
      .ant-picker-header-super-next-btn {
        color: #5e6466;
      }
      .ant-picker-header-view {
        color: var(--text-color);
      }
    }
    .ant-picker-cell-today {
      .ant-picker-cell-inner {
        &:before {
          cursor: pointer;
          border: none;
          top: 24px;
          left: 50%;
          width: 4px;
          height: 4px;
          border-radius: 4px;
          background: var(--primary-1);
          transform: translateX(-50%);
        }
      }
    }
    .ant-picker-cell.ant-picker-cell-in-view {
      .ant-picker-cell-inner {
        border-radius: 6px;
      }
    }

    .ant-picker-content {
      th {
        color: #8d9799;
      }
      tr {
        height: 36px;
      }
      .ant-picker-cell-disabled {
        color: #d7dadb;
        &::before {
          height: 32px;
        }
      }
    }

    .ant-picker-cell-in-view.ant-picker-cell-range-start,
    .ant-picker-cell-in-view.ant-picker-cell-range-end {
      &:before {
        background: var(--primary-3);
        position: absolute;
        top: 50%;
        right: 0;
        left: 0;
        z-index: 1;
        height: 32px;
        content: '';
      }

      &.ant-picker-cell-range-start:before {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      &.ant-picker-cell-range-end:before {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .ant-picker-cell-inner {
        border-radius: 6px !important;
      }
    }

    .ant-picker-cell-range-hover-start,
    .ant-picker-cell-range-hover-end,
    .ant-picker-cell-range-hover {
      &::after {
        border: none !important;
      }
      &::before {
        border: none;
        background-color: var(--primary-2) !important;
        height: 32px;
      }
      &.ant-picker-cell-range-hover-end::before {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      &.ant-picker-cell-range-hover-start::before {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
    }

    .ant-picker-cell-range-start-near-hover.ant-picker-cell-selected,
    .ant-picker-cell-range-end-near-hover.ant-picker-cell-selected {
      &.ant-picker-cell-range-start-near-hover::before {
        border-radius: 0 !important;
      }
      &.ant-picker-cell-range-end-near-hover::before {
        border-radius: 0 !important;
      }
    }

    .ant-picker-cell-in-view.ant-picker-cell-in-range {
      .ant-picker-cell-inner::after {
        background: none !important;
      }
      &::before {
        height: 32px;
      }
      &.ant-picker-cell-range-hover::before {
        background-color: var(--primary-2) !important;
      }
    }

    .ant-picker-ranges {
      display: flex;
      flex-wrap: wrap;

      .ant-picker-preset > .ant-tag-blue {
        color: var(--primary-color);
        background-color: var(--primary-3);
        border-color: var(--primary-color);
      }
    }

    // 主题定制
    .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
    .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
    .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
      background: var(--primary-color);
    }

    .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
      background-color: var(--primary-3) !important;
    }
  }
}
