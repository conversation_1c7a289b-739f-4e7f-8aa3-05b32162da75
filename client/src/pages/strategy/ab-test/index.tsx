/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-21 14:40:04
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-02 16:02:22
 * @Description:
 */

import { DownOutlined, EyeOutlined, PlusOutlined, UpOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import React, { useEffect, useMemo, useState } from 'react';
import { history, useModel } from 'umi';
import styles from './index.less';

import OperateRender, { OperateRenderItem } from '@/components/OperateRender';
import { StatusMap } from '@/constants';
import {
  ABTestBreadOptions,
  ABTestBtnDesc,
  ABTestColumns,
  ABTestColumnsKeyMap,
  ABTestSearchOption,
  ABTestTab,
  ABTestTabDesc,
  ABTestTabOptions,
  ContentKeyMap
} from '@/constants/strategy/ab-test';

import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import HoverToolTip from '@/components/ToolTip/BundleTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { Tooltip } from 'antd';
import Paragraph from 'antd/lib/typography/Paragraph';
import AddABTestDrawer from '../components/AddABTestDrawer';

const Page: React.FC = () => {
  const { dataSource: ABTestList, reload, loading } = useModel('useABTestList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const { dataSource: profitList, reload: reloadProfit } = useModel('useProfitList');
  const { adSizeOptions, adSizeMapByValue, fetchAdSize } = useModel('useAdSizeOptions');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [searchOptions, setSearchOptions] = useState(ABTestSearchOption);
  const [currentRow, setCurrentRow] = useState<StrategyAPI.ABTestListItem>();
  const [currentTab, setCurrentTab] = useState(ABTestTab['Profit']);
  const [ellipsisExpandKeys, setEllipsisExpandKeys] = useState<number[]>([]);
  const [dataSource, setDataSource] = useState<StrategyAPI.ABTestListItem[]>([]);
  const handleEditABTest = (params: StrategyAPI.ABTestListItem) => {
    setIsEdit(true);
    setVisible(true);
    setCurrentRow(params);
  };
  const { initialState } = useModel('@@initialState');
  const currentTabOptions = useMemo(() => {
    const tntId = initialState?.currentUser?.tnt_id || 0;
    // 如果当前用户的租户为1046，只显示 BidFloor
    if (tntId === 1046) {
      setCurrentTab(ABTestTab.BidFloor);
      return ABTestTabOptions.filter(option => option.value === ABTestTab.BidFloor);
    } else if (tntId !== 1052) {
      // 过滤掉 TransferFormat，这个 tab 仅针对 1052 租户开放
      return ABTestTabOptions.filter(option => option.value !== ABTestTab.TransferFormat);
    }
    return ABTestTabOptions;
  }, [initialState]);
  const handleClickCreate = (seller_id?: number) => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleViewData = (params: StrategyAPI.ABTestListItem) => {
    // history.push(`/data-report/abtest-report?id=${params.id}&type=${params.type}`, { row: params });
    history.push(`/data-report/abtest-report?id=${params.id}&type=${params.type}`);
  };
  const btnOptions: OperateRenderItem[] = [
    { label: 'Edit', onClick: handleEditABTest, accessCode: 'updateABTestAuth' },
    { label: 'Data', onClick: handleViewData, accessCode: 'ABTestReportPermission', icon: <EyeOutlined /> }
  ];

  const tmpColumns: ColumnProps<StrategyAPI.ABTestListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 135,
      fixed: 'right',
      render: (txt, params) => <OperateRender btnOptions={btnOptions} params={params} />
    }
  ];
  const [columns, setColumns] = useState<ColumnProps<StrategyAPI.ABTestListItem>[]>([]);

  useEffect(() => {
    reload();
    !demandList && reloadDemand();
    !supplyList && reloadSupply();
    !profitList && reloadProfit();
    fetchAdSize();
  }, []);

  const abTestColumns = useMemo(() => {
    const arr = ABTestColumns.map(v => ({ ...v }));
    const index = arr.findIndex(v => v.dataIndex === 'ad_size');
    if (index !== -1) {
      arr[index].render = _ => {
        return (
          <>
            <HoverToolTip title={`${adSizeMapByValue[_] || '-'}`}>
              <span>{adSizeMapByValue[_] || '-'}</span>
            </HoverToolTip>
          </>
        );
      };
    }
    return arr;
  }, [adSizeMapByValue]);

  useEffect(() => {
    let columns: ColumnProps<StrategyAPI.ABTestListItem>[] = [];
    let tmpList: StrategyAPI.ABTestListItem[] = [];
    if (currentTab) {
      tmpList = ABTestList?.filter(item => item.type === currentTab) || [];
      const columnKeys = ABTestColumnsKeyMap[currentTab];
      columns = abTestColumns.filter(item => columnKeys.includes(item.dataIndex as string));
    }
    columns.forEach(item => {
      if (item.dataIndex === 'content') {
        item.render = (txt, row) => {
          let content;
          let content_str = '';
          try {
            content = JSON.parse(txt);
            if (Array.isArray(content) && content.length > 1) {
              if (currentTab === ABTestTab.TransferFormat) {
                // 专门处理TransferFormat的情况
                content_str = content.reduce((prev, cur) => {
                  // status: 1表示Active, 2表示Pause
                  const formatType = cur.status === 1 ? 'Active' : 'Pause';
                  prev += `${formatType}; Traffic(%): ${cur.ratio}\n`;
                  return prev;
                }, '');
              } else {
                content_str = content.reduce((prev, cur) => {
                  prev += `${ABTestTabDesc[currentTab]}(%): ${cur[ContentKeyMap[currentTab]]}; Traffic(%): ${
                    cur.ratio
                  }\n`;
                  return prev;
                }, '');
              }
            } else if (content?.length === 1) {
              if (currentTab === ABTestTab.TransferFormat) {
                // 单个选项的情况
                const formatType = content[0].status === 1 ? 'Active' : 'Pause';
                content_str = `${formatType}; Traffic(%): ${content[0].ratio}`;
              } else {
                content_str = `${ABTestTabDesc[currentTab]}(%): ${content[0][ContentKeyMap[currentTab]]}; Traffic(%): ${
                  content[0].ratio
                }`;
              }
            }
          } catch (error) {
            content_str = txt;
            content = txt;
          }
          const contentNode =
            Array.isArray(content) && content.length > 0 ? (
              content.map((v: any, index: number) => {
                let contentText;
                if (currentTab === ABTestTab.TransferFormat) {
                  // 展示格式为"Active/Pause; Traffic(%):比例"
                  const formatType = v.status === 1 ? 'Active' : 'Pause';
                  contentText = `${formatType}; Traffic(%):${v.ratio}`;
                } else {
                  contentText = `${ABTestTabDesc[currentTab]}(%):${v[ContentKeyMap[currentTab]]}; Traffic(%):${
                    v.ratio
                  }`;
                }
                return (
                  <span
                    key={index}
                    style={{ 
                      display: currentTab === ABTestTab.TransferFormat ? 'block' : 
                      (ellipsisExpandKeys.includes(row.id) || index === 0 ? 'block' : 'none') 
                    }}
                    className={styles['content-item']}
                  >
                    {contentText}
                  </span>
                );
              })
            ) : (
              <>{content}</>
            );
          return (
            <div className={styles['content-container']}>
              <Tooltip title={contentNode}>
                <div>{contentNode}</div>
              </Tooltip>

              <div className={styles['icon-container']}>
                <Paragraph
                  copyable={{
                    tooltips: false,
                    text: content_str || '',
                    icon: <RixEngineFont type="rix-copy" style={{ fontSize: 16 }} />
                  }}
                  className={styles['copy-icon']}
                ></Paragraph>
                {content?.length > 1 && currentTab !== ABTestTab.TransferFormat && (
                  <a onClick={e => handleClickExpand(e, row.id)}>
                    {ellipsisExpandKeys.includes(row.id) ? <UpOutlined /> : <DownOutlined />}
                  </a>
                )}
              </div>
            </div>
          );
        };
        item.width = currentTab === ABTestTab.Profit ? 230 : 260;
      }
      return item;
    });
    setColumns([...columns, ...tmpColumns]);
    setDataSource(tmpList);
  }, [currentTab, ellipsisExpandKeys, ABTestList, abTestColumns]);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList, currentTab]);
  const handleClickExpand = (e: any, id: number) => {
    e.preventDefault();
    if (ellipsisExpandKeys.includes(id)) {
      setEllipsisExpandKeys(ellipsisExpandKeys.filter(item => item !== id));
    } else {
      setEllipsisExpandKeys([...ellipsisExpandKeys, id]);
    }
  };

  const handleClose = () => {
    setVisible(false);
    setIsEdit(false);
  };
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(ABTestSearchOption));
    if (Array.isArray(demandList)) {
      const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (Array.isArray(supplyList)) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  };
  const handleTabChange = (tab: number | string) => {
    setCurrentTab(tab as number);
  };
  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={ABTestBreadOptions} className={styles['floor-container']}>
      <FrontTable<StrategyAPI.ABTestListItem>
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: `Create ${ABTestBtnDesc[currentTab]}`,
            type: 'primary',
            size: 'small',
            onClick: () => handleClickCreate(),
            icon: <PlusOutlined />,
            accessCode: 'addABTestAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={80}
        emptyRender={dataSource && dataSource?.length ? normalEmptyRender : undefined}
        tabOptions={currentTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange}
        isFold={currentTab === ABTestTab['Profit']}
        initialValues={{ status: [StatusMap['Active']] }}
      />
      <AddABTestDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadABTest={reload}
        row={currentRow}
        demandList={demandList}
        supplyList={supplyList}
        profitList={profitList}
        adSizeOptions={adSizeOptions}
        currentTab={currentTab}
        blackName={ABTestBtnDesc[currentTab]}
      />
    </PageContainer>
  );
};

export default Page;
