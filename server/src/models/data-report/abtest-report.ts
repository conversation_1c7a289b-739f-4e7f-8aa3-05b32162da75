/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-05 10:19:47
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-12 10:48:38
 * @Description:
 */

import { ABTestAPI } from '@/types/ab-test';
// /**
//  * @deprecated 该方法已废弃
//  */
// const getReportSql = (
//   options: ABTestAPI.sqlValue,
//   isAll?: boolean,
//   isCount?: boolean
// ) => {
//   const { conditions, dimension, groupBy, order, limit } = options;
//   return `
//     select
//       ${dimension}
//       SUM(request) AS request,
//       SUM(response) AS response,
//       SUM(impression) AS impression,
//       SUM(request) as total_request,
//       (CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(100 * SUM(response) / SUM(request), 2) END) AS fill_rate,
//       (CASE WHEN SUM(response) = 0 THEN 0 ELSE ROUND(100 * SUM(impression) / SUM(response), 2) END) AS win_rate,
//       ROUND(SUM(buyer_net_revenue), 2) AS buyer_net_revenue,
//       (CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(SUM(buyer_net_revenue) / SUM(request) * 1000000, 2) END) AS ecpr,
//       (CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(1000000 * (SUM(buyer_net_revenue) - SUM(seller_net_revenue)) / SUM(request), 2) END) AS profit_ecpr,
//       ROUND(SUM(buyer_net_revenue) - SUM(seller_net_revenue),2) AS profit,
//       (CASE WHEN SUM(buyer_net_revenue) = 0 THEN 0 ELSE ROUND((SUM(buyer_net_revenue * 100) - SUM(seller_net_revenue * 100)) / SUM(buyer_net_revenue), 2) END) AS profit_rate,
//       (CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(SUM(total_buyer_bid_floor) / SUM(request), 2) END) AS avg_bid_floor,
//       (CASE WHEN SUM(response) = 0 THEN 0 ELSE ROUND(SUM(total_buyer_bid_price) / SUM(response), 2) END) AS avg_bid_price,
//     from
//       \`${ReportSchema}.buyer_report\`
//     ${conditions}
//     ${groupBy}
//     ${order}
//     ${isAll ? (isCount ? '' : 'limit 10000') : limit}`;
// };
class ABTestReportModel implements ABTestAPI.ABTestReportModel {
  // /**
  //  * @deprecated 该方法已废弃，请使用 queryBQDataWithCache 方法
  //  */
  // async getABTestReportList(
  //   options: ABTestAPI.sqlValue,
  //   labels: LabelGenerationParams
  // ): Promise<ABTestAPI.ABTestReportListItem[]> {
  //   const sql = getReportSql(options);
  //   return await queryStackOverflow(sql, {
  //     cacheTime: 60 * 10,
  //     ...labels
  //   });
  // }
  // /**
  //  * @deprecated 该方法已废弃，请使用 getNewCountList 方法
  //  */
  // async countList(
  //   params: ABTestAPI.sqlValue,
  //   labels: LabelGenerationParams
  // ): Promise<any> {
  //   const reprotSql = getReportSql(params, true, true);
  //   const countSql = `select count(*) as count from (${reprotSql}) as t`;
  //   const res = await queryStackOverflow(countSql, {
  //     cacheTime: 60 * 10,
  //     ...labels
  //   });
  //   if (Array.isArray(res) && res.length) {
  //     return res[0];
  //   }
  //   return { count: 0 };
  // }
  // async getNewCount(
  //   sql: string,
  //   labels: LabelGenerationParams
  // ): Promise<number> {
  //   const countSql = `select count(*) as count from (${sql}) as t`;
  //   const res = await queryStackOverflow(countSql, {
  //     cacheTime: 60 * 10,
  //     ...labels
  //   });
  //   return res?.[0]?.count || 0;
  // }
}
export const abTestReportModel = new ABTestReportModel();
