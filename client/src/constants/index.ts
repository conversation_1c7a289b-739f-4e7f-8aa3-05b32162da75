/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 12:18:48
 * @Description: 通用的
 */
import { objectFlip } from '@/utils';
type Type = {
  [key: string]: number | string;
};
export type OptionsType = {
  label: string;
  value: any;
  key?: string | number;
};
export const LoginPath = '/user/login';
export const ProfilePath = '/my-profile';
/**
 * @deprecated 迁移到 ./common.ts 中
 */
export const StatusMap = {
  Active: 1,
  Paused: 2
} as const;

/**
 * @deprecated 迁移到 ./common.ts 中
 */
export const DemandAndSupplyStatusMap = {
  Active: 1,
  Paused: 2,
  Testing: 3
} as const;

/**
 * @deprecated 迁移到 ./common.ts 中
 */
export const DemandAndSupplyStatusOptions = [
  {
    label: 'Active',
    value: 1
  },
  {
    label: 'Paused',
    value: 2
  },
  {
    label: 'Testing',
    value: 3
  }
];
export type DemandAndSupplyStatusProps = keyof typeof DemandAndSupplyStatusMap;
export const DemandAndSupplyStatusDesc = objectFlip(DemandAndSupplyStatusMap);

export type StatusProps = keyof typeof StatusMap;
export const StatusDesc = objectFlip(StatusMap);
export const YesNoMap = {
  Yes: 1,
  No: 2
} as const;
export const YesNoDesc = objectFlip(YesNoMap);
export const YesNoOptions = [
  {
    label: 'Yes',
    value: 1
  },
  {
    label: 'No',
    value: 2
  }
];
/**
 * @deprecated 迁移到 ./common.ts 中
 */
export const StatusOptions = [
  {
    label: 'Active',
    value: 1
  },
  {
    label: 'Paused',
    value: 2
  }
];
export const ResetPwdOptions = [
  {
    label: 'No',
    value: 1
  },
  {
    label: 'Yes',
    value: 2
  }
];

export const ResetPwdType = {
  No: 1,
  Yes: 2
};
export const AuthLevel = {
  Supply: 1,
  'Supply App': 2,
  'Supply Placement': 3
};

export const ExportStatusMap = {
  Creating: 1,
  Created: 2,
  // Expired: 3,
  Failed: 4
};
export const ExportStatusDesc = objectFlip(ExportStatusMap);
export const ExportStatusOptions = [
  {
    label: 'Creating',
    value: 1
  },
  {
    label: 'Created',
    value: 2
  },
  // {
  //   label: 'Expired',
  //   value: 3
  // },
  {
    label: 'Failed',
    value: 4
  }
];
export const AppItemColor: API.StringType = {
  // '0': '#FA8C16',
  // '1': '#52C41A',
  // '2': '#13C2C2',
  // '3': '#FA541C',
  // '4': '#722ED1',
  // '5': '#1890FF',
  '0': '#0A5CF4,#25A1FF',
  '1': '#FF9012'
};

export const AppIconColor: API.StringType = {
  android: '#A4C639',
  ios: '#303333',
  roku: '#6C3C97'
};

export const Alphabets = [
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'q',
  'r',
  's',
  't',
  'u',
  'v',
  'w',
  'x',
  'y',
  'z'
];
// iconfont 配置(本地跟线上)
export const IconFontUrl: API.StringToStringType = {
  dev: '//at.alicdn.com/t/c/font_3852340_kd04igzymx.js',
  prod: '/js/iconfont.js'
};

export const ProfitModelOptions = [
  { label: 'Net', value: 1 },
  { label: 'Rev Share', value: 2 }
];

export const ProfitModelType = {
  Net: 1,
  'Rev Share': 2
};

export const ProfitModelTypeToString: API.StringToStringType = {
  1: 'Net',
  2: 'Rev Share'
};

export const UIConfig = {
  SiderWidth: 236, // 侧边菜单宽度
  SiderCollapsedWidth: 50 // 关闭后的宽度
};

export const SpecialSuccessCode = {
  QPS_DUPLICATED_WHEN_PAUSED: 2304,
  IVT_DUPLICATED_WHEN_PAUSED: 2307
};

export const isErrorCode = {
  CURRENT_PASSWORD_ERROR: 1105,
  DOWNLOAD_ERROR: -6,
  SYSTEM_ERROR: -1
};

export type TopBarButtonAuth = {
  Search?: boolean;
  Export?: boolean;
  ExportAll?: boolean;
};

export const UserType = {
  Tenant: 1,
  Supply: 2,
  Demand: 3,
  Rix_Admin: 4,
  Rix_Data_Analyst: 5,
  Partner: 6
};

export const LinkMenuType = {
  Ordinary: 1,
  Dashboard: 2
};

export const SpecialAccessCode = ['AdvReportPermission', 'PubReportPermission'];

// 不需要登录访问的路由
export const IgnoreLoginPath = [
  '/user/login',
  '/help/openrtb',
  '/help/androidsdk',
  '/help/iossdk',
  '/help/mw-jstag',
  '/help/vasttag',
  '/help/sspapi',
  '/help/sspapi-v2',
  '/help/demandapi',
  '/help/demandapi-v2',
  '/help/supply-partner-api',
  '/help/demand-partner-api',
  '/help/operational-guide',
  '/help/ai-guide',
  '/help/daily-csv-reporting-api',
  '/iion/ab7e1dec65824a2c5c517deb9da70f81'
];

export const RegionType = {
  'All Region': 0,
  USE: 1,
  APAC: 2
};
export const RegionOptions = [
  // { label: 'All Region', value: 0 },
  { label: 'USE', value: 1 },
  { label: 'APAC', value: 2 },
  { label: 'EUW', value: 3 }
];
export const RegionTypeDesc: API.StringToStringType = {
  // 0: 'All Region',
  1: 'USE',
  2: 'APAC',
  3: 'EUW'
};

export const StatusType = {
  Active: 1,
  Paused: 0
};

export const AdFormatMap: any = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

/**
 * profit 最大值 常量导出
 * @constant ProfitMaxValue 80
 */
export const ProfitMaxValue = 80;
/**
 * profit 最小值 常量导出
 * @constant ProfitMinValue -100
 */
export const ProfitMinValue = -100;

/**
 * 1052 租户不限制 profit ratio 最小值，给定一个默认值 -100000
 */
export const SpecialProfitMinValue = -100000;

/**
 * 根据租户 id 获取 profit 最小值
 * @param tenantId 租户 id
 * @returns profit 最小值
 */
export const getProfitMinValue = (tenantId: number) => {
  if (tenantId === 1052) {
    return SpecialProfitMinValue;
  }
  return ProfitMinValue;
};

/**
 * daily csv reporting api 开放的租户
 * 1052 是 Rix 的租户，用来测试是否展示文档
 */
export const DailyCSVReportingTenants = [1046, 1050, 1052];
