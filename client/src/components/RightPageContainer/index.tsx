/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-09-30 11:31:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-28 19:51:53
 * @Description:
 */
import React from 'react';
import Breadcrumb, { BreadcrumbItem } from '@/components/Breadcrumb';
import { Spin, SpinProps } from 'antd';
import styles from './index.less';
import { LeftOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import CusLoadingIcon from '@/components/LoadingIcon';
import classNames from 'classnames';
type PageProps = SpinProps & {
  options?: BreadcrumbItem[];
  loading?: boolean;
  flexDirection?: 'row' | 'column';
  className?: string;
  isBack?: boolean;
  backUrl?: string;
  handleGoBack?: () => void;
  hideMenu?: boolean;
  isAuto?:boolean; // 是否自动生成面包屑
};
const PageContainer: React.FC<PageProps & React.HTMLProps<HTMLDivElement>> = ({
  options,
  children,
  loading = false,
  size,
  className,
  flexDirection = 'column',
  isBack = false,
  backUrl,
  handleGoBack,
  hideMenu,
  id,
  isAuto = false
}) => {
  const handleBack = () => {
    if (backUrl) {
      history.push(backUrl);
    }
    if (!backUrl && handleGoBack) {
      handleGoBack();
    }
  };
  return (
    <Spin spinning={loading} size={size} wrapperClassName={styles['spin-container']} indicator={<CusLoadingIcon />}>
      <div
        className={classNames(styles['page-container'], className)}
        style={{ flexDirection: flexDirection }}
        id={id || 'right-page-container'}
      >
        <div className={(options?.length || isAuto) ? styles['page-top'] : ''}>
          {isBack && (
            <div className={hideMenu ? styles['page-back-hide'] : styles['page-back']} onClick={handleBack}>
              <LeftOutlined style={{ fontSize: 13 }} />
              <span style={{ paddingRight: '6px' }}>Back</span>
              {isAuto || options && options.length && <span style={{ color: '#c1cbcc', padding: '0px 6px' }}>|</span>}
            </div>
          )}
          {(options && options.length || isAuto) && <Breadcrumb options={options} separator=">" isAuto={isAuto}/>}
        </div>
        {children}
      </div>
    </Spin>
  );
};

export default PageContainer;
