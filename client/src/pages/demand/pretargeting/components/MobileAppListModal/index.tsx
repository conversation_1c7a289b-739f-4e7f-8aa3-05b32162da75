/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-10-26 17:30:40
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { Typography } from 'antd';
import { EyeOutlined, CheckOutlined } from '@ant-design/icons';
import Modal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';
import LongContentMore from '@/components/LongContentMore';
import InputSearch from '@/components/Input/InputSearch';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import classnames from 'classnames';

const { Paragraph } = Typography;

type MobileAppListProps = {
  values: (string | number)[];
  title?: string;
};
const MobileAppList: React.FC<MobileAppListProps> = ({ values, title }) => {
  const [visible, setVisible] = useState(false);
  const [data, setData] = useState<string[]>([]);
  const [filterList, setFilterList] = useState<string[]>([]);
  const [showList, setShowList] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    // 重置数据
    if (visible) {
      const result = values.filter(v => v && `${v}`.trim()).map(v => `${v}`);
      setData(result);
      setSearchValue('');
      setFilterList(result);
    }
  }, [values, visible]);

  const handleCancel = () => {
    setVisible(false);
  };

  const handleOpen = () => {
    setVisible(true);
  };

  const handleSearchApp = (value: string) => {
    if (value) {
      const res = data.filter(item => item.toLowerCase().indexOf(value.toLowerCase()) !== -1);
      setFilterList(res);
    } else {
      setFilterList(data);
    }
  };

  const handleShowDataChange = (val: string[] | number[]) => {
    setShowList(val as string[]);
  };

  const handleValueChange = (value: string) => {
    setSearchValue(value);
  };
  return (
    <>
      <EyeOutlined className={styles['btn-svg']} title="Open" onClick={handleOpen} />
      <Modal
        title={title || 'App'}
        open={visible}
        footer={
          <div className={styles['footer']}>
            <div className={styles['content-bottom']}>
              <Paragraph
                copyable={{
                  tooltips: ['Copy the above list', 'Copied'],
                  text: filterList.join('\n'),
                  icon: [
                    <div className={styles['copy-container']} key={1}>
                      <RixEngineFont type="copy" style={{ width: 30, fontSize: 18 }} />
                      Copy
                    </div>,
                    <div className={styles['copy-container']} key={2}>
                      <CheckOutlined style={{ color: '#fff' }} />
                    </div>
                  ]
                }}
              />
            </div>
          </div>
        }
        onCancel={handleCancel}
        maskClosable={false}
        width="1200px"
        style={{ top: 50 }}
        className={styles['app-model-container']}
      >
        <div className={styles['container']}>
          <InputSearch
            placeholder="Please Input"
            handleSearch={handleSearchApp}
            value={searchValue}
            onValueChange={handleValueChange}
          />
          <div className={styles['bar']}></div>
          <LongContentMore
            contentMaxHeight={'calc(100vh - 320px)'}
            defaultShowNum={300}
            open={visible}
            onShowDataChange={handleShowDataChange}
            dataSource={filterList}
            border={false}
            className={styles['long-content-more-container']}
          >
            <div className={styles['content-container']}>
              {showList.map((item, index) => {
                return (
                  <div className={styles['content-item']} key={index}>
                    <HoverToolTip title={item}>
                      <span className={classnames(styles['ellipsis'], styles['app-name'])} title={item}>
                        {item}
                      </span>
                    </HoverToolTip>
                  </div>
                );
              })}
            </div>
          </LongContentMore>
        </div>
      </Modal>
    </>
  );
};

export default MobileAppList;
