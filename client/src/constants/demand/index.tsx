/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 15:39:24
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:58:11
 * @Description:
 */
import type { TopBarSearchItem } from '@/components/TopBar';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { DemandAndSupplyStatusOptions } from '@/constants';

export function OptionsToMap<V = any>(options: { label: string; value: V }[], type: 'label' | 'value' = 'label') {
  return options.reduce((acc, cur) => {
    if (type === 'label') {
      acc[cur.label] = cur.value;
    } else {
      acc[cur.value as string] = cur.label;
    }
    return acc;
  }, {} as Record<string, any>);
}

export const EndpointFormData: { endpoint: DemandAPI.EndpointItem[] } = {
  endpoint: [
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 1,
      ad_format: 1
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 1,
      ad_format: 2
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 1,
      ad_format: 3
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 2,
      ad_format: 1
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 2,
      ad_format: 2
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 2,
      ad_format: 3
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 3,
      ad_format: 1
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 3,
      ad_format: 2
    },
    {
      url: '',
      connect_timeout: 0,
      socket_timeout: 0,
      gzip: 1,
      server_region: 3,
      ad_format: 3
    }
  ]
};

export const ZipMap: API.StringToStringType = {
  1: 'Active',
  2: 'Paused'
};

export const ServerRegion = ['', 'USE', 'APAC', 'EUW'];
export const ADFormat: API.NumberType = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

export const DemandSearchOption: TopBarSearchItem[] = [
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: []
  },
  {
    name: 'Integration Type',
    type: 'select',
    key: 'integration_type',
    value: '',
    mode: 'multiple',
    options: []
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: '',
    options: DemandAndSupplyStatusOptions
  }
];

export const DemandBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Demand',
    icon: 'rix-demand'
  },
  {
    name: 'Advertiser List'
  }
];

export const AccountBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Demand',
    url: '/demand/advertiser',
    icon: 'rix-supply'
  },
  {
    name: 'Advertiser',
    url: '/demand/advertiser'
  },
  {
    name: 'Account'
  }
];

export const EndpointBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Demand',
    url: '/demand/advertiser',
    icon: 'rix-demand'
  },
  {
    name: 'Advertiser List',
    url: '/demand/advertiser'
  },
  {
    name: 'Endpoint'
  }
];

export const AuthBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Demand',
    url: '/demand/advertiser',
    icon: 'rix-demand'
  },
  {
    name: 'Advertiser List',
    url: '/demand/advertiser'
  },
  {
    name: 'Authorization'
  }
];
export const AuctionOptions = [
  { label: 'First Price', value: 1 },
  { label: 'Second Price', value: 2 }
];

export const AuctionType = {
  'First Price': 1,
  'Second Price': 2
};
export const AuctionTypeToString: API.StringToStringType = {
  1: 'First Price',
  2: 'Second Price'
};

export const ImpTrackingOptions = [
  { label: 'JS', value: 1 },
  { label: 'IMG_PIXEL', value: 2 }
];

export const ImpTrackingType = {
  JS: 1,
  IMG_PIXEL: 2
};

export const ImpTrackingTypeToString: API.StringToStringType = {
  1: 'JS',
  2: 'IMG_PIXEL'
};

export const NativeFormatOptions = [
  { label: 'String', value: 1 },
  { label: 'JsonObject', value: 2 }
];

export const NativeFormatType = {
  String: 1,
  JsonObject: 2
};

export const NativeFormatTypeToString: API.StringToStringType = {
  1: 'String',
  2: 'JsonObject'
};

export const NativeVersionOptions = [
  { label: '1.1', value: 1 },
  { label: '1.2', value: 2 }
];

export const NativeVersionType = NativeVersionOptions.reduce((acc, cur) => {
  acc[cur.label] = cur.value;
  return acc;
}, {} as any);

export const NativeVersionTypeToString: API.StringToStringType = NativeVersionOptions.reduce((acc, cur) => {
  acc[cur.value] = cur.label;
  return acc;
}, {} as any);

export const IvtType: API.StringToStringType = {
  1: 'Limited',
  2: 'Unlimited'
};

export const IvtTypeOptions = [
  { label: 'Limited', value: 1 },
  { label: 'Unlimited', value: 2 }
];

export const IvtTypeMap = {
  Limited: 1,
  Unlimited: 2
};

// 0 for incomplete, 1 for complete
export const SchainCompleteOptions = [
  { label: 'Active', value: 1 },
  { label: 'Paused', value: 0 }
];

export const SchainCompleteMap = OptionsToMap(SchainCompleteOptions);
export const SchainCompleteMapByValueKey = OptionsToMap(SchainCompleteOptions, 'value');

// schain_hops default value undefined
export const DefaultSchainHops = undefined;
