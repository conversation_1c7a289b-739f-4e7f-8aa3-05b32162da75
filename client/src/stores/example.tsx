// 使用示例：展示如何使用 scope 状态管理
//
// 使用方法
// 1. 在 scope 下 定义 state 类型 和 默认值
// 2. 使用 createScopeHooks 创建类型化的 hook
// 3. 将组件 使用 withScopeState 包装
//
// 对于页面级的状态：建议在每个页面下创建 store.tsx, 完成类型、初始值、hook定义，并导出。
// 最后包装 Page，就可以在page下使用hook
//
// 对于组件级的状态：建议在组件下创建 store.tsx, 完成类型、初始值、hook定义，并导出。
// 最后包装组件，就可以在组件下使用hook

import React from 'react';
import { createScopeHooks, useScopeState, useScopeStore, withScopeState } from './scope';

// 定义状态类型
interface UserState {
  name: string;
  age: number;
  email: string;
  preferences: {
    theme: 'light' | 'dark';
    language: string;
  };
}

// 初始状态
const initialUserState: UserState = {
  name: '',
  age: 0,
  email: '',
  preferences: {
    theme: 'light',
    language: 'zh-CN'
  }
};

// 创建类型化的 hooks（修复后的版本，符合 React hooks 规则）
const { useScopeState: useUserState, useScopeStore: useUserStore } = createScopeHooks<UserState>();

// 子组件 - 显示用户信息
function UserProfile() {
  // 使用类型安全的状态读取
  const store = useUserStore();

  return (
    <div>
      <h2>用户信息</h2>
      <p>姓名: {store.name}</p>
      <p>年龄: {store.age}</p>
      <p>邮箱: {store.email}</p>
      <p>主题: {store.preferences.theme}</p>
      <p>语言: {store.preferences.language}</p>
    </div>
  );
}

// 子组件 - 编辑用户信息
function UserEditor() {
  // 使用类型安全的状态修改
  const state = useUserState();

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    state.name = e.target.value; // 完全类型安全
  };

  const handleAgeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    state.age = parseInt(e.target.value) || 0;
  };

  const handleThemeToggle = () => {
    state.preferences.theme = state.preferences.theme === 'light' ? 'dark' : 'light';
  };

  return (
    <div>
      <h2>编辑用户信息</h2>
      <div>
        <label>
          姓名:
          <input type="text" value={state.name} onChange={handleNameChange} />
        </label>
      </div>
      <div>
        <label>
          年龄:
          <input type="number" value={state.age} onChange={handleAgeChange} />
        </label>
      </div>
      <div>
        <button onClick={handleThemeToggle}>切换主题 (当前: {state.preferences.theme})</button>
      </div>
    </div>
  );
}

// 主组件
function UserApp() {
  return (
    <div>
      <UserProfile />
      <UserEditor />
    </div>
  );
}

// 使用高阶函数包装，提供类型安全的状态管理
export const UserAppWithState = withScopeState(UserApp, initialUserState);

// 另一种使用方式：直接使用泛型 hooks
function AlternativeUserComponent() {
  // 直接使用泛型版本的 hooks
  const state = useScopeState<UserState>();
  const store = useScopeStore<UserState>();

  return (
    <div>
      <h3>替代使用方式</h3>
      <p>当前用户: {store.name}</p>
      <button onClick={() => (state.name = '新用户')}>更改用户名</button>
    </div>
  );
}

export { AlternativeUserComponent };
