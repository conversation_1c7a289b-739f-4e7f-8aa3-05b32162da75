/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-12 15:10:20
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-06 16:30:42
 * @Description:,
 */

import storage from '@/db/localStorage';
import log4js from 'log4js';
import path from 'path';

// 日志根目录
const baseLogPath = path.resolve(__dirname, '../../logs');

log4js.configure({
  appenders: {
    app: {
      // 设置类型为 dateFile
      type: 'dateFile',
      filename: `${baseLogPath}/rix.platform.app.log`,
      // 指定编码格式为 utf-8
      encoding: 'utf-8',
      // 配置 layout，此处使用自定义模式 pattern
      layout: {
        type: 'pattern',
        // 配置模式，下面会有介绍
        pattern: '[%d{yyyy-MM-dd hh:mm:ss}] [%p] %m%n'
      },
      // 日志文件按日期（天）切割
      pattern: 'yyyy-MM-dd',
      compress: false,
      numBackups: process.env.NODE_ENV === 'prod' ? 360 : 1,
      // 输出的日志文件名是都始终包含 pattern 日期结尾
      alwaysIncludePattern: true
    },
    http: {
      // 设置类型为 dateFile
      type: 'dateFile',
      filename: `${baseLogPath}/rix.platform.http.log`,
      // 指定编码格式为 utf-8
      encoding: 'utf-8',
      // 配置 layout，此处使用自定义模式 pattern
      layout: {
        type: 'pattern',
        // 配置模式，下面会有介绍
        pattern: '[%d{yyyy-MM-dd hh:mm:ss}] [%p] %m%n'
      },
      // 日志文件按日期（天）切割
      pattern: 'yyyy-MM-dd',
      compress: false,
      numBackups: process.env.NODE_ENV === 'prod' ? 360 : 1,
      // 输出的日志文件名是都始终包含 pattern 日期结尾
      alwaysIncludePattern: true
    },
    // 报错的拆分出去
    error: {
      // 设置类型为 dateFile
      type: 'dateFile',
      filename: `${baseLogPath}/rix.platform.error.log`,
      // 指定编码格式为 utf-8
      encoding: 'utf-8',
      // 配置 layout，此处使用自定义模式 pattern
      layout: {
        type: 'pattern',
        // 配置模式，下面会有介绍
        pattern: '[%d{yyyy-MM-dd hh:mm:ss}] [%p] %m%n'
      },
      // 日志文件按日期（天）切割
      pattern: 'yyyy-MM-dd',
      compress: false,
      numBackups: process.env.NODE_ENV === 'prod' ? 360 : 1,
      // 输出的日志文件名是都始终包含 pattern 日期结尾
      alwaysIncludePattern: true
    },
    sql: {
      // sql日志
      // 设置类型为 dateFile
      type: 'dateFile',
      filename: `${baseLogPath}/rix.platform.sql.log`,
      // 指定编码格式为 utf-8
      encoding: 'utf-8',
      // 配置 layout，此处使用自定义模式 pattern
      layout: {
        type: 'pattern',
        // 配置模式，下面会有介绍
        pattern: '[%d{yyyy-MM-dd hh:mm:ss}] [%p] %m%n'
      },
      // 日志文件按日期（天）切割
      pattern: 'yyyy-MM-dd',
      compress: false,
      numBackups: process.env.NODE_ENV === 'prod' ? 360 : 1,
      // 输出的日志文件名是都始终包含 pattern 日期结尾
      alwaysIncludePattern: true
    },
    redis: {
      // redis日志
      // 设置类型为 dateFile
      type: 'dateFile',
      filename: `${baseLogPath}/rix.platform.redis.log`,
      // 指定编码格式为 utf-8
      encoding: 'utf-8',
      // 配置 layout，此处使用自定义模式 pattern
      layout: {
        type: 'pattern',
        // 配置模式，下面会有介绍
        pattern: '[%d{yyyy-MM-dd hh:mm:ss}] [%p] %m%n'
      },
      // 日志文件按日期（天）切割
      pattern: 'yyyy-MM-dd',
      compress: false,
      numBackups: process.env.NODE_ENV === 'prod' ? 360 : 1,
      // 输出的日志文件名是都始终包含 pattern 日期结尾
      alwaysIncludePattern: true
    }
  },
  categories: {
    // 设置默认的 categories
    app: {
      appenders: ['app'],
      level: process.env.NODE_ENV === 'prod' ? 'info' : 'ALL'
    },
    default: {
      appenders: ['http'],
      level: process.env.NODE_ENV === 'prod' ? 'info' : 'ALL'
    },
    error: {
      appenders: ['error'],
      level: process.env.NODE_ENV === 'prod' ? 'info' : 'ALL'
    },
    sql: {
      appenders: ['sql'],
      level: process.env.NODE_ENV === 'prod' ? 'info' : 'ALL'
    },
    redis: {
      appenders: ['redis'],
      level: process.env.NODE_ENV === 'prod' ? 'info' : 'ALL'
    }
  },
  pm2: true,
  disableClustering: true
});

export const httpLogger = log4js.getLogger('http');

// 全局日志打印 不用logjs的context http记录最原始值 方便比对
export function getLogger(type: 'app' | 'error' | 'sql' | 'redis') {
  const logger = log4js.getLogger(type);

  const log_id = storage.getItem('log_id') || '';
  const api = storage.getItem('api') || '';
  const user = storage.getItem('user') || '';
  const tnt = storage.getItem('tnt') || '';

  return {
    info: (msg: string) =>
      logger.info(`${log_id} ${tnt} ${user} ${api} ${msg}`),
    error: (msg: string) =>
      logger.error(`${log_id} ${tnt} ${user} ${api} ${msg}`)
  };
}
