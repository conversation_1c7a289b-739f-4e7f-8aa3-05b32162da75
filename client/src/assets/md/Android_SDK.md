# Android SDK 中文接入文档

## 1. **工程设置**

### 1.1 导入`aar`及`SDK`依赖的`jar`包

将本`SDK` 压缩包内的`rixengine**.*.*.aar `复制到 `Application Module/libs `文件夹(没有的话须手动创建), 并将以下代码添加到您` Moudle app `的`build.gradle`中：

`//Unity`导出的项目`Application`名一般为`unityLibrary`

```Java
repositories {
    flatDir {
        dirs 'libs'
    }
}
depedencies {
    implementation(name: 'rixengine.*.*.*', ext: 'aar') //要自行改成具体版本号
 
}
```

### 1.2 示例代码

您可以在[Github](https://github.com/RixEngine-code/Rixengine-ads-demo-android/) 上看相应的示例代码。

## 2. **全局配置**

### 2.1 添加权限

`RixEngine SDK`建议您添加下述权限，并建议在您的隐私协议中向开发者声明`RixEngine SDK`会获取下述权限并应用于广告投放

```Java
<!--必要权限-->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<!—可选权限-->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<!--可选权限：安卓11及以上，按照实际情况可选择添加此权限-->
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
<!-- 播放器应用需要防止屏幕变暗 -->
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### 2.2 运行环境配置

本 `SDK `可运行于` Android4.1 (API Level 16) `及以上版本。

`<uses-sdk android:minSdkVersion="16" android:targetSdkVersion="29" />`

如果开发者声明`targetSdkVersion`到`API 23`以上，请确保调用本`SDK`的任何接口前，已经申请到了`SDK`要求的所有权限，否则`SDK`部分特性可能受限。

### 2.3 代码混淆设置 

在`App`文件夹下的`.pro`文件(`Android`中一般叫`proguard-rules.pro`，`Unity`导出的为`proguard-unity.txt`)中添加：

```Java
-keep class com.rixengine.api.** {*;}
-keep class com.rixengine.api.nativead.** {*;}

-keep class com.admob.custom.adapter.** {*;}
-keep class com.anythink.custom.adapter.** {*;}
-keep class com.tradplus.custom.adapter.** {*;}
-keep class com.applovin.mediation.adapters.** {*;}
-keep class com.ironsource.adapters.** {*;}
```

## 3. **集成**

首先去申请开通账号，配置好自己的应用，主要是`Host`,`Token`,`SID`和`APP_ID`以及相关的广告位 ，其中4个关键参数`TOKEN`,`HOST`, `SID`, `APP_ID` 均由 `RixEngine SaaS` 后台生成，请联系后台相关运营人员。



初始化`SDK`，开发者需要在`Application#onCreate()`方法中调用以下代码来初始化`RixEngine SDK`

```Java
AlxAdSDK.init(context, HOST, TOKEN, SID, APP_ID,new AlxSdkInitCallback() {
    @Override
public void onInit(boolean b, String s) {
// 初始化成功后才能请求广告，否则可能会影响填充率
    }
});
```



### 3.1 横幅广告

横幅广告会占据应用布局中的一处位置，要么是设备屏幕的顶部，要么是底部。这类广告会在用户与应用互动时停留在屏幕上，并且可在一段时间后自动刷新,支持直接加载展示和预加载、手动展示。

 **集成代码**

向布局中添加`AlxBannerView`

要展示横幅广告，首先要将`AlxBannerAd`放置到您希望用于展示广告的`Activity` 或 `Fragment` 的布局中。最简单的放置方法是将其添加到相应的 `XML` 布局文件中。支持两种加载方式，一种是加载并展示，另一种是预加载后手动调用展示，建议使用第一种，代码会较少。

下例展示了某一 `Activity` 的 `AlxBannerView`（加载完成，直接展示）：

```Java
<com.rixengine.api.AlxBannerView
  android:id****="@+id/alx_ad_banner"**
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:layout_centerHorizontal="true"
  android:layout_alignParentBottom="true">
</com.rixengine.api.AlxBannerView>
```

或者新建一个`Framelayout` 容器，来显示（预加载，手动控制展示）：

```Java
<FrameLayout
  android:id="@+id/ad_container"
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:layout_gravity="center_horizontal"
  android:layout_marginTop="20dp"/>
```

下例显示了如何通过 `Activity` 的 `onCreate()`方法加载广告：

```Java
    public class BannerActivity extends AppCompatActivity implements View.OnClickListener {
    private final String TAG = "AlxBannerDemoActivity";

    private Button mBnLoad;
    private Button mBnShow;
    private TextView mTvTip;
    private Button mBnLoadAndShow;

    private FrameLayout mAdContainer;
    private AlxBannerView mAlxBannerView;
    private AlxBannerView mAlxBannerView2;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_banner);
        setTitle("BannerAd");

        mTvTip = (TextView) findViewById(R.id.tv_tip);
        mBnLoad = (Button) findViewById(R.id.bn_load);
        mBnShow = (Button) findViewById(R.id.bn_show);
        mBnLoadAndShow = (Button) findViewById(R.id.bn_load_show);
        mAdContainer = (FrameLayout) findViewById(R.id.ad_container);
        mAlxBannerView = (AlxBannerView) findViewById(R.id.do_ad_banner);

        mBnLoad.setOnClickListener(this);
        mBnShow.setOnClickListener(this);
        mBnLoadAndShow.setOnClickListener(this);
        mBnShow.setEnabled(false);
    }

    @Override
    protected void onDestroy() {
        if (mAlxBannerView2 != null) {
            mAlxBannerView2.destroy();
        }
        if (mAlxBannerView != null) {
            mAlxBannerView.destroy();
        }
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bn_load:
                bnPreLoad();
                break;
            case R.id.bn_show:
                bnShow();
                break;
            case R.id.bn_load_show:
                bnLoadAndShow();
                break;
        }
    }

    private void bnPreLoad() {
        mBnLoad.setEnabled(false);
        mAlxBannerView2 = new AlxBannerView(this);
        mAlxBannerView2.setBannerCanClose(false);
        mAlxBannerView2.setBannerRefresh(0);//cancel auto refresh
        mAlxBannerView2.setVisibility(View.VISIBLE);
        mAlxBannerView2.loadAd(unitid, new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "onAdLoaded");
                mTvTip.setText("Alx Banner AD load success | ecpm：" + mAlxBannerView2.getPrice());
                mBnShow.setEnabled(true);
                mBnLoad.setEnabled(true);

                mAlxBannerView2.reportBiddingUrl();
                mAlxBannerView2.reportChargingUrl();
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.d(TAG, "onAdError: errorMsg=" + errorMsg + "  errorCode=" + errorCode);
                mBnShow.setEnabled(false);
                mBnLoad.setEnabled(true);
                mTvTip.setText("Alx Banner AD load failed");
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "onAdClicked");
            }

            @Override
            public void onAdShow() {
                Log.d(TAG, "onAdShow");
            }

            @Override
            public void onAdClose() {
                Log.d(TAG, "onAdClose");
            }
        });
    }

    private void bnShow() {
        if (mAlxBannerView2 != null && mAlxBannerView2.isReady()) {
            mAdContainer.removeAllViews();
            mAdContainer.addView(mAlxBannerView2);
            mTvTip.setText("");
        }
    }

    private void bnLoadAndShow() {
        mAlxBannerView.setBannerCanClose(true);
        mAlxBannerView.loadAd(AdConfig.ALX_BANNER_AD_PID, new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "onAdLoaded:  | ecpm：" + mAlxBannerView.getPrice());
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.d(TAG, "onAdError: errorMsg=" + errorMsg + "  errorCode=" + errorCode);
                Toast.makeText(getBaseContext(), "Alx Banner AD load failed", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "onAdClicked");
            }

            @Override
            public void onAdShow() {
                Log.d(TAG, "onAdShow");
            }

            @Override
            public void onAdClose() {
                Log.d(TAG, "onAdClose");
            }
        });
    }

}    
```



### 3.2 激励视频广告

激励视频的激励下发时会回调`onReward()`,开发者可以在`onReward`回调中下发激励给用户。；

集成建议：

1. 请提前调用加载方法进行广告的请求（比如在应用启动就开始加载广告），以便需要触发广告时可以快速展示
1. 在`onRewardedVideoAdClosed`回调中，调用加载方法进行广告的请求，方便下一次广告的展示
1. 禁止在`onRewardedVideoAdFailed`回调中执行广告加载的方法，否则会引起很多无用请求且可能会导致应用卡顿

接入流程建议：

* 启动应用或者游戏通过`AlxRewardVideoAd.load`来执行加载广告
* 在需要展示激励视频的位置通过`AlxRewardVideoAd.isReady`判断是否能展示：

**false** :重新执行`AlxRewardVideoAd.load`来加载广告

**true** : 执行`AlxRewardVideoAd.show`展示广告，在`onRewardedVideoAdClosed`的回调中再执行`AlxRewardVideoAd.load`来预加载下一次的广告（在`close`的回调里可直接调用`load`不需要经过`isReady`的判断，有助于提升优先级比较高的广告源的展示量.）

**集成代码**

```Java
//新建一个 RewardVideo 对象
AlxRewardVideoAD alxRewardVideoAD = new AlxRewardVideoAD();
//然后加载激励视频
alxRewardVideoAD.load(this, mPid , new AlxRewardVideoADListener() { //mPid 广告位
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                Log.i(TAG, "视频广告加载成功");
showVideo(); //播放激励视频
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.i(TAG, "视频广告加载错误 " + errCode + " " + errMsg);
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                Log.i(TAG, "视频广告展示成功");
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
               Log.i(TAG, "视频广告观看完整");
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
               Log.i(TAG, "视频播放失败:"+errCode+";"+errMsg);
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                Log.i(TAG, "点击按钮关闭广告");
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                Log.i(TAG, "视频广告点击成功");
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                Log.i(TAG, "视频广告观看完整--给予奖励");
            }
        });

public void showRewardVideo(){
if (alxRewardVideoAD.isLoaded()) {
          alxRewardVideoAD.showVideo(this);
        }
}
```



### 3.3 插屏广告

插屏是全屏广告，它会覆盖其托管应用的整个界面

集成建议

1. 提前调用加载方法进行广告的请求，以便需要触发广告时可以快速展示

2. 在`onInterstitialAdClose`回调中，调用加载方法进行广告的请求，方便下一次广告的展示

3. 禁止在`onInterstitialAdLoadFail`回调中执行广告加载的方法，否则会引起很多无用请求且可能会导致应用卡顿

接入流程建议：

* 启动应用或者游戏通过`AlxInterstitialAD.load`来执行加载广告
* 在需要展示插屏广告的位置通过`AlxInterstitialAD.isReady`判断是否能展示：

**false**: 重新执行`AlxInterstitialAD.load`来加载广告

**true**: 执行`AlxInterstitialAD.show`展示广告，在`onInterstitialAdClose`的回调中再执行`AlxInterstitialAD.load`来预加载下一次的广告.

**集成代码**

```Java
//新建一个 AlxInterstitialAD对象
AlxInterstitialAD  alxInterstitialAD = new AlxInterstitialAD();
alxInterstitialAD.load(mContext, mPid, new AlxInterstitialADListener() { //mPid 广告位
            @Override
            public void onInterstitialAdLoaded() {
                Log.d(TAG, "onInterstitialAdLoaded success");
                   If(alxInterstitialAD.isReady();){
                    alxInterstitialAD.show(MainActivity.this);//播放插屏
        
            }

            @Override
            public void onInterstitialAdLoadFail(int i, String s) {
                Log.d(TAG, "onInterstitialAdLoadFail " + "errorCode: " + i + " errorMSg: " + s);
            }

            @Override
            public void onInterstitialAdClicked() {
                Log.d(TAG, "onInterstitialAdClicked");
            }

            @Override
            public void onInterstitialAdShow() {
                Log.d(TAG, "onInterstitialAdShow");
            }

            @Override
            public void onInterstitialAdClose() {
                Log.d(TAG, "onInterstitialAdClose");
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int i, String s) {
                Log.d(TAG, "onInterstitialAdVideoError " + "errorCode: " + i + " errorMSg: " + s);
            }
        });
});
```



### 3.4 原生广告

`SDK`为接入方提供了可自定义布局的信息流广告，包含大图、小图、组图和视频等基本样式类型（具体的布局摆放可自行定义）

构建原生广告步骤如下：

1.构建`AlxNativeAdLoader` 并加载广告

​	注意：请在主线程中进行对移动广告`SDK`的所有调用。

  以下代码演示了如何构建可加载统一原生广告的 `AlxNativeAdLoader` ：

  ```Java
  AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
  loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
              @Override
              public void onAdFailed(int errorCode, String errorMsg) {
              //广告加载失败时调用
              }

              @Override
              public void onAdLoaded(List<AlxNativeAd> ads) {
                  //广告加载成功时调用
              }
          }); 
  ```
2.展示原生广告

a. 定义原生广告布局

需要自定义一个布局用于展示`AlxNativeAd`中的素材。

>  **说明** ：**必须将** `AlxNativeAdView` 作为原生广告的根布局，否则会影响广告收益


对于使用`RelativeLayout`来展示素材视图的原生广告，其视图层次结构示例如下：

```Java
<com.rixengine.api.nativead.AlxNativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    ... >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        ... >
        <!-- 多媒体视图 -->
        <com.rixengine.api.nativead.AlxMediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            ... />
        <RelativeLayout
            ... >
        <TextView
            android:id="@+id/ad_title"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            ... />
        <!-- 其他素材 -->
        ...
        </RelativeLayout>
        <!-- 其他素材 -->
        ...
    </RelativeLayout>
</com.rixengine.api.nativead.AlxNativeAdView>
```

b. 注册和填充素材视图

获取到`AlxNativeAdView`对象后，需要注册和填充素材。代码如下：

```Java
private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
        ImageView logo = (ImageView) nativeView.findViewById(R.id.ad_logo);
        ImageView icon = (ImageView) nativeView.findViewById(R.id.ad_icon);
        TextView title = (TextView) nativeView.findViewById(R.id.ad_title);
        TextView description = (TextView) nativeView.findViewById(R.id.ad_desc);
        TextView source = (TextView) nativeView.findViewById(R.id.ad_source);
        Button callToAction = (Button) nativeView.findViewById(R.id.ad_call_to_action);
        ImageView close = (ImageView) nativeView.findViewById(R.id.ad_close);
        AlxMediaView mediaView = (AlxMediaView) nativeView.findViewById(R.id.ad_media);

        //注册和填充标题素材视图
        nativeView.setTitleView(title);
        title.setText(nativeAd.getTitle());

        //注册和填充多媒体素材视图
        nativeView.setMediaView(mediaView);
        mediaView.setMediaContent(nativeAd.getMediaContent());

        //注册和填充其他素材视图
        nativeView.setDescriptionView(description);
        nativeView.setIconView(icon);
        nativeView.setCallToActionView(callToAction);
        nativeView.setCloseView(close);
        nativeView.setAdSourceView(source);
        description.setText(nativeAd.getDescription());
        logo.setImageBitmap(nativeAd.getAdLogo());

        if (TextUtils.isEmpty(nativeAd.getAdSource())) {
            source.setVisibility(View.GONE);
        } else {
            source.setVisibility(View.VISIBLE);
            source.setText(nativeAd.getAdSource());
        }
        if (TextUtils.isEmpty(nativeAd.getCallToAction())) {
            callToAction.setVisibility(View.GONE);
        } else {
            callToAction.setVisibility(View.VISIBLE);
            callToAction.setText(nativeAd.getCallToAction());
        }

        //视频广告监听器
        if (nativeAd.getMediaContent() != null && nativeAd.getMediaContent().hasVideo()) {
            nativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {
                @Override
                public void onVideoStart() {
                }

                @Override
                public void onVideoEnd() {
                }

                @Override
                public void onVideoPlay() {
                }

                @Override
                public void onVideoPause() {
                }

                @Override
                public void onVideoMute(boolean isMute) {
                }
            });
        }

        //注册原生广告对象
        nativeView.setNativeAd(nativeAd);
    }
```

依次设置其他要展示的广告素材。

`AlxMediaView`用于展示多媒体素材。如果获取的广告含有视频素材，则视频会在`AlxMediaView`内播放。否则`AlxMediaView`会显示一个图片素材。

c. 向`NativeView`注册原生广告对象

示例代码如下所示：

```Java
nativeView.setNativeAd(nativeAd);
```





d. 展示`AlxNativeAdView`

将`AlxNativeAdView`添加到界面即可展示原生广告。示例代码如下所示：

```Java
private void loadAd() {
   AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
   loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
         @Override
         public void onAdLoaded(List<AlxNativeAd> ads) {
         ……
             AlxNativeAd nativeAd = ads.get(0);

             //获取AlxNativeAdView视图
             AlxNativeAdView nativeAdView =(AlxNativeAdView) getLayoutInflater().inflate(R.layout.native_ad_template, null);

             //注册和填充原生广告素材
             initNativeAdView(nativeAd,nativeAdView);

             //将AlxNativeAdView添加到界面
             FrameLayout adFrameLayout = (FrameLayout) findViewById(R.id.ad_container);
             adFrameLayout.removeAllViews();
             adFrameLayout.addView(nativeAdView);
             ……
           }
     });
}

private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
     ……
     //注册和填充标题素材视图
     nativeView.setTitleView(title);
     title.setText(nativeAd.getTitle());

      //注册和填充多媒体素材视图
      nativeView.setMediaView(mediaView);
      mediaView.setMediaContent(nativeAd.getMediaContent());

      //注册和填充其他素材视图
      ……      

      //注册原生广告对象
      nativeView.setNativeAd(nativeAd);
}
```



e . 销毁广告

当原生广告不再展示时，应将其销毁。示例代码如下所示：

```Java
nativeAd.destroy();
```

下面是对`AlxNativeAd`类进行详细说明

|**方法** |**参数** |**说明** |
|---|---|---|
|getCreateType |- |广告素材类型【如：大图、小图、组图、视频、其他：未知类型】 |
|getAdSource |- |广告来源 |
|getAdLogo |- |广告logo |
|getTitle |- |广告标题 |
|getDescription |- |广告内容描述 |
|getIcon |- |广告小图标 |
|getImages |- |广告内容多图素材 |
|getCallToAction |- |广告行为按钮的显示文字（如：”查看详情” 或 “安装”） |
|getMediaContent |- |广告多媒体素材 |
|destroy |- |销毁广告对象 |
|setNativeEventListener |`AlxNativeEventListener listener` |广告事件监听器 |

**关于** `Bidding` **价格返回和事件上报**

(重要)注意：

`SDK`提供三个关于`bidding`的接口：

```Java
//返回bidding价格
public double getPrice() {

}
```

```Java
//上报广告竞价成功事件
public void reportBiddingUrl() {

}
```

```Java
//上报广告展示成功事件
public void reportChargingUrl() {
      
}
```

开发者可以在广告请求成功后，有填充的时候在`onAdLoaded`回调接口里面

调用`getPrice()`接口，可以获取到`Bidding`价格，单位为美元，如果为`0`表示没有返回实时竞价。



开发者通过竞价比价后，选择展示我们的广告的时候，可以在`load`或者`show`接口里面调用一下

`reportBiddingUrl()`;



开发者展示我们的广告时候，在`show`接口需要同时调用

`reportChargingUrl()`;



## 4. **用户隐私**

##### 1. **GDPR**

作为开发者，您需要在自己的 `app` 里面整合 `Consent Management Platform (CMP)`，并且根据` IAB Europe` 的`“Mobile In-App CMP API v1.0: Transparency & Consent Framework”` 和`“IAB Tech Lab – CMP API v2”`要求，获取用户的同意。 

如果您使用的是自建的 `CMP`，您需要将收集到的用户同意信息储存在 `SharedPreferences` 里面（使用 `IABTCF_gdprApplies` 和`IABTCF_TCString` 这两个 `key`）。 

`Transparency and Consent Framework (TCF) v2:``**Key**`

**类型描述**

![img](https://static.rixengine.com/a/platform/help/sdk/4-1-1.png)

`RixEngine`是 `IAB Europe` 透明度和同意框架（TCF）的供应商之一。更多关于`RixEngine` 对 `GDPR` 的合规，您可以参考 `RixEngine` 的隐私政策。 

`RixEngine SDK` 会自动从 `SharedPreferences` 的 `IABTCF_gdprApplies` 和 `IABTCF_TCString` 这两个 `key` 里面读取 `GDPR` 相关的用户同意字段。您也可以使用下列的 `SDK` 函数手动传入 `GDPR`相关的用户同意字段。注意：请尽量在 `SDK` 初始化之后调用。 

如果您的 `app` 已经接入了 `CMP`，并且将用户同意字段储存在 `SharedPreferences` 的 

`IABTCF_gdprApplies` 和 `IABTCF_TCString` 这两个 `key` 里面，您就不需要再调用下列函数。 

```Java
AlxAdSDK.setSubjectToGDPR(TRUE); 
AlxAdSDK.setUserConsent(<#Base64ConsentString#>); // Base64 加密的 consent string 
```

`"subject to GDPR"` 标志可以设置为 `YES / TRUE`（用户受到 `GDPR` 法规保护）或 `NO / FALSE` （用户不受到 `GDPR` 法规保护）。 只有在应用程序已经判断 `GDPR` 是否适用于用户时，才能调用此函数。 如果这个功能没有被调用，`RixEngine` 认为应用程序没有做出这样的判断，因此 `RixEngine` 会自行判断 `GDPR` 的适用性。 

`setUserConsent` 函数提供`“0”`（用户不同意），`“1”`（用户同意）或更详细的同意信息 

（`Base64` 加密的 `consent string`）。 这个更详细的同意信息是在 `IAB` 支持的透明和同意框架 中描述的同意信息，详情可参考

[`https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework`](https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework)

##### 2. **CCPA**

`California Consumer Privacy Act (CCPA`) 的发布是为了提升和加强加利福尼亚用户个人隐私信息使用的透明度和管控。作为开发者，您需要对加利福尼亚州的用户的个人隐私信息的使用获取同意。更多关于 `RixEngine` 对 `CCPA` 的合规，您可以参考 `RixEngine` 的隐私政策。 

**注意**:如果您是从以前版本升级并且已经设置隐私标志位，请不要担心。新版本的 `SDK` 依 

然会使用以前版本中设置的值，无需再次手工设置。但是我们推荐您使用新的方法。 

您可以参考下面的代码示例传入 `US Privacy` 标志。注意：请尽量在 `SDK` 初始化之后调用。`AlxAdSDK.subjectToUSPrivacy("1YYY")`; `subjectToUSPrivacy` 接受字符串值（比如`"1YYY"`）。 此标志采用的字符串要求符合 `IAB` 的美国隐私字符串格式。 



##### 3. **年龄相关的合规要求**

根据相关法律法规（`COPPA`，`GDPR` 等），如果您的 `app` 是面向儿童用户，您需要使用下列 

函数标注出用户是儿童。您有责任决定和标注您的 `app` 的某些用户是儿童还是所有用户都 

视作儿童。 

```Java
AlxAdSDK.setBelowConsentAge(TRUE); 
```

`belowConsentAge` 标志采用布尔值，可以为 `TRUE`（用户在相关法规下的定义是儿童）或 

`FALSE`（用户在相关法规下的定义不是儿童）。



## 5.  第三方聚合后台配置流程（仅供参考）：

适配器源代码已经包含在sdk压缩包里面，无需再开发

![img](https://static.rixengine.com/a/platform/help/sdk/5-1.png)

### 5.1 Admob

1.创建应用（如果已经有请忽略）

1).首先在Admob官网登录，添加第三方广告平台，具体按照Admob官网说明文档进行调整[https://apps.admob.com/](https://apps.admob.com/) 如果没有创建应用，首先创建一个应用（下面是测试用例）

 

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-1.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-2.png)

 



2.)然后添加创建对应的广告单元

 

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-3.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-4.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-5.png)

 

2.添加Alx中介组

1).首先创建Alx中介组

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-6.png)

 

2).创建添加Alx 广告单元，支持 激励视频广告,插屏广告,横幅广告(以激励视频为例)

填入名称Alx激励视频中介 选择广告单元类型为激励广告

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-7.png)

 

 

3).然后点击-添加广告单元 选择对应的应用和广告单元

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-8.png)

 

4).点击- 完成

到下一个界面广告瀑布流-添加自定义事件

输入名称和价格

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-9.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-10.png)

 

 

5).点击继续

输入一下广告单元信息（重要）

然后输入映射广告单元信息：

输入 com.admob.custom.adapter.AlxRewardVideoAdapter (激励视频)

支持的具体Adapter类名:

激励视频 com.admob.custom.adapter.AlxRewardVideoAdapter

横幅广告 com.admob.custom.adapter.AlxBannerAdapter

插屏广告 com.admob.custom.adapter.AlxInterstitialAdapter



```json
 参数为：
 {
   "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",

  "unitid": "171998",

  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",

  "isdebug": "false",

  "sid": "60188"
  }  
```



其中appid   unitid  token  sid在RixEngine平台申请（具体可咨询运营对接人员）

appid对应RixEngine平台的App ID

token对应RixEngine平台的Token

sid对应RixEngine平台的ID

unitid对应RixEngine平台的Placement ID

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-11.png)

 

6.)输入完成后点击-完成 

到以下界面

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-12.png)



7)..点击保存按钮

，然后点击关闭回到主页面就完成了

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.1-13.png)

 

### 5.2 MAX

1.首先在官网 [Applovin｜Everything you need to grow your mobile apps（](https://www.applovin.com/)[applovin.com](https://www.applovin.com/)[）](https://www.applovin.com/) ，创建添加自己的app应用，具体按照MAX官网说明文档进行调整,如果没有创建应用，首先创建一个应用（下面是测试用例）

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-1.png)

 

 

创建一个android App



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-2.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-3.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-4.png)



2.添加完app后，回到主界面然后点击NetWorks，点击图中的 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-5.png)

  

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-6.png)



3.选择SDK 接入方式 ，输入Custom Networks Name并且输入适配器包路径 

com.applovin.mediation.adapters.AlgorixMediationAdapter （重要，路径一定要对）

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-7.png)



4.把Rixengine压缩包里面的适配器文件拷贝到项目中对应的的目录如下图       

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-8.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-9.png)



5.导入Rixengine Sdk 和添加混淆配置

将本 SDK 压缩包内的 rixengine.*.*.*.aar 复制到 Application Module/libs 文件夹(没有的话须手动创建), 并将以下代码添加到您 Moudle app 的build.gradle中：

//Unity导出的项目Application名一般为unityLibrary

```json
repositories {
    flatDir {
        dirs 'libs'
    }
}
depedencies {
    implementation(name: 'rixengine.*.*.*', ext: 'aar') //要自行改成具体版本号
 
}
```



在App文件夹下的.pro文件(Android中一般叫proguard-rules.pro，Unity导出的为proguard-unity.txt)中添加：

```
-keep class com.rixengine.** {*;}  

-keep class admob.custom.adapter.** {*;}

-keep class anythink.custom.adapter.** {*;}

-keep class com.tradplus.custom.adapter.** {*;}

-keep class com.applovin.mediation.adapters.** {*;}

-keep class com.ironsource.adapters.** {*;}
```

 

6.创建完NetWorks后，点击右侧的 Ad Units 依次创建广告位 支持4种类型

BANNER，INTER，NATIVE，REWARD 。

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-10.png)

**创建NATIVE 类型广告时要注意选择Manual（自渲染）这个很重要，不然会没有填充**

**具体代码实现可以参考demo中的集成案例**

```Java
mAdLoader = new MaxNativeAdLoader(MAX_NATIVE_AD,context);
mAdLoader.setNativeAdListener(mMaxNativeAdListener);
mAdLoader.loadAd(createNativeAdView()); //Manual (自渲染)
```



****

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-11.png)



7．创建完广告位之后，点击 banner（以BANNER为例）

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-12.png)

 

****

 

 

拉到下面，点击图中的 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-13.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-14.png)

 

Enable   图中的

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-15.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-16.png)

并且填入广告参数 广告参数在Rixengine 平台申请

**Custom Parameters 格式**

  

```json
{

  "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",

  "unitid": "171998",

  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",

  "isdebug": "false",

  "sid": "60188"
}
```

  

****

appid对应Rixengine平台的App ID

token对应Rixengine平台的Token

sid对应Rixengine平台的ID

united对应Rixengine平台的Placement ID

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-17.png)

 

8.输入完成后 点击保存即可。



9.广告集成测试 可以调整图中的

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-18.png)

来优先显示Rixengine广告



### 5.3 IronSource

1.首先在[ironSource | Turning Apps Into Scalable Businesses (](https://www.is.com/)[is.com](https://www.is.com/)[)](https://www.is.com/)官网登录，创建添加自己的app应用，具体按照IronSource官网说明文档进行调整,如果没有创建应用，首先创建一个应用（下面是测试用例）

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-1.png)

 

2.添加完app后，回到主界面然后点击SDK NetWorks

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-2.png)

 

3.然后在点击下图中的Available Networks  ，选择Custom Adapter

（PS：刚开始可能看到不到Custom Adapter选项，需要找相关的AM开通）

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-3.png)

 

4．输入Network Key : 15b958455 (重要，这个是AlgoriX专用的Network key)

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-4.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-5.png)

 

5.把AlgoriX压缩包里面的适配器文件拷贝到项目中对应的的目录如下图

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-6.png)

​       

6．输入对应的 sid 和 token  在Rixengine SSP平台申请（具体可咨询运营对接人员）

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-7.png)

 IronSource 平台比较特殊 适配器里面的 host需要手动去更改

host对应SSP平台的host

appid对应SSP平台的App ID

token对应SSP平台的Token

sid对应SSP平台的ID

unitid对应SSP平台的Placement ID



7.添加完成后点击图中的Setup 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-8.png)

 

8.输入对应的appid 和 unitid

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-9.png)

 

9.然后点击图中的Algorix  图标，看看对应的广告位是否生效（确保为Active）

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-10.png)

 

10.然后到Meidiation 界面能看到具体的排序

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-11.png)

11.可以通过手动更改 ecpm的值，来调整广告的加载排序

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-12.png)

 

 

12.测试聚合是否成功，对于已经上线的app 可以在Testing里面的 Mdeiation Testing 添加一个测试设备。

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-13.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-14.png)



13.然后选择对应的广告源，点 Test按钮开启就好。

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-15.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-16.png)

 

### 5.4 Topon

1. 第一步： 聚合sdk包获取地址

[https://docs.toponad.com/#/zh-cn/android/download/package?_t=UnCmEcPpebJEXBdkOHG7dVe114c4837z](https://docs.toponad.com/#/zh-cn/android/download/package?_t=UnCmEcPpebJEXBdkOHG7dVe114c4837z) 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-1.png)

注意： 选择最新版本的sdk,  非中国内地版本， 接入方式建议选gradle。

聚合广告平台选择： 华为、Mintegral 、UnityAds 、Admob

 

1. 第二步：接入文档请参考如下链接 ：  

[https://docs.toponad.com/#/zh-cn/android/android_doc/android_sdk_config_access](https://docs.toponad.com/#/zh-cn/android/android_doc/android_sdk_config_access) 

       主要是在topon上面配置一下自定义广告平台，添加广告适配器映射、自定义广告参数

输入映射广告单元信息：

 com.anythink.custom.adapter.AlxRewardVideoAdapter (激励视频)

现在暂时支持四种广告类型，具体Adapter类名:

激励视频 com.anythink.custom.adapter.AlxRewardVideoAdapter

横幅广告 com.anythink.custom.adapter.AlxBannerAdapter

插屏广告 com.anythink.custom.adapter.AlxInterstitialAdapter

原生广告 com.anythink.custom.adapter.AlxNativeAdapter



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-2.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-3.png)

 

 



配置完后再添加自定义广告参数



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-4.png)

**图中的 参数格式**

  

```json
{
  "host": "http://testaa.rixengine.com/rtb",
  "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
  "unitid": "171998",
  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
  "isdebug": "false",
  "sid": "60188"
}  
```

  

host对应SSP平台的 host

appid对应SSP平台的App ID

token对应SSP平台的Token

sid对应SSP平台的ID

united对应SSP平台的Placement ID

 

1. Rixengine SDK android集成说明：

1). 将本 SDK 压缩包内的 rixengine.*.*.*.aar 复制到 Application Module/libs 文件夹(没有的话须手动创建), 并将以下代码添加到您 Moudle app 的build.gradle中



```json
repositories {
	flatDir {
    dirs 'libs
	}
}
depedencies {
	implementation(name: 'rixengine.*.*.*', ext: 'aar') //要自行改成具体版本号
}
```



2).将com目录的适配器代码放到你们的Android工程代码src/main/java源代码目录中，注意是整个包路径拷贝过去，包全路径位：com.anythink.custom.adapter

具体如下图所示：

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-5.png)

 

4.Android混淆规则配置（若在Android调用开屏广告，打包需要添加）：

```
-dontwarn com.rixengine.**
 -keep class com.rixengine.** {*;}
 -keep class com.anythink.** {*;}
 -keep public class com.anythink.network.**
 -keepclassmembers class com.anythink.network.** {
   public *;
 }
 -dontwarn com.anythink.hb.**
 -keep class com.anythink.hb.**{ *;}****-dontwarn com.anythink.china.api.**
 -keep class com.anythink.china.api.**{ *;}****-keep class com.anythink.myoffer.ui.**{ *;}
 -keepclassmembers public class com.anythink.myoffer.ui.** {
   public *;
 }
```

 

## 6. `SDK`返回错误码说明

| 错误码 | 说明                                                         |
| ------ | ------------------------------------------------------------ |
| 1100   | 服务器异常，请联系广告对接人员排查问题                       |
| 1101   | 请求时网络错误，请检查网络是否打开或者可用                   |
| 1102   | 广告没填充错误，请检查sid和广告位，联系广告对接人员排查问题  |
| 1103   | 广告物料数据不合法，请检查sid和广告位，联系广告对接人员排查问题 |
| 1104   | 服务器异常，请联系广告对接人员排查问题（建议关闭VPN再测试）  |
| 1105   | 服务器异常，请联系广告对接人员排查问题                       |
| 1106   | 播放视频请求失败，请检查网络是否打开或者可用                 |
| 1107   | 播放视频错误，请检查网络是否打开或者可用                     |
| 1108   | 视频下载失败，无法播放，请检查网络是否打开或者可用           |
| 1109   | 视频格式不支持，无法播放，请联系广告对接人员排查问题         |
| 1110   | 视频文件不存在，无法播放，请联系广告对接人员排查问题         |
| 1111   | 参数错误，无法播放，请联系广告对接人员排查问题               |
| 1112   | 渲染失败，请联系广告对接人员排查问题                         |
| 1113   | SDK 没有初始化                                               |
| 3001   | 广告类型与广告位不匹配  请联系广告对接人员排查问题           |
| 3002   | 服务器异常 广告位未生效 请联系广告对接人员排查问题           |
| 3003   | 服务器异常App未生效 请联系广告对接人员排查问题               |

## 7.Java  SDK 高版本升级（非必须，如果遇到java版本问题可以按下流程修改配置）

当开发者的gradle版本高于7.1 ，jdk版本就需要升级到java11 ，SDK同时也需要升级到Java11的版本，开发者可以搭配以下两套gradle 版本配置

配置一
![img](https://static.rixengine.com/a/platform/help/sdk/7-1.jpeg)

![img](https://static.rixengine.com/a/platform/help/sdk/7-2.jpeg)
配置二
![img](https://static.rixengine.com/a/platform/help/sdk/7-3.jpeg)

![img](https://static.rixengine.com/a/platform/help/sdk/7-4.png)
