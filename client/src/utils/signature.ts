import Hex from 'crypto-js/enc-hex';
import hmacSHA256 from 'crypto-js/hmac-sha256';

const secretKey = process.env.SECRET_KEY as string;

/**
 * 生成签名
 * @param params 参数
 * @returns 签名
 */
export function generateSignature(params: Record<string, any>) {
  const sortedParams = Object.entries(params)
    .filter(([_, value]) => value !== undefined)
    .sort((a, b) => a[0].localeCompare(b[0]))
    .map(([key, value]) => `${key}=${JSON.stringify(value)}`);

  const stringToSign = sortedParams.join('&');
  // 使用 HMAC-SHA256 生成签名
  return hmacSHA256(stringToSign, secretKey).toString(Hex).slice(0, 32);
}

/**
 * 生成额外请求头
 * @param params 参数
 * @returns 请求头
 */
export function generateExtraHeaderOptions(params: Record<string, any>) {
  const timestamp = Date.now();
  const signature = generateSignature({ ...params, timestamp });
  return {
    'x-timestamp': timestamp,
    // 取一个不是很被注意的名称，否则可能被用户篡改
    'x-platform': signature
  };
}
