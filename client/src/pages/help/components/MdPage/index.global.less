.markdown-body {
  background-color: #fff;
  --color-fg-default: var(--text-color);
  --color-canvas-default: #fff;
  --color-border-default: #e5e5e5;
  --color-canvas-subtle: #fff;
  // transform: translate(0, 0);
  & > p:first-child {
    padding-top: 20px;
  }
  h2,
  h3,
  h4,
  h5,
  h6 {
    border: none;
    border-color: #e5e5e5;
  }
  hr {
    height: 1px;
  }
  h1 {
    border-color: #d2d0d0;
  }
  h1:first-child {
    border-color: #d2d0d0;
    // position: sticky;
    // top: 0;
    // z-index: 2;
    background-color: #fff;
    padding-top: 20px;
  }

  pre {
    background-color: var(--background-color);
  }

  .cus-list {
    list-style-type: auto;
  }
}
