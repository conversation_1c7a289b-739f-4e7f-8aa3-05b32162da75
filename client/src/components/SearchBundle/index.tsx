/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-12 11:43:00
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:26:45
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import EllipsisPopover from './ellipsis-popover';
import { CloseOutlined } from '@ant-design/icons';

type SearchBundleProps = {
  onChange?: (val: string[]) => void;
  value?: string[];
  limit?: number;
  splitBy?: RegExp; // 多行切割
  placeholder?: string;
}

const SearchBundle: React.FC<SearchBundleProps> = ({
  onChange,
  value,
  limit,
  splitBy = /,|，|\s+/g,
  placeholder = 'Input to filter/add,separated by comma/space'
}) => {
  const [contentList, setContentList] = useState<string[]>([]);

  useEffect(() => {
    if (value) {
      setContentList(value);
    } else {
      setContentList([]);
    }
  }, [value]);

  const handleClearBundle = () => {
    setContentList([]);
    onChange && onChange([]);
  };

  const onValueChange = (val: string[]) => {
    setContentList(val);
    onChange && onChange(val);
  };

  return <div
    className={styles['search-bundle-container']}
  >
    {
      <div
        className={styles['content-container']}
        style={contentList.length > 0 ? { width: 'calc(100% - 20px)' } : { width: '100%' }}
      >
        <EllipsisPopover
          dataSource={contentList}
          onValueChange={onValueChange}
          limit={limit}
          splitBy={splitBy}
          placeholder={placeholder}
        />
      </div>
    }
    {
      contentList.length > 0 && (
        <div className={styles['close-container']}>
          <CloseOutlined onClick={() => handleClearBundle()} />
        </div>
      )
    }
  </div>;
};

export default SearchBundle;
