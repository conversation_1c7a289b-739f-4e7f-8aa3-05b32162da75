import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import { StatusMap } from '@/constants/common';
import { ProfitBreadOptions, ProfitTab, ProfitTabOptions, ProfitTabType } from '@/constants/strategy/profit';
import { useCurrentTabState } from '@/hooks/useCurrentTabState';
import { useOptionsData } from '@/hooks/useOptionsData';
import { FormInstance } from 'antd';
import { useCallback, useMemo, useRef } from 'react';
import AddProfitDrawer from './AddProfitDrawer';
import { useProfitData } from './helper/useProfitData';
import { useProfitDrawerState } from './helper/useProfitDrawerState';
import { useProfitJump } from './helper/useProfitJump';
import { useSearchOptions } from './helper/useSearchOptions';
import { useTableColumns } from './helper/useTableColumns';
import { useTableOperations } from './helper/useTableOperations';

const ProfitPage = () => {
  // 使用自定义hook管理Tab状态
  const [currentTab, setCurrentTab] = useCurrentTabState<ProfitTabType>(ProfitTab.advertiser, ProfitTabOptions);

  // 使用自定义hook管理选项数据
  const { demandOptions, supplyTestingOptions: supplyOptions } = useOptionsData(['demand', 'supply_testing']);

  // 使用自定义hook管理利润数据
  const { processedData, loading, reloadData } = useProfitData({ currentTab });

  // 表单引用
  const profitFormRef = useRef<FormInstance<any>>(null);
  const searchFormRef = useRef<FormInstance<any> | undefined>(undefined);

  // 使用自定义hook管理抽屉状态
  const { drawerVisible, isDrawerEdit, openCreateDrawer, openEditDrawer, closeDrawer } = useProfitDrawerState({
    formRef: profitFormRef,
    currentTab
  });

  // 使用自定义hook管理跳转逻辑
  const { jumpToBundle, checkAndTriggerSearch } = useProfitJump({
    searchFormRef: searchFormRef as React.RefObject<FormInstance<any>>,
    onTabChange: setCurrentTab as (tab: string) => void
  });

  // 处理标签页切换
  const handleTabChange = useCallback(
    (tab: ProfitTabType) => {
      setCurrentTab(tab);
    },
    [setCurrentTab]
  );

  // 获取当前tab的搜索选项
  const currentSearchOptions = useSearchOptions(demandOptions, supplyOptions, currentTab);

  // 使用自定义hook管理表格操作
  const { getOperationColumn, getExpandButtons } = useTableOperations({
    currentTab,
    onEdit: openEditDrawer,
    onCreate: openCreateDrawer
  });

  // 获取操作列
  const operationColumn = useMemo(() => getOperationColumn(), [getOperationColumn]);

  // 使用自定义hook管理表格列
  const { getColumns } = useTableColumns({
    currentTab,
    operationColumn,
    onJumpToBundle: jumpToBundle
  });

  // 获取表格列
  const currentColumns = getColumns;

  // 获取表格扩展按钮
  const tableExpandButtons = useMemo(() => getExpandButtons(), [getExpandButtons]);

  // 处理数据源，支持跳转后自动搜索
  const dataSource = useMemo(() => {
    // 检查是否需要触发自动搜索
    checkAndTriggerSearch();
    return processedData;
  }, [processedData, checkAndTriggerSearch]);

  return (
    <PageContainer flexDirection="column" options={ProfitBreadOptions}>
      <FrontTable<StrategyAPI.ProfitListItem>
        isFold
        searchFormRef={searchFormRef}
        searchOptions={currentSearchOptions}
        loading={loading}
        columns={currentColumns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reloadData}
        labelWidth={80}
        tabOptions={ProfitTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange as any}
        btnOptions={tableExpandButtons}
        expandable={{
          expandRowByClick: true
        }}
        scroll={{ y: 'calc(100vh - 220px)' }}
        initialValues={{ status: [StatusMap.Active] }}
      />
      <AddProfitDrawer
        formRef={profitFormRef}
        visible={drawerVisible}
        isEdit={isDrawerEdit}
        currentTab={currentTab}
        handleClose={closeDrawer}
        demandOptions={demandOptions}
        supplyOptions={supplyOptions}
        reloadProfit={reloadData}
      />
    </PageContainer>
  );
};

export default ProfitPage;
