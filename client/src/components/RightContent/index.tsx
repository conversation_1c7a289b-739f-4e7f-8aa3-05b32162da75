/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-09-12 18:05:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-07 19:41:21
 * @Description:
 */
import HeaderDropDown from '@/components/HeaderDropdown';
import TimeZoneDropdown from '@/components/TimeZone';
import { DailyCSVReportingTenants, UserType } from '@/constants';
import { DocsItems } from '@/constants/base';
import { customRole, customTenant } from '@/constants/hardCode/full-report';
import { Link } from '@umijs/max';
import { type MenuProps } from 'antd';
import React, { useEffect, useState } from 'react';
import { useAccess, useModel } from 'umi';
import NotificationIcon from '../Notification';
import SwitchAccountDropdown from '../SwitchAccount/dropdown';
import Avatar from './AvatarDropdown';
import styles from './index.less';

export type SiderTheme = 'light' | 'dark';

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const access = useAccess();
  const [visible, setVisible] = useState(true);
  const [menuItems, setMenuItems] = useState<MenuProps['items']>(DocsItems);

  useEffect(() => {
    const { tnt_id, type, role_id = 0 } = initialState?.currentUser || {};
    const isTenantCustomRole = customTenant.includes(tnt_id!) && customRole[tnt_id!]?.includes(role_id);
    const isIionDataAnalyst = customTenant.includes(tnt_id!) && isTenantCustomRole;

    if (type === UserType.Demand || type === UserType.Supply || isIionDataAnalyst || type === UserType.Partner) {
      setVisible(false);
    }

    if (DailyCSVReportingTenants.includes(tnt_id!)) {
      const newItems = DocsItems?.map(item => {
        if (item?.key === '3') {
          return {
            ...item,
            children: [
              // @ts-ignore
              ...(item?.children || []),
              {
                key: '3-4',
                label: (
                  <Link to="/help/daily-csv-reporting-api" target="_blank">
                    Daily CSV Reporting API
                  </Link>
                )
              }
            ]
          };
        }
        return item;
      });
      setMenuItems(newItems);
    }
  }, [initialState]);

  if (!initialState || !initialState.currentUser) {
    return null;
  }

  return (
    <div className={styles.right}>
      {visible && access.AlertPermission && <NotificationIcon />}
      {visible && <HeaderDropDown items={menuItems} title={`Docs`} />}
      <TimeZoneDropdown></TimeZoneDropdown>
      {access.SwitchAccountPermission && <SwitchAccountDropdown />}
      {access.SwitchAccountPermission && <div className={styles.divider} />}
      <Avatar />
    </div>
  );
};
export default GlobalHeaderRight;
