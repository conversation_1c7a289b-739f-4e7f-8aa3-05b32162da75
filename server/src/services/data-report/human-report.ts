/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-01 15:01:11
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-12 15:02:29
 * @Description:
 */
import { humanModel } from '@/models';
import { joinQueries, joinStr } from '@/utils/params';
import {
  HumanDefaultMetrics,
  HumanDimensions,
  HumanMetricOrderKey,
  getCalculationSQL
} from '@/constants/report/human-report';
import { DashboardAPI } from '@/types/dashboard';
import { getOrderBy } from '@/utils/report';

class HumanReport {
  getReportParams(formData: DashboardAPI.QueryHumanReportParams) {
    const {
      start_date,
      end_date,
      start,
      end,
      order,
      dimension = [],
      metrics = [],
      tnt_id
    } = formData;
    let { order_key = [] } = formData;
    const normal_dimension = HumanDimensions.filter((v) => dimension.includes(v));
    if (Array.isArray(order_key)) {
      order_key = order_key.filter(
        (v) => normal_dimension.includes(v) || HumanMetricOrderKey.includes(v)
      );
    }
    const condition = joinQueries(
      {
        numberCols: ['seller_id', 'buyer_id'],
        arrayQueries: ['seller_id', 'buyer_id', 'bundle', 'domain'],
        extra: `day >= '${start_date}' and day <= '${end_date}' and tnt=${tnt_id}`
      },
      formData
    );
    const limit = `limit ${end - start} offset ${start}`;
    const order_str = getOrderBy(order_key, order, {
      isEmptyStr: true,
      isDefault: false,
    });
    const dimension_arr = normal_dimension.map((v) => {
      if (v === 'month') {
        return `FORMAT_DATETIME('%Y-%m', PARSE_DATE('%Y%m%d', day)) AS month`;
      } else if (v === 'day') {
        return `format_date('%F', PARSE_DATE('%Y%m%d',day)) as day`;
      }
      return v;
    });
    const metrics_arr =
      Array.isArray(metrics) && metrics.length
        ? metrics.map((v) => getCalculationSQL(v))
        : [...HumanDefaultMetrics];
    return {
      limit,
      order: order_str,
      condition,
      normal_dimension_str: dimension_arr.join(','),
      metrics_str: metrics_arr.join(','),
      dimension: normal_dimension.join(',')
    };
  }

  // 获取指标公式
  getMetricsSql = (metrics: string[]) => {
    if (!metrics) return '';
    let metricsArr: string[] = [];
    metricsArr = Array.from(new Set(metricsArr));
    const metricsSql = metricsArr.map((item: string) => getCalculationSQL(item));
    return joinStr(metricsSql, ',');
  };

  async getHumanReport(option: any, api_url: string) {
    const result = await humanModel.getHumanReport(option, api_url);
    let totalNum = 0;
    const data = (result || []).map(({ total, ...rest }, index) => {
      if (index === 0) {
        totalNum = total;
      }
      return { ...rest, id: index };
    });

    return {
      data,
      total: totalNum
    };
  }

  async downloadHumanReport(options: any, api_url: string) {
    return await humanModel.downloadHumanReport(options, api_url);
  }
}

export const humanService = new HumanReport();
