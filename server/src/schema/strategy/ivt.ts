/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-09 19:45:18
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-09 20:12:43
 * @Description:
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON>@algorix.co
 * @Date: 2023-08-08 17:24:01
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-28 16:44:20
 * @Description:
 */

import Joi from 'joi';

export const AddIvtConfigSchema = Joi.object({
  seller_id: Joi.array().items(Joi.number()),
  buyer_id: Joi.array().items(Joi.number()),
  bundle: Joi.string().allow(''),
  type: Joi.number().integer().valid(1, 2).required(),
  ratio: Joi.number().integer().required().min(1)
    .max(100)
});

export const UpdateIvtConfigSchema = Joi.object({
  id: Joi.number().integer().required(),
  seller_id: Joi.array().items(Joi.number()),
  buyer_id: Joi.array().items(Joi.number()),
  status: Joi.number().integer().valid(1, 2).required(),
  bundle: Joi.string().allow(''),
  type: Joi.number().integer().valid(1, 2).required(),
  ratio: Joi.number().integer().required().min(1)
    .max(100)
    .required()
});
