/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:52:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 11:52:21
 * @Description:
 */
import { request } from '@umijs/max';

export async function getBuyerIntegrationType(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>('/api/common/getBuyerIntegrationType', {
    data: body || {},
    ...(options || {})
  });
}

export async function getSellerIntegrationType(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>('/api/common/getSellerIntegrationType', {
    data: body || {},
    ...(options || {})
  });
}

export async function getDashboardUser(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<SupplyAPI.SupplyUser>>('/api/user/getDashboardUser', {
    data: body || {},
    ...(options || {})
  });
}

export async function getWelcomeDashboardList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DashboardAPI.DashboardListItem>>('/api/common/getWelcomeDashboardList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getNotificationList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.NotificationListItem>>('/api/common/getNotificationList', {
    data: body || {},
    ...(options || {})
  });
}

export async function updateNotificationStatus(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.UpdateNotificationParams>>('/api/common/updateNotificationStatus', {
    data: body || {},
    ...(options || {})
  });
}

export async function getBrandInfo(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.BrandInfo>>('/api/common/brandInfo', {
    data: body || {},
    ...(options || {})
  });
}

// 组装菜单使用
export async function getMenuList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/menu/getMenuList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getDict(params?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.DictItem>>('/api/common/getDict', {
    method: 'GET',
    params,
    ...(options || {})
  });
}

export async function authLogin(body?: { token: string }, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/user/authLogin', {
    data: body || {},
    ...(options || {})
  });
}
