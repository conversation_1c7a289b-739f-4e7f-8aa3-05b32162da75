/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-06 23:33:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-11 14:38:24
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import { Typography } from 'antd';
import RixEngineFont from '@/components/RixEngineFont';
const { Paragraph } = Typography;

import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { RoleType, RoleTypeMap } from '@/constants/permission/role';

const DocInfo: Record<
  string,
  {
    url: string;
    desc: string;
  }
> = {
  'Supply Partner': {
    url: '/help/supply-partner-api',
    desc: 'Supply Partner API'
  },
  'Demand Partner': {
    url: '/help/demand-partner-api',
    desc: 'Demand Partner API'
  }
};

export const PartnerColumnsColumns: ColumnProps<PartnerAPI.PartnerUser>[] = [
  {
    title: 'User Name',
    dataIndex: 'account_name',
    width: 200,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Type',
    dataIndex: 'role_type',
    width: 120,
    ellipsis: { showTitle: false },
    render: _ => {
      const str = RoleTypeMap[_];
      return (
        <HoverToolTip title={str}>
          <span>{str}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100
  },
  {
    title: 'API Status',
    dataIndex: 'api_status',
    width: 100
  },
  {
    title: 'Operation',
    dataIndex: 'operate',
    width: 120
  },
  {
    title: 'Partner ID',
    dataIndex: 'partner_id',
    width: 80,
    render: id => {
      return (
        <span className="copy-container">
          <HoverToolTip title={id}>
            <span className="info">{id}</span>
          </HoverToolTip>
          <Paragraph
            copyable={{
              tooltips: false,
              text: id,
              icon: <RixEngineFont type="rix-copy" />
            }}
          ></Paragraph>
        </span>
      );
    }
  },
  {
    title: 'Token',
    dataIndex: 'token',
    width: 220,
    ellipsis: { showTitle: false },
    render: txt => {
      return (
        <span className="copy-container">
          <HoverToolTip title={txt}>
            <span className="info">{txt}</span>
          </HoverToolTip>
          <Paragraph
            copyable={{
              tooltips: false,
              text: txt,
              icon: <RixEngineFont type="rix-copy" />
            }}
          ></Paragraph>
        </span>
      );
    }
  },
  {
    title: 'Doc',
    dataIndex: 'doc',
    width: 100,
    ellipsis: { showTitle: false },
    render: (txt, row) => {
      const doc = DocInfo[RoleTypeMap[row.role_type]];
      return (
        <HoverToolTip title={doc.desc}>
          <a target="_blank" href={doc.url} rel="noreferrer">
            {doc.desc}
          </a>
        </HoverToolTip>
      );
    }
  }
];

export const AccountBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Partner',
    url: '/partner',
    icon: 'rix-partner'
  },
  {
    name: 'Account'
  }
];

export const PartnerTypeOptions = [
  {
    label: 'Demand Partner',
    value: RoleType['Demand Partner']
  },
  {
    label: 'Supply Partner',
    value: RoleType['Supply Partner']
  }
];
