.bread-item {
  height: 22px;
  display: inline-block;
  span {
    color: #5e6466;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 20px !important;
      height: 20px !important;
    }
    &.last {
      font-weight: 700;
      color: var(--text-color);
    }
    &.active {
      color: #5e6466;
      cursor: pointer;
      svg {
        color: #5e6466;
      }
      &:hover {
        color: var(--primary-color);
        svg {
          color: var(--primary-color);
        }
      }
    }
  }
}

.bread-container {
  ol {
    li {
      span {
        color: var(--primary-color);
        svg {
          color: var(--primary-color);
        }
      }
      &:last-child {
        span {
          color: var(--text-color);
        }
      }
    }
  }
}
