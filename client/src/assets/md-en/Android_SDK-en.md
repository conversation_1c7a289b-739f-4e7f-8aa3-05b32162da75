# 「RixEngine」Android SDK Integration Document

## 1. **Project setting**

### 1.1 Import `aar` and `jar` dependencies file of `SDK`

Copy `rixengine**.*.*.aar ` in this `SDK` zip file to `Application Module/libs `file (need to create mannuallt if no file found), and add following code into `build.gradle` under `Module app`：

Name `Application` imported from `//Unity` as `unityLibrary`

```Java
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation(name: 'rixengine.*.*.*', ext: 'aar') //need to change to specific version manually
 
}
```

### 1.2 Code examples

You can check corresponding code examples on [Github](https://github.com/RixEngine-code/Rixengine-ads-demo-android/).

## 2. **Global configuration**

### 2.1 Add permission

`RixEngine SDK` suggests adding the following permissions and declaring to developers in your privacy agreement that `RixEngine SDK` will obtain the following permissions and apply them to ad serving.

```Java
<!--compulsory permission-->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
<!—optional permission-->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_PHONE_STATE" />
<!--optional permission：can choose to add this permission according to the actual situation for Android 11 and above-->
<uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
<!--Player Application needs to prevent screen dimming-->
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### 2.2 Runtime environment setting

This `SDK ` can run on ` Android4.1 (API Level 16) ` and above versions。

`<uses-sdk android:minSdkVersion="16" android:targetSdkVersion="29" />`

Please ensure that you have applied for all the permissions required by `SDK` before calling any interface of this `SDK` if the developer declares `targetSdkVersion` to be `API 23` or above, otherwise, some features of `SDK` may be restricted.

### 2.3 Proguard setting 

Add the following texts in `.pro` file under `App` file (called `proguard-rules.pro` in `Android` and `proguard-unity.txt` imported from `Unity`)：

```Java
-keep class com.rixengine.api.** {*;}
-keep class com.rixengine.api.nativead.** {*;}

-keep class com.admob.custom.adapter.** {*;}
-keep class com.anythink.custom.adapter.** {*;}
-keep class com.tradplus.custom.adapter.** {*;}
-keep class com.applovin.mediation.adapters.** {*;}
-keep class com.ironsource.adapters.** {*;}
```

## 3. **Integration**

The first step is to obtain account registration information and configure your apps and ad placements, such as `Host`,`Token`,`SID`,`APP_ID` and related ad placements. 4 key parameters `TOKEN`,`HOST`, `SID`, `APP_ID` are generated by `RixEngine SaaS` console，please contact your contact person in RixEngine.



To initiate `SDK`, the developer needs to call the following codes in `Application#onCreate()` method to initiate `RixEngine SDK`

```Java
AlxAdSDK.init(context, HOST, TOKEN, SID, APP_ID,new AlxSdkInitCallback() {
    @Override
public void onInit(boolean b, String s) {
// Ads cannot be requested until initialization is successful, otherwise, the fill rate may be affected
    }
});
```



### 3.1 Banner

Banner ads would occupy a location in the app layout, either at the top or bottom of the screen. This type of ad will stay on the screen when the user interacts with the application, and can be automatically refreshed after a period of time. It supports direct loading, preloading and manual display.

 **Integration Code**

Add `AlxBannerView` to layout

To display a banner ad, first place `AlxBannerAd` into the layout of `Activity` or `Fragment` that you wish used to display ads. The easiest way to place is to add a corresponding `XML` layout file. Two loading methods are supported, one is to load and display, and the other is to manually call the display after preloading. It is recommended to use the first method, which will have fewer codes.

Following example shows the method `AlxBannerView` of a certain `Activity`（directly present after  loading completed）：

```Java
<com.rixengine.api.AlxBannerView
  android:id****="@+id/alx_ad_banner"**
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:layout_centerHorizontal="true"
  android:layout_alignParentBottom="true">
</com.rixengine.api.AlxBannerView>
```

Or create a new `Framelayout` container to display (preloading and display manually)：

```Java
<FrameLayout
  android:id="@+id/ad_container"
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:layout_gravity="center_horizontal"
  android:layout_marginTop="20dp"/>
```

Following example shows how to load ads using the method `onCreate()` of `Activity`：

```Java
    public class BannerActivity extends AppCompatActivity implements View.OnClickListener {
    private final String TAG = "AlxBannerDemoActivity";

    private Button mBnLoad;
    private Button mBnShow;
    private TextView mTvTip;
    private Button mBnLoadAndShow;

    private FrameLayout mAdContainer;
    private AlxBannerView mAlxBannerView;
    private AlxBannerView mAlxBannerView2;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_banner);
        setTitle("BannerAd");

        mTvTip = (TextView) findViewById(R.id.tv_tip);
        mBnLoad = (Button) findViewById(R.id.bn_load);
        mBnShow = (Button) findViewById(R.id.bn_show);
        mBnLoadAndShow = (Button) findViewById(R.id.bn_load_show);
        mAdContainer = (FrameLayout) findViewById(R.id.ad_container);
        mAlxBannerView = (AlxBannerView) findViewById(R.id.do_ad_banner);

        mBnLoad.setOnClickListener(this);
        mBnShow.setOnClickListener(this);
        mBnLoadAndShow.setOnClickListener(this);
        mBnShow.setEnabled(false);
    }

    @Override
    protected void onDestroy() {
        if (mAlxBannerView2 != null) {
            mAlxBannerView2.destroy();
        }
        if (mAlxBannerView != null) {
            mAlxBannerView.destroy();
        }
        super.onDestroy();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.bn_load:
                bnPreLoad();
                break;
            case R.id.bn_show:
                bnShow();
                break;
            case R.id.bn_load_show:
                bnLoadAndShow();
                break;
        }
    }

    private void bnPreLoad() {
        mBnLoad.setEnabled(false);
        mAlxBannerView2 = new AlxBannerView(this);
        mAlxBannerView2.setBannerCanClose(false);
        mAlxBannerView2.setBannerRefresh(0);//cancel auto refresh
        mAlxBannerView2.setVisibility(View.VISIBLE);
        mAlxBannerView2.loadAd(unitid, new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "onAdLoaded");
                mTvTip.setText("Alx Banner AD load success | ecpm：" + mAlxBannerView2.getPrice());
                mBnShow.setEnabled(true);
                mBnLoad.setEnabled(true);

                mAlxBannerView2.reportBiddingUrl();
                mAlxBannerView2.reportChargingUrl();
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.d(TAG, "onAdError: errorMsg=" + errorMsg + "  errorCode=" + errorCode);
                mBnShow.setEnabled(false);
                mBnLoad.setEnabled(true);
                mTvTip.setText("Alx Banner AD load failed");
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "onAdClicked");
            }

            @Override
            public void onAdShow() {
                Log.d(TAG, "onAdShow");
            }

            @Override
            public void onAdClose() {
                Log.d(TAG, "onAdClose");
            }
        });
    }

    private void bnShow() {
        if (mAlxBannerView2 != null && mAlxBannerView2.isReady()) {
            mAdContainer.removeAllViews();
            mAdContainer.addView(mAlxBannerView2);
            mTvTip.setText("");
        }
    }

    private void bnLoadAndShow() {
        mAlxBannerView.setBannerCanClose(true);
        mAlxBannerView.loadAd(AdConfig.ALX_BANNER_AD_PID, new AlxBannerViewAdListener() {
            @Override
            public void onAdLoaded() {
                Log.d(TAG, "onAdLoaded:  | ecpm：" + mAlxBannerView.getPrice());
            }

            @Override
            public void onAdError(int errorCode, String errorMsg) {
                Log.d(TAG, "onAdError: errorMsg=" + errorMsg + "  errorCode=" + errorCode);
                Toast.makeText(getBaseContext(), "Alx Banner AD load failed", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "onAdClicked");
            }

            @Override
            public void onAdShow() {
                Log.d(TAG, "onAdShow");
            }

            @Override
            public void onAdClose() {
                Log.d(TAG, "onAdClose");
            }
        });
    }

}    
```



### 3.2 Rewarded video ads

`onReward()` will be called back when the incentive for the rewarded video is issued. Developers can issue incentives to users in the `onReward` callback.;

Integration suggestion：

1. Please call the loading method in advance to request the ads (for example, starting loading ad when the app starts) so that the ad can be displayed quickly when it needs to be triggered
2. Call loading methods to process ad requests in the callback of `onRewardedVideoAdClosed` to facilitate the display of the next advertisement
3. Don't execute the ad loading method in the `onRewardedVideoAdFailed` callback, doing so will result in numerous pointless requests and the possibility of application lagging

Integrating process suggestion:

* Start the application or game to load ads through `AlxRewardVideoAd.load`
* Use `AlxRewardVideoAd.isReady` to determine whether the rewarded video can be displayed where it needs to be displayed：

**false**: Re-execute `AlxRewardVideoAd.load` to load the ad

**true**: Execute `AlxRewardVideoAd.show` to display ads, re-execute `AlxRewardVideoAd.load` in `onRewardedVideoAdClosed` callback to preload the subsequent ads (you can call `load` directly in the callback of `close`, bypassing the `isReady` judgment, which contributes to a higher display volume of high-priority advertising sources)

**Integration code**

```Java
//Create a RewardVideo object
AlxRewardVideoAD alxRewardVideoAD = new AlxRewardVideoAD();
//Then load the incentive video
alxRewardVideoAD.load(this, mPid , new AlxRewardVideoADListener() { //mPid ad placement
            @Override
            public void onRewardedVideoAdLoaded(AlxRewardVideoAD var1) {
                Log.i(TAG, "onRewardedVideoAdLoaded success");
showVideo(); //Play rewarded video ad
            }

            @Override
            public void onRewardedVideoAdFailed(AlxRewardVideoAD var1, int errCode, String errMsg) {
                Log.i(TAG, "onRewardedVideoAdLoadFail" + errCode + " " + errMsg);
            }

            @Override
            public void onRewardedVideoAdPlayStart(AlxRewardVideoAD var1) {
                Log.i(TAG, "onRewardedVideoAdPlayStart");
            }

            @Override
            public void onRewardedVideoAdPlayEnd(AlxRewardVideoAD var1) {
               Log.i(TAG, "onRewardedVideoAdPlayEnd");
            }

            @Override
            public void onRewardedVideoAdPlayFailed(AlxRewardVideoAD var2, int errCode, String errMsg) {
               Log.i(TAG, "onRewardedVideoAdPlayFailed:"+errCode+";"+errMsg);
            }

            @Override
            public void onRewardedVideoAdClosed(AlxRewardVideoAD var1) {
                Log.i(TAG, "onRewardedVideoAdClosed");
            }

            @Override
            public void onRewardedVideoAdPlayClicked(AlxRewardVideoAD var1) {
                Log.i(TAG, "onRewardedVideoAdPlayClicked");
            }

            @Override
            public void onReward(AlxRewardVideoAD var1) {
                Log.i(TAG, "onRewardedVideoCompleted--Reward");
            }
        });

public void showRewardVideo(){
if (alxRewardVideoAD.isLoaded()) {
          alxRewardVideoAD.showVideo(this);
        }
}
```



### 3.3 Interstitial Ads

Interstitials are full-screen ads that cover the entire interface of the app they host.

Integration suggestion:

1. Please call the loading method in advance to request the ads so that ad can be displayed quickly when they need to be triggered
2. Call loading methods to process ad requests in the callback of `onRewardedVideoAdClosed` to facilitate the display of the next advertisement
3. Don't execute the ad loading method in the `onRewardedVideoAdFailed` callback, doing so will result in numerous pointless requests and the possibility of application lagging

Integrating process suggestion：

* Start the application or game to load ads through `AlxRewardVideoAD.load`
* Use `AlxRewardVideoAD.isReady` to determine whether the rewarded video can be displayed where it needs to be displayed：

**false**: Re-execute `AlxRewardVideoAD.load` to load the ad

**true**: Execute `AlxRewardVideoAD.show` to display ads, re-execute `AlxRewardVideoAD.load` in `onRewardedVideoAdClosed` callback to preload the subsequent ads

**Integration code**

```Java
//Create a AlxInterstitialAD Object
AlxInterstitialAD  alxInterstitialAD = new AlxInterstitialAD();
alxInterstitialAD.load(mContext, mPid, new AlxInterstitialADListener() { //mPid ad placement
            @Override
            public void onInterstitialAdLoaded() {
                Log.d(TAG, "onInterstitialAdLoaded success");
                   If(alxInterstitialAD.isReady();){
                    alxInterstitialAD.show(MainActivity.this);//Play interstitial ads
        
            }

            @Override
            public void onInterstitialAdLoadFail(int i, String s) {
                Log.d(TAG, "onInterstitialAdLoadFail " + "errorCode: " + i + " errorMSg: " + s);
            }

            @Override
            public void onInterstitialAdClicked() {
                Log.d(TAG, "onInterstitialAdClicked");
            }

            @Override
            public void onInterstitialAdShow() {
                Log.d(TAG, "onInterstitialAdShow");
            }

            @Override
            public void onInterstitialAdClose() {
                Log.d(TAG, "onInterstitialAdClose");
            }

            @Override
            public void onInterstitialAdVideoStart() {

            }

            @Override
            public void onInterstitialAdVideoEnd() {

            }

            @Override
            public void onInterstitialAdVideoError(int i, String s) {
                Log.d(TAG, "onInterstitialAdVideoError " + "errorCode: " + i + " errorMSg: " + s);
            }
        });
});
```



### 3.4 Native ads

`SDK` provides developers with in-feed ads with customizable layouts, including basic style types such as large pictures, small pictures, group pictures and videos (the specific layout can be customized)

The steps to build a native ad are as follows:

1. Create `AlxNativeAdLoader` and load ads

​	Note: Please make all calls to mobile ads `SDK` in the main thread:

Following code demonstrates how to build an `AlxNativeAdLoader` that can load unified native ads:

  ```Java
  AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
  loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
              @Override
              public void onAdFailed(int errorCode, String errorMsg) {
              //Call when ad loading failed
              }

              @Override
              public void onAdLoaded(List<AlxNativeAd> ads) {
                  //Call when the ad successfully loaded
              }
          }); 
  ```
2. Display native ads

a. Define native ads layout

Need to customize a layout to display creatives in `AlxNativeAd`

>  **NOTE** ：**Must** serve `AlxNativeAdView` as the root layout of native ads, otherwise, it will affect ad revenue


For native ads that use `RelativeLayout` to display the layout of creatives, an example of the layout Structure is shown as follows:

```Java
<com.rixengine.api.nativead.AlxNativeAdView xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    ... >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        ... >
        <!-- Multimedia View -->
        <com.rixengine.api.nativead.AlxMediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            ... />
        <RelativeLayout
            ... >
        <TextView
            android:id="@+id/ad_title"
            android:layout_width="match_parent"
            android:layout_height="34dp"
            ... />
        <!-- Other assets -->
        ...
        </RelativeLayout>
        <!-- Other assets -->
        ...
    </RelativeLayout>
</com.rixengine.api.nativead.AlxNativeAdView>
```

b. Register and populate the asset view

After obtaining the AlxNativeAdView object, register and populate the asset view. Refer to the sample code below:

```Java
private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
        ImageView logo = (ImageView) nativeView.findViewById(R.id.ad_logo);
        ImageView icon = (ImageView) nativeView.findViewById(R.id.ad_icon);
        TextView title = (TextView) nativeView.findViewById(R.id.ad_title);
        TextView description = (TextView) nativeView.findViewById(R.id.ad_desc);
        TextView source = (TextView) nativeView.findViewById(R.id.ad_source);
        Button callToAction = (Button) nativeView.findViewById(R.id.ad_call_to_action);
        ImageView close = (ImageView) nativeView.findViewById(R.id.ad_close);
        AlxMediaView mediaView = (AlxMediaView) nativeView.findViewById(R.id.ad_media);

        //Register and populate the title view
        nativeView.setTitleView(title);
        title.setText(nativeAd.getTitle());

        //Register and populate the multimedia view
        nativeView.setMediaView(mediaView);
        mediaView.setMediaContent(nativeAd.getMediaContent());

        //Register and populate other asset views
        nativeView.setDescriptionView(description);
        nativeView.setIconView(icon);
        nativeView.setCallToActionView(callToAction);
        nativeView.setCloseView(close);
        nativeView.setAdSourceView(source);
        description.setText(nativeAd.getDescription());
        logo.setImageBitmap(nativeAd.getAdLogo());

        if (TextUtils.isEmpty(nativeAd.getAdSource())) {
            source.setVisibility(View.GONE);
        } else {
            source.setVisibility(View.VISIBLE);
            source.setText(nativeAd.getAdSource());
        }
        if (TextUtils.isEmpty(nativeAd.getCallToAction())) {
            callToAction.setVisibility(View.GONE);
        } else {
            callToAction.setVisibility(View.VISIBLE);
            callToAction.setText(nativeAd.getCallToAction());
        }

        //Video ads listener
        if (nativeAd.getMediaContent() != null && nativeAd.getMediaContent().hasVideo()) {
            nativeAd.getMediaContent().setVideoLifecycleListener(new AlxMediaContent.VideoLifecycleListener() {
                @Override
                public void onVideoStart() {
                }

                @Override
                public void onVideoEnd() {
                }

                @Override
                public void onVideoPlay() {
                }

                @Override
                public void onVideoPause() {
                }

                @Override
                public void onVideoMute(boolean isMute) {
                }
            });
        }

        //Register the native ad object
        nativeView.setNativeAd(nativeAd);
    }
```

Register and populate the creative assets in sequence as shown in the sample code above. 

**AlxMediaView** is used to display multimedia assets. If the loaded native ad includes a video asset, the video will be rendered in **AlxMediaView**. Otherwise, an image will be rendered in AlxMediaView.

c. Register native ad objects with `NativeView`

The sample code shows as follows:

```Java
nativeView.setNativeAd(nativeAd);
```





d. Display `AlxNativeAdView`

Add `AlxNativeAdView` to the interface to display native ads. Sample codes shows below:

```Java
private void loadAd() {
   AlxNativeAdLoader loader = new AlxNativeAdLoader.Builder(this, "172943").build();
   loader.loadAd(new AlxAdParam.Builder().build(), new AlxNativeAdLoadedListener() {
         @Override
         public void onAdLoaded(List<AlxNativeAd> ads) {
         ……
             AlxNativeAd nativeAd = ads.get(0);

             //Obtain AlxNativeAdView
             AlxNativeAdView nativeAdView =(AlxNativeAdView) getLayoutInflater().inflate(R.layout.native_ad_template, null);

             //Register and populate the native ad asset views
             initNativeAdView(nativeAd,nativeAdView);

             //Add AlxNativeAdView to the UI
             FrameLayout adFrameLayout = (FrameLayout) findViewById(R.id.ad_container);
             adFrameLayout.removeAllViews();
             adFrameLayout.addView(nativeAdView);
             ……
           }
     });
}

private void initNativeAdView(AlxNativeAd nativeAd,AlxNativeAdView nativeView) {
     ……
     //Register and populate the title view
     nativeView.setTitleView(title);
     title.setText(nativeAd.getTitle());

      //Register and populate the multimedia view
      nativeView.setMediaView(mediaView);
      mediaView.setMediaContent(nativeAd.getMediaContent());

      //Register and populate other asset views
      ……      

      //Register the native ad object
      nativeView.setNativeAd(nativeAd);
}
```



e. Destroy ads

Native ads should be destroyed when they are no longer displayed. Sample code is shown below:

```Java
nativeAd.destroy();
```

Following is a detailed description of the `AlxNativeAd` class

|**Method** |**Parameter** |**Note** |
|---|---|---|
|getCreateType |- |Ad creative types [For example: Large pics; small pics; group pics; video; others: unknown type] 
|getAdSource |- |Ad sources |
|getAdLogo |- |Ad logo |
|getTitle |- |Ad title |
|getDescription |- |Ad content description |
|getIcon |- |Ad icon |
|getImages |- |Ad content with multi-pic creative |
|getCallToAction |- |The display text of the ad action button (e.g. "View details" or "Install") |
|getMediaContent |- |Ad multi-media creative |
|destroy |- |Destroy ad object |
|setNativeEventListener |`AlxNativeEventListener listener` |Ad event listener |

**About** `Bidding` **Price return and event reporting**

(Important)NOTE:

`SDK` provides three interfaces for `bidding`:

```Java
//Return bidding price
public double getPrice() {

}
```

```Java
//Report successfully bidden ads events
public void reportBiddingUrl() {

}
```

```Java
//Report successfully displayed ad events
public void reportChargingUrl() {
      
}
```
Developers can use the `onAdLoaded` callback interface when the ad request is successful and there is a fill.

Call the `getPrice()` interface to get the `Bidding` price in US dollars. If it is `0`, then no real-time bidding is returned.

Developers can call `load` or `show` interface after developers have done price comparison through bidding and chosen to display our ads

`reportBiddingUrl()`;


`show` interface needs to be called meanwhile developer is displaying our ads

`reportChargingUrl()`;



## 4. **User privacy**

##### 1. **GDPR**

As a publisher, you need to integrate `Consent Management Platform (CMP)` in your app and obtain user consent according to `“Mobile In-App CMP API v1.0: Transparency & Consent Framework”` and `“IAB Tech Lab – CMP API v2”` under ` IAB Europe` requirements.

If you are using a self-built `CMP`, then you need to store the collected user consent information in `SharedPreferences` (using the two `keys` `IABTCF_gdprApplies` and `IABTCF_TCString`).

`Transparency and Consent Framework (TCF) v2:``**Key**`

**Field Description**

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/4-1-1-1.png)

`RixEngine` is one of the `IAB Europe` Transparency and Consent Framework vendors(TCF). For more information about `RixEngine`’s compliance with `GDPR`, you can refer to `RixEngine`’s privacy policy.

`RixEngine SDK` will automatically read `GDPR` related consent string from `IABTCF_gdprApplies` and `IABTCF_TCString` under `SharedPreferences`. You can also use the following `SDK` function to manually pass in the `GDPR` related user consent fields. Note: Please try to call after `SDK` is initialized.

You do not need to call the following functions if your `app` has integrated with `CMP` and stored the user consent field in `IABTCF_gdprApplies` and `IABTCF_TCString` under `SharedPreferences`.

```Java
AlxAdSDK.setSubjectToGDPR(TRUE); 
AlxAdSDK.setUserConsent(<#Base64ConsentString#>); // Base64 encrypted consent string
```

`"subject to GDPR"` signal can be set as `YES / TRUE` (the user is protected by `GDPR` ) or `NO / FALSE` (the user is not protected by `GDPR`). This function should only be called if the application has determined whether `GDPR` applies to the user. If this function is not called, `RixEngine` assumes that the application has not made such a determination, and therefore `RixEngine` will determine the applicability of `GDPR` on its own.

`setUserConsent` function provides `“0”`（the user does not agree），`“1”`（the user agrees）or more detailed consent information

(`Base64` encrypted `consent string`). This more detailed consent information is described in GDPR Transparency and Consent Framework supported by `IAB`, more information refers to 

[`https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework`](https://github.com/InteractiveAdvertisingBureau/GDPR-Transparency-and-Consent-Framework)

##### 2. **CCPA**

The `California Consumer Privacy Act' (CCPA`) was issued to improve and strengthen the transparency and control of the use of personal privacy information of California users. As a developer, you are required to obtain consent for the use of the personal information of users in California. For more information about `RixEngine`’s compliance with `CCPA`, you can refer to `RixEngine`’s privacy policy.

**NOTE**: It is not an issue if you are upgrading from a previous version and have set the privacy signal. The new version of `SDK` will still use the values set in the previous version, so there is no need to manually set them again. But we recommend you use the new method.

You can refer to the following code sample to pass in the `US Privacy` signal. Note: please try to call after `SDK` is initialized. For example, `AlxAdSDK.subjectToUSPrivacy("1YYY")`; `subjectToUSPrivacy` accepts a string value (such as `"1YYY"`), which complies with the `IAB` US privacy string format.



##### 3. **Age-related compliance requirements**

Use the method below if you have child-directed apps to flag specific end-users as children, as may be permitted or required by applicable law (e.g., `COPPA`, `GDPR`, etc.). Developers of child-directed apps are responsible for determining whether an app is permitted to flag at the end-user level or must treat all end-users as children.

```Java
AlxAdSDK.setBelowConsentAge(TRUE); 
```

`belowConsentAge` signal uses boolean valua and can be `TRUE`（the user is defined as a child under the relevant regulations）or `FALSE`(the user are not defined as a child under the relevant regulations)



## 5. Integration guide for third-party mediation platform integration (reference only):

The adapter source code is already included in the SDK zip file and no further development is required

![img](https://static.rixengine.com/a/platform/help/sdk/5-1.png)

### 5.1 Admob

1. Create an App (please ignore if already exists)

1). Firstly, log in to the Admob account, add a third-party ad platform and make changes in accordance with guidelines provided by Admob [https://apps.admob.com/](https://apps.admob.com/). Need to create a new app first, if you don’t have one. (the test case is provided below)



![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-1-1-1.png)

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-1-1-2.png)



2). Then create ad units

 

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-1-2-1.png)



![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-1-2-2.png)



![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-1-2-3.png)

 

2. Add Alx mediation group

1). Create Alx mediation group


![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-1-1.png)



2). Create an Alx ad unit. Support rewarded videos, interstitials and banners (take rewarded videos as an example)

Fill in the ad unit name `Alx rewarded video mediation` and select ad unit format as `Rewarded`



![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-2-1.png)

 

 

3). Then click `ADD AD UNITS` and choose the corresponding app and ad unit

 

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-3-1.png)

 

4). Click `DONE` 

Set up a waterfall mediation ad source and a custom event on the next page

Fill in with name and eCPM

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-4-1.png)



![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-4-2.png)

 

 

5). Click `CONTINUE` 

Fill in with Ad unit parameter (Important!)

Then fill in ad unit mapping details:

Enter `com.admob.custom.adapter.AlxRewardVideoAdapter` (RewardVideo)

Other Adapter Class Name:

Rewarded Video `com.admob.custom.adapter.AlxRewardVideoAdapter`

Banner `com.admob.custom.adapter.AlxBannerAdapter`

Interstitial `com.admob.custom.adapter.AlxInterstitialAdapter`


 Parameters are:
```json
 {
   "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",

  "unitid": "171998",

  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",

  "isdebug": "false",

  "sid": "60188"
  }  
```



`appid`, `unitid`, `token` and `sid` need to apply at RixEngine (consult operation team for more details）

`appid` refers to App ID at RixEngine

`token` refers to Token at RixEngine

`sid` refers to ID at RixEngine

`unitid` refers to placement ID at RixEngine

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-5-1.png)

 

6). Click `DONE` after filling

Go to the next page

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-6-1.png)



7). Click `SAVE`

And back to the home page. It’s all set.

 

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-1-2-7-1.png)

 

### 5.2 MAX

1. First, add your app to Applovin official website according to the document provided by MAX [Applovin｜Everything you need to grow your mobile apps（](https://www.applovin.com/)[applovin.com](https://www.applovin.com/)[）](https://www.applovin.com/). Need to create a new app first, if you don’t have one. (the test case is provided below)

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-1.png)

 

 

Create an Android App



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-2.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-3.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-4.png)



2. GO back to the homepage after adding the app and click to add a custom network as shown in the picture 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-5.png)

  

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-6.png)



3. Choose `SDK` as Network Type, enter Custom Networks Name and enter Android Adapter Class Name as

com.applovin.mediation.adapters.AlgorixMediationAdapter (Important! The path must be correct）

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-7.png)



4. Copy the adapter file from Rixengine compression package to the corresponding directory in the project as shown below

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-8.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-9.png)



5. Import Rixengine SDK and add ProGuard Configuration

Copy `rixengine.*.*.*.aar` from the SDK compression package to `Application Module/libs` folder (If not exist, create manually), then add the following codes to `build.gradle` in your Module app：


//In Unity, the application folder is exported as unityLibrary

```json
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation(name: 'rixengine.*.*.*', ext: 'aar') // Replace with the specific sdk
version number
 
}
```



Add the following codes to the .pro file under the APP folder (for Android, the file is usually exported as proguard-rules.pro，for Unity, the file is exported as proguard-unity.txt):

```
-keep class com.rixengine.** {*;}  

-keep class admob.custom.adapter.** {*;}

-keep class anythink.custom.adapter.** {*;}

-keep class com.tradplus.custom.adapter.** {*;}

-keep class com.applovin.mediation.adapters.** {*;}

-keep class com.ironsource.adapters.** {*;}
```

 

6. Click `Create Ad Unit` on the right to create ad placement in sequence after creating Networks. Four ad types are supported.

BANNER; INTER; NATIVE; REWARD

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-10.png)

**It is important to choose `Manual` (custom rendering) when creating native ads, otherwise, there will be no filling**

**For specific code implementation, please refer to the integration case in the demo**

```Java
mAdLoader = new MaxNativeAdLoader(MAX_NATIVE_AD,context);
mAdLoader.setNativeAdListener(mMaxNativeAdListener);
mAdLoader.loadAd(createNativeAdView()); //Manual (custom rendering)
```



****

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-11.png)



7. Click `banner` after creating ad units (take banner as an example)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-12.png)

 

****

 

 

Scroll down and click `Custom Network (SDK)-Algorix` 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-13.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-14.png)

 

Enable `Status` 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-15.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-16.png)

And fill in the ad parameters, which need to be applied on the RixEngine SaaS console

**Custom Parameters Format**

  

```json
{

  "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",

  "unitid": "171998",

  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",

  "isdebug": "false",

  "sid": "60188"
}
```

  

****

`appid` refers to App ID in Rixengine

`token` refers to Token in Rixengine

`sid` refers to ID in Rixengine

`unitid` refers Placement ID in Rixengine

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-17.png)

 

8. Click `Save` after filling



9. Can adjust the price in the picture to prioritize RixEngine ad when conducting an ad integration test

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.2-18.png)



### 5.3 IronSource

1. Firstly, Log in to IronSource's official website at [ironSource | Turning Apps Into Scalable Businesses (](https://www.is.com/)[is.com](https://www.is.com/)[)](https://www.is.com/) and Add your app according to the document provided by IronSource. Need to create a new app first, if you don’t have one. (the test case is provided below)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-1.png)

 

2. Go back to the homepage and click `SDK Networks`

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-2.png)

 

3. Then click `Available Networks` in the picture below and choose `Custom Adapter`

(Note: To activate the Custom Adapter option, contact your account manager as you might not see it at first.)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-3.png)

 

4. Enter Network Key: 15b958455 (This is important cause the Network key is only dedicated to AlgoriX)

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-4.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-5.png)

 

5. Copy the adapter file from Algorix compression package to the corresponding directory shown below

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-6.png)

​       

6. Enter the corresponding `sid` and `token` that is applied in Rixengine (consult operation team for more details)

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-7.png)

Need to manually change the host in the adapter since IronSource is a special

`host` refers to host in the SSP

`appid` refers to App ID in the SSP

`token` refers to Toekn in the SSP

`sid` refers to ID in the SSP

`unitid` refers to Placement ID in the SSP



7. Click `Setup` after filling

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-8.png)

 

8. Enter the corresponding `appid` and `unitid`

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-9.png)

 

9. Click the icon of Algorix shown in the picture to check if the corresponding ad unit is effective (ensure the status is active)

 

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-10.png)

 

10. Then go to the Mediation page to check the detailed rank

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-11.png)

11. Can adjust the ad loading order by manually changing the value of eCPM

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-12.png)

 

 

12. To test whether the integration succeeds, you can add a new test device in `Mediation Testing` under `Testing` for apps already online

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-13.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-14.png)



13. Then choose the corresponding ad source and click `Test` button to activate



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-15.png)

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.3-16.png)

 

### 5.4 Topon

1. Step 1: Download the Mediation SDK package

[https://docs.toponad.com/#/en-us/android/download/package?_t=YzYVOgZulwVFdIaaqaD1mMC713U6521H](https://docs.toponad.com/#/en-us/android/download/package?_t=YzYVOgZulwVFdIaaqaD1mMC713U6521H) 

![img](https://static.rixengine.com/a/platform/help/android-sdk-en/5-4-1-1-1.png)

Note: choose SDK with the latest version and recommend selecting gradle as an integrating  approach for ones not in mainland China

Medaition ad platform options: Huawei; Mintegral; UnityAds; Admob

 

2. Step 2: refer to the links below for integration documents:

[https://docs.toponad.com/#/en-us/android/android_doc/android_sdk_config_access](https://docs.toponad.com/#/en-us/android/android_doc/android_sdk_config_access) 

       Mainly set up the custom ad platform on Topon, add ad adapter mapping, and customize ad parameters.

Enter ad unit mapping details:

 `com.anythink.custom.adapter.AlxRewardVideoAdapter` (RewardVideo)

Four ad unit types are supported, detailed Adapter Class Names are shown below:

Rewarded Video `com.anythink.custom.adapter.AlxRewardVideoAdapter`

Banner `com.anythink.custom.adapter.AlxBannerAdapter`

Interstitial `com.anythink.custom.adapter.AlxInterstitialAdapter`

Native `com.anythink.custom.adapter.AlxNativeAdapter`



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-2.png)



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-3.png)



Add custom ad parameters after completing the configuration



![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-4.png)

**Refer to the format in the figure**

  

```json
{
  "host": "http://testaa.rixengine.com/rtb",
  "appid": "acfe79f8aa3e36c8129ccf95a6f16f2fzJ",
  "unitid": "171998",
  "token": "bec2b4898ec3c60124339b44d4d9b95d8v",
  "isdebug": "false",
  "sid": "60188"
}  
```

  

`host` refers to host in the SSP

`appid` refers to App ID in the SSP

`token` refers to Token in the SSP

`sid` refers to ID in the SSP

`united` refers to Placement ID in the SSP

 

3. RixEngine SDK Android Integration Guide:

1). Copy `rixengine.*.*.*.aar` from this SDK compression package to `Application Module/libs` folder (If not exist, create manually), then add the following codes to `build.gradle` in your Module app



```json
repositories {
	flatDir {
    dirs 'libs
	}
}
dependencies {
	implementation(name: 'rixengine.*.*.*', ext: 'aar') // Replace with the specific sdk
version number
}
```



2). Place the adapter codes which are under the `com` directory to `src/main/java` source code directory of your Android project, please note that copy the full path, full path name: `com.anythink.custom.adapter`

Details are shown below:

![img](https://static.rixengine.com/a/platform/help/sdk/5-5.4-5.png)

 

4. Android ProGuard Configuration (if display splash ads in Android, then the packaging needs to be added): 

```
-dontwarn com.rixengine.**
 -keep class com.rixengine.** {*;}
 -keep class com.anythink.** {*;}
 -keep public class com.anythink.network.**
 -keepclassmembers class com.anythink.network.** {
   public *;
 }
 -dontwarn com.anythink.hb.**
 -keep class com.anythink.hb.**{ *;}****
 -dontwarn com.anythink.china.api.**
 -keep class com.anythink.china.api.**{ *;}****
 -keep class com.anythink.myoffer.ui.**{ *;}
 -keepclassmembers public class com.anythink.myoffer.ui.** {
   public *;
 }
```

 

## 6. `SDK` Error Code Specification

| Error Code | Description                                                         |
| ------ | ------------------------------------------------------------ |
| 1100   | Server Error. Please contact your AlgoriX Account Manager for further assistance                       |
| 1101   | An error in the network request. Check if the network status is normal                   |
| 1102   | No ads fill. Check sid and placement id, or contact your AlgoriX Account Manager for further assistance  |
| 1103   | Bad ad creatives, check sid and placement id. Please contact your AlgoriX Account Manager for further assistance |
| 1104   | Server Error. Please contact your AlgoriX Account Manager for further assistance, During the testing, we suggest please not use any VPN  |
| 1105   | Server Error. Please contact your AlgoriX Account Manager for further assistance                       |
| 1106   | Video request failed, check if the network status is normal                 |
| 1107   | Video playback failed, check if the network status is normal                     |
| 1108   | Video download failed, unable to playback, check if the network status is normal           |
| 1109   | Video file type not support, unable to playback. Please contact your AlgoriX Account Manager for further assistance         |
| 1110   | Video file doesn’t exist, unable to playback. Please contact your AlgoriX Account Manager for further assistance         |
| 1111   | Wrong parameter, unable to playback. Please contact your Algorix Account Manager for further assistance               |
| 1112   |  Ads rendered failed. Please contact your Algorix Account Manager for further assistance                         |
| 1113   | SDK is not initialized                                               |
| 3001   | Server Error. Ad format doesn't match with placement, please contact your AlgoriX Account Manager for further assistance           |
| 3002   |  Server Error. Ad. Ads placement is not enable by AlgoriX admin, please contact your AlgoriX Account           |
| 3003   |  Server Error. Ad. App is not enable by AlgoriX admin, please contact your AlgoriX Account               |

## 7.Java SDK version upgrade 
(This is not mandatory, but if you have problems with the Java version, you can try to modify the configuration following the processes below)

If the developer's gradle version is higher than 7.1, jdk version and SDK need to upgrade to java11. Developers can follow any one of the below two sets of gradle version configurations

Configuration 1
![img](https://static.rixengine.com/a/platform/help/sdk/7-1.jpeg)

![img](https://static.rixengine.com/a/platform/help/sdk/7-2.jpeg)
Configuration 2
![img](https://static.rixengine.com/a/platform/help/sdk/7-3.jpeg)

![img](https://static.rixengine.com/a/platform/help/sdk/7-4.png)
