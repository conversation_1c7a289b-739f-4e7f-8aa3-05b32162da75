@import '~antd/es/style/variable.less';

@font-face {
  font-family: 'Montserrat';
  src: url('~@/assets/fonts/Montserrat.otf');
}
@font-face {
  font-family: 'Satoshi Variable';
  font-weight: 400;
  src: url('~@/assets/fonts/Satoshi-Bold.ttf');
}

body {
  // 导航栏高度 64px
  --header-nav-height: 64px;
  // 主题色
  --primary-color: #0cafc7;
  --primary-color-hover: #0096ad;
  --primary-color-active: #00a1ba;
  --primary-color-outline: rgba(12, 175, 199, 0.2);
  --primary-1: #0798b5;
  --primary-2: #b8edf5;
  --primary-3: #daf3f7;
  --primary-shadow: #58c1d0;
  --text-color: #252829;
  --background-color: #f3f5f5;
  // 禁用
  --primary-disabled: #f5f5f5;

  color: var(--text-color) !important;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
}

body,
body div,
#root {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Montserrat', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%) !important;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset !important;
  box-shadow: none !important;
}

.ant-pro-sider-light .ant-pro-sider-collapsed-button {
  border: none !important;
}

.ant-menu-inline .ant-menu-item::after {
  display: none !important;
}

canvas {
  display: block;
}

ul,
ol {
  list-style: none;
}

.ant-menu-submenu-popup {
  .ant-menu {
    border-radius: 6px !important;
  }
}

.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.ant-menu-inline-collapsed {
  .ant-menu-submenu-selected {
    background: var(--primary-3) !important;
  }
}

.ant-layout {
  min-height: 100vh !important;

  .ant-pro-global-header {
    box-shadow: none !important;
    border-bottom: 1px solid #e2eaeb !important;
  }
}

div .ant-picker {
  border-radius: 6px;
}

.ant-input-number-wrapper.ant-input-number-group > .ant-input-number:first-child,
.ant-input-number-wrapper .ant-input-number-group-addon:first-child {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

.ant-checkbox {
  .ant-checkbox-inner {
    border-radius: 4px;
  }

  &.ant-checkbox-checked::after {
    border-radius: 4px;
  }
}

.ant-form-vertical {
  .ant-form-item {
    margin-bottom: 12px;

    .ant-form-item-explain-error {
      margin-bottom: 12px;
    }
  }
}

// 做定制化操作的样式
body {
  // .ant-input:focus,
  // .ant-input-focused {
  //   border: 1px solid var(--primary-color);
  //   box-shadow: 0px 0px 4px var(--primary-shadow);
  // }

  .ant-select-dropdown {
    border-radius: 6px;

    .ant-select-item-option-active {
      background: #e6e8eb;
    }

    .ant-select-item-option-selected {
      background: var(--primary-3) !important;

      &:hover {
        background: #e6e8eb;
      }
    }

    .ant-select-item-option {
      color: var(--text-color);
    }
  }

  .ant-checkbox-wrapper {
    color: var(--text-color);
    .ant-checkbox-checked::after {
      border-color: var(--primary-color);
    }
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
  }

  .ant-checkbox-indeterminate .ant-checkbox-inner::after {
    background-color: var(--primary-color);
  }

  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner,
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: var(--primary-color);
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: var(--primary-3);
  }

  .ant-input-affix-wrapper > input.ant-input:focus {
    border: none;
  }

  // radio
  .ant-radio-wrapper {
    .ant-radio-checked::after {
      border-color: var(--primary-color);
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: var(--primary-color);
    }

    .ant-radio-inner::after {
      background-color: var(--primary-color);
    }

    &:hover .ant-radio,
    .ant-radio:hover .ant-radio-inner,
    .ant-radio-input:focus + .ant-radio-inner {
      border-color: var(--primary-color);
    }
  }

  // 按钮样式
  .ant-btn {
    border-radius: 6px;
    &.ant-btn-link {
      color: var(--primary-color);
    }

    &.ant-btn-primary {
      border: none;
      text-shadow: none;

      &:not([disabled]) {
        background-color: var(--primary-color);
        &:hover {
          background: var(--primary-color-hover);
          border: none;
        }

        &:active {
          background: var(--primary-color-active);
          border: none;
        }
      }
    }

    &.ant-btn-default {
      color: #5e6466;
      background: var(--background-color);
      border-radius: 6px;
      border: none;

      &:hover {
        color: #5e6466;
        background: #d7dadb !important;
        border: none;
      }
    }
  }

  // 复制
  .ant-typography-copy {
    color: var(--primary-color);

    &:hover {
      color: var(--primary-color-hover);
    }

    &:active {
      color: var(--primary-color-active);
    }
  }

  // 加载按钮样式
  .ant-spin-nested-loading {
    .ant-spin-dot-item {
      background-color: var(--primary-color);
    }

    .ant-spin-text {
      color: var(--primary-color);
    }
  }

  // 切换按钮样式
  .ant-switch.ant-switch-checked {
    background-color: var(--primary-color);
  }

  .ant-input {
    border-radius: 6px;
  }

  .ant-input-affix-wrapper {
    border-radius: 6px;
  }

  .ant-layout-sider {
    .ant-layout-sider-children {
      background-color: var(--background-color);

      .ant-menu.ant-pro-sider-menu {
        box-shadow: none;
        border: none;
        background-color: var(--background-color);

        .ant-menu-item {
          margin-top: 0 !important;
        }
      }

      .ant-menu {
        color: var(--text-color);

        // 结构分析：ant-menu
        // 一、菜单项与分组类
        // item：菜单项（叶子节点）
        // submenu：子菜单容器（包含子菜单标题和下拉内容）
        // item-group：菜单项分组容器
        // item-group-title：菜单项分组标题
        // item-divider：菜单项分隔线（实线）
        // item-divider-dashed：虚线分隔线
        // 二、状态类
        // hidden：菜单项隐藏
        // submenu-hidden：子菜单隐藏
        // item-selected：菜单项被选中
        // submenu-selected：子菜单被选中（标题高亮）
        // item-active：菜单项被激活（如 hover 或 focus 状态）
        // inline-collapsed：内嵌菜单折叠状态（仅显示图标）
        // submenu-open：子菜单展开状态
        // submenu-active：子菜单激活状态（如 hover 时）
        // item-disabled：菜单项禁用
        // submenu-disabled：子菜单禁用（标题不可点击）

        // 针对 页面左侧菜单的样式定制
        &.ant-menu-root {
          // 需要单独定制一份状态样式
          > .ant-menu-item {
            color: var(--text-color);
            &.ant-menu-item-selected {
              color: var(--primary-color) !important;
              // 重置背景色
              background: var(--background-color) !important;
              background-color: var(--background-color);

              color: var(--primary-color) !important;

              a {
                color: var(--primary-color) !important;
              }
              // 选中的状态样式
              .ant-menu-title-content {
                background-color: var(--primary-3);
              }
            }

            &:hover,
            &:focus,
            &.ant-menu-item-active {
              color: var(--primary-color) !important;

              a {
                color: var(--primary-color) !important;
              }

              .ant-menu-title-content {
                background-color: #e6e8eb;
              }
            }

            &.ant-menu-item-only-child {
              padding: 0 !important;
              .ant-menu-title-content {
                border-radius: 6px;
                margin: 0 8px;
                padding: 0 16px;
                border-radius: 6px;
              }
            }
          }

          > .ant-menu-submenu {
            margin: 0px 8px;
          }

          // item 状态样式
          .ant-menu-item {
            &.ant-menu-item-selected {
              color: var(--primary-color);
              background-color: var(--primary-3);
              a {
                color: var(--primary-color) !important;
              }
            }

            &:hover,
            &:focus,
            &.ant-menu-item-active {
              color: var(--primary-color);

              a {
                color: var(--primary-color);
              }
            }
          }

          // // submenu 状态样式
          // .ant-menu-submenu {
          //   &.ant-menu-submenu-selected {
          //     color: var(--primary-color);
          //   }
          // }

          // 折叠按钮样式
          > .ant-pro-sider-collapsed-button {
            &.ant-menu-item-active {
              .ant-menu-title-content {
                background: none;
              }
            }
          }
        }

        .ant-menu-submenu {
          &.ant-menu-submenu-selected {
            color: var(--primary-color);
          }
        }

        .ant-menu-item {
          &.ant-menu-item-selected {
            color: var(--primary-color) !important;
          }

          &:hover,
          &:focus,
          &.ant-menu-item-active {
            color: var(--primary-color) !important;
          }
        }

        > .ant-menu-submenu {
          // margin: 0px 8px;
          border-radius: 6px;

          &.ant-menu-submenu-selected {
            color: var(--primary-color);
          }

          .ant-menu-submenu-arrow {
            color: var(--text-color);
          }

          .ant-menu-title-content .ant-pro-menu-item {
            display: flex;
            align-items: center;
          }
        }

        > .ant-menu-submenu-open {
          .ant-menu-submenu-arrow {
            color: var(--text-color);
          }

          .ant-menu-sub {
            .ant-menu-item-only-child {
              border-radius: 6px;

              .ant-pro-menu-item-title {
                font-weight: 400;
                color: #5e6466;
              }

              &:not(.ant-menu-item-selected):hover {
                background: #e6e8eb;

                .ant-pro-menu-item-title {
                  color: #5e6466;
                  // font-weight: 700;
                  text-shadow: 0 0 0.85px #5e6466;
                }
              }

              &.ant-menu-item-selected {
                background: var(--primary-3);

                .ant-pro-menu-item-title {
                  color: var(--primary-color);
                  font-weight: 700;
                }

                &:hover {
                  .ant-pro-menu-item-title {
                    color: var(--primary-color);
                  }
                }
              }
            }

            .ant-menu-item-selected {
              border-radius: 6px;

              &:hover {
                background: var(--primary-3);
                border-radius: 6px;

                .ant-pro-menu-item-title {
                  color: var(--primary-color);
                }
              }
            }
          }
        }

        .ant-menu-submenu-selected > div[role='menuitem'] {
          .ant-menu-submenu-arrow {
            color: var(--primary-color);
          }
        }
      }

      .ant-menu-inline-collapsed {
        & > .ant-menu-item[role='menuitem'] {
          .ant-menu-title-content {
            margin: 0 !important;
          }
        }
        .ant-menu-submenu {
          margin: 4px 4px;

          .ant-menu-submenu-title {
            padding: 2px 9px;
          }
        }
        &.ant-menu-root > .ant-menu-item-only-child {
          margin: 0;
        }
      }

      .ant-menu-root {
        > .ant-menu-submenu {
          .ant-menu-submenu-title {
            &:hover {
              background: #e6e8eb;
              border-radius: 6px;

              span {
                color: var(--text-color);
              }
            }
          }

          &.ant-menu-submenu-selected {
            color: var(--primary-color);
            .ant-menu-submenu-title {
              &:hover {
                background: #e6e8eb;
                border-radius: 6px;

                span {
                  color: var(--primary-color);
                }
              }
            }
          }
        }
      }
    }
  }

  .ant-menu-submenu-popup.ant-menu {
    .ant-menu-item {
      &.ant-menu-item-selected,
      &:hover,
      &:focus,
      &.ant-menu-item-active {
        color: var(--primary-color) !important;

        a {
          color: var(--primary-color) !important;
        }
      }
    }

    .ant-menu-submenu {
      &.ant-menu-submenu-selected,
      &:hover,
      &:focus,
      &.ant-menu-submenu-active {
        color: var(--primary-color) !important;

        .ant-menu-submenu-title {
          color: var(--primary-color) !important;
        }
      }
    }
  }

  .ant-menu-light .ant-menu-item:hover,
  .ant-menu-light .ant-menu-item-active,
  .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  .ant-menu-light .ant-menu-submenu-active,
  .ant-menu-light .ant-menu-submenu-title:hover {
    color: var(--primary-color);
  }

  // 下拉菜单
  .ant-dropdown {
    .ant-dropdown-menu-item-selected,
    .ant-dropdown-menu-submenu-title-selected {
      color: var(--primary-color) !important;
    }
  }

  // 树形 checkbox 样式
  .ant-tree {
    .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
    .ant-tree-checkbox:hover .ant-tree-checkbox-inner,
    .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner {
      border-color: var(--primary-color);
    }

    .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
      background-color: var(--primary-color);
    }

    .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
      background-color: #f5f5f5;
      border-color: #d9d9d9 !important;
    }

    .ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
      background-color: rgba(0, 0, 0, 0.25);
    }
  }

  // data-picker
  .ant-picker {
    &-range {
      .ant-picker-active-bar {
        background: var(--primary-color);
      }

      .ant-picker-active-preset > .ant-tag-blue {
        color: var(--primary-color);
      }
    }
    &-panel-focused {
      border-color: var(--primary-color);
    }
    &-header-view button:hover {
      color: var(--primary-color);
    }
  }

  .ant-form .ant-form-item {
    .ant-form-item-label > label {
      color: #5e6466;
    }

    .ant-form-item-control {
      padding-left: 6px;
    }
  }

  .ant-input-group-addon {
    border-radius: 6px 0 0 6px;
  }

  .ant-switch-inner {
    font-size: 14px;
  }

  a {
    color: var(--primary-color);

    &:hover {
      color: var(--primary-color-hover);
    }

    &:active {
      color: var(--primary-color-active);
    }
  }

}

::selection {
  color: #fff !important;
  background-color: var(--primary-color) !important;
}

// 全局使用的溢出隐藏
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.ant-spin-nested-loading .ant-space-align-center {
  position: absolute !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ant-spin.ant-spin-lg.ant-spin-spinning {
  position: absolute !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// body {
//   --primary-color: #2678ff;
//   --primary-color-hover: #145fd8;
//   --primary-color-active: #145fd8;
//   --primary-color-outline: rgba(38, 120, 255, 0.2);
//   --primary-1: #e6f2ff;
//   --primary-2: #e6f2ff;
//   --primary-3: #e6f2ff;
//   --primary-shadow: #2678ff;
//   // 字体颜色
//   --text-color: #1f2126;
//   // 禁用
//   --primary-disabled: #f5f5f5;
//   --background-color: #f5f6fa;

//   color: var(--text-color) !important;
// }

// 针对 topon 的定制样式
// 开启方式：运行时判断url设置类名称，该类名称(挂载在body上)下设置定制样式
.topon {
  --header-nav-height: 0px;
  --primary-color: #2678ff;
  --primary-color-hover: #145fd8;
  --primary-color-active: #145fd8;
  --primary-color-outline: rgba(38, 120, 255, 0.2);
  --primary-1: #e6f2ff;
  --primary-2: #e6f2ff;
  --primary-3: #e6f2ff;
  --primary-shadow: #2678ff;
  // 字体颜色
  --text-color: #1f2126;
  // 禁用
  --primary-disabled: #f5f5f5;
  --background-color: #f5f6fa;

  color: var(--text-color) !important;

  .custom-logo {
    height: 48px;
  }
}
