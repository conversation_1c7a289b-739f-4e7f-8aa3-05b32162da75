export declare namespace PmpAPI {
  type AddDealParams = {
    name: string;
    pmp_id: number;
    buyer_id: number;
    deal_id: string;
    bidfloor: number;
    auction_type: number;
    status: number;
    op_id: number;
    tnt_id: number;
  }

  type addInventoryParams = {
    name: string;
    seller_id?: number[];
    inventory_type: number[];
    bundle: string[];
    ad_format: number[];
    country: string[];
    ad_size: number[];
    seller_deal_id: string;
    remark: string;
    status: number;
    op_id: number;
    tnt_id: number;
  }

  type UpdateDealParams = {
    id: number;
    auction_type: number;
    bidfloor: number;
    deal_id: string;
    name: string;
    status: number;
    op_id: number;
    tnt_id: number;
  }

  type updateInventoryParams = {
    id: number;
    name: string;
    seller_id?: number[];
    inventory_type: number[];
    bundle: string[];
    ad_format: number[];
    country: string[];
    ad_size: number[];
    seller_deal_id: string;
    remark: string;
    status: number;
    op_id: number;
    tnt_id: number;
  }

  type IsExistsDeal = {
    name: string;
    tnt_id: number;
    deal_id: number;
    buyer_id: number;
    pmp_id: number;
    bidfloor: number;
    id?: number;
  }

  type isExistsInventory = {
    name: string;
    tnt_id: number;
    id?: number;
  }
}
