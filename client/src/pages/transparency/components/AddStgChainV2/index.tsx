/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:16:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-12 14:26:59
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalInput from '@/components/Input/NormalInput';
import { fetchData, removeZWSpace } from '@/utils';
import { addStgChainV2, updateStgChainV2 } from '@/services/api';
import { DefaultFormData, SupplyChainTypeOptions } from '@/constants/transparency/stg-v2';
import { StatusOptions } from '@/constants';

type AddSupplyChainProps = {
  isEdit: boolean;
  visible: boolean;
  item?: TransparencyAPI.StgItem;
  onClose: () => void;
  onSave: () => void;
  tenantList?: any[];
};
const AddSupplyChainModel: React.FC<AddSupplyChainProps> = ({ visible, isEdit, item, onClose, onSave, tenantList }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tenantOptions, setTenantOptions] = useState<{ label: string; value: number }[]>();

  useEffect(() => {
    form.resetFields();
    if (visible && isEdit && item) {
      form.setFieldsValue(item);
    }
  }, [item, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    if (changeValue.tnt_id) {
      const params = {
        ...allValue
      };
      // 重置数据
      form.setFieldsValue(params);
    }
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  const handleAddStgChain = (params: any) => {
    fetchData({ request: addStgChainV2, params, onSuccess, setLoading });
  };

  const handleUpdateStgChain = (params: any) => {
    fetchData({ request: updateStgChainV2, params, onSuccess, setLoading });
  };

  const handleFinish = (values: any) => {
    let { developer_website_domain } = values;
    developer_website_domain = removeZWSpace(developer_website_domain).trim();
    if (isEdit) {
      const params = {
        ...values,
        developer_website_domain,
        id: item?.id,
        ori_data: item
      };
      handleUpdateStgChain(params);
    } else {
      handleAddStgChain({ ...values, developer_website_domain });
    }
  };
  const vaildDomain = (rule: any, value: any) => {
    const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
    if (!value) {
      return Promise.resolve();
    } else if (!reg.test(value)) {
      return Promise.reject(new Error('Please enter the correct domain format'));
    }
    return Promise.resolve();
  };
  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit Supply Chain' : 'Add Supply Chain'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        initialValues={{ ...DefaultFormData }}
      >
        <Form.Item
          label="Publisher ID"
          name="publisher_id"
          rules={[{ required: true, message: 'Please Input Publisher ID' }]}
        >
          <NormalInput placeholder="Please Input Publisher ID"></NormalInput>
        </Form.Item>
        <Form.Item label="Type" name="type">
          <NormalRadio options={SupplyChainTypeOptions}></NormalRadio>
        </Form.Item>

        <Form.Item
          label="Domain"
          tooltip="Developer Website Domain"
          name="developer_website_domain"
          rules={[
            {
              required: true,
              message: 'Please Input Developer Website Domain'
            },
            { validator: vaildDomain }
          ]}
        >
          <NormalInput placeholder="Please Input Domain"></NormalInput>
        </Form.Item>

        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddSupplyChainModel;
