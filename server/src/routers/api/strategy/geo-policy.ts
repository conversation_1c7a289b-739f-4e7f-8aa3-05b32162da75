import { geoPolicyController } from '@/controllers';
import Router from 'koa-router';

const router = new Router({ prefix: '/geo-policy' });

const routers = router
  .post('/getGeoPolicyKeyList', geoPolicyController.getGeoPolicyKeyList)
  .post('/addGeoPolicyKey', geoPolicyController.addGeoPolicyKey)
  .post('/updateGeoPolicyKey', geoPolicyController.updateGeoPolicyKey)
  .post(
    '/getGeoPolicyKeyRelationList',
    geoPolicyController.getGeoPolicyKeyRelationList
  )
  .post('/addGeoPolicyKeyRelation', geoPolicyController.addGeoPolicyKeyRelation)
  .post(
    '/updateGeoPolicyKeyRelation',
    geoPolicyController.updateGeoPolicyKeyRelation
  );

export default routers;
