/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:41:24
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 15:08:41
 * @Description:
 */
import path from 'path';
import fs from 'fs';
import { Readable } from 'stream';
import { UnReadType } from '@/constants';
import { FaviconPathMap, LogoPathMap } from '@/constants/brand-manage';
import { commonModel } from '@/models';
import { CommonAPI } from '@/types/common';
import { getBrandStorageOptions } from '@/utils/gcp-utils';

class CommonService implements CommonAPI.Common {
  async getBuyerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    return await commonModel.getBuyerIntegrationType();
  }

  async getSellerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    return await commonModel.getSellerIntegrationType();
  }

  async getNotificationList(
    tnt_id: number,
    user_id: number,
    cur_time_zone: string
  ): Promise<CommonAPI.NotificationListItem[]> {
    const [msgList, readList] = await commonModel.getNotificationList(
      tnt_id,
      user_id,
      cur_time_zone
    );
    // 表中没有加msg_id唯一key, 这里去重
    const readIdList = [...new Set(readList.map(item => item.msg_id))];
    const resultList = msgList.map(item => ({
      ...item,
      unread: readIdList.includes(item.id) ? UnReadType.Read : UnReadType.UnRead
    }));
    return Promise.resolve(resultList);
  }

  async updateNotificationStatus(
    params: CommonAPI.UpdateNotificationParams
  ): Promise<any> {
    return await commonModel.updateNotificationStatus(params);
  }

  async getBrandInfo(params: CommonAPI.GetFaviconParams): Promise<any> {
    return await commonModel.getBrandInfo(params);
  }

  async getFavicon(params: CommonAPI.GetFaviconParams): Promise<Readable> {
    const brandInfo = await commonModel.getBrandInfo(params);
    const { brand_favicon_path } = brandInfo[0] || {};
    const { bucket } = getBrandStorageOptions('favicon');
    if (brand_favicon_path) {
      return await commonModel.getFavicon({
        fileName: brand_favicon_path,
        bucket
      });
    }
    const file_path = FaviconPathMap.default;
    const resolvePath = path.join(__dirname, `../../../webroot/${file_path}`);
    const stream = fs.createReadStream(resolvePath);
    return Promise.resolve(stream);
  }

  async getLogo(params: CommonAPI.GetFaviconParams): Promise<Readable> {
    const brandInfo = await commonModel.getBrandInfo(params);
    const { brand_logo_path } = brandInfo[0] || {};
    const { bucket } = getBrandStorageOptions('logo');
    if (brand_logo_path) {
      return await commonModel.getLogo({
        fileName: brand_logo_path,
        bucket
      });
    }
    const file_path = LogoPathMap.default;
    const resolvePath = path.join(__dirname, `../../../webroot/${file_path}`);
    const stream = fs.createReadStream(resolvePath);
    return Promise.resolve(stream);
  }
}

export default new CommonService();
