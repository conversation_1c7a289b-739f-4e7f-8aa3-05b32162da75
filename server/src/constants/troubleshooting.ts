/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-23 14:22:33
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-23 14:29:08
 * @Description:
 */
export const RegionType = {
  USE: 1,
  APAC: 2
};
export const RegionTypeDesc: { [key: string | number]: string } = {
  1: 'USE',
  2: 'APAC'
};

export const AdFormatType = {
  Banner: 1,
  Native: 2,
  Video: 3,
  'Reward Video': 4
};

export const AdFormatTypeDesc: { [key: string | number]: string } = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

// 针对app-ads.txt的 响应状态码
export const AppInfoStatusCode = {
  // 成功
  SUCCESS: 1000,
  // 系统服务异常
  SERVICE_ERROR: 1001,
  // 请求参数异常
  CONTENT_ERROR: 1002
};
