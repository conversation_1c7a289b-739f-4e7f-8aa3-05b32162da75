/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-09 19:30:38
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-09 19:30:40
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getIvtConfigList } from '@/services/api';

interface IvtProps {
  reload: () => Promise<any>;
  loading: boolean;
  dataSource: StrategyAPI.IvtConfigItem[];
}

export default function useIvtList(): IvtProps {
  const { data, run, loading, error } = useCustomRequest(getIvtConfigList, {
    cacheKey: 'ivt-config-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading
  };
}
