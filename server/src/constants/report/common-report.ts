/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-16 20:10:04
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 10:51:55
 * @Description:
 */

const DimensionsHeader = [
  { header: 'Date', key: 'date' },
  { header: 'Publisher', key: 'seller' },
  { header: 'Advertiser', key: 'buyer' },
  { header: 'Pub Partner', key: 'pub_partner' },
  { header: 'Adv Partner', key: 'adv_partner' },
  { header: 'Bundle', key: 'app_bundle_id' },
  { header: 'Unit ID', key: 'placement_id' },
  { header: 'Ad Format', key: 'ad_format' },
  { header: 'Ad Size', key: 'ad_size' },
  { header: 'Country', key: 'country' },
  { header: 'Platform', key: 'platform' },
  { header: 'Server Region', key: 'region' },
  { header: 'App Name', key: 'app_name' },
  { header: 'Ad Domain', key: 'ad_domain' },
  { header: 'Device Type', key: 'device_type' },
  { header: 'Inventory', key: 'inventory' },
  { header: '<PERSON>hain <PERSON>', key: 'seller_schain_hop' },
  { header: 'Schain Complete', key: 'seller_schain_complete' },
  { header: 'Crid', key: 'res_crid' }
];
const AllMetricsHeader = [
  { header: 'Advertiser Net Revenue', key: 'buyer_net_revenue' },
  { header: 'Publisher Net Revenue', key: 'seller_net_revenue' },
  {
    header: 'Profit',
    key: 'profit'
  },
  { header: 'Request', key: 'request' },
  { header: 'Total Request', key: 'total_request' },
  { header: 'Block Request', key: 'block_request' },
  { header: 'Out Request', key: 'out_request' },
  { header: 'Response', key: 'response' },
  { header: 'Win', key: 'win' },
  { header: 'Impression(ADM)', key: 'impression' },
  { header: 'Click', key: 'click' },
  { header: 'Bid Floor', key: 'total_seller_bid_floor' },
  {
    header: 'Fill Rate',
    key: 'fill_rate'
  },
  { header: 'Advertiser Gross Revenue', key: 'buyer_gross_revenue' },
  { header: 'Publisher Gross Revenue', key: 'seller_gross_revenue' },
  {
    header: 'eCPR',
    key: 'ecpr'
  },
  {
    header: 'Advertiser eCPR',
    key: 'adv_ecpr'
  },
  {
    header: 'Click Rate',
    key: 'click_rate'
  },
  {
    header: 'CPC',
    key: 'cpc'
  },
  {
    header: 'Profit Rate',
    key: 'profit_rate'
  },
  {
    header: 'Win Rate',
    key: 'win_rate'
  },
  {
    header: 'Render Rate',
    key: 'impression_rate'
  },
  {
    header: 'Advertiser Gross eCpm',
    key: 'buyer_gross_ecpm'
  },
  {
    header: 'Advertiser Net eCpm',
    key: 'buyer_net_ecpm'
  },
  {
    header: 'Publisher Net eCpm',
    key: 'seller_net_ecpm'
  },
  {
    header: 'Bid Price',
    key: 'bid_price'
  },
  {
    header: 'QPS (Real)',
    key: 'real_qps'
  },
  {
    header: 'Advertiser QPS (Config)',
    key: 'adv_config_qps'
  },
  {
    header: 'Publisher QPS (Config)',
    key: 'pub_config_qps'
  },
  {
    header: 'Impression(Pay)',
    key: 'seller_payment_impression'
  }
];

export const HeaderOptions = [...DimensionsHeader, ...AllMetricsHeader];

export const TimeZoneMap: { [key: string]: string } = {
  'Etc/GMT+12': 'UTC-12',
  'Etc/GMT+11': 'UTC-11',
  'Etc/GMT+10': 'UTC-10',
  'Etc/GMT+9': 'UTC-9',
  'Etc/GMT+8': 'UTC-8',
  'Etc/GMT+7': 'UTC-7',
  'Etc/GMT+6': 'UTC-6',
  'Etc/GMT+5': 'UTC-5',
  'Etc/GMT+4': 'UTC-4',
  'Etc/GMT+3': 'UTC-3',
  'Etc/GMT+2': 'UTC-2',
  'Etc/GMT+1': 'UTC-1',
  'Etc/UTC': 'UTC+0',
  'Etc/GMT-12': 'UTC+12',
  'Etc/GMT-11': 'UTC+11',
  'Etc/GMT-10': 'UTC+10',
  'Etc/GMT-9': 'UTC+9',
  'Etc/GMT-8': 'UTC+8',
  'Etc/GMT-7': 'UTC+7',
  'Etc/GMT-6': 'UTC+6',
  'Etc/GMT-5': 'UTC+5',
  'Etc/GMT-4': 'UTC+4',
  'Etc/GMT-3': 'UTC+3',
  'Etc/GMT-2': 'UTC+2',
  'Etc/GMT-1': 'UTC+1'
};

export const Country: {
  [key: string]: string;
} = {
  AFG: 'Afghanistan',
  ALB: 'Albania',
  DZA: 'Algeria',
  ASM: 'American Samoa',
  AND: 'Andorra',
  AGO: 'Angola',
  AIA: 'Anguilla',
  ATA: 'Antarctica',
  ATG: 'Antigua and Barbuda',
  ARG: 'Argentina',
  ARM: 'Armenia',
  ABW: 'Aruba',
  AUS: 'Australia',
  AUT: 'Austria',
  AZE: 'Azerbaijan',
  BHS: 'Bahamas',
  BHR: 'Bahrain',
  BGD: 'Bangladesh',
  BRB: 'Barbados',
  BLR: 'Belarus',
  BEL: 'Belgium',
  BLZ: 'Belize',
  BEN: 'Benin',
  BMU: 'Bermuda',
  BTN: 'Bhutan',
  BOL: 'Bolivia',
  BIH: 'Bosnia and Herzegovina',
  BWA: 'Botswana',
  BRA: 'Brazil',
  IOT: 'British Indian Ocean Territory',
  VGB: 'British Virgin Islands',
  BRN: 'Brunei',
  BGR: 'Bulgaria',
  BFA: 'Burkina Faso',
  BDI: 'Burundi',
  KHM: 'Cambodia',
  CMR: 'Cameroon',
  CAN: 'Canada',
  CPV: 'Cape Verde',
  CYM: 'Cayman Islands',
  CAF: 'Central African Republic',
  TCD: 'Chad',
  CHL: 'Chile',
  CHN: 'China',
  CXR: 'Christmas Island',
  CCK: 'Cocos Islands',
  COL: 'Colombia',
  COM: 'Comoros',
  COK: 'Cook Islands',
  CRI: 'Costa Rica',
  HRV: 'Croatia',
  CUB: 'Cuba',
  CUW: 'Curacao',
  CYP: 'Cyprus',
  CZE: 'Czech Republic',
  COD: 'Democratic Republic of the Congo',
  DNK: 'Denmark',
  DJI: 'Djibouti',
  DMA: 'Dominica',
  DOM: 'Dominican Republic',
  TLS: 'East Timor',
  ECU: 'Ecuador',
  EGY: 'Egypt',
  SLV: 'El Salvador',
  GNQ: 'Equatorial Guinea',
  ERI: 'Eritrea',
  EST: 'Estonia',
  ETH: 'Ethiopia',
  FLK: 'Falkland Islands',
  FRO: 'Faroe Islands',
  FJI: 'Fiji',
  FIN: 'Finland',
  FRA: 'France',
  PYF: 'French Polynesia',
  GAB: 'Gabon',
  GMB: 'Gambia',
  GEO: 'Georgia',
  DEU: 'Germany',
  GHA: 'Ghana',
  GIB: 'Gibraltar',
  GRC: 'Greece',
  GRL: 'Greenland',
  GRD: 'Grenada',
  GUM: 'Guam',
  GTM: 'Guatemala',
  GGY: 'Guernsey',
  GIN: 'Guinea',
  GNB: 'Guinea-Bissau',
  GUY: 'Guyana',
  HTI: 'Haiti',
  HND: 'Honduras',
  HKG: 'Hong Kong(China)',
  HUN: 'Hungary',
  ISL: 'Iceland',
  IND: 'India',
  IDN: 'Indonesia',
  IRN: 'Iran',
  IRQ: 'Iraq',
  IRL: 'Ireland',
  IMN: 'Isle of Man',
  ISR: 'Israel',
  ITA: 'Italy',
  CIV: 'Ivory Coast',
  JAM: 'Jamaica',
  JPN: 'Japan',
  JEY: 'Jersey',
  JOR: 'Jordan',
  KAZ: 'Kazakhstan',
  KEN: 'Kenya',
  KIR: 'Kiribati',
  XKX: 'Kosovo',
  KWT: 'Kuwait',
  KGZ: 'Kyrgyzstan',
  LAO: 'Laos',
  LVA: 'Latvia',
  LBN: 'Lebanon',
  LSO: 'Lesotho',
  LBR: 'Liberia',
  LBY: 'Libya',
  LIE: 'Liechtenstein',
  LTU: 'Lithuania',
  LUX: 'Luxembourg',
  MAC: 'Macau',
  MKD: 'Macedonia',
  MDG: 'Madagascar',
  MWI: 'Malawi',
  MYS: 'Malaysia',
  MDV: 'Maldives',
  MLI: 'Mali',
  MLT: 'Malta',
  MHL: 'Marshall Islands',
  MRT: 'Mauritania',
  MUS: 'Mauritius',
  MYT: 'Mayotte',
  MEX: 'Mexico',
  FSM: 'Micronesia',
  MDA: 'Moldova',
  MCO: 'Monaco',
  MNG: 'Mongolia',
  MNE: 'Montenegro',
  MSR: 'Montserrat',
  MAR: 'Morocco',
  MOZ: 'Mozambique',
  MMR: 'Myanmar',
  NAM: 'Namibia',
  NRU: 'Nauru',
  NPL: 'Nepal',
  NLD: 'Netherlands',
  ANT: 'Netherlands Antilles',
  NCL: 'New Caledonia',
  NZL: 'New Zealand',
  NIC: 'Nicaragua',
  NER: 'Niger',
  NGA: 'Nigeria',
  NIU: 'Niue',
  PRK: 'North Korea',
  MNP: 'Northern Mariana Islands',
  NOR: 'Norway',
  OMN: 'Oman',
  PAK: 'Pakistan',
  PLW: 'Palau',
  PSE: 'Palestine',
  PAN: 'Panama',
  PNG: 'Papua New Guinea',
  PRY: 'Paraguay',
  PER: 'Peru',
  PHL: 'Philippines',
  PCN: 'Pitcairn',
  POL: 'Poland',
  PRT: 'Portugal',
  PRI: 'Puerto Rico',
  QAT: 'Qatar',
  COG: 'Republic of the Congo',
  REU: 'Reunion',
  ROU: 'Romania',
  RUS: 'Russia',
  RWA: 'Rwanda',
  BLM: 'Saint Barthelemy',
  SHN: 'Saint Helena',
  KNA: 'Saint Kitts and Nevis',
  LCA: 'Saint Lucia',
  MAF: 'Saint Martin',
  SPM: 'Saint Pierre and Miquelon',
  VCT: 'Saint Vincent and the Grenadines',
  WSM: 'Samoa',
  SMR: 'San Marino',
  STP: 'Sao Tome and Principe',
  SAU: 'Saudi Arabia',
  SEN: 'Senegal',
  SRB: 'Serbia',
  SYC: 'Seychelles',
  SLE: 'Sierra Leone',
  SGP: 'Singapore',
  SXM: 'Sint Maarten',
  SVK: 'Slovakia',
  SVN: 'Slovenia',
  SLB: 'Solomon Islands',
  SOM: 'Somalia',
  ZAF: 'South Africa',
  KOR: 'South Korea',
  SSD: 'South Sudan',
  ESP: 'Spain',
  LKA: 'Sri Lanka',
  SDN: 'Sudan',
  SUR: 'Suriname',
  SJM: 'Svalbard and Jan Mayen',
  SWZ: 'Swaziland',
  SWE: 'Sweden',
  CHE: 'Switzerland',
  SYR: 'Syria',
  TWN: 'Taiwan(China)',
  TJK: 'Tajikistan',
  TZA: 'Tanzania',
  THA: 'Thailand',
  TGO: 'Togo',
  TKL: 'Tokelau',
  TON: 'Tonga',
  TTO: 'Trinidad and Tobago',
  TUN: 'Tunisia',
  TUR: 'Turkey',
  TKM: 'Turkmenistan',
  TCA: 'Turks and Caicos Islands',
  TUV: 'Tuvalu',
  VIR: 'U.S. Virgin Islands',
  UGA: 'Uganda',
  UKR: 'Ukraine',
  ARE: 'United Arab Emirates',
  GBR: 'United Kingdom',
  USA: 'United States',
  URY: 'Uruguay',
  UZB: 'Uzbekistan',
  VUT: 'Vanuatu',
  VAT: 'Vatican',
  VEN: 'Venezuela',
  VNM: 'Vietnam',
  WLF: 'Wallis and Futuna',
  ESH: 'Western Sahara',
  YEM: 'Yemen',
  ZMB: 'Zambia',
  ZWE: 'Zimbabwe'
};

export const AdFormatToLabel: { [key: string]: string } = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

export const MoblieOS: { [key: string]: string } = {
  '0': 'Undefined',
  '1': 'iOS',
  '2': 'Android',
  '3': 'Other',
  '4': 'Linux',
  '5': 'MacOS',
  '6': 'Windows',
  // OTT/CTV 预留11~29
  '11': 'tvOS',
  '12': 'Roku',
  '13': 'Amazon',
  '14': 'Microsoft',
  '15': 'Samsung Smart TV',
  '16': 'LG Smart TV',
  '17': 'Sony Playstation',
  '18': 'Vizio',
  '19': 'Philips Smart TV',
  '50': 'Tizen',
  '51': 'KaiOS'
};

export const RegionLabelMap = {
  // 0: 'All Region',
  USE: 'USE',
  APAC: 'APAC'
};
