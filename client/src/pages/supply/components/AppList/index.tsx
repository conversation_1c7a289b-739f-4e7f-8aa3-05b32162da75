/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 10:40:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-12-26 15:53:39
 * @Description:
 */
import React, { useState, useEffect, useMemo } from 'react';
import styles from './index.less';
import { useRequest } from 'ahooks';
import { getSupplyAppPlacement } from '@/services/api';
import { fetchData } from '@/utils';
import AppLeft from '@/pages/developer/components/AppLeft';
import { parse } from 'query-string';
import { useLocation } from '@umijs/max';
import AppPlacement from '../AppPlacement';

type AppListProps = {
  demandList: DemandAPI.DemandListItem[];
  loading: boolean;
  isMax: boolean;
  token: string;
  domain: string;
  pvDomain: string; // 租户自定义域名
  publisherType: number;
};
//  需要supply判断sdk
const Page: React.FC<AppListProps> = ({ demandList, loading, isMax, token, domain, pvDomain, publisherType }) => {
  const location: any = useLocation();
  const { id } = parse(location.search);
  const [filterList, setFilterList] = useState<SupplyAPI.SellerAppItem[]>([]);
  const [currentApp, setCurrentApp] = useState<SupplyAPI.SellerAppItem | undefined>();
  const [dataLoading, setDataLoading] = useState(false);
  const [appList, setAppList] = useState<SupplyAPI.SellerAppItem[]>([]);

  useEffect(() => {
    handleGetAppList();
  }, []);

  useEffect(() => {
    setFilterList(appList || []);
  }, [appList]);

  useEffect(() => {
    if (filterList && filterList.length) {
      const item = filterList.find(item => item.app_id === currentApp?.app_id);
      if (!item) {
        setCurrentApp(filterList[0]);
      } else {
        setCurrentApp(item);
      }
    } else {
      setCurrentApp(undefined);
    }
  }, [filterList]);

  useEffect(() => {
    if (appList && appList.length) {
      setFilterList(appList);
    } else {
      setFilterList([]);
      setCurrentApp(undefined);
    }
  }, [appList]);

  const handleSearch = (val: string) => {
    if (val) {
      const list = appList.filter((item: SupplyAPI.SellerAppItem) => {
        return (
          item.app_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 ||
          item.app_id.toString().indexOf(val) !== -1 ||
          item.bundle.indexOf(val.toLowerCase()) !== -1
        );
      });
      setFilterList(list);
    } else {
      setFilterList(appList);
    }
  };

  const handleChangeCurrentApp = (current: SupplyAPI.SellerAppItem | undefined) => {
    setCurrentApp(current);
  };

  const handleGetAppList = () => {
    const onSuccess = (data: any) => {
      setAppList(data);
    };
    fetchData({
      setLoading: setDataLoading,
      request: getSupplyAppPlacement,
      params: { seller_id: id, developer: 0 },
      onSuccess
    });
  };

  return (
    <div className={styles.main}>
      {useMemo(() => {
        return (
          <AppLeft
            dataLoading={dataLoading || loading}
            filterList={filterList as any}
            currentApp={currentApp}
            handleSearch={handleSearch}
            handleChangeCurrentApp={handleChangeCurrentApp as any}
            isDeveloper={false}
            maxHeight="calc(100vh - 160px)"
          />
        );
      }, [dataLoading, filterList, currentApp, loading])}
      <AppPlacement
        currentApp={currentApp}
        demandList={demandList}
        loading={loading}
        handleGetAppList={handleGetAppList}
        isMax={isMax}
        token={token}
        domain={domain}
        pvDomain={pvDomain}
        publisherType={publisherType}
      />
    </div>
  );
};

export default Page;
