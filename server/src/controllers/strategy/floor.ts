/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-23 17:04:40
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:57:09
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import { AddFloorSchema, UpdateFloorSchema } from '@/schema/strategy/floor';
import { floorService } from '@/services';
import { FloorAPI } from '@/types/floor';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';
class FloorCtrl implements FloorAPI.FloorCtrl {
  async getFloorList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    const { plm_id, type, cur_time_zone } = formData;

    const list = await floorService.getFloorList(
      tnt_id,
      cur_time_zone,
      plm_id, 
      type
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }
  async getAllSupplyPlacement(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const list = await floorService.getAllSupplyPlacement(tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddFloorSchema)
  async addFloor(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;

    const list = await floorService.addFloor(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `addFloor success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(UpdateFloorSchema)
  async updateFloor(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    // const { isChange } = formData;
    // const { count } = await floorService.isExistedFloor(formData);

    const list = await floorService.updateFloor(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updateFloor success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }
  async deleteFloor(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const list = await floorService.deleteFloor(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `deleteFloor success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }
}

export default new FloorCtrl();
