/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 19:58:43
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-16 14:54:49
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';

export const AccountTabOptions = [
  { label: 'Base', value: '1' },
  { label: 'Password', value: '2' }
];

export const AccountTab = {
  Base: '1',
  Password: '2'
};

export const AccountBreadOptions: BreadcrumbItem[] = [
  {
    name: 'My Account'
  }
];
