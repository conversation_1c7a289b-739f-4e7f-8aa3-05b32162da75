import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { BeAPIAuthTnts } from '@/constants/backend-api/auth';
import { APIRESULT } from '@/constants/backend-api/common';
import { BeApiTimeZoneMap } from '@/constants/time-zone';
import { getRedisByKey } from '@/db/redis';
import { beCommonModel } from '@/models';
import { getConfig, getUserIp, md5 } from '@/utils';
import { sendLarkAlertCard } from '@/utils/alert';
import {
  getTokenInfo,
  handleInvalidCsDomain,
  handleInvalidToken,
  transformApiParams
} from '@/utils/backend-api';
import { Context, Next } from 'koa';
import moment from 'moment';

const { redisConfig } = getConfig();

/**
 * @description Validate the API token in the request headers.
 * @returns The return value of next(), or void if an error occurred.
 */
export const validateApiToken = async (
  ctx: Context,
  next: Next
): Promise<ReturnType<Next> | void> => {
  const { user_id, token, cs_domain } = getTokenInfo(ctx);
  if (user_id && token) {
    const vaildInfo = await beCommonModel.validateApiToken({
      user_id,
      token,
      cs_domain
    });
    if (Array.isArray(vaildInfo) && vaildInfo.length > 0) {
      const { tnt_id, account_name } = vaildInfo[0];
      ctx.state.tnt_id = tnt_id;
      ctx.state.user_id = user_id;
      ctx.state.cs_domain = cs_domain;
      ctx.state.account_name = account_name;
      ctx.state.token = token;
      return await next();
    }
  }
  handleInvalidToken(ctx, user_id, token, cs_domain);
};

/**
 * Initialize the request parameters of the backend API interface
 * @returns The return value of next(), or void if an error occurred.
 */
export async function initApiParams(
  ctx: Context,
  next: Next
): Promise<ReturnType<Next> | void> {
  const body = ctx.request.body || {};
  const zone = body?.timezone || 'UTC+0';
  const timeZone = BeApiTimeZoneMap[zone] || 'Etc/UTC';
  const { tnt_id = 0 } = ctx.state!;
  let formData = {
    ...ctx.request.body,
    tnt_id,
    cur_time_zone: timeZone
  };
  formData = transformApiParams(formData);
  ctx.request.body = formData;
  return await next();
}

/**
 * limit the number of requests per day
 * @param ctx
 * @param next
 * @returns
 */
export async function validVisitDayLimit(ctx: Context, next: Next) {
  const { user_id, token } = getTokenInfo(ctx);
  const day = moment().format('YYYYMMDD');
  // 每天的key 仅api使用 origin 增加api_url 是为了预防 demand/supply sid/token相同导致
  const key = md5(`${ctx.state.cs_domain || ''}_${user_id}_${token}_${day}`);
  // 当天查询次数
  const count = (await getRedisByKey(key)) || 0;
  if (+count >= redisConfig.BE_API_DAILY_LIMIT) {
    ctx.response.status = 200;
    const result = { ...APIRESULT };
    result.data = null;
    result.status = {
      code: Code.REQUEST_COUNT_LIMIT,
      msg: Message.REQUEST_COUNT_LIMIT
    };
    result.timestamp = moment
      .utc(new Date())
      .format('ddd MMM D HH:mm:ss Z YYYY');
    ctx.body = result;
    let params =
      ctx.request.method.toLowerCase() === 'post' ? ctx.request.body || {} : {};
    params = typeof params === 'object' ? params : {};

    const { cur_log_id, tnt_id, account_name } = ctx.state;
    const msg = `
      account_name=[${account_name}],
      tnt_id=[${tnt_id}],
      log_id=[${cur_log_id}],
      api=[${ctx.request.originalUrl}],
      params=[${JSON.stringify(ctx.request.body || {}, undefined, 4)}]
      ua=[${ctx.request.headers['user-agent'] || ''}]
      method=[${ctx.request.method}],
      user_id=[${user_id}],
      token=[${token || ''}],
      redis_key=[${key}]
    `;
    getLogger('error').error(
      `[STD API Request Limit] msg=${msg} request rate limit failed.`
    );
    const ip = getUserIp(ctx);
    sendLarkAlertCard({
      title: 'saas STD Platform API Request Limit',
      data: {
        'Log ID': cur_log_id,
        IP: ip,
        method: ctx.request.method,
        'API URL': ctx.request.originalUrl,
        'Page URL': ctx.request.header.referer,
        Operator: `${account_name}(${user_id})`,
        token,
        redis_key: key,
        'Tenant ID': tnt_id || 0,
        'Session ID': ctx.session!.session_id || 0,
        'User Agent': ctx.request.headers['user-agent'],
        'x-time-zone': ctx.request.headers['x-time-zone'],
        Params: ctx.request.body || {},
        Error: result
      }
    });
  } else {
    // 一天过期
    const expired = 24 * 60 * 60;
    const num = +count + 1;
    // 只统计成功的次数 失败的不管
    ctx.state.redis_count_key = key;
    ctx.state.redis_count = num;
    ctx.state.redis_expired = expired;
    return await next();
  }
}

/**
 *
 */
export async function validateTntAuth(ctx: Context, next: Next) {
  const { tnt_id, user_id, token, cs_domain } = ctx.state;
  if (BeAPIAuthTnts.includes(tnt_id)) {
    return await next();
  }
  handleInvalidToken(ctx, user_id, token, cs_domain);
}

/**
 * 考验 cs_domain 是否一致，确保 cs_domain 一致，才允许访问
 */
export async function validateCsDomain(ctx: Context, next: Next) {
  // 开发环境，不校验 cs_domain
  if (process.env.NODE_ENV === 'development') {
    return await next();
  }

  const { cs_domain, user_id, token } = ctx.state;

  const csDomain = ctx.request.body?.cs_domain;

  if (csDomain === cs_domain) {
    return await next();
  }
  handleInvalidCsDomain(ctx, user_id, token, cs_domain);
}
