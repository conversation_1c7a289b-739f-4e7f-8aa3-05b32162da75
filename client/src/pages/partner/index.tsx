/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 16:39:22
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 12:38:46
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import FrontTable from '@/components/Table/FrontTable';
import { PartnerColumns, PartnerSearchOption } from '@/constants/partner';
import PageContainer from '@/components/RightPageContainer';
import { useModel, history } from '@umijs/max';
import { PlusOutlined } from '@ant-design/icons';
import EditPartnerModel from './components/EditPartnerModel';
import { addPartner, updatePartner } from '@/services/api';
import { OperateRenderItem } from '@/components/OperateRender';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';

const Page: React.FC = () => {
  const { dataSource, reload, loading } = useModel('usePartnerList');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [partner, setCurPartner] = useState<PartnerAPI.PartnerListItem>();
  const [columns, setColumns] = useState(PartnerColumns);
  const [searchOptions, setSearchOptions] = useState(PartnerSearchOption);

  const handleEditPartner = (val: PartnerAPI.PartnerListItem) => {
    setIsEdit(true);
    setVisible(true);
    setCurPartner(val);
  };

  const handleOpenAccount = (params: PartnerAPI.PartnerListItem) => {
    history.push(`/partner/account?id=${params.partner_id}`);
  };

  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      accessCode: 'EditPartnerBtn',
      onClick: handleEditPartner,
      icon: <RixEngineFont type="edit" />
    },
    {
      label: 'Account',
      onClick: handleOpenAccount,
      icon: <RixEngineFont type="rix-account" />,
      accessCode: 'PartnerAccountPMS'
    }
  ];

  useEffect(() => {
    reload();
    const arr = PartnerColumns.map(v => ({ ...v }));
    const index = arr.findIndex(v => v.dataIndex === 'operate');
    if (index !== -1) {
      arr[index].render = (txt, params) => <OperateRender btnOptions={OperateOptions} params={params} />;
    }
    setColumns(arr);
  }, []);

  useEffect(() => {
    if (Array.isArray(dataSource) && dataSource.length) {
      const arr = PartnerSearchOption.map(v => ({ ...v }));
      const index = arr.findIndex(v => v.key === 'partner_id');
      if (index !== -1) {
        arr[index].options = dataSource.map(v => ({ label: `${v.partner_name}(${v.partner_id})`, value: v.partner_id }));
      }
      setSearchOptions(arr);
    }
  }, [dataSource]);

  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurPartner(undefined);
  };

  return (
    <PageContainer isAuto>
      <FrontTable<PartnerAPI.PartnerListItem>
        pageTitle="Partner"
        searchOptions={searchOptions}
        columns={columns}
        rowKey="partner_id"
        isFold
        defaultFold
        scroll={{ y: 'auto' }} // 非动态， 需要自己指定
        labelWidth={100}
        defaultFormItemWidth={300}
        dataSource={dataSource}
        loading={loading}
        btnOptions={[
          {
            label: 'Create Partner',
            type: 'primary',
            size: 'small',
            accessCode: 'AddPartnerBtn',
            onClick: handleClickCreate,
            icon: <PlusOutlined />
          }
        ]}
      />
      <EditPartnerModel
        visible={visible}
        isEdit={isEdit}
        editItem={partner}
        request={isEdit ? updatePartner : addPartner}
        onClose={() => {
          setVisible(false);
          setIsEdit(false);
        }}
        onSave={() => {
          setVisible(false);
          setIsEdit(false);
          reload();
        }}
      />
    </PageContainer>
  );
};

export default Page;
