.title-con {
  display: flex;
  max-width: calc(100% - 32px);
  flex: 1;
  &:has(> span:only-child) {
    > span:first-of-type {
      // 在这里添加你的样式
      // flex: none;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
    }
  }
}
.black {
  color: var(--text-color);
  font-weight: bold;
  white-space: nowrap;
}
.max-width {
  // width: 100%;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // -webkit-line-clamp: 1;
}
.title {
  font-size: 16px;
}
.line {
  color: #e2eaeb;
  margin: 0px 12px;
}
.gray {
  color: #8d9799;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.span-num {
  background: #edeff0;
  border-radius: 6px;
  padding: 0px 8px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  color: #5e6466;
  margin-left: 12px;
  white-space: nowrap;
}
