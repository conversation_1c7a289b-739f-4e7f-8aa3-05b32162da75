/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-10 10:58:57
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useMemo, useState } from 'react';
import { useModel, history } from '@umijs/max';
import FrontTable from '@/components/Table/FrontTable';
import { DemandColumns } from '@/constants/demand/demand-columns';
import { DemandSearchOption, DemandBreadOptions, ADFormat } from '@/constants/demand';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import { PlusOutlined } from '@ant-design/icons';
import AddDemandModel, { isBannerMultiSizeLimitTnTs } from '../components/AddDemandDrawer';
import OperateRender from '@/components/OperateRender';
import type { OperateRenderItem } from '@/components/OperateRender';
import { TopBarSearchItem } from '@/components/TopBar';
import RixEngineFont from '@/components/RixEngineFont';
import InfoBar from '@/components/InfoBar';
import { DemandAndSupplyStatusMap, RegionTypeDesc } from '@/constants';
import { DemandInfoTabs } from '@/constants/demand/info';
import { fetchData } from '@/utils';
import { getDemandAuth } from '@/services/demand';

const initialSearchValues = {
  status: [DemandAndSupplyStatusMap.Active, DemandAndSupplyStatusMap.Testing]
};

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  const currentTntId = initialState?.currentUser?.tnt_id ?? 0;

  const demandColumns = useMemo(() => {
    const isCustom = currentTntId === 1071;
    const isBannerMultiSize = isBannerMultiSizeLimitTnTs(currentTntId);

    // isCustom 为 false，添加过滤项 rev_share_ratio
    // isBannerMultiSize 为 false，添加过滤项 banner_multi_size
    const filterItems: any[] = [!isCustom && 'rev_share_ratio', !isBannerMultiSize && 'banner_multi_size'].filter(
      Boolean
    );

    return DemandColumns.filter(item => !filterItems.includes(item.dataIndex));
  }, [currentTntId]);
  // 用途：表格数据
  const { dataSource: demandList, reload, loading } = useModel('useDemandListWithTesting');
  // 用途：info 弹窗的 endpoint 列表数据
  const { dataSource: endpointList, reload: reloadEndpoint } = useModel('useDemandEndpointList');
  // 用途：创建/编辑 model 的 partner 的下拉框选项
  const { buyerPartnerList: partnerList, reload: reloadPartner, loading: partnerLoading } = useModel('usePartnerList');
  // 用途：搜索项
  const { dataSource: integrationTypeList, reload: reloadIntegrationType } = useModel('useSellerIntegrationTypeList');

  // model 弹窗逻辑
  const [visible, setVisible] = useState(false);
  // model 是否是编辑
  const [isEdit, setIsEdit] = useState(false);
  // 当前编辑的demand
  const [demand, setCurrentDemand] = useState<DemandAPI.DemandListItem | undefined>(undefined);
  // 搜索条件
  const [searchOptions, setSearchOptions] = useState(DemandSearchOption);
  // 表格数据
  const dataSource = useMemo(() => {
    if (!demandList) return [];

    const csDomain = initialState?.currentUser?.cs_domain || location.hostname;
    return demandList.map((item: DemandAPI.DemandListItem) => ({
      ...item,
      cs_domain: csDomain
    }));
  }, [demandList, initialState?.currentUser]);
  // 当前选中的行的数据
  const [currentRow, setCurrentRow] = useState<DemandAPI.DemandListItem | undefined>(undefined);
  // info 弹窗的 tab 切换
  const [activeKey, setActiveKey] = useState('basic');
  // info 弹窗的 tab 数据
  const [demandInfoTabs, setDemandInfoTabs] = useState(DemandInfoTabs);
  // info 弹窗的 loading
  const [infoLoading, setInfoLoading] = useState(false);

  useEffect(() => {
    if (!demandList) {
      reload();
    }
    reloadEndpoint();
    if (!integrationTypeList) {
      reloadIntegrationType();
    }
    if (!partnerList?.length) {
      reloadPartner();
    }
  }, []);

  useEffect(() => {
    if (currentRow) {
      const tabs = [...DemandInfoTabs];
      const baseIndex = tabs.findIndex(item => item.key === 'basic' && item.titleIcon);
      if (baseIndex !== -1) {
        if (!isBannerMultiSizeLimitTnTs(currentTntId)) {
          const children = tabs[baseIndex].children;
          const filterIndex = children?.findIndex(item => item.key === 'filter-and-compatibility-strategy') || -1;

          if (filterIndex !== -1 && children) {
            const filterSection = children[filterIndex];
            filterSection.columns = filterSection.columns?.filter(item => item.dataIndex !== 'banner_multi_size');
          }
        }

        if (currentRow?.status === DemandAndSupplyStatusMap.Testing) {
          tabs[baseIndex].titleIcon!.icon = '';
        } else {
          tabs[baseIndex].titleIcon!.icon = <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />;
          tabs[baseIndex].titleIcon!.onClick = row => {
            setCurrentRow(undefined);
            handleEditDemand(row!);
          };
        }
      }

      setDemandInfoTabs(
        currentRow?.status === DemandAndSupplyStatusMap.Testing ? tabs.filter(item => item.key === 'basic') : tabs
      );
      if (activeKey === 'authorization') {
        getDemandAuthList();
      }
    }
  }, [currentRow, currentTntId]);

  // 用途：初始化搜索项下拉项数据
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    if (integrationTypeList && integrationTypeList.length) {
      const tmp = integrationTypeList.map(item => {
        return {
          label: item.itg_name,
          value: item.id
        };
      });
      const index = options.findIndex(item => item.key === 'integration_type');
      if (index !== -1) {
        options[index].options = tmp;
      }
    }
    if (demandList) {
      const sOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'buyer_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  }, [integrationTypeList, demandList]);

  const handleEditDemand = (params: DemandAPI.DemandListItem) => {
    setCurrentDemand(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = () => {
    setIsEdit(false);
    setVisible(true);
    setCurrentDemand(undefined);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleEndpoint = (params: DemandAPI.DemandListItem) => {
    history.push(`/demand/advertiser/endpoint?id=${params.buyer_id}`, { row: params });
  };

  const handlePretarget = (params: DemandAPI.DemandListItem) => {
    history.push(`/demand/advertiser/pretargeting?id=${params.buyer_id}`, { row: params });
  };

  const handleAuth = (params: DemandAPI.DemandListItem) => {
    history.push(`/demand/advertiser/auth?buyer_id=${params.buyer_id}`, { row: params });
  };
  const handleOpenDemandAccount = (params: DemandAPI.DemandListItem) => {
    history.push(`/demand/advertiser/account?id=${params.buyer_id}&u_id=${params.user_id}&name=${params.buyer_name}`, {
      row: params
    });
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditDemand,
      icon: <RixEngineFont type="edit" />,
      accessCode: 'updateDemandAuth'
    },
    {
      label: 'Endpoint',
      onClick: handleEndpoint,
      icon: <RixEngineFont type="rix-endpoint" />,
      accessCode: 'DemandEndpointPermission'
    },
    {
      label: 'Pretargeting',
      onClick: handlePretarget,
      icon: <RixEngineFont type="rix-pretargeting" />,
      accessCode: 'DemandPretargetingPermission'
    },
    {
      label: 'Authorization',
      onClick: handleAuth,
      icon: <RixEngineFont type="rix-authorization" />,
      accessCode: 'DemandAuthorizationPermission'
    },
    {
      label: 'Account',
      onClick: handleOpenDemandAccount,
      icon: <RixEngineFont type="rix-account" />,
      accessCode: 'DemandAccountPermission'
    }
  ];
  const tmpColumns: ColumnProps<DemandAPI.DemandListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 150,
      fixed: 'right',
      render: (txt, params) => {
        let tmp = [...OperateOptions];
        if (params.status === DemandAndSupplyStatusMap.Testing) {
          tmp = tmp.map((item, index) => {
            return {
              ...item,
              disabled: true
            };
          });
          return <OperateRender btnOptions={tmp} params={params} moreBtnOptions={{ disabled: true }} />;
        }
        return <OperateRender btnOptions={tmp} params={params} />;
      }
    }
  ];
  const columns = [...demandColumns, ...tmpColumns];
  const normalEmptyRender = () => <span></span>;

  const handleClickRow = (record: DemandAPI.DemandListItem) => {
    if (record.buyer_id === currentRow?.buyer_id) {
      setCurrentRow(undefined);
    } else {
      const columnsKeys = ['connect_timeout', 'socket_timeout', 'server_region', 'gzip', 'url', 'ad_format'];
      const endpoint = endpointList?.filter(item => item.buyer_id === record.buyer_id);
      console.log('endpoint', endpoint);
      const tmp: any = { ...record };
      if (Array.isArray(endpoint)) {
        endpoint.forEach(item => {
          columnsKeys.forEach(key => {
            tmp[`${RegionTypeDesc[item.server_region]}_${ADFormat[item.ad_format]}_${key}`] =
              item[key as keyof DemandAPI.EndpointItem];
          });
        });
        console.log('tmp', tmp);
        setCurrentRow(tmp);
      }
    }
  };
  const getDemandAuthList = () => {
    fetchData({
      setLoading: setInfoLoading,
      request: getDemandAuth,
      params: { buyer_id: currentRow?.buyer_id },
      onSuccess: (data: any[]) => {
        if (data) {
          const index = demandInfoTabs.findIndex(item => item.key === 'authorization');
          const tabs = [...demandInfoTabs];
          if (index !== -1) {
            tabs[index].tableData = data.map(item => {
              return {
                auth_seller_id: item.seller_id,
                auth_seller_name: item.seller_name,
                integration_type_desc: integrationTypeList.find(i => i.id === item.integration_type)?.itg_name
              };
            });
          }
          setDemandInfoTabs(tabs);
        }
      }
    });
  };
  const handleInfoTabChange = (activeKey: string) => {
    setActiveKey(activeKey);
    if (activeKey === 'authorization') {
      const index = demandInfoTabs.findIndex(item => item.key === 'authorization');
      if (index !== -1) {
        getDemandAuthList();
      }
    }
  };

  return (
    <PageContainer flexDirection="column" options={DemandBreadOptions}>
      <FrontTable<DemandAPI.DemandListItem>
        pageTitle="Advertiser List"
        searchOptions={searchOptions}
        loading={loading || partnerLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'buyer_id'}
        request={reload}
        isFold
        btnOptions={[
          {
            label: 'Create Advertiser',
            type: 'primary',
            size: 'small',
            onClick: handleClickCreate,
            icon: <PlusOutlined />,
            accessCode: 'addDemandAuth'
          }
        ]}
        labelWidth={120}
        scroll={{ y: 'calc(100vh - 220px)' }}
        emptyRender={demandList && demandList.length ? normalEmptyRender : undefined}
        initialValues={initialSearchValues}
        onRow={record => {
          return {
            onClick: () => handleClickRow(record)
          };
        }}
        rowClassName={record => {
          return record.buyer_id === currentRow?.buyer_id ? styles['row-selected'] : '';
        }}
        allowSaveSearch
      />
      <AddDemandModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadDemand={reload}
        demand={demand}
        partnerList={partnerList}
      />
      <InfoBar<DemandAPI.DemandListItem, DemandAPI.EndpointItem>
        loading={infoLoading}
        dataSource={currentRow}
        title={currentRow?.buyer_name || ''}
        tabs={demandInfoTabs}
        width={window.innerWidth < 730 ? window.innerWidth : 730}
        handleClose={() => setCurrentRow(undefined)}
        defaultActiveKey={activeKey}
        handleTabChange={handleInfoTabChange}
      />
    </PageContainer>
  );
};

export default Page;
