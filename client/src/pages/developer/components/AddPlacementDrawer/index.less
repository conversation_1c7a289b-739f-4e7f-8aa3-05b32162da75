.container {
  display: flex;
  height: 100%;
  > div {
    height: 100%;
    &:first-child {
      flex: 1;
    }
  }
  form {
    overflow: hidden;
  }
  .img-right {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-left: 20px;
    width: 230px;
    span {
      color: #8d9799;
      font-weight: 700;
      font-size: 16px;
      margin-bottom: 32px;
      margin-top: 30px;
      &:last-child {
        margin-top: 16px;
        color: var(--text-color);
      }
    }
    img {
      width: 200px;
    }
  }
  .horizontal-item-flex {
    display: flex;
    align-items: center;
    > div {
      margin-right: 3px;
    }
  }
  .form-label-default-padding {
    > div {
      > div:first-child {
        padding-bottom: 3px;
      }
    }
  }
  .form-label-checkbox {
    > div {
      > div:last-child > div {
        min-height: 20px;
      }
    }
  }
  .checkbox-flex-3 {
    display: flex;
    flex-wrap: wrap;
    > label {
      &:nth-child(odd) {
        width: 180px;
      }
      &:nth-child(even) {
        width: 180px;
      }
    }
  }
}
