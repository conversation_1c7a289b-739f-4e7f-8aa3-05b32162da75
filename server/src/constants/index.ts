/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-23 10:25:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-09 19:47:03
 * @Description:
 */

import { getCtxResult } from '@/utils/response';
type Type = {
  [key: string | number]: string;
};

export const EmailParams = {
  from: 'Rix Engine<<EMAIL>>',
  bcc: '<EMAIL>,<EMAIL>',
  cc:
    process.env.NODE_ENV === 'prod'
      ? 'Rix Engine<<EMAIL>>'
      : 'Rix Engine<<EMAIL>>'
};

export const ProfitType = {
  Seller: 1,
  Demand: 2,
  'Seller-Demand': 3,
  DemandBundle: 4,
  DemandSellerBundle: 5
};

export const UserType = {
  Tenant: 1,
  Supply: 2,
  Demand: 3,
  Rix_Admin: 4,
  Rix_Data_Analyst: 5,
  Partner: 6
};

/** @description 内部运营用户 */
export const InternalUsers = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];

export const LinkMenuType = {
  Ordinary: 1,
  Dashboard: 2
};

export const TenantUserLimitCount = 20;

export const DemoTenantUserLimitCount = 100;

export const StatusMap = {
  Active: 1,
  Paused: 2,
  Testing: 3
};

export const StatusDesc: Type = {
  1: 'Active',
  2: 'Paused',
  3: 'Testing'
};

export const UnReadType = {
  UnRead: 1,
  Read: 2
};

export const RESULT = Object.freeze({
  ...getCtxResult('ERROR_SYS')
});

export const AdFormats = [1, 2, 3, 4];

export const AdFormatMap: Type = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

/**
 * profit 最大值 常量导出
 * @constant ProfitMaxValue 80
 */
export const ProfitMaxValue = 80;
/**
 * profit 最小值 常量导出
 * @constant ProfitMinValue -100
 */
export const ProfitMinValue = -100;
/**
 * 1052 租户不限制 profit ratio 最小值，给定一个默认值 -100000
 */
export const SpecialProfitMinValue = -100000;
