/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2023-10-10 10:20:41
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-22 15:54:55
 * @Description:
 */
import { bigqueryStream, queryStackOverflow } from '@/db/bigquery';

// 直接查询正式数据
function joinSql(options: any) {
  const table = 'saas-373106.pixalate.fraud_report';
  const sql = `
    select
      ${options.normal_dimension_str ? `${options.normal_dimension_str}, ` : ''}
      coalesce(sum(gross_tracked_ads), 0) as gross_tracked_ads,
      coalesce(sum(sivt_imp), 0) as sivt_imp,
      coalesce(sum(givt_imp), 0) as givt_imp,
      coalesce(sum(measured_imp), 0) as measured_imp,
      coalesce(sum(views), 0) as views,
      (case when sum(gross_tracked_ads) =0 then 0 else round(sum(sivt_imp) * 100 *1.0/ sum(gross_tracked_ads), 2) end) as sivt_imp_rate,
      (case when sum(gross_tracked_ads) =0 then 0 else round(sum(givt_imp) * 100 *1.0/ sum(gross_tracked_ads), 2) end) as givt_imp_rate,
      round((sum(sivt_imp) + sum(givt_imp)) * 100 / sum(gross_tracked_ads),2) as ivt_rate,
       (case when sum(measured_imp) = 0 then 0 else round(sum(views) * 100 *1.0/ sum(measured_imp), 2) end) as viewability,
      COUNT(*) OVER() AS total
    from ${table} as i where ${options.condition}
    ${options.dimension ? `group by ${options.dimension}` : ''}
  `;
  return sql;
}

class PixalateReport {
  async getPixalateReport(options: any, api_url: string) {
    const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
    return await queryStackOverflow(sql, {tag: api_url});
  }

  async downloadPixalateReport(options: any, api_url: string) {
    const sql = `${joinSql(options)} ${options.order}`;
    console.log('xxsql', sql);
    return await bigqueryStream(sql, {tag: api_url});
  }
}

export const pixalateModel = new PixalateReport();
