/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-05 11:31:40
 * @Description:
 */
import React from 'react';
import { Input } from 'antd';
import type { InputProps } from 'antd';
import styles from './index.less';

function NormalInput(props: InputProps): JSX.Element {
  return <Input {...props} className={`${styles['normal-input-radius-6']} ${props.className || ''}`} />;
}
NormalInput.TextArea = Input.TextArea;
NormalInput.Password = Input.Password;
export default NormalInput;
