/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-02 01:34:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 11:54:04
 * @Description:
 */

export const AllBillingDimension = [
  'day',
  'month',
  'seller_id',
  'buyer_id',
  'partner_id',
  'app_bundle_id',
  'country',
  'ad_format',
  'ad_width, ad_height',
];

export const AdvertiserBillingMetrics = [
  'request',
  'response',
  'buyer_net_revenue',
  'impression'
  // 'seller_payment_impression'
];

/**
 * 下载文件指定的指标
 */
export const PublisherBillingMetrics = [
  'request',
  // 'block_request',
  'response',
  'impression',
  'seller_payment_impression',
  'total_request',
  'seller_net_revenue',
  'profit',
  'profit_rate'
];
