# OpenRTB API

# API Request Method

- Request Method: **POST**
- Header: Content-Type: application/json

# API Request Parameters

| **Parent Object** | **Object/Parameter** | **Type** | **Description** |
| --- | --- | --- | --- |
|  | id | string; required | Unique ID of the bid request, provided by the exchange. |
|  | at | integer; default 1 | Auction type, where 1 = First Price, 2 = Second Price. |
|  | tmax | integer | Maximum time in milliseconds the exchange allows for bids to be received including Internet latency to avoid timeout. This value supersedes any a priori guidance from the exchange. |
|  | cur | string array | Array of allowed currencies for bids on this bid request using ISO-4217 alpha codes. Recommended only if the exchange accepts multiple currencies.**Temporarily Only support "USD"**. |
|  | bcat | string array | Blocked advertiser categories using the IAB content categories. Refer to List Content Categories. Refer to <a  href="#IAB Category Type">IAB Category Type</a>([Open RTB Document](https://iabtechlab.com/wp-content/uploads/2016/07/OpenRTB-API-Specification-Version-2-5-FINAL.pdf) Section 5.1). |
|  | badv | string array | Block list of advertisers by their domains (e.g., [“ford.com”](https://www.ford.com/)). |
|  | imp | object array; required | Array of Imp objects representing the impressions offered. At least 1 Imp object is required.**Temporarily only support one Imp**. |
| imp | id | string; required | A unique identifier for this impression within the context of the bid request (typically, starts with 1 and increments). |
| imp | tagid | string | Identifier for specific ad placement or ad tag that was used to initiate the auction. This can be useful for debugging of any issues, or for optimization by the buyer. |
| imp | instl | integer; default 0 | 1 = the ad is interstitial or full screen, 0 = not interstitial. |
| imp | bidfloor | float; default 0 | Minimum bid for this impression expressed in CPM |
| imp | bidfloorcur | string; default “USD” | Currency specified using ISO-4217 alpha codes. This may be different from bid currency returned by bidder if this is allowed by the exchange.**Temporarily Only support "USD"**. |
| imp | clickbrowser | integer | Indicates the type of browser opened upon clicking the creative in an app, where 0 = embedded, 1 = native. Note that the Safari View Controller in iOS 9.x devices is considered a native browser for purposes of this attribute. |
| imp | secure | integer | Flag to indicate if the impression requires secure HTTPS URL creative assets and markup, where 0 = non-secure, 1 = secure. If omitted, the secure state is unknown, but non-secure HTTP support can be assumed. |
| imp | banner | object | A Banner object; required if this impression is offered as a banner ad opportunity. |
| imp.banner | format | object array; recommended | Array of format objects representing the banner sizes permitted. If none are specified, then use of the h and w attributes is highly recommended. |
| imp.banner.format | w | integer | Width in device independent pixels (DIPS). |
| imp.banner.format | h | integer | Height in device independent pixels (DIPS). |
| imp.banner.format | wratio | integer | Relative width when expressing size as a ratio. |
| imp.banner.format | hratio | integer | Relative height when expressing size as a ratio. |
| imp.banner.format | wmin | integer | The minimum width in device independent pixels (DIPS) at which the ad will be displayed the size is expressed as a ratio. |
| imp.banner | w | integer | Exact width in device independent pixels (DIPS); recommended if no format objects are specified. |
| imp.banner | h | integer | Exact height in device independent pixels (DIPS); recommended if no format objects are specified. |
| imp.banner | btype | integer array | Blocked banner ad types. Refer to <a href="#Banner Ad Types">Banner Ad Type</a>. |
| imp.banner | battr | integer array | Blocked creative attributes. Refer to <a href="#Creative Attributes">Creative Attributes</a>. |
| imp.banner | pos | integer | Ad position on screen. Refer to <a href="#Ad Position">Ad Position</a>. |
| imp.banner | mimes | string array | Content MIME types supported. Popular MIME types may include “application/x-shockwave-flash”, “image/jpg”, and “image/gif”. |
| imp.banner | api | integer array | List of supported API frameworks for this impression. Refer to <a href="#API Frameworks">API Frameworks</a>. If an API is not explicitly listed, it is assumed not to be supported. |
| imp.banner | id | string | Unique identifier for this banner object. Recommended when Banner objects are used with a Video object (Section 3.2.7) to represent an array of companion ads. Values usually start at 1 and increase with each object; should be unique within an impression. |
| imp | video | object | A Video object; required if this impression is offered as a video ad opportunity. |
| imp.video | mimes | string array; required | Content MIME types supported (e.g., “video/x-ms-wmv”, “video/mp4”). |
| imp.video | minduration | integer; recommended | Minimum video ad duration in seconds |
| imp.video | maxduration | integer; recommended | Maximum video ad duration in seconds. |
| imp.video | protocols | integer array; recommended | Array of supported video protocols. Refer to <a href="#Protocols">Protocols</a>. |
| imp.video | w | integer; recommended | Width of the video player in device independent pixels (DIPS). |
| imp.video | h | integer; recommended | Height of the video player in device independent pixels (DIPS). |
| imp.video | startdelay | integer; recommended | Indicates the start delay in seconds for pre-roll, mid-roll, or post-roll ad placements. Refer to <a href="#Start Delay">Start Delay</a> for additional generic values. |
| imp.video | linearity | integer | Indicates if the impression must be linear, nonlinear, etc. If none specified, assume all are allowed. Refer to <a href="#Video Linearity">Video Linearity</a>. |
| imp.video | skip | integer | Indicates if the player will allow the video to be skipped, where 0 = no, 1 = yes. If a bidder sends markup/creative that is itself skippable, the Bid object should include the attr array with an element of 16 indicating skippable video. Refer to <a href="#Creative Attribute">Creative Attribute</a>. |
| imp.video | skipmin | integer; default 0 | Videos of total duration greater than this number of seconds can be skippable; only applicable if the ad is skippable. |
| imp.video | skipafter | integer; default 0 | Number of seconds a video must play before skipping is enabled; only applicable if the ad is skippable. |
| imp.video | battr | integer array | Blocked creative attributes. Refer to <a href="#Creative Attribute">Creative Attribute</a>. |
| imp.video | maxextended | integer | Maximum extended ad duration if extension is allowed. If blank or 0, extension is not allowed. If -1, extension is allowed, and there is no time limit imposed. If greater than 0, then the value represents the number of seconds of extended play supported beyond the maxduration value. |
| imp.video | minbitrate | integer | Minimum bit rate in Kbps. |
| imp.video | maxbitrate | integer | Maximum bit rate in Kbps. |
| imp.video | boxingallowed | integer; default 1 | Indicates if letter-boxing of 4:3 content into a 16:9 window is allowed, where 0 = no, 1 = yes. |
| imp.video | playbackmethod | integer array | Playback methods that may be in use. If none are specified, any method may be used. Refer to <a href="#Playback Methods">Playback Methods</a>. Only one method is typically used in practice. As a result, this array may be converted to an integer in a future version of the specification. It is strongly advised to use only the first element of this array in preparation for this change |
| imp.video | playbackend | integer | The event that causes playback to end. Refer to <a href="#Playback Cessation Modes">Playback Cessation Modes</a>. |
| imp.video | delivery | integer array | Supported delivery methods (e.g., streaming, progressive). If none specified, assume all are supported. Refer to <a href="#Content Delivery Methods">Content Delivery Methods</a>. |
| imp.video | pos | integer | Ad position on screen. Refer to <a href="#Ad Position">Ad Position</a>. |
| imp.video | companionad | object array | Array of Banner objects if companion ads are available. |
| imp.video | api | integer array | List of supported API frameworks for this impression. Refer to <a #href=""#API Frameworks>API Frameworks</a>. If an API is not explicitly listed, it is assumed not to be supported. |
| imp.video | companiontype | integer array | Supported VAST companion ad types. Refer to <a href="#Companion Types">Companion Types</a>. Recommended if companion Banner objects are included via the companionad array. If one of these banners will be rendered as an end-card, this can be specified using the vcm attribute with the particular banner. |
| imp.video | ext | object | Placeholder for exchange-specific extensions to OpenRTB. |
| imp.video.ext | rewarded | integer | Indicates if the video will support rewarded. Blank for not supported, 1 for supported. |
| imp | native | object | A Native object; required if this impression is offered as a native ad opportunity. |
| imp.native | request | string; required | Request payload complying with the [Native Ad Specification](https://www.iab.com/wp-content/uploads/2016/03/OpenRTB-Native-Ads-Specification-1-1_2016.pdf). |
| imp.native | ver | string; recommended | Version of the Dynamic Native Ads API to which request complies; highly recommended for efficient parsing. |
| imp.native | api | integer array | List of supported API frameworks for this impression. Refer to <a href="#API Frameworks">API Frameworks</a>. If an API is not explicitly listed, it is assumed not to be supported. |
| imp.native | battr | integer array | Blocked creative attributes. Refer to <a href="#Creative Attribute">Creative Attribute</a>. |
| imp | ext | object | Placeholder for exchange-specific extensions to OpenRTB. |
| imp.ext | skadn | object | The IAB Tech Lab has introduced technical specifications aimed at adapting Apple’s [SKAdNetwork](https://developer.apple.com/documentation/storekit/skadnetwork), a method for validating advertiser app installations, for programmatic ad buying. |
| imp.ext.skadn | versions | string array | Array of strings containing the supported skadnetwork versions. Always "2.0" or higher. Dependent on both the OS version and the SDK version. |
| imp.ext.skadn | version | string | Version of skadnetwork supported. Always "2.0" or higher. Dependent on both the OS version and the SDK version. |
| imp.ext.skadn | sourceapp | string | ID of publisher app in Apple’s App Store. Should match `app.bundle` in OpenRTB 2.x and `app.storeid` in AdCOM 1.x |
| imp.ext.skadn | skadnetids | string array | A subset of SKAdNetworkItem entries in the publisher app’s Info.plist, **expressed as lowercase strings**, that are relevant to the bid request. Recommended that this list not exceed 10. |
| imp.ext.skadn | productpage | integer | Custom Product Page support. See Apple's [Custom Product Page](https://developer.apple.com/app-store/custom-product-pages/) doc for details. |
| imp.ext.skadn | skadnetlist | object array | Object containing the IABTL list definition |
| imp.ext.skadn.skadnetlist | max | integer | IABTL list containing the max entry ID of SKAdNetwork ID. Format will be: "max entity ID" where 306 in the example on the right will be all SKAdNetwork IDs entry number 306 and below. |
| imp.ext.skadn.skadnetlist | excl | integer array | Comma separated list of integer IABTL registration IDs to be excluded from IABTL shared list. |
| imp.ext.skadn.skadnetlist | addl | string array | Comma separated list of string SKAdNetwork IDs, **expressed as lowercase strings**, not included in the IABTL shared list. The intention of addl is to be the permanent home for raw SKAdNetwork IDs, migrating away from `BidRequest.imp.ext.skadn.skadnetids`. Recommended that this list not exceed 10. |
|  | site | object; recommended | Details via a Site object about the publisher’s website. Only applicable and recommended for websites. |
| site | id | string; recommended | Exchange-specific site ID. |
| site | name | string | Site name (may be aliased at the publisher’s request). |
| site | domain | string | Domain of the site (e.g., “mysite.foo.com”). |
| site | cat | string array | Array of IAB content categories of the site. Refer to <a  href="#IAB Category Type">IAB Content Type</a>. |
| site | publisher | object | Details about the publisher of the site. |
| site.publisher | id | string | Exchange-specific publisher ID. |
| site | sectioncat | string array | Array of IAB content categories that describe the current section of the site. Refer to <a  href="#IAB Category Type">IAB Content Type</a>. |
| site | pagecat | string array | Array of IAB content categories that describe the current page or view of the site. Refer to <a  href="#IAB Category Type">IAB Content Type</a>. |
| site | page | string | URL of the page where the impression will be shown. |
| site | ref | string | Referrer URL that caused navigation to the current page. |
| site | search | string | Search string that caused navigation to the current page. |
| site | mobile | integer | Indicates if the site has been programmed to optimize layout when viewed on mobile devices, where 0 = no, 1 = yes. |
| site | privacypolicy | integer | Indicates if the site has a privacy policy, where 0 = no, 1 = yes. |
| site | keywords | string | Comma separated list of keywords about the site. |
|  | app | object; recommended | Details via an App object about the publisher’s app (i.e., non-browser applications). Only applicable and recommended for apps. |
| app | id | string; recommended | Exchange-specific site ID. |
| app | name | string | App name (may be aliased at the publisher’s request). |
| app | bundle | string | A platform-specific application identifier intended to be unique to the app and independent of the exchange. On Android, this should be a bundle or package name (e.g., com.foo.mygame). On iOS, it is typically a numeric ID. |
| app | domain | string | Domain of the app (e.g., “mygame.foo.com”). |
| app | storeurl | string | App store URL for an installed app; for IQG 2.1 compliance. |
| app | cat | string array | Array of IAB content categories of the site. Refer to <a  href="#IAB Category Type">IAB Category Type</a>. |
| app | publisher | object | Details about the publisher of the app. |
| app.publisher | id | string | Exchange-specific publisher ID. |
| app | sectioncat | string array | Array of IAB content categories that describe the current section of the app. Refer to <a  href="#IAB Category Type">IAB Category Type</a>. |
| app | pagecat | string array | Array of IAB content categories that describe the current page or view of the app. Refer to <a  href="#IAB Category Type">IAB Category Type</a>. |
| app | ver | string | Application version. |
| app | privacypolicy | integer | Indicates if the site has a privacy policy, where 0 = no, 1 = yes. |
| app | paid | integer | 0 = app is free, 1 = the app is a paid version. |
| app | keywords | string | Comma separated list of keywords about the site. |
|  | device | object; required | Details via a Device object about the user’s device to which the impression will be delivered. |
| device | ua | string; required | Browser user agent string. |
| device | geo | object; recommended | Location of the device assumed to be the user’s current location defined by a Geo object. |
| device.geo | lat | float | Latitude from -90.0 to +90.0, where negative is south. |
| device.geo | lon | float | Longitude from -180.0 to +180.0, where negative is west. |
| device.geo | country | String | Country code using ISO-3166-1-alpha-3. |
| device.geo | region | string | Region code using ISO-3166-2; 2-letter state code if USA. |
| device.geo | city | string | City using United Nations Code for Trade & Transport Locations. See Appendix A for a link to the codes. |
| device.geo | zip | string | Zip or postal code. |
| device | dnt | integer; recommended | Standard “Do Not Track” flag as set in the header by the browser, where 0 = tracking is unrestricted, 1 = do not track. |
| device | lmt | integer; recommended | “Limit Ad Tracking” signal commercially endorsed (e.g., iOS, Android), where 0 = tracking is unrestricted, 1 = tracking must be limited per commercial guidelines. |
| device | ip | string; required | IPv4 address closest to device. |
| device | devicetype | integer | The general type of device. Refer to <a href="#Device Type">Device Types</a> |
| device | make | string | Device make (e.g., “Apple”). |
| device | model | string | Device model (e.g., “iPhone”). |
| device | os | string | Device operating system (e.g., “iOS”). |
| device | osv | string | Device operating system version (e.g., “3.1.2”). |
| device | hwv | string | Hardware version of the device (e.g., “5S” for iPhone 5S). |
| device | h | integer | Physical height of the screen in pixels. |
| device | w | integer | Physical width of the screen in pixels |
| device | ppi | integer | Screen size as pixels per linear inch. |
| device | pxratio | float | The ratio of physical pixels to device independent pixels. |
| device | js | integer | Support for JavaScript, where 0 = no, 1 = yes. |
| device | language | string | Browser language using ISO-639-1-alpha-2. |
| device | carrier | string | Carrier or ISP (e.g., “VERIZON”) using exchange curated string names which should be published to bidders a priori. |
| device | mccmnc | string | Mobile carrier as the concatenated MCC-MNC code (e.g.,“310-005” identifies Verizon Wireless CDMA in the USA).Refer to https://en.wikipedia.org/wiki/Mobile_country_codefor further examples. Note that the dash between the MCCand MNC parts is required to remove parsing ambiguity. |
| device | connectiontype | integer | Network connection type. Refer to <a href="#Connection Type">Connection Type</a>. |
| device | ifa | string | ID sanctioned for advertiser use in the clear (i.e., not hashed). |
| device | didsha1 | string | Hardware device ID (e.g., IMEI); hashed via SHA1. |
| device | didmd5 | string | Hardware device ID (e.g., IMEI); hashed via MD5. |
| device | dpidsha1 | string | Platform device ID (e.g., Android ID); hashed via SHA1. |
| device | dpidmd5 | string | Platform device ID (e.g., Android ID); hashed via MD5. |
| device | macsha1 | string | MAC address of the device; hashed via SHA1. |
| device | macmd5 | string | MAC address of the device; hashed via MD5. |
| device | ext | object | Placeholder for exchange-specific extensions to OpenRTB. |
| device.ext | ifv | string | Placeholder for exchange-specific extensions to OpenRTB. |
|  | user | object; recommended | Details via a User object about the human user of the device; the advertising audience. |
| user | yob | integer | Year of birth as a 4-digit integer. |
| user | gender | string | Gender, where “M” = male, “F” = female, “O” = known to be other (i.e., omitted is unknown). |
| user | ext | object | Placeholder for exchange-specific extensions. |
| user.ext | consent | string | The user consent string is optional, but highly recommended if the request is subject to GDPR regulations (i.e., Regs.ext.gdpr = 1). |
|  | source | object | A Source object that provides data about the inventory source and which entity makes the final decision. |
| source | ext | object | Placeholder for exchange-specific extensions. |
| source.ext | omidpn | string | Open Measurement Interface Definition SDK partner information |
| source.ext | omidpv | string | Open Measurement Interface Definition SDK partner version |
|  | regs | object | A Regs object that specifies any industry, legal, or governmental regulations in force for this request. |
| regs | coppa | integer | Flag indicating if this request is subject to the COPPA regulations established by the USA FTC, where 0 = no, 1 = yes. |
| regs | ext | object | Optional exchange-specific extensions. |
| regs.ext | gdpr | integer | The “gdpr” will signal whether or not the request is subject to GDPR regulations. It is an optional integer that indicates: 0 = No, 1 = Yes. Under OpenRTB conventions for optional attributes, omission indicates Unknown. |
| regs.ext | us_privacy | string | Must follow the [US Privacy string format](https://github.com/InteractiveAdvertisingBureau/USPrivacy/blob/master/CCPA/US%20Privacy%20String.md). |

# API Response Parameters

| **Parent Object** | **Object/Parameter** | **Type** | **Description** |
| --- | --- | --- | --- |
|  | id | string; required | Unique ID of the bid request, provided by the exchange. |
|  | bidid | string | Bidder generated response ID to assist with logging/tracking. |
|  | cur | string; default "USD" | Currency specified using ISO-4217 alpha codes. This may be different from bid currency returned by bidder if this is allowed by the exchange.**Temporarily Only support "USD"**. |
|  | seatbid | object array | Array of seatbid objects; 1+ required if a bid is to be made. |
| seatbid | seat | string | ID of the buyer seat (e.g., advertiser, agency) on whose behalf this bid is made. |
| seatbid | bid | object array; required | Array of 1+ Bid objects each related to an impression. Multiple bids can relate to the same impression. |
| seatbid.bid | id | string; required | Bidder generated bid ID to assist with logging/tracking. |
| seatbid.bid | impid | string; required | ID of the Imp object in the related bid request. |
| seatbid.bid | price | float; required | Bid price expressed as CPM although the actual transaction is for a unit impression only. Note that while the type indicates float, integer math is highly recommended when handling currencies (e.g., BigDecimal in Java). |
| seatbid.bid | nurl | string | Win notice URL called by the exchange if the bid wins (not necessarily indicative of a delivered, viewed, or billable ad); optional means of serving ad markup. Substitution macros may be included in both the URL and optionally returned markup. |
| seatbid.bid | burl | string | Billing notice URL called by the exchange when a winning bid becomes billable based on exchange- specific business policy (e.g., typically delivered, viewed, etc.). Substitution macros (Section 4.4) may be included. |
| seatbid.bid | lurl | string | Loss notice URL called by the exchange when a bid is known to have been lost. Substitution macros (Section 4.4) may be included. Exchange-specific policy may preclude support for loss notices or the disclosure of winning clearing prices resulting in ${AUCTION_PRICE} macros being removed (i.e., replaced with a zero-length string). |
| seatbid.bid | adm | string | Optional means of conveying ad markup in case the bid wins; supersedes the win notice if markup is included in both. Substitution macros may be included. |
| seatbid.bid | adomain | string array | Advertiser domain for block list checking (e.g., “ford.com”). This can be an array of for the case of rotating creatives. Exchanges can mandate that only one domain is allowed. |
| seatbid.bid | bundle | string | A platform-specific application identifier intended to be unique to the app and independent of the exchange. On Android, this should be a bundle or package name (e.g., com.foo.mygame). On iOS, it is a numeric ID. |
| seatbid.bid | cid | string | Campaign ID to assist with ad quality checking; the collection of creatives for which iurl should be representative. |
| seatbid.bid | crid | string; required | Creative ID to assist with ad quality checking. |
| seatbid.bid | iurl | string | URL without cache-busting to an image that is representative of the content of the campaign for ad quality/safety checking. |
| seatbid.bid | cat | string array | IAB content categories of the creative. Refer to <a href="#IAB Category Type">IAB Category Type</a>. |
| seatbid.bid | api | integer | API required by the markup if applicable. Refer to List <a href="#API Framework">API Framework</a>. |
| seatbid.bid | w | integer | Width of the creative in device independent pixels (DIPS). |
| seatbid.bid | h | integer | Height of the creative in device independent pixels (DIPS). |

# API Sample

## Banner Request Sample

```JSON
{
    "id": "IxexyLDIIk",
    "at": 1,
    "tmax": 300,
    "bcat": [
        "IAB25",
        "IAB7-39",
        "IAB8-18",
        "IAB8-5",
        "IAB9-9"
    ],
    "badv": [
        "apple.com",
        "go-text.me",
        "heywire.com"
    ],
    "imp": [
        {
            "id": "1",
            "bidfloor": 0.5,
            "instl": 0,
            "tagid": "agltb3B1Yi1pbmNyDQsSBFNpdGUY7fD0FAw",
            "banner": {
                "w": 320,
                "h": 50,
                "pos": 1,
                "btype": [
                    4
                ],
                "battr": [
                    14
                ],
                "api": [
                    3
                ]
            }
        }
    ],
    "app": {
        "ver": "3.16.50",
        "paid": 0,
        "keywords": "Text Scanner",
        "storeurl": "https://play.google.com/store/apps/details?id=com.bestai.scannerlite",
        "id": "1103",
        "name": "Text Scanner",
        "domain": "algorix.co",
        "bundle": "com.bestai.scannerlite",
        "publisher": {
		"id": "1"
	}

    },
    "device": {
        "carrier": "VERIZON",
        "devicetype": 4,
        "ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36",
        "ip": "***************",
        "dpidmd5": "1822e31ea5956736d81cc9f069182e37",
        "geo": {
          "lat": 35.012345,
          "lon": -115.12345,
          "country": "USA",
          "region": "CA",
          "city": "Los Angeles",
          "zip": "90049"
        },
        "js": 1,
        "connectiontype": 2,
        "macsha1": "b5a6abf843a35392e1dc81b8c00a740d64aa31be",
        "make": "samsung",
        "os": "Android",
        "dnt": 0,
        "ifa": "a098ce09-76f8-4aec-9775-4e4fd293e441",
        "macmd5": "20dd47a5c710e7af4120f033fd3a8a4b",
        "osv": "7.0",
        "dpidsha1": "7a97e4312aa4496ebb68ff05c25ea2d54eb6bfec",
        "model": "SM-A310F"
    },
    "user": {
        "id": "ffffffd5135596709273b3a1a07e466ea2bf4fff",
        "yob": 1984,
        "gender": "M"
    },
    "source": {
		"ext": {
			"omidpn": "Rixengine",
			"omidpv": "1.4.2"
		}
	},
    "regs": {
	      "ext": {
		      "gdpr": 0
		}
	}
}
```

## Banner Response Sample

```JSON
{
    "id": "IxexyLDIIk",
    "cur": "USD",
    "bidid": "11887b9ffa8541d1ad7edf0b3fa5af12",
    "seatbid": [
        {
            "seat": "MV8xMjM=",
            "bid": [
                {
                    "id": "7853845b-af39-41f1-ba02-a5ee1831d0e8",
                    "impid": "1",
                    "price": 0.095,
                    "adm": "<html snippet>",
                    "cid": "12",
                    "crid": "12-2398",
                    "adomain": [
                        "advertiserdomain.com"
                    ],
                    "w": 320,
                    "h": 50,
                    "cat": [
                        "IAB1"
                    ]
                }
            ]
        }
    ]
}
```

## Native Request Sample

```JSON
{
    "id": "80ce30c53c16e6ede735f123ef6e32361bfc7b22",
    "at": 1,
    "cur": [
        "USD"
    ],
    "imp": [
        {
            "id": "1",
            "bidfloor": 0.03,
            "native": {
                "request": "{\"native\":{\"ver\":\"1.1\",\"context\":2,\"contextsubtype\":20,\"plcmttype\":11,\"plcmtcnt\":1,\"assets\":[{\"id\":123,\"required\":1,\"title\":{\"len\":140}},{\"id\":128,\"required\":0,\"img\":{\"wmin\":836,\"hmin\":627,\"type\":3}},{\"id\":124,\"required\":1,\"img\":{\"wmin\":50,\"hmin\":50,\"type\":1}},{\"id\":126,\"required\":1,\"data\":{\"type\":1,\"len\":25}},{\"id\":127,\"required\":1,\"data\":{\"type\":2,\"len\":140}}]}}",
                "ver": "1.0",
                "api": [
                    3
                ],
                "battr": [
                    13,
                    14
                ]
            }
        }
    ],
    "app": {
        "ver": "3.16.50",
        "paid": 0,
        "keywords": "Text Scanner",
        "storeurl": "https://play.google.com/store/apps/details?id=com.bestai.scannerlite",
        "id": "1103",
        "name": "Text Scanner",
        "domain": "algorix.co",
        "bundle": "com.bestai.scannerlite",
        "publisher": {
		"id": "1"
	}

    },
    "device": {
        "carrier": "VERIZON",
        "devicetype": 4,
        "ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36",
        "ip": "***************",
        "dpidmd5": "1822e31ea5956736d81cc9f069182e37",
        "geo": {
          "lat": 35.012345,
          "lon": -115.12345,
          "country": "USA",
          "region": "CA",
          "city": "Los Angeles",
          "zip": "90049"
        },
        "js": 1,
        "connectiontype": 2,
        "macsha1": "b5a6abf843a35392e1dc81b8c00a740d64aa31be",
        "make": "samsung",
        "os": "Android",
        "dnt": 0,
        "ifa": "a098ce09-76f8-4aec-9775-4e4fd293e441",
        "macmd5": "20dd47a5c710e7af4120f033fd3a8a4b",
        "osv": "7.0",
        "dpidsha1": "7a97e4312aa4496ebb68ff05c25ea2d54eb6bfec",
        "model": "SM-A310F"
    },
    "user": {
        "id": "55816b39711f9b5acf3b90e313ed29e51665623f"
    },
    "source": {
		"ext": {
			"omidpn": "Rixengine",
			"omidpv": "1.4.2"
		}
	},
    "regs": {
	      "ext": {
		      "gdpr": 0
		}
	}
}
```

## Native Response Sample

```JSON
{
    "id": "80ce30c53c16e6ede735f123ef6e32361bfc7b22",
    "bidid": "14b680b580c34c309499a65ee6774d19",
    "cur": "USD",
    "seatbid": [
        {
            "bid": [
                {
                    "id": "407d6a3a88f849fdb05ed8323513a541",
                    "impid": "201293",
                    "nurl": "https://apse.trk.adserver.com/win?info=VBwgBTXWLrJlH71n3qAqCnhOJsw0wOuQN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4Iyi3B4g6oW6QHD5pV6YHFOxeQjSBSC1uEQ2_dsQgTpJ3FOilHV-mvt2MOTcXmaesdeFefWeF4ip1nE396mGigGRvLDAGTATIzmFIaW2flNLyIIgB_KDU61BG1uTLFj3ExCE_7sydoVqohNJxZN4otphfCS-VuiJ8uMgTEMoRAX2W5moRfHYia8t1Y0lpO4bD71_zZQbjGD3IEV-6aMC1fpW3ezgz0iYXfHcnm0MpjuRwEXq7I-whMXiAh_Orz0jg0-nW-UTsM08ljs6V8U-1ardWpq3xPK8tkpq-eFktG4ZzKDXoRxDWKl_GmB_9KeFCO0dNR6B6ExivzxzBSyUcRNS35q_02B9Q1uoXoxwcVcPjTCqAYcXa29f1I4tK5lQiQHI-2ICd8e_K7yYfao4IbzKPnq5SaF8JA25_xg5IkT89Zsdf1QrkdZU-R49AFFn8wUPS_My5MtLThkVmiDpEwNCp-9nni5ofGIHh6WYvYpwLFtDQ4akndneMh9jKLaOnCL6Ef8bH5GXfvl-04dRoEmfGbuAVLbgGfeeKCTMST037ICnnmk5g0ra1m0YmMZJD0xFPbhnorVch8h64KNfMYrOihDUrucT5sNx3i8DHC9fPOernUJWSe_bz4jlo4GDlAr0gXrf3O-C_2Tuhhn2-QfoOmDOCxG574yD5Uf3DjzqMSknXbKS6UfR08r2U4nCWWsbjozyX_ycrtbsy1qEWrTVDMOQw4NmDc5ChjACdKHl5XzjlCY21ngoP3netf4U3Zc8-ap9d6b5aOXJyridCxfFaX7GgKq1JGlMhgNDKii7dacmKGBMyiKUZWw7IemVen_5I=&price=${AUCTION_PRICE}",
                    "burl": "https://apse.trk.adserver.com/imp?info=VBwgBTXWLrJlH71n3qAqCnhOJsw0wOuQN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4Iyi3B4g6oW6QHD5pV6YHFOxeQjSBSC1uEQ2_dsQgTpJ3FOilHV-mvt2MOTcXmaesdeFefWeF4ip1nE396mGigGRvLDAGTATIzmFIaW2flNLyIIgB_KDU61BG1uTLFj3ExCE_7sydoVqohNJxZN4otphfCS-VuiJ8uMgTEMoRAX2W5moRfHYia8t1Y0lpO4bD71_zZQbjGD3IEV-6aMC1fpW3ezgz0iYXfHcnm0MpjuRwEXq7I-whMXiAh_Orz0jg0-nW-UTsM08ljs6V8U-1ardWpq3xPK8tkpq-eFktG4ZzKDXoRxDWKl_GmB_9KeFCO0dNR6B6ExivzxzBSyUcRNS35q_02B9Q1uoXoxwcVcPjTCqAYcXa29f1I4tK5lQiQHI-2ICd8e_K7yYfao4IbzKPnq5SaF8JA25_xg5IkT89Zsdf1QrkdZU-R49AFFn8wUPS_My5MtLThkVmiDpEwNCp-9nni5ofGIHh6WYvYpwLFtDQ4akndneMh9jKLaOnCL6Ef8bH5GXfvl-04dRoEmfGbuAVLbgGfeeKCTMST037ICnnmk5g0ra1m0YmMZJD0xFPbhnorVch8h64KNfMYrOihDUrucT5sNx3i8DHC9fPOernUJWSe_bz4jlo4GDlAr0gXrf3O-C_2Tuhhn2-QfoOmDOCxG574yD5Uf3DjzqMSknXbKS6UfR08r2U4nCWWsbjozyX_ycrtbsy1qEWrTVDMOQw4NmDc5ChjACdKHl5XzjlCY21ngoP3netf4U3Zc8-ap9d6b5aOXJyridCxfFaX7GgKq1JGlMhgNDKii7dacn-IVdqrKU9B7pkLnxK__NY5&price=${AUCTION_PRICE}",
                    "adm": "{\"native\":{\"ver\":\"1.2\",\"assets\":[{\"id\":123,\"required\":1,\"title\":{\"text\":\"Text Scanner\"}},{\"id\":128,\"required\":1,\"img\":{\"type\":3,\"url\":\"https://ww0.svr-algorix.com/pic/22f6310cc6a0da84c91feae6fbe0a5c0.jpg\",\"w\":1200,\"h\":627}},{\"id\":124,\"required\":1,\"img\":{\"type\":1,\"url\":\"https://ww0.svr-algorix.com/pic/5e52f36602b3e7488ca5b3bbb4c84935.png\",\"w\":50,\"h\":50}},{\"id\":126,\"required\":1,\"data\":{\"type\":1,\"value\":\"Algorix\"}},{\"id\":127,\"required\":1,\"data\":{\"type\":2,\"value\":\"A scanner app, using OCR technology.\"}}],\"link\":{\"url\":\"https://play.google.com/store/apps/details?id=com.bestai.TextScanner&clickid=8FB5__AD_IuzIGWir_uT1O3h_qwDlywr6TF6ivT3QVCztiH98nrJXIz444ThHYnbAP4k6Q7zAu8HJHWBPHLeUIxx7nPUM96HZB_Rm5MdsqYz2YjsvL8L2z5GsPtZ3gnXE0qt4dcmGawMz9yThFJejfAiVEHWDMZmHXI5i4S_stEhmcTsUdFNpaRTOoNk_6dTF2UAMctMfrLt4WywMcofxLZPIB6ZpHPNsSljLbJ11lYsi2hOLqH1_LOF1eH75lF1kvOtkZa6WKYn-vLUeMpsbwzm9gHvc1e_1tWUdPGawj9In4YHFgSluNgDdbMSbJTD8cB0-_E6hCs3y6gwXUsNwCrPiGuF4scLeZudpH_nLB6meZJt0CIQ0m0sXzLEYJ03l8MyH8-CR9lIH5n3Uksbynli1VKE4nmztOtbApC6feI2o_cEmoia-3JAMkxXIxzCOBMx-yCi7eoUsoh8rvhrporI6Xg%3D%3D_{info}&advertising_id=a098ce09-76f8-4aec-9775-4e4fd293e441&af_siteid=36047\",\"clicktrackers\":[\"https://apac.trk.adserver.com/clk?info=info&price={AUCTION_PRICE}\"]},\"imptrackers\":[\"https://apac.trk.adserver.com/imp?info=info&price={AUCTION_PRICE}\"]}}",
                    "cid": "103",
                    "crid": "160_8124",
                    "adomain": [
                        "advertiserdomain.com"
                    ],
                    "price": 0.0313,
                    "lurl": "https://apse.trk.adserver.com/lbr?info=VBwgBTXWLrJlH71n3qAqCnhOJsw0wOuQN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4Iyi3B4g6oW6QHD5pV6YHFOxeQjSBSC1uEQ2_dsQgTpJ3FOilHV-mvt2MOTcXmaesdeFefWeF4ip1nE396mGigGRvLDAGTATIzmFIaW2flNLyIIgB_KDU61BG1uTLFj3ExCE_7sydoVqohNJxZN4otphfCS-VuiJ8uMgTEMoRAX2W5moRfHYia8t1Y0lpO4bD71_zZQbjGD3IEV-6aMC1fpW3ezgz0iYXfHcnm0MpjuRwEXq7I-whMXiAh_Orz0jg0-nW-UTsM08ljs6V8U-1ardWpq3xPK8tkpq-eFktG4ZzKDXoRxDWKl_GmB_9KeFCO0dNR6B6ExivzxzBSyUcRNS35q_02B9Q1uoXoxwcVcPjTCqAYcXa29f1I4tK5lQiQHI-2ICd8e_K7yYfao4IbzKPnq5SaF8JA25_xg5IkT89Zsdf1QrkdZU-R49AFFn8wUPS_My5MtLThkVmiDpEwNCp-9nni5ofGIHh6WYvYpwLFtDQ4akndneMh9jKLaOnCL6Ef8bH5GXfvl-04dRoEmfGbuAVLbgGfeeKCTMST037ICnnmk5g0ra1m0YmMZJD0xFPbhnorVch8h64KNfMYrOihDUrucT5sNx3i8DHC9fPOernUJWSe_bz4jlo4GDlAr0gXrf3O-C_2Tuhhn2-QfoOmDOCxG574yD5Uf3DjzqMSknXbKS6UfR08r2U4nCWWsbjozyX_ycrtbsy1qEWrTVDMOQw4NmDc5ChjACdKHl5XzjlCY21ngoP3netf4U3Zc8-ap9d6b5aOXJyridCxfFaX7GgKq1JGlMhgNDKii7dac2KGBMyiKUZWw7IemVen_5I=&price=${AUCTION_PRICE}&loss=${AUCTION_LOSS}",
                    "bundle": "com.advertiserdomain.android",
                    "w": 1200,
                    "h": 627
                }
            ],
            "seat": "Zh2Kghgjfghyo="
        }
    ]
}
```

## Video Request Sample

```JSON
{
    "id": "1234567893",
    "at": 1,
    "tmax": 120,
    "imp": [
        {
            "id": "1",
            "bidfloor": 0.03,
            "video": {
                "w": 720,
                "h": 1280,
                "pos": 1,
                "startdelay": 0,
                "minduration": 5,
                "maxduration": 30,
                "maxextended": 30,
                "minbitrate": 300,
                "maxbitrate": 1500,
                "api": [
                    1,
                    2
                ],
                "protocols": [
                    2,
                    3
                ],
                "mimes": [
                    "video/x-flv",
                    "video/mp4",
                    "application/x-shockwave-flash",
                    "application/javascript"
                ],
                "linearity": 1,
                "boxingallowed": 1,
                "playbackmethod": [
                    1,
                    3
                ],
                "delivery": [
                    2
                ],
                "battr": [
                    13,
                    14
                ],
                "companionad": [
                    {
                        "id": "1234567893-1",
                        "w": 720,
                        "h": 1280,
                        "pos": 1,
                        "battr": [
                            13,
                            14
                        ],
                        "expdir": [
                            2,
                            4
                        ]
                    }
                ],
                "companiontype": [
                    1,
                    2
                ]
            }
        }
    ],
    "app": {
        "ver": "3.16.50",
        "paid": 0,
        "keywords": "Text Scanner",
        "storeurl": "https://play.google.com/store/apps/details?id=com.bestai.scannerlite",
        "id": "1103",
        "name": "Text Scanner",
        "domain": "algorix.co",
        "bundle": "com.bestai.scannerlite",
        "publisher": {
		"id": "1"
	}
    },
    "device": {
        "carrier": "VERIZON",
        "devicetype": 4,
        "ua": "Mozilla/5.0 (Linux; Android 7.0; SM-A310F Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.132 Mobile Safari/537.36",
        "ip": "***************",
        "dpidmd5": "1822e31ea5956736d81cc9f069182e37",
        "geo": {
          "lat": 35.012345,
          "lon": -115.12345,
          "country": "USA",
          "region": "CA",
          "city": "Los Angeles",
          "zip": "90049"
        },
        "js": 1,
        "connectiontype": 2,
        "macsha1": "b5a6abf843a35392e1dc81b8c00a740d64aa31be",
        "make": "samsung",
        "os": "Android",
        "dnt": 0,
        "ifa": "a098ce09-76f8-4aec-9775-4e4fd293e441",
        "macmd5": "20dd47a5c710e7af4120f033fd3a8a4b",
        "osv": "7.0",
        "dpidsha1": "7a97e4312aa4496ebb68ff05c25ea2d54eb6bfec",
        "model": "SM-A310F"
    },
    "user": {
        "id": "456789876567897654678987656789",
        "buyeruid": "545678765467876567898765678987654"
    },
    "source": {
		"ext": {
			"omidpn": "Rixengine",
			"omidpv": "1.4.2"
		}
	},
    "regs": {
	      "ext": {
		      "gdpr": 0
		}
	}
}
```

## Video Response Sample

```JSON
{
    "id": "1234567893",
    "bidid": "d81a50f9be554aa1bd63e0bda1b79de1",
    "cur": "USD",
    "seatbid": [
        {
            "bid": [
                {
                    "id": "efc6921eebdc462c9f14b840dbbad438",
                    "impid": "201294dfdadfsaf",
                    "nurl": "https://apse.trk.adserver.com/win?info=VBwgBTXWLrJlH71n3qAqCnhOJsw0wOuQN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4Iyi3B4g6oW6QHD5pV6YHFOxeQjSBSC1uEQ2_dsQgTpJ3FOilHV-mvt2MOTcXmaesdeFefWeF4ip1nE396mGigGRvLDAGTATIzmFIaW2flNLyIIgB_KDU61BG1uTLFj3ExCE_7sydoVqohNJxZN4otphfCS-VuiJ8uMgTEMoRAX2W5moRfHYia8t1Y0lpO4bD71_zZQbjGD3IEV-6aMC1fpW3ezgz0iYXfHcnm0MpjuRwEXq7I-whMXiAh_Orz0jg0-nW-UTsM08ljs6V8U-1ardWpq3xPK8tkpq-eFktG4ZzKDXoRxDWKl_GmB_9KeFCO0dNR6B6ExivzxzBSyUcRNS35q_02B9Q1uoXoxwcVcPjTCqAYcXa29f1I4tK5lQiQHI-2ICd8e_K7yYfao4IbzKPnq5SaF8JA25_xg5IkT89Zsdf1QrkdZU-R49AFFn8wUPS_My5MtLThkVmiDpEwNCp-9nni5ofGIHh6WYvYpwLFtDQ4akndneMh9jKLaOnCL6Ef8bH5GXfvl-04dRoEmfGbuAVLbgGfeeKCTMST037ICnnmk5g0ra1m0YmMZJD0xFPbhnorVch8h64KNfMYrOihDUrucT5sNx3i8DHC9fPOernUJWSe_bz4jlo4GDlAr0gXrf3O-C_2Tuhhn2-QfoOmDOCxG574yD5Uf3DjzqMSknXbKS6UfR08r2U4nCWWsbjozyX_ycrtbsy1qEWrTVDMOQw4NmDc5ChjACdKHl5XzjlCY21ngoP3netf4U3Zc8-ap9d6b5aOXJyridCxfFaX7GgKq1JGlMhgNDKii7dacmKGBMyiKUZWw7IemVen_5I=&price=${AUCTION_PRICE}",
                    "burl": "https://apse.trk.adserver.com/imp?info=VBwgBTXWLrJlH71n3qAqCnhOJsw0wOuQN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4Iyi3B4g6oW6QHD5pV6YHFOxeQjSBSC1uEQ2_dsQgTpJ3FOilHV-mvt2MOTcXmaesdeFefWeF4ip1nE396mGigGRvLDAGTATIzmFIaW2flNLyIIgB_KDU61BG1uTLFj3ExCE_7sydoVqohNJxZN4otphfCS-VuiJ8uMgTEMoRAX2W5moRfHYia8t1Y0lpO4bD71_zZQbjGD3IEV-6aMC1fpW3ezgz0iYXfHcnm0MpjuRwEXq7I-whMXiAh_Orz0jg0-nW-UTsM08ljs6V8U-1ardWpq3xPK8tkpq-eFktG4ZzKDXoRxDWKl_GmB_9KeFCO0dNR6B6ExivzxzBSyUcRNS35q_02B9Q1uoXoxwcVcPjTCqAYcXa29f1I4tK5lQiQHI-2ICd8e_K7yYfao4IbzKPnq5SaF8JA25_xg5IkT89Zsdf1QrkdZU-R49AFFn8wUPS_My5MtLThkVmiDpEwNCp-9nni5ofGIHh6WYvYpwLFtDQ4akndneMh9jKLaOnCL6Ef8bH5GXfvl-04dRoEmfGbuAVLbgGfeeKCTMST037ICnnmk5g0ra1m0YmMZJD0xFPbhnorVch8h64KNfMYrOihDUrucT5sNx3i8DHC9fPOernUJWSe_bz4jlo4GDlAr0gXrf3O-C_2Tuhhn2-QfoOmDOCxG574yD5Uf3DjzqMSknXbKS6UfR08r2U4nCWWsbjozyX_ycrtbsy1qEWrTVDMOQw4NmDc5ChjACdKHl5XzjlCY21ngoP3netf4U3Zc8-ap9d6b5aOXJyridCxfFaX7GgKq1JGlMhgNDKii7dacn-IVdqrKU9B7pkLnxK__NY5&price=${AUCTION_PRICE}",
                    "adm": "<VAST version=\"2.0\">content</VAST>",
                    "cid": "227",
                    "crid": "515_2142",
                    "adomain": [
                        "advertiserdomain.com"
                    ],
                    "price": 0.0313,
                    "lurl": "https://apse.trk.adserver.com/lbr?info=VBwgBTXWLrJlH71n3qAqCnhOK8M0xeeUN1a5xL_7kyooAjRxYqs5o6YIfAwmCdxzlZjKrEOns0h2L6GiXnBdJ57xbW6coK_4rO7pC3gYc3gUkYY-molq2s8VZUM8c2SuLdVZmHsl53pvqmeOtxC-RFXAkYihp6WlCX4FxSzE6Q2oW6QHD5pV6YHFOxCaj3IDXgmEQDfV4whDp8_GMXtCAOj8tmABH5Diab0ceFefWeF4ip1nE396mGigGRLJXFCVB2Y-kgIJQSCjMKGyYUo6KyYy1Vjx-zmC4itjGxes4CQ_SLpwfdJFM4EtuwqQFPRiioc7akjSGJRBDwDhzpJQAprfp4MYlUlh-Iu7mKGLHemuSjREGLWcLR8YoyqXiV2rwZmAXMn4zvBzoBsUV-m7sAMdEiB4sbDzgH9rrHWzdnMQwsVsqeI0DKgMqZPa_npYKYF8vq-CFkVIopnxOnkX0TCfwemtB-tYNRa1w8xc_xP5iGOxkiJE3wpXGSDwrv8sLJRm6Oi5h0oeZ6yAWet3NiPk8qoLs4zEhASYL4yoECpnPZfti9mZ88JUheilx9bdAehL25vLj44lMpMF8Im3VLYBaRzIuNcCMH0wYOmpGANbp6fqiknlFpsuKypi4C6noLrDK3RrY86Q-F-G2HduMA7AgOdxqV3XPK6xf6IC_IGrUS-8wuMgNUZbzKncy0pXYgWBPofBBZC-xGzjAH-gi5Mmr7Nzg9fBcJzzxF3Gi3tlTdB4mrknYrFz_aPpDQKpM1E9YF-k7jGTkLLffLDYcyfVqujGmApkSGkD5F5Q8oDc7DjNVuIcxzidfoLwGLL4Spr42E90dVfk-KwfmWmcYTyUfQw5iV4jnhudquO35iWpmJyueszihFbmTw-LJwU-ISuQsVsnEyx4WAsKg3JfJDtQxYXNmv4WoRKTeg==&price=${AUCTION_PRICE}&loss=${AUCTION_LOSS}",
                    "bundle": "com.advertiserdomain.android",
                    "w": 720,
                    "h": 1280
                }
            ],
            "seat": "Zhdds2Kgyo="
        }
    ]
}
```

# Appendix

## IAB Category Type

Refer to [IAB Category Type](https://iabtechlab.com/wp-content/uploads/2016/07/OpenRTB-API-Specification-Version-2-5-FINAL.pdf)([Open RTB Document](https://iabtechlab.com/wp-content/uploads/2016/07/OpenRTB-API-Specification-Version-2-5-FINAL.pdf) Section 5.1).

## Banner Ad Types

The following table indicates the types of ads that can be accepted by the exchange unless restricted by publisher site settings.

| Value | Description                                                     |
| ----- | --------------------------------------------------------------- |
| 1     | XHTML Text Ad (usually mobile)                                  |
| 2     | XHTML Banner Ad. (usually mobile)                               |
| 3     | JavaScript Ad; must be valid XHTML (i.e., Script Tags Included) |
| 4     | iframe                                                          |

## Creative Attributes

The following table specifies a standard list of creative attributes that can describe an ad being served or serve as restrictions of thereof.

| Value | Description                                                                 |
| ----- | --------------------------------------------------------------------------- |
| 1     | Audio Ad (Auto-Play)                                                        |
| 2     | Audio Ad (User Initiated)                                                   |
| 3     | Expandable (Automatic)                                                      |
| 4     | Expandable (User Initiated - Click)                                         |
| 5     | Expandable (User Initiated - Rollover)                                      |
| 6     | In-Banner Video Ad (Auto-Play)                                              |
| 7     | In-Banner Video Ad (User Initiated)                                         |
| 8     | Pop (e.g., Over, Under, or Upon Exit)                                       |
| 9     | Provocative or Suggestive Imagery                                           |
| 10    | Shaky, Flashing, Flickering, Extreme Animation, Smileys                     |
| 11    | Surveys                                                                     |
| 12    | Text Only                                                                   |
| 13    | User Interactive (e.g., Embedded Games)                                     |
| 14    | Windows Dialog or Alert Style                                               |
| 15    | Has Audio On/Off Button                                                     |
| 16    | Ad Provides Skip Button (e.g. VPAID-rendered skip button on pre-roll video) |
| 17    | Adobe Flash                                                                 |

## Content Delivery Methods

The following table lists the various options for the delivery of video or audio content.

| Value | Description |
| ----- | ----------- |
| 1     | Streaming   |
| 2     | Progressive |
| 3     | Download    |

## Ad Position

The following table specifies the position of the ad as a relative measure of visibility or prominence. This OpenRTB table has values derived from the Inventory Quality Guidelines (IQG). Practitioners should keep in sync with updates to the IQG values as published on IAB.com. Values “4” - “7” apply to apps per the mobile addendum to IQG version 2.1.

| Value | Description                                                                           |
| ----- | ------------------------------------------------------------------------------------- |
| 0     | Unknown                                                                               |
| 1     | Above the Fold                                                                        |
| 2     | DEPRECATED - May or may not be initially visible depending on screen size/resolution. |
| 3     | Below the Fold                                                                        |
| 4     | Header                                                                                |
| 5     | Footer                                                                                |
| 6     | Sidebar                                                                               |
| 7     | Full Screen                                                                           |

## API Frameworks

The following table is a list of API frameworks supported by the publisher. There are more api frameworks. Check this [document](https://github.com/InteractiveAdvertisingBureau/AdCOM/blob/master/AdCOM%20v1.0%20FINAL.md#list--api-frameworks-).

| Value | Description |
| ----- | ----------- |
| 1     | VPAID 1.0   |
| 2     | VPAID 2.0   |
| 3     | MRAID-1     |
| 4     | ORMMA       |
| 5     | MRAID-2     |
| 6     | MRAID-3     |

## Protocols

The following table lists the options for the various bid response protocols that could be supported by an exchange.

| Value | Description       |
| ----- | ----------------- |
| 1     | VAST 1.0          |
| 2     | VAST 2.0          |
| 3     | VAST 3.0          |
| 4     | VAST 1.0 Wrapper  |
| 5     | VAST 2.0 Wrapper  |
| 6     | VAST 3.0 Wrapper  |
| 7     | VAST 4.0          |
| 8     | VAST 4.0 Wrapper  |
| 9     | DAAST 1.0         |
| 10    | DAAST 1.0 Wrapper |

## Start Delay

The following table lists the various options for the video or audio start delay. If the start delay value is greater than 0, then the position is mid-roll and the value indicates the start delay.

| Value | Description                                      |
| ----- | ------------------------------------------------ |
| > 0   | Mid-Roll (value indicates start delay in second) |
| 0     | Pre-Roll                                         |
| -1    | Generic Mid-Roll                                 |
| -2    | Generic Post-Roll                                |

## Video Linearity

The following table indicates the options for video linearity. “In-stream” or “linear” video refers to preroll, post-roll, or mid-roll video ads where the user is forced to watch ad in order to see the video content. “Overlay” or “non-linear” refer to ads that are shown on top of the video content. This OpenRTB table has values derived from the Inventory Quality Guidelines (IQG). Practitioners should keep in sync with updates to the IQG values.

| Value | Description          |
| ----- | -------------------- |
| 1     | Linear / In-Stream   |
| 2     | Non-Linear / Overlay |

## Playback Methods

The following table lists the various playback methods.

| Value | Description                                              |
| ----- | -------------------------------------------------------- |
| 1     | Initiates on Page Load with Sound On                     |
| 2     | Initiates on Page Load with Sound Off by Default         |
| 3     | Initiates on Click with Sound On                         |
| 4     | Initiates on Mouse-Over with Sound On                    |
| 5     | Initiates on Entering Viewport with Sound On             |
| 6     | Initiates on Entering Viewport with Sound Off by Default |

## Playback Cessation Modes

The following table lists the various modes for when playback terminates.

| Value | Description                                                                                               |
| ----- | --------------------------------------------------------------------------------------------------------- |
| 1     | On Video Completion or when Terminated by User                                                            |
| 2     | On Leaving Viewport or when Terminated by User                                                            |
| 3     | On Leaving Viewport Continues as a Floating/Slider Unit until Video Completion or when Terminated by User |

## Companion Types

The following table lists the options to indicate markup types allowed for companion ads that apply to

video and audio ads. This table is derived from VAST 2.0+ and DAAST 1.0 specifications. Refer to

www.iab.com/guidelines/digital-video-suite for more information.

| Value | Description     |
| ----- | --------------- |
| 1     | Static Resource |
| 2     | HTML Resource   |
| 3     | iframe Resource |

## Device Type

The following table lists the type of device from which the impression originated. OpenRTB version 2.2 of the specification added distinct values for Mobile and Tablet. It is recommended that any bidder adding support for 2.2 treat a value of 1 as an acceptable alias of 4 & 5. This OpenRTB table has values derived from the Inventory Quality Guidelines (IQG). Practitioners should keep in sync with updates to the IQG values.

| Value | Description       | Notes               |
| ----- | ----------------- | ------------------- |
| 1     | Mobile/Tablet     | Version 2.0         |
| 2     | Personal Computer | Version 2.0         |
| 3     | Connected TV      | Version 2.0         |
| 4     | Phone             | New for Version 2.2 |
| 5     | Tablet            | New for Version 2.2 |
| 6     | Connected Device  | New for Version 2.2 |
| 7     | Set Top Box       | New for Version 2.2 |

## Connection Type

The following table lists the various options for the type of device connectivity.

| Value | Description                           |
| ----- | ------------------------------------- |
| 0     | Unknown                               |
| 1     | Ethernet                              |
| 2     | WIFI                                  |
| 3     | Cellular Network – Unknown Generation |
| 4     | Cellular Network – 2G                 |
| 5     | Cellular Network – 3G                 |
| 6     | Cellular Network – 4G                 |
