/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-04-27 15:59:57
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-07 15:25:04
 * @Description:
 */
import Router from 'koa-router';
import { qpsController } from '@/controllers';
import { validTesting } from '@/middleware';

const router = new Router({ prefix: '/qps' });

const routers = router
  .post('/addQps', validTesting, qpsController.addQps)
  .post('/updateQps', qpsController.updateQps)
  .post('/getQpsList', qpsController.getQpsList);

export default routers;
