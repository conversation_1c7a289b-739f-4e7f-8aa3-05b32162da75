/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-06 18:36:44
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-13 14:55:10
 * @Description:
 */
import { Context } from 'koa';

declare namespace ProfitAPI {
  type ProfitListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    bundle: string; 
    type: number;
    profit_ratio: number;
    status: number;
    op_id: number;
    account_name: string;
    create_time: string;
    update_time: string;
    account_status: number;
    seller_status: number;
    seller_name: string;
    buyer_name: string;
    tmp_id?: number;
    name?: string;
    children?: ProfitListItem[];
    // 标记位：判断是否存在更高优先级的配置（只针对 adv + pub 类型， 通过判断是否存在更高优先级的配置）
    exist_higher_priority?: boolean;
  };
  type AddProfitParams = {
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
    type: number;
    seller_id: number | number[];
    buyer_id: number | number[];
  };

  type UpdateProfitParams = {
    id: number;
    status: number;
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
  };

  type AddBundleProfitParams = {
    buyer_id: number;
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
    type: number;
    seller_id?: number;
    bundle: string | string[];
  }

  interface Profit {
    addProfit(params: AddProfitParams): Promise<boolean>;
    updateProfit(params: UpdateProfitParams): Promise<boolean>;
  }
  interface ProfitCtrlInterface {
    getProfitList(ctx: Context): void;
    addProfit(ctx: Context): void;
    updateProfit(ctx: Context): void;
  }
}
