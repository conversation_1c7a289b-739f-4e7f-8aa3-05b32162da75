/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-20 17:18:10
 * @Description:
 */
import styles from './index.less';
import React from 'react';
import { G2 } from '@ant-design/charts';
import Breadcrumb from '@/components/Breadcrumb';
import Overview from '../components/Overview';
import TopCountry from '../components/TopCountry';
import SupplyDemand from '../components/SupplyDemand';
import EcpmAndEcpr from '../components/EcpmAndEcpr';
import { OverviewBreadOptions } from '@/constants/ree-ai/dashboard';
const Page: React.FC = () => {
  const { registerTheme } = G2;
  registerTheme('custom-theme', {
    colors10: ['#126BF0', '#1CAF34', '#6C2CEB', '#FF9012', '#0CAFC7', '#C1CBCC']
  });
  return (
    <div className={styles['top-container']}>
      <Breadcrumb options={OverviewBreadOptions} separator=">" className="dashbaord-breadcrumb" />
      <Overview />
      <TopCountry />
      <SupplyDemand></SupplyDemand>
      <EcpmAndEcpr />
    </div>
  );
};

export default Page;
