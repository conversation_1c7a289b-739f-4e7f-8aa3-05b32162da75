/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 15:14:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-20 16:49:45
 * @Description:
 */
import { request } from '@umijs/max';

export async function getOverview(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.OverviewItem>>('/api/ai-board/getOverview', {
    data: body || {},
    ...(options || {})
  });
}

export async function getTopCountry(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.TopCountryItem>>('/api/ai-board/getTopCountry', {
    data: body || {},
    ...(options || {})
  });
}

export async function getBudgetAndTraffic(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.BudgetAndTrafficItem>>('/api/ai-board/getBudgetAndTraffic', {
    data: body || {},
    ...(options || {})
  });
}

export async function trafficRequest(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.BudgetAndTrafficItem>>('/api/ai-board/trafficRequest', {
    data: body || {},
    ...(options || {})
  });
}

// getTffReqLog
export async function getTffReqLog(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.TrafficRequestLog>>('/api/ai-board/getTffReqLog', {
    data: body || {},
    ...(options || {})
  });
}

export async function getSupplyDemand(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.SupplyDemandItem>>('/api/ai-board/getSupplyDemand', {
    data: body || {},
    ...(options || {})
  });
}

export async function getTopAdFormatEcpmAndEcpr(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<BoardAPI.EcpmAndEcprItem>>('/api/ai-board/getTopAdFormatEcpmAndEcpr', {
    data: body || {},
    ...(options || {})
  });
}
