# 🌟MaxJS-Tag对接文档说明🌟 

在配置Max-JSTag之前，需在RixEngine平台上生成Ad Tag，生成后即可在Max后台进行配置

# RixEngine平台配置

## Step1:  在RixEngineSaaS平台创建相关信息

1. 登陆 **RixEngine SaaS** 平台,填写相关信息，生成对应的Publisher

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.1-1.png)



## Step2: 【Developer】模块创建App和ad Placements

* **注意：一个开发者(Apps)仅创建一个虚拟App，对接多个不同的开发者可分别创建Publisher**

创建虚拟App，生成开发者对应的app id

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.2-1.png)

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.2-2.png)

## Step3:创建开发者不同App对应的Ad Format和相关Bid Floor

例如：

命名规则

Ocrscanner_IOS_320 * 50Banner_Low/Mid/High,
Ocrscanner_And_320 * 50Banner_0.2/0.5/0.8

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.3-1.png)

## Step4:为开发者生成对应的Js-Tag，不同尺寸需重新生成（尺寸不支持宏替换

**Js tag入口**：

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.4-1.png)

**生成JsTag代码（注意：不同尺寸生成不同Js Tag）**：

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.4-2.png)





# Max后台配置

## Step1:创建一个Custom Network

**路径** ： MAX > Mediation > Manage > Networks 点击【**Click here to add a Custom Network**】

![](https://static.rixengine.com/a/platform/help/maxjs-tag/2.1-1.png)

进入页面后选择Network Type，**且粘贴RixEngine平台生成的****Js****Tag代码**

**注意：RixEngine生成不同尺寸（Ad Format）的****Js****Tag需与Max后台Ad Format对应**

![](https://static.rixengine.com/a/platform/help/maxjs-tag/2.1-2.png)

# **启动配置生效**

## **Step1:配置Max相关配置**

* 路径：MAX > Mediation > Manage > Ad Units

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-1.png)

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-2.png)

将RixEngine平台生成的Unit ID填入对应的Placement ID，针对你需求的国家设置对应的CPM

**注意**：确保你的Placement ID与RixEngine平台是一致的

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-3.png)



