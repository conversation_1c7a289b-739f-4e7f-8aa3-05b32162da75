.toc-anchor {
  :global(.ant-anchor-ink-ball) {
    position: absolute;
    width: 2px;
    height: 24px;
    transform: translate(-50%, -30%);
    transition: top 0.3s ease-in-out;
    left: 1px;
  }

  :global {
    .ant-anchor-link-active > .ant-anchor-link-title {
      color: var(--primary-color);
    }

    .ant-anchor-ink-ball.ant-anchor-ink-ball-visible {
      display: inline-block;
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
}
