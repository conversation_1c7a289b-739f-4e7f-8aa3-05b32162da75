import { ProfitType, StatusMap } from '@/constants';
import { ProfitAPI } from '@/types/profit';

// 支持的利润类型
export type ProfitTabType = 'publisher' | 'advertiser' | 'bundle';

// 维护 tab type 和 profit_types 的映射关系
export const TabTypeToProfitTypes: Record<ProfitTabType, number[]> = {
  publisher: [
    ProfitType.Seller,
    ProfitType['Seller-Demand'],
    ProfitType.DemandBundle,
    ProfitType.DemandSellerBundle
  ],
  advertiser: [
    ProfitType.Demand,
    ProfitType['Seller-Demand'],
    ProfitType.DemandBundle,
    ProfitType.DemandSellerBundle
  ],
  bundle: [ProfitType.DemandBundle, ProfitType.DemandSellerBundle]
} as const;

// Bundle信息查询接口
export interface BundleInfo {
  /** 检查是否存在指定key的bundle配置 */
  hasBundleForKey: (key: string | number) => boolean;
  /** 检查是否存在buyer与seller的组合bundle */
  hasCombinedBundle: (
    buyerId: string | number,
    sellerId: string | number
  ) => boolean;
}

// 数据处理策略配置接口
interface ProcessStrategy {
  /** 父级数据类型 */
  parentType: number;
  /** 子级数据类型 */
  childType: number;
  /** 获取父级数据的关联键 */
  getParentKey: (item: ProfitAPI.ProfitListItem) => string | number;
  /** 获取子级数据的关联键 */
  getChildKey: (item: ProfitAPI.ProfitListItem) => string | number;
  /** 转换父级数据格式 */
  transformParent: (
    item: ProfitAPI.ProfitListItem,
    bundleInfo: BundleInfo
  ) => ProfitAPI.ProfitListItem;
  /** 转换子级数据格式 */
  transformChild: (
    item: ProfitAPI.ProfitListItem,
    bundleInfo: BundleInfo
  ) => ProfitAPI.ProfitListItem;
  /** 可选的父级数据过滤条件 */
  filterParent?: (item: ProfitAPI.ProfitListItem) => boolean;
}

/**
 * 检查是否存在更高优先级的配置
 */
const checkHigherPriority = (
  item: ProfitAPI.ProfitListItem,
  bundleInfo: BundleInfo
): boolean => {
  const buyerHasBundle = bundleInfo.hasBundleForKey(item.buyer_id);
  const hasCombinedBundle = bundleInfo.hasCombinedBundle(
    item.buyer_id,
    item.seller_id
  );
  return buyerHasBundle || hasCombinedBundle;
};

// 各类型的数据处理策略
export const ProcessStrategies: Record<
  Exclude<ProfitTabType, 'bundle'>,
  ProcessStrategy
> = {
  advertiser: {
    parentType: ProfitType.Demand,
    childType: ProfitType['Seller-Demand'],
    getParentKey: item => item.buyer_id,
    getChildKey: item => item.buyer_id,
    transformParent: (item, bundleInfo) => ({
      ...item,
      name: item.buyer_name,
      tmp_id: item.buyer_id,
      exist_higher_priority: bundleInfo.hasBundleForKey(item.buyer_id)
    }),
    transformChild: (item, bundleInfo) => ({
      ...item,
      name: item.seller_name,
      tmp_id: item.seller_id,
      exist_higher_priority: checkHigherPriority(item, bundleInfo)
    })
  },
  publisher: {
    parentType: ProfitType.Seller,
    childType: ProfitType['Seller-Demand'],
    getParentKey: item => item.seller_id,
    getChildKey: item => item.seller_id,
    transformParent: (item, bundleInfo) => ({
      ...item,
      name: item.seller_name,
      tmp_id: item.seller_id
    }),
    transformChild: (item, bundleInfo) => ({
      ...item,
      name: item.buyer_name,
      tmp_id: item.buyer_id,
      exist_higher_priority: checkHigherPriority(item, bundleInfo)
    }),
    filterParent: item => item.seller_status !== 3
  }
} as const;

/**
 * 通用的层级数据处理函数
 * 将扁平的数据列表转换为父子层级结构
 *
 * @param list 原始数据列表
 * @param strategy 处理策略配置
 * @returns 处理后的层级数据列表
 */
export function processHierarchicalData(
  list: ProfitAPI.ProfitListItem[],
  strategy: ProcessStrategy
): ProfitAPI.ProfitListItem[] {
  const {
    parentType,
    childType,
    getParentKey,
    getChildKey,
    transformParent,
    transformChild,
    filterParent
  } = strategy;

  // 一次遍历完成所有数据处理
  const parentMap = new Map<string | number, ProfitAPI.ProfitListItem>();
  const childrenMap = new Map<string | number, ProfitAPI.ProfitListItem[]>();

  // 存储bundle信息的高效数据结构
  const bundleKeys = new Set<string | number>();
  const combinedBundleKeys = new Set<string>();

  // Bundle信息查询对象
  const bundleInfo: BundleInfo = {
    // 存储的是 buyer_id
    // 判断依据 type 的优先级：adv+pub(子项) < adv+bundle < adv+pub+bundle
    hasBundleForKey: (key: string | number) => bundleKeys.has(key),
    hasCombinedBundle: (buyerId: string | number, sellerId: string | number) =>
      combinedBundleKeys.has(`${buyerId}/${sellerId}`)
  };

  // 第一次遍历：收集bundle信息
  for (const item of list) {
    if (
      item.status === StatusMap.Active &&
      (item.type === ProfitType.DemandBundle ||
        item.type === ProfitType.DemandSellerBundle)
    ) {
      bundleKeys.add(item.buyer_id);
      combinedBundleKeys.add(`${item.buyer_id}/${item.seller_id}`);
    }
  }

  // 第二次遍历：构建父子层级结构
  for (const item of list) {
    if (item.type === parentType) {
      // 应用父级过滤条件（如果存在）
      if (!filterParent || filterParent(item)) {
        const parentKey = getParentKey(item);
        parentMap.set(parentKey, transformParent(item, bundleInfo));
      }
    } else if (item.type === childType) {
      const parentKey = getChildKey(item);
      const childItem = transformChild(item, bundleInfo);

      if (!childrenMap.has(parentKey)) {
        childrenMap.set(parentKey, []);
      }
      childrenMap.get(parentKey)?.push(childItem);
    }
  }

  // 组合父子数据
  return Array.from(parentMap.entries()).map(([key, parent]) => ({
    ...parent,
    children: childrenMap.get(key) || []
  }));
}
