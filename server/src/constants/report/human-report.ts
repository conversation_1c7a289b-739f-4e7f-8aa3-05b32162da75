/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-01 15:00:27
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-29 11:07:10
 * @Description:
 */
export const HumanDimensions = [
  'day',
  'month',
  'buyer_id',
  'seller_id',
  'bundle',
  'domain',
  'seat',
  'country',
  'publisher_id'
];

export const HumanOrderKey = [
  'day',
  'month',
  'buyer_id',
  'seller_id',
  'total_events',
  'valid_traffic',
  'sivt',
  'givt',
  'ivt_rate',
  'sivt_rate',
  'givt_rate',
  'valid_traffic_rate'
];

export const HumanMetricOrderKey = [
  'total_events',
  'valid_traffic',
  'sivt',
  'givt',
  'ivt_rate',
  'sivt_rate',
  'givt_rate',
  'valid_traffic_rate'
];
export const HumanDefaultMetrics = [
  'total_events',
  'valid_traffic',
  'sivt',
  'givt',
  'ivt_rate'
];
// model层里面的计算sql
export const getCalculationSQL = (metric: string) => {
  const sql = {
    // seat: "coalesce(seat, '') as seat",
    total_events: 'coalesce(sum(total_events), 0) as total_events',
    // publisher_id: 'coalesce(publisher_id, "") as publisher_id',
    // country: 'coalesce(country, "") as country',
    ivt_rate:
      '(case when sum(total_events) = 0 then 0 else round((sum(sivt) + sum(givt)) * 100 / sum(total_events),2) end) as ivt_rate',
    sivt: 'coalesce(sum(sivt), 0) as sivt',
    sivt_rate:
      '(case when sum(total_events) = 0 then 0 else round(sum(sivt) * 100 *1.0 / sum(total_events), 2) end) as sivt_rate',

    givt: 'coalesce(sum(givt), 0) as givt',
    givt_rate:
      '(case when sum(total_events) = 0 then 0 else round(sum(givt) * 100 *1.0 / sum(total_events), 2) end) as givt_rate',
    valid_traffic: 'coalesce(sum(valid_traffic), 0) as valid_traffic',
    valid_traffic_rate:
      '(case when sum(total_events) = 0 then 0 else round(sum(valid_traffic) * 100 *1.0 / sum(total_events), 2) end) as valid_traffic_rate',
    sivt_automated_browsing:
      '(case when sum(sivt) = 0 then 0 else round(sum(sivt_automated_browsing) * 100 *1.0 / sum(sivt), 2) end) as sivt_automated_browsing',
    sivt_false_representation:
      '(case when sum(sivt) = 0 then 0 else round(sum(sivt_false_representation) * 100 *1.0 / sum(sivt), 2) end) as sivt_false_representation',
    sivt_manipulated_behavior:
      '(case when sum(sivt) = 0 then 0 else round(sum(sivt_manipulated_behavior) * 100 *1.0 / sum(sivt), 2) end) as sivt_manipulated_behavior',
    sivt_misleading_user_interface:
      '(case when sum(sivt) = 0 then 0 else round(sum(sivt_misleading_user_interface) * 100 *1.0 / sum(sivt), 2) end) as sivt_misleading_user_interface',
    sivt_undisclosed_classification:
      '(case when sum(sivt) = 0 then 0 else round(sum(sivt_undisclosed_classification) * 100 *1.0 / sum(sivt), 2) end) as sivt_undisclosed_classification',
    givt_data_center:
      '(case when sum(givt) = 0 then 0 else round(sum(givt_data_center) * 100 *1.0 / sum(givt), 2) end) as givt_data_center',
    givt_irregular_pattern:
      '(case when sum(givt) = 0 then 0 else round(sum(givt_irregular_pattern) * 100 *1.0 / sum(givt), 2) end) as givt_irregular_pattern',
    givt_known_crawler:
      '(case when sum(givt) = 0 then 0 else round(sum(givt_known_crawler) * 100 *1.0 / sum(givt), 2) end) as givt_known_crawler',
    givt_false_representation:
      '(case when sum(givt) = 0 then 0 else round(sum(givt_false_representation) * 100 *1.0 / sum(givt), 2) end) as givt_false_representation',
    givt_misleading_user_interface:
      '(case when sum(givt) = 0 then 0 else round(sum(givt_misleading_user_interface) * 100 *1.0 / sum(givt), 2) end) as givt_misleading_user_interface'
  };

  return sql[metric as keyof typeof sql];
};
