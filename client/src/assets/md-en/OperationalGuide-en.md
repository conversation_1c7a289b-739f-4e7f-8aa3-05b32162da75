**This operational guide aims to provide operational suggestions to RixEngine SaaS console users on core issues, assist users in improving operational efficiency, and increase performance.**

![image-20231027172835306](https://static.rixengine.com/a/platform/help/operational-guide-en/1.png)

# 1 Live test at the early stage

- Inventory volume for both publisher and advertiser should be kept within **200QPS** when just onboarded. Live test is usually performed at **100QPS**. During live tests, data comparison must be done to check for any discrepancy. After live tests, data should also be compared regularly. If data discrepancy exceeds **5%**, root causes should be investigated first, and QPS should be reduced to minimize platform losse due to data discrepancy.

# 2 Data Comparison

- Pay extra attention to the signed contract with your demand and supply partners regarding data discrepancy settlement, confirm which party's data shall prevail in case of data discrepancy. Generally, monthly settlement amount with data discrepancy within 5% can be based on the recorded data of either party. If data discrepancy exceeds 5%, both parties will bear half of it. Discrepancy settlement shall be subject to the actual contract.

## 2.1 Data Comparison

### 2.1.1 Comparing with advertisers

- Key metrics for comparing data with your advertisers are **Advertiser Net Revenue, Request and Impression**, and data discrepancies usually should not exceed **5%**. It should be noted that when total revenue is low, you might see a larger discrepancy percentage. In this case, discrepancy between **Request** and **Impression** can be checked as the main focus with the advertiser.

### 2.1.2 Comparing with publishers

- Key metrics for comparing data with publishers are **Publisher Net Revenue, Total Request and Impression**, and data discrepancies usually should not exceed **5%**. It should be noted that when total revenue is low, you might see a larger discrepancy percentage. In this case, discrepancy between **Request** and **Impression** can be checked as the main focus with the publisher.

### 2.1.3 Cadence of data comparison

- At early stage of publisher and advertiser onboarding, data needs to be checked daily. After the integration is stable (historical data discrepancy is less than **5%**), it is recommended to check it weekly. If the discrepancy is large, it is recommended to increase the cadence of data comparison and investigate the cause of the discrepancy by comparing data in different dimensions, such as **Country/Ad Format/Ad Size/Bundle**.

# 3 Settlement Risk

## 3.1 Settlement Deduction

- It should be noted whether the advertiser has any requirements in **traffic quality IVT**. When sending inventory to advertisers and agencies with such requirements, keep the **IVT metrics** monitored regularly to avoid settlement deductions.
- In general, non-Google budgets rarely encounter settlement deductions (if there are any advertisers who feedbacked about high traffic IVT, you need to take actions to block IVT inventory timely).

# 4 Operational Tips

- When you see Publishers having high revenue bundles and high eCPR, you might consider asking them to increase inventory QPS.
- Authorize inventory to your advertisers based on their budget requirements. Try as much as possible to match inventory with budget.
- Advertiser budgets might be seasonal, consider optimizing inventory allocation based on seasonal adjustment of budgets.
