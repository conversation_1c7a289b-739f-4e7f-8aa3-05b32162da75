/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-09 19:39:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-10 11:07:41
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 12:22:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-04 15:19:16
 * @Description:
 */

import { UserType } from '@/constants';
import dbUtils from '@/db/mysql';
import { IVTAPI } from '@/types/ivt';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];
const getValues = (params: IVTAPI.AddIvtParams) => {
  const {
    op_id,
    status = 1,
    tnt_id,
    buyer_id = [],
    seller_id = [],
    type,
    ratio
  } = params;

  const bundle = params?.bundle
    ? params.bundle.split(',').sort().join(',')
    : '';

  const values: any[][] = [];
  const b_ids = buyer_id.length ? buyer_id : [0];
  const s_ids = seller_id.length ? seller_id : [0];

  for (const b_id of b_ids) {
    for (const s_id of s_ids) {
      values.push([b_id, s_id, op_id, status, tnt_id, type, ratio, bundle]);
    }
  }
  return values;
};

class IvtModel implements IVTAPI.IvtModel {
  // !注释不删除, 后续可能会用到（维度校验）
  async isIvtExist(params: IVTAPI.CheckIvtParams) {
    const { tnt_id, bundle = '', id, type, buyer_id, seller_id } = params;

    const keys = 'id,status,tnt_id,buyer_id,seller_id,type,ratio,bundle';
    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['type = ?', type],
      ['id != ?', id],
      ['seller_id in (?)', seller_id?.length ? seller_id : 0],
      ['buyer_id in (?)', buyer_id?.length ? buyer_id : 0],
      ['bundle = ?', bundle]
    ]);

    const sql = `select ${keys} from stg_traffic_quality ${clause} limit 1`;
    return await dbUtils.query(sql, values);

    // const level_tnt = tnt_id && !buyer_id && !seller_id && !bundle;

    // const level_tnt_s_or_b = tnt_id && (buyer_id || seller_id) && !bundle;
    // const level_tnt_s_or_b_condition = `(${
    //   buyer_id ? `buyer_id in (${buyer_id})` : ''
    // } ${buyer_id && seller_id ? 'or' : ''} ${
    //   seller_id ? `seller_id in (${seller_id})` : ''
    // })`;

    // const level_tnt_s_and_b = tnt_id && buyer_id && seller_id && !bundle;
    // const level_tnt_s_and_b_condition = `(buyer_id in (${buyer_id}) and seller_id in (${seller_id}))`;

    // const level_tnt_bundle = tnt_id && bundle && !buyer_id && !seller_id;
    // const level_tnt_bundle_condition = `bundle = '${bundle}'`;

    // const level_tnt_s_or_b_bundle = tnt_id && bundle && (buyer_id || seller_id);
    // const level_tnt_s_or_b_bundle_condition = `(buyer_id in (${
    //   buyer_id || 0
    // }) or seller_id in (${seller_id || 0})) and bundle = '${bundle}'`;

    // const level_tnt_s_and_b_bundle = tnt_id && bundle && buyer_id && seller_id;
    // const level_tnt_s_and_b_bundle_condition = `buyer_id in (${buyer_id}) and seller_id in (${seller_id}) and bundle = '${bundle}'`;

    // if (level_tnt) {
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_or_b) {
    //   const condition = `and (${level_tnt_s_or_b_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_and_b) {
    //   const condition = `and (${level_tnt_s_and_b_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_bundle) {
    //   const condition = `and (${level_tnt_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_or_b_bundle) {
    //   const condition = `and (${level_tnt_s_or_b_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_and_b_bundle) {
    //   const condition = `and (${level_tnt_s_and_b_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // console.log('sql', sql);
  }

  async getIvtList(tnt_id: number) {
    const sql = `
    select 
      ivt.id,
      ivt.tnt_id,
      ivt.buyer_id,
      ivt.seller_id,
      ivt.bundle,
      ivt.ratio,
      ivt.op_id,
      ivt.type,
      ivt.status,
      ivt.update_time,
      tnt.tnt_name,
      (case when u.type in (?) then "System"
        when u.account_name is null then "System" 
        else u.account_name end
      ) as op_name,
      sl.seller_name,
      byr.buyer_name
      from (select * from stg_traffic_quality where tnt_id = ?) ivt
      left join tenant tnt on ivt.tnt_id = tnt.tnt_id
      left join user u on ivt.op_id = u.user_id
      left join seller sl on ivt.seller_id = sl.seller_id
      left join buyer byr on ivt.buyer_id = byr.buyer_id 
      order by ivt.update_time desc`;
    return dbUtils.query(sql, [SystemType, tnt_id]);
  }

  async addIvt(
    params: IVTAPI.AddIvtParams,
    shouldUpdateOnDuplicate: boolean = false
  ) {
    const values = getValues(params);
    let sql = `insert into stg_traffic_quality (buyer_id,seller_id,op_id,status,tnt_id,type,ratio,bundle) values ?`;

    if (shouldUpdateOnDuplicate) {
      sql += ` on duplicate key update buyer_id = values(buyer_id),seller_id=values(seller_id),op_id=values(op_id),status=values(status),type=values(type),ratio=values(ratio),bundle=values(bundle)`;
    }

    return !!(await dbUtils.query(sql, [values]));
  }

  async updateIvt(params: IVTAPI.UpdateIvtParams) {
    const {
      id,
      op_id,
      status,
      tnt_id,
      // 类型错误：应为 number
      buyer_id = 0,
      // 类型错误：应为 number
      seller_id = 0,
      bundle = `''`,
      ratio = 1,
      type = 1
    } = params;

    const ivtObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id],
      ['buyer_id', buyer_id],
      ['seller_id', seller_id],
      ['type', type],
      ['ratio', ratio],
      ['bundle', bundle]
    ]);

    const sql = `update stg_traffic_quality set ? where id = ?`;
    return !!(await dbUtils.query(sql, [ivtObj, id]));
  }
}

export const ivtModel = new IvtModel();
