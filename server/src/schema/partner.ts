/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 15:39:23
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 15:43:50
 * @Description:
 */
import Jo<PERSON> from 'joi';
const email_reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;

export const AddPartnerSchema = Joi.object({
  partner_name: Joi.string().max(256).required(),
  sp_email: Joi.string().pattern(email_reg).max(128).min(0),
  dp_email: Joi.string().pattern(email_reg).max(128).min(0)
});

export const UpdatePartnerSchema = Joi.object({
  partner_id: Joi.number().integer().required(),
  partner_name: Joi.string().max(256).required(),
  sp_email: Joi.string().pattern(email_reg).max(128).min(0),
  dp_email: Joi.string().pattern(email_reg).max(128).min(0)
});

export const GetPartnerAccountSchema = Joi.object({
  partner_id: Joi.number().integer().required()
});

export const CreatePartnerAccountSchema = Joi.object({
  partner_id: Joi.number().integer().min(1).required(),
  account_name: Joi.string().required(),
  password: Joi.string().min(6).required(),
  send_email: Joi.array().items(Joi.string()).min(0),
  to_self: Joi.number().integer().valid(1, 2).required(),
  role_type: Joi.number().integer().valid(9, 10).required()
});

