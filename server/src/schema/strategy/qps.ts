/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-03-22 19:06:11
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 11:02:17
 * @Description:
 */
import <PERSON><PERSON> from 'joi';
import { QpsLevel } from '@/constants/strategy';

const AllLevel = Object.values(QpsLevel);
const addQpsSchema = {
  buyer_id: Joi.number().integer(),
  pub_id: Joi.number().integer(),
  ots_id: Joi.string().allow(''),
  qps: Joi.number().integer().required().min(0)
    .max(80000),
  region: Joi.number().integer().required(),
  level: Joi.number()
    .integer()
    .required()
    .valid(...AllLevel)
};
export const AddQpsSchema = Joi.object({ ...addQpsSchema });

export const UpdateQpsSchema = Joi.object({
  ...addQpsSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
