// 采用点击下拉方式，减少交互的层数
import useCustomRequest from '@/hooks/useCustomRequest';
import { getUserLinkList, switchAccount } from '@/services/user';
import { CheckOutlined, UserSwitchOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Dropdown, Empty, message } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.less';

interface MenusProps {
  items: MenuItemType[];
}

// 自定义渲染 menuitems 的组件
const Menus = ({ items }: MenusProps) => {
  return (
    <div>
      {items.map(item => (
        <div
          key={item.key}
          title={item.label as string}
          className={`${styles.menuItem} ${item.disabled ? styles.disabled : ''}`}
          onClick={(e: any) => {
            if (item.disabled) {
              return;
            }
            item.onClick?.(e);
          }}
        >
          {item.icon}
          {item.label}
        </div>
      ))}
    </div>
  );
};

const SwitchAccountDropdown = () => {
  const [open, setOpen] = useState(false);
  const { initialState } = useModel('@@initialState');
  const [menuItems, setMenuItems] = useState<MenuItemType[]>([]);

  const userId = useMemo(() => {
    return initialState?.currentUser?.user_id || -1;
  }, [initialState]);

  // 获取可切换的账号列表
  const { run: getUserLinkListRun } = useCustomRequest(getUserLinkList, {
    onSuccess: (data: any) => {
      // 初始化时，获取可切换的账号列表
      const newMenuItems: MenuItemType[] =
        data
          ?.sort((a: any, b: any) => a.tnt_id - b.tnt_id)
          ?.map((user: any) => {
            const { user_id, tnt_name, tnt_id, account_name } = user;
            return {
              key: user_id,
              label: `${tnt_name}(${tnt_id})/${account_name}(${user_id})`,
              disabled: user_id === userId,
              icon: user_id === userId && (
                <CheckOutlined style={{ color: 'var(--primary-color)', fontWeight: 600, paddingRight: 4 }} />
              ),
              onClick: () => handleClick(user_id)
            };
          }) || [];
      setMenuItems(newMenuItems);
    }
  });

  // 切换账号的接口请求
  const { run: switchAccountRun } = useCustomRequest(switchAccount, {
    onSuccess: (data: any) => {
      if (data.user_id) {
        message.success('Auto refreshing...');
        window.location.reload();
      }
    }
  });

  const handleClick = (specialUserId: number) => {
    switchAccountRun({ switch_user_id: specialUserId });
  };

  useEffect(() => {
    // 初始化时获取可切换的账号列表
    const { special_user_id } = initialState?.currentUser || {};
    getUserLinkListRun({ special_user_id });
  }, [initialState?.currentUser]);

  const handleOpenChange = useCallback((open: boolean) => {
    setOpen(open);
  }, []);

  return (
    <Dropdown
      placement="bottom"
      trigger={['click']}
      overlayClassName={styles.dropdown}
      open={open}
      onOpenChange={handleOpenChange}
      dropdownRender={() => {
        if (!open) {
          return <></>;
        }
        return (
          <div className={styles.container}>
            {menuItems.length === 0 ? <Empty description="No account available" /> : <Menus items={menuItems} />}
          </div>
        );
      }}
    >
      <div>
        <UserSwitchOutlined className={styles.switchIcon} />
      </div>
    </Dropdown>
  );
};

export default SwitchAccountDropdown;
