/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:11:59
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 19:20:46
 * @Description:
 */
import { Context } from 'koa';
import { pmpService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import {
  AddPmpDealSchema,
  addPmpInventorySchema,
  EditPmpDealSchema,
  EditPmpInternalSchema
} from '@/schema/strategy/pmp';
import { RESULT } from '@/constants';

class PmpCtrl {
  async getPmpDealList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await pmpService.getPmpDealList(
      ctx.session!.tnt_id,
      cur_time_zone
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddPmpDealSchema)
  async addDeal(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const num = await pmpService.isExistsDeal(formData);
    if (num) {
      result =
        num === 2
          ? getCtxResult('PMP_DEAL_REPEAT', [])
          : getCtxResult('PMP_DEAL_EXISTS', []);
    } else {
      const list = await pmpService.addDeal(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addDeal success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  @validateBody(EditPmpDealSchema)
  async updateDeal(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const num = await pmpService.isExistsDeal(formData);
    if (num) {
      result =
        num === 2
          ? getCtxResult('PMP_DEAL_REPEAT', [])
          : getCtxResult('PMP_DEAL_EXISTS', []);
    } else {
      const list = await pmpService.updateDeal(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateDeal success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  async getPmpInventoryList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await pmpService.getPmpInventoryList(
      ctx.session!.tnt_id,
      cur_time_zone
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(addPmpInventorySchema, true, true)
  async addInventory(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const data = await pmpService.isExistsInventory(formData);
    if (data?.length) {
      result = getCtxResult('PMP_INTERNAL_EXISTS', data);
    } else {
      const list = await pmpService.addInventory(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addInventory success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  @validateBody(EditPmpInternalSchema, true, true)
  async updateInventory(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const data = await pmpService.isExistsInventory(formData);
    if (data?.length) {
      result = getCtxResult('PMP_INTERNAL_EXISTS', data);
    } else {
      const list = await pmpService.updateInventory(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateInventory success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }
}

export const pmpCtrl = new PmpCtrl();
