import { pmpModel, supplyModel } from '@/models';
import { PmpAPI } from '@/types/pmp';
import { array2Object } from '@/utils';

class PmpService {
  async isExistsDeal(params: PmpAPI.IsExistsDeal) {
    const data = await pmpModel.isExistsDeal(params);
    if (data?.length) {
      const { buyer_id, pmp_id, bidfloor} = data[0];
      if (buyer_id === +params.buyer_id && pmp_id === +params.pmp_id && bidfloor === params.bidfloor) {
        //  bidfloor重复
        return 2;
      }
      // name or id 重复
      return 1;
    }
    return 0;
  }

  async getPmpDealList(tnt_id: number, cur_time_zone: string) {
    return await pmpModel.getPmpDealList(tnt_id, cur_time_zone);
  }

  async addDeal(params: PmpAPI.AddDealParams) {
    return await pmpModel.addDeal(params);
  }

  async updateDeal(params: PmpAPI.UpdateDealParams) {
    return await pmpModel.updateDeal(params);
  }

  async isExistsInventory(params: PmpAPI.IsExistsDeal) {
    return await pmpModel.isExistsInventory(params);
  }

  async getPmpInventoryList(tnt_id: number, cur_time_zone: string) {
    const data = await pmpModel.getPmpInventoryList(tnt_id, cur_time_zone);
    const sellers: string[] = data.map((v: any) => v.seller_id).join(',').split(',');
    const seller_id = [...new Set(sellers)].filter((v) => v && v.trim()).map((v) => +v);
    if (seller_id.length) {
      const seller = await supplyModel.getDownloadSupplyList({seller_id, tnt_id});
      const sellerMap = array2Object(seller, 'seller_id');
      return data.map((v: any) => {
        const tmp = v.seller_id.split(',').filter((e: string) => e && e.trim()).map((t: any) => ({seller_id: t, seller_name: sellerMap[t]?.seller_name || ''}));
        return {
          ...v,
          sellers: tmp,
          inventory_type: v.inventory_type.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
          ad_format: v.ad_format.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
          bundle: v.bundle.split(',').filter((e: string) => e && e.trim()),
          seller_id: v.seller_id.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
          ad_size: v.ad_size.split(',').filter((e: string) => e && e.trim()),
          country: v.country.split(',').filter((e: string) => e && e.trim())
        };
      });
    }
    return data.map((v: any) => ({
        ...v,
        sellers: [],
        inventory_type: v.inventory_type.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
        ad_format: v.ad_format.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
        bundle: v.bundle.split(',').filter((e: string) => e && e.trim()),
        seller_id: v.seller_id.split(',').filter((e: string) => e && e.trim()).map((e: string) => +e),
        ad_size: v.ad_size.split(',').filter((e: string) => e && e.trim()),
        country: v.country.split(',').filter((e: string) => e && e.trim())
      }));
  }

  async addInventory(params: PmpAPI.addInventoryParams) {
    return await pmpModel.addInventory(params);
  }

  async updateInventory(params: PmpAPI.updateInventoryParams) {
    return await pmpModel.updateInventory(params);
  }
}

export const pmpService = new PmpService();
