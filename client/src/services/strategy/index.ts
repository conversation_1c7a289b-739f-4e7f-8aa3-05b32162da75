/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:13:57
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-21 15:59:04
 * @Description:
 */
import { request } from '@umijs/max';

export async function getBlAndWlList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.BlAndWlListItem>>('/api/blWl/getBlAndWlList', {
    data: body || {},
    ...(options || {})
  });
}

export async function addBlAndWl(body: StrategyAPI.AddBlAndWlParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/blWl/addBlAndWl', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateBlAndWl(body: StrategyAPI.updateBlAndWlParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/blWl/updateBlAndWl', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getCapList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.CapListItem>>('/api/cap/getCapList', {
    data: body || {},
    ...(options || {})
  });
}

export async function addCap(body: StrategyAPI.AddCapParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/cap/addCap', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateCap(body: StrategyAPI.updateBlAndWlParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/cap/updateCap', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getQpsList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.QpsListItem>>('/api/qps/getQpsList', {
    data: body || {},
    ...(options || {})
  });
}

export async function addQps(body: StrategyAPI.AddQpsParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/qps/addQps', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateQps(body: StrategyAPI.UpdateQpsParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/qps/updateQps', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addProfit(body: StrategyAPI.AddProfitParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/profit/addProfit', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateProfit(body: StrategyAPI.UpdateProfitParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/profit/updateProfit', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getProfitList(
  body?: { type: 'advertiser' | 'publisher' | 'bundle' },
  options?: { [key: string]: any }
) {
  return request<API.ResponseResult<StrategyAPI.ProfitListItem>>('/api/profit/getProfitList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addBundleProfit(body: StrategyAPI.AddBundleProfitParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/profit/addBundleProfit', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateBundleProfit(body: StrategyAPI.UpdateBundleProfitParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/profit/updateBundleProfit', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getFloorList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.FloorListItem>>('/api/floor/getFloorList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getAllSupplyPlacement(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<AppAPI.PlacementListItem>>('/api/floor/getAllSupplyPlacement', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addFloor(body: StrategyAPI.AddFloorParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/floor/addFloor', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateFloor(body: StrategyAPI.UpdateFloorParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/floor/updateFloor', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function deleteFloor(body: StrategyAPI.UpdateFloorParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/floor/deleteFloor', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getCreativeList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.CreativeListItem>>('/api/creative/getCreativeList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addCreative(body: StrategyAPI.AddCreativeParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/creative/addCreative', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateCreative(body: StrategyAPI.UpdateCreativeParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/creative/updateCreative', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getIvtConfigList(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/ivt/getIvtConfigList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getAtcModelList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.AtcListItem>>('/api/atc/getAtcModelList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addIvtConfig(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/ivt/addIvt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateIvtConfig(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/ivt/updateIvt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAtcModel(body: StrategyAPI.UpdateAtcModelParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/atc/updateAtcModel', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getABTestList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.ABTestListItem>>('/api/ab-test/getABTestList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addABTest(body: StrategyAPI.AddABTestParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/ab-test/addABTest', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updateABTest(body: StrategyAPI.UpdateABTestParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/ab-test/updateABTest', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addPmpDeal(body: StrategyAPI.AddPmpDealParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/addDeal', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updatePmpDeal(body: StrategyAPI.UpdatePmpDealParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/updateDeal', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getPmpDealList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/getPmpDealList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function addPmpInventory(body: StrategyAPI.AddPmpInventoryParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/addInventory', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function updatePmpInventory(body: StrategyAPI.UpdatePmpInventoryParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/updateInventory', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

export async function getPmpInventoryList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/pmp/getPmpInventoryList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

/**
 * 获取 policy key list
 * @param body
 * @param options
 * @returns API.ResponseResult<StrategyAPI.GeoPolicyKeyListItem>
 */
export async function getGeoPolicyKeyList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.GeoPolicyKeyListItem>>('/api/geo-policy/getGeoPolicyKeyList', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

/**
 * 新增 policy key
 * @param body
 * @param options
 * @returns API.ResultWithType<boolean>
 */
export async function addGeoPolicyKey(body: StrategyAPI.AddGeoPolicyKeySchema, options?: { [key: string]: any }) {
  return request<API.ResultWithType<boolean>>('/api/geo-policy/addGeoPolicyKey', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

/**
 * 更新 policy key
 * @param body
 * @param options
 * @returns API.ResultWithType<boolean>
 */
export async function updateGeoPolicyKey(body: StrategyAPI.UpdateGeoPolicyKeySchema, options?: { [key: string]: any }) {
  return request<API.ResultWithType<boolean>>('/api/geo-policy/updateGeoPolicyKey', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

/**
 * 获取 policy key relation list
 * @param body
 * @param options
 * @returns API.ResponseResult<StrategyAPI.GeoPolicyKeyRelationListItem>
 */
export async function getGeoPolicyKeyRelationList(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<StrategyAPI.GeoPolicyKeyRelationListItem>>(
    '/api/geo-policy/getGeoPolicyKeyRelationList',
    {
      method: 'POST',
      data: body || {},
      ...(options || {})
    }
  );
}

/**
 * 新增 policy key relation
 * @param body
 * @param options
 * @returns API.ResultWithType<boolean>
 */
export async function addGeoPolicyKeyRelation(
  body: StrategyAPI.AddGeoPolicyKeyRelationSchema,
  options?: { [key: string]: any }
) {
  return request<API.ResultWithType<boolean>>('/api/geo-policy/addGeoPolicyKeyRelation', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}

/**
 * 更新 policy key relation
 * @param body
 * @param options
 * @returns API.ResultWithType<boolean>
 */
export async function updateGeoPolicyKeyRelation(
  body: StrategyAPI.UpdateGeoPolicyKeyRelationSchema,
  options?: { [key: string]: any }
) {
  return request<API.ResultWithType<boolean>>('/api/geo-policy/updateGeoPolicyKeyRelation', {
    method: 'POST',
    data: body || {},
    ...(options || {})
  });
}
