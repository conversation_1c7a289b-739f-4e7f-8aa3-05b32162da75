import { useEffect, useState } from 'react';
import { useAccess, useModel } from '@umijs/max';
import OriginalTable from '@/components/Table/OriginalTable';
import styles from './index.less';
import { Button } from 'antd';
import { columns, PlatformTypeToLabel } from '@/constants/app-list';
import AddPlacementDrawer from '../AddPlacementDrawer';
import AddAppModel from '../AddAppDrawer';
import EditButton from '@/components/Button/EditButton';
import { ColumnProps } from 'antd/es/table';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import NormalTitle from '@/components/NormalTitle';
import RixEngineFont from '@/components/RixEngineFont';
import InputSearch from '@/components/Input/InputSearch';
import NormalModal from '@/components/Modal/NormalModal';
import Paragraph from 'antd/lib/typography/Paragraph';
import Pagination from '@/components/Pagination/NormalPagination';

type AppPlacementProps = {
  loading: boolean;
  dataSource: AppAPI.PlacementListItem[];
  rowKey: string;
  isSDK: boolean;
  supply: SupplyAPI.SupplyListItem | undefined;
  disabled: boolean;
  currentApp: AppAPI.AppListItem | undefined;
  fetchAppList: () => void;
  itemColor: string;
  isJstag: boolean;
  isIOS: boolean;
  isSite: boolean;
  isVastTag: boolean;
};

const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

const getCurrentData = <T, >(data: T[], page: number, pageSize: number): T[] => {
  if (!data) {
    return [];
  }
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  return data.slice(start, end);
};

function AppPlacement({
  loading,
  dataSource,
  rowKey,
  isSDK,
  supply,
  disabled,
  currentApp,
  fetchAppList,
  itemColor,
  isJstag,
  isIOS,
  isSite,
  isVastTag
}: AppPlacementProps): JSX.Element {
  const { initialState } = useModel('@@initialState');
  const access = useAccess();
  const current: any = currentApp || {};
  const { app_name = '', bundle = '', app_id, platform = '', store_url = '' } = current;
  const str = app_name.replace(/[^a-zA-Z]/g, '');
  const alphabet = str.length ? str[0].toLowerCase() : 'a';
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [appVisible, setAppVisible] = useState(false);
  const [sampleVisible, setSampleVisible] = useState(false);
  const [placement, setPlacement] = useState<AppAPI.PlacementListItem | undefined>(undefined);
  const [filterList, setFilterList] = useState<AppAPI.PlacementListItem[]>(dataSource);
  const [copyValue, setCopyValue] = useState('');
  const [currentPage, setCurrentPage] = useState<number>(DefCurrentPage);
  const [pageSize, setPageSize] = useState<number>(PageSize);
  const [curPageData, setCurPageData] = useState<AppAPI.PlacementListItem[]>([]);

  const OperateColumns: ColumnProps<AppAPI.PlacementListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      fixed: 'right',
      width: 150,
      render: (txt, params) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <EditButton onClick={() => handleEditPlm(params)} disabled={access.DisabledButton('updatePlacementAuth')}>
            Edit
          </EditButton>
          {isVastTag && (
            <Button onClick={() => handleEditSample(params, supply)} type="text" style={{ color: 'var(--primary-1)' }}>
              Sample
            </Button>
          )}
        </div>
      )
    }
  ];

  const tableColumns = [...columns, ...OperateColumns];

  useEffect(() => {
    if (dataSource && dataSource.length) {
      setFilterList(dataSource);
    } else {
      setFilterList([]);
    }
  }, [dataSource]);

  useEffect(() => {
    setCurrentPage(1);
    setPageSize(PageSize);
    setCurPageData(getCurrentData(filterList, DefCurrentPage, PageSize));
  }, [filterList]);

  const handleSearch = (val: string) => {
    if (val) {
      const list = dataSource.filter((item: AppAPI.PlacementListItem) => {
        return (
          item.plm_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 || item.plm_id.toString().indexOf(val) !== -1
        );
      });
      setFilterList(list);
    } else {
      setFilterList(dataSource || []);
    }
  };

  const handleClose = () => {
    setVisible(false);
    setAppVisible(false);
  };
  const handleOpenModel = (flag: boolean) => {
    setVisible(true);
    setIsEdit(flag);
  };
  const handleOpenAppModel = () => {
    setAppVisible(true);
  };
  const handleEditPlm = (params: AppAPI.PlacementListItem) => {
    setPlacement(params);
    handleOpenModel(true);
  };

  const handleEditSample = (params: AppAPI.PlacementListItem, supply?: SupplyAPI.SupplyListItem) => {
    console.log('params', params);
    const { seller_id = 0, token } = supply || {};
    const { app_id, plm_id } = params;
    const host_prefix = initialState?.currentUser?.host_prefix || '';
    const pv_domain = initialState?.currentUser?.pv_domain || '';
    let url = `https://${host_prefix}.svr.rixengine.com`;
    if (pv_domain) {
      url = `https://bid.${pv_domain}`;
    }
    const smp_template = `${url}/rtb?sid=${seller_id}&token=${token}&app_id=${app_id}&adslot_id=${plm_id}&app.bundle={APP_BUNDLE}&app.name={APP_NAME}&url={APP_STORE_URL}&ifa={IFA}&vwd={WIDTH}&vht={HEIGHT}&cb={CACHE_BUSTER}&dev.type={DEVICE_TYPE}&dev.os={DEVICE_OS}`;
    setCopyValue(smp_template);
    setSampleVisible(true);
  };

  const handleCloseSample = () => {
    setSampleVisible(false);
    setCopyValue('');
  };
  const normalEmptyRender = () => <span></span>;

  const handlePageChange = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
    setCurPageData(getCurrentData(filterList, page, pageSize));
  };

  return (
    <div className={styles['app-right']}>
      <div className={styles['app-right-top']}>
        <div className={styles['app-top-left']}>
          <div className={styles['profile-container']} style={{ background: itemColor }}>
            <span>
              <RixEngineFont type={`${alphabet}`} style={{ fontSize: 36 }} />
            </span>
            {platform ? (
              <div>
                <RixEngineFont
                  type={`${platform < 4 ? PlatformTypeToLabel[platform].toLocaleLowerCase() : 'rix-others'}`}
                  style={{ fontSize: 14 }}
                />
              </div>
            ) : null}
          </div>
          <div className={styles['app-top-left-desc']}>
            <div className={styles['item-desc']}>
              <span>App Name:</span>
              <HoverTooltip title={app_name}>
                <a
                  className={`ellipsis ${styles['span-link']}`}
                  target="_blank"
                  href={store_url.indexOf('//') > -1 ? store_url : `//${store_url}`}
                  rel="noreferrer"
                >
                  {app_name}
                </a>
              </HoverTooltip>
            </div>
            <div className={styles['item-desc']}>
              <span>{isSite ? 'Domain' : `Bundle ID:`}</span>
              <HoverTooltip title={bundle}>
                <span>{bundle}</span>
              </HoverTooltip>
            </div>
            <div className={styles['item-desc']}>
              <span>App ID:</span>
              <span>{app_id}</span>
            </div>
            <div className={styles['item-desc']}>
              <span>Platform:</span>
              <span>{currentApp?.platform ? PlatformTypeToLabel[currentApp.platform] : ''}</span>
            </div>
          </div>
        </div>
        <div className={styles['app-top-right']}>
          <EditButton onClick={handleOpenAppModel} disabled={access.DisabledButton('updateAppAuth') || disabled}>
            Edit
          </EditButton>
        </div>
      </div>
      <div style={{ display: 'flex', alignItems: 'flex-end', justifyContent: 'space-between', marginBottom: 12 }}>
        <NormalTitle blackName="Ad Unit" num={`${filterList.length || 0}`} bottom={0} isTitle toolTip={false} />
        <Button
          type="primary"
          onClick={() => handleOpenModel(false)}
          disabled={access.DisabledButton('addPlacementAuth') || disabled}
        >
          Create Ad Unit
        </Button>
      </div>
      <div className={styles['table-top']}>
        <InputSearch placeholder="Please input unit name or unit id" handleSearch={handleSearch} />
        <Pagination
          onChange={handlePageChange}
          total={filterList?.length || 0}
          pageSize={pageSize}
          current={currentPage}
          showSizeChanger
          size="small"
          responsive
          showTotal={total => `Total ${total} items`}
          pageSizeOptions={PageSizeOptions}
        />
      </div>
      <OriginalTable
        loading={loading}
        columns={tableColumns}
        dataSource={curPageData}
        rowKey={rowKey}
        scroll={{ y: 'calc(100vh - 350px)' }}
      />

      {/* <FrontTable<any>
        searchOptions={[]}
        loading={loading}
        columns={tableColumns}
        dataSource={filterList}
        rowKey={rowKey}
        labelWidth={120}
        scroll={{ y: 'calc(100vh - 420px)' }}
        emptyRender={filterList && filterList.length ? normalEmptyRender : undefined}
        showTopBar={false}
        style={{ height: '100%' }}
      /> */}
      <AddPlacementDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        isSDK={isSDK}
        currentApp={currentApp}
        fetchAppList={fetchAppList}
        placement={placement}
        isJstag={isJstag}
        isIOS={isIOS}
        isSite={isSite}
        isVastTag={isVastTag}
      />
      <AddAppModel
        visible={appVisible}
        isEdit={true}
        handleClose={handleClose}
        fetchAppList={fetchAppList}
        supply={supply}
        currentApp={currentApp}
        isIOS={isIOS}
        isSite={isSite}
        isVastTag={isVastTag}
      />
      <NormalModal
        open={sampleVisible}
        footer={null}
        title={
          <div className={styles['smp-con']}>
            <span>Request Sample</span>
            <Paragraph
              copyable={{
                tooltips: ['Copy Value', 'Copy Success'],
                text: copyValue
              }}
            ></Paragraph>
          </div>
        }
        onCancel={handleCloseSample}
      >
        <div className={styles['item-container']}>
          <div className={`${styles['item-copy']} ${styles['item-max-mw']}`}>{`${copyValue}`}</div>
        </div>
      </NormalModal>
    </div>
  );
}

export default AppPlacement;
