/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-04-27 19:49:01
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-07-03 16:07:03
 * @Description:
 */
/**
 * @Author: <EMAIL>
 * @Date: 2023-04-25 15:08:00
 */

import { Context, Next } from 'koa';
import { CommonAPI } from '@/types/common';
import { md5, objKeySort } from '@/utils';
import { ReportRDSKey, ReportRDSExpiredSecs } from '@/constants/redis';
import { getRedisByKey, setRedisByKey } from '@/db/redis';

const buildKey = (ori: string[] | string) => {
  return Array.isArray(ori)
    ? ori.map(or => md5(`${ReportRDSKey}_${or}`))
    : md5(`${ReportRDSKey}_${ori}`);
};

const CacheUrls = [
  '/api/dashboard/getdashboardlist',
  '/api/dashboard/getpubreportlist',
  '/api/dashboard/getadvreportlist',
  '/api/billing/getadvertiserbillinglist',
  '/api/billing/getpublisherbillinglist'
];

export async function cache(ctx: Context, next: Next) {
  const SuccessCode = 0;
  const oriUrl = ctx.originalUrl.toLowerCase();
  const tnt_id = ctx.session?.tnt_id;
  const body = ctx.request.body ?? {};
  const params = { ...body, tnt_id };
  const key = buildKey(`${oriUrl}:${JSON.stringify(objKeySort(params))}`);
  // skip cache module
  if (!CacheUrls.includes(oriUrl)) {
    console.log('skip', oriUrl);
    return next();
  } else {
    const rCache: CommonAPI.Result = (await getRedisByKey(
      key
    )) as CommonAPI.Result;
    if (rCache && rCache.code === SuccessCode) {
      console.log('from cache');
      ctx.body = rCache;
    } else {
      await next();
      console.log('from db', ctx.body);
      const resp: CommonAPI.Result = ctx.body as CommonAPI.Result;
      if (resp?.code === SuccessCode) {
        if (Array.isArray(resp.data.data) && resp.data.data.length !== 0) {
          console.log('set cache');
          setRedisByKey(key, resp, ReportRDSExpiredSecs);
        }
      }
    }
  }
}
