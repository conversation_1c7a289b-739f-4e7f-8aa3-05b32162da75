import React, { useEffect, useState } from 'react';
import { Input, Button, notification } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import Modal from '@/components/Modal/NormalModal';
import styles from './index.less';
import { classifyBundles, mergeAndUnique, uniqueAndFilterEmpty } from './helper';

type BundleProps = {
  visible: boolean;
  handleOk: (val: any) => void;
  handleCancel: () => void;
  defaultList: string[];
  validator?: (val: string) => boolean;
  title?: React.ReactNode;
  tips?: string;
  maskClosable?: boolean;
  keyboard?: boolean;
  isReverse?: boolean; // 值是否是反过来的
  isEdit?: boolean; //
  currentEditBundle?: string;
  limitCreateNum?: number; // 限制创建的数量
};

function BundleInput({
  defaultList,
  visible,
  handleCancel,
  handleOk,
  validator,
  title,
  tips,
  maskClosable = false,
  keyboard = false,
  isReverse = false,
  isEdit,
  currentEditBundle,
  limitCreateNum
}: BundleProps): JSX.Element {
  const [value, setValue] = useState('');
  const [editBundleList, setEditBundleList] = useState<string[]>([]);
  const [singleBundle, setSingleBundle] = useState<string>('');

  useEffect(() => {
    if (isEdit) setSingleBundle(currentEditBundle as string);
  }, [isEdit, currentEditBundle]);

  useEffect(() => {
    if (isEdit) {
      setSingleBundle(currentEditBundle as string);
    }
  }, [isEdit, currentEditBundle]);

  // 校验是否合规跟重复
  const handleAdd = () => {
    const checkDuplicates = (list1: string[], list2: string[]) => list1.filter(item => list2.includes(item));
    // 合并和去重
    const mergedList = uniqueAndFilterEmpty(mergeAndUnique(defaultList, editBundleList, isReverse));
    // 查找重复项
    const repeat = checkDuplicates(defaultList, uniqueAndFilterEmpty(editBundleList));
    // 分类有效和无效的bundle
    const { valid: validBundle, invalid: invalidBundle } = classifyBundles(mergedList, validator);
    // 计算新增的有效项
    const diff = validBundle.filter(bundle => !defaultList.includes(bundle));
    // 检查是否超过限制
    // 不校验已经超过限制的情况
    if (limitCreateNum && defaultList.length <= limitCreateNum && validBundle.length > limitCreateNum) {
      notification.open({
        message: 'Warning',
        description: `The total number of items exceeds the limit ${limitCreateNum}. Please reduce the number of items before saving.`,
        duration: 5
      });
      return;
    }
    // 处理结果
    handleOk(validBundle);
    handleCancel();

    // 通知处理
    if (invalidBundle.length > 0 || repeat.length > 0 || validBundle.length > 0) {
      const getNotificationContent = () => (
        <>
          {diff.length > 0 && (
            <div>
              <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
              <span>succeed:</span>
              <div className={styles['tips-content']}>
                {diff.map((v, index) => (
                  <p key={index} style={{ marginBottom: 0 }}>
                    {v}
                  </p>
                ))}
              </div>
            </div>
          )}
          {repeat.length > 0 && (
            <div>
              <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
              <span>repeated:</span>
              <div className={styles['tips-content']}>
                {repeat.map((v, index) => (
                  <p key={index} style={{ marginBottom: 0 }}>
                    {v}
                  </p>
                ))}
              </div>
            </div>
          )}
          {invalidBundle.length > 0 && (
            <div>
              <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
              <span>invalid:</span>
              <div className={styles['tips-content']}>
                {invalidBundle.map((v, index) => (
                  <p key={index} style={{ marginBottom: 0 }}>
                    {v}
                  </p>
                ))}
              </div>
            </div>
          )}
        </>
      );

      notification.open({
        message: 'Add',
        description: getNotificationContent(),
        duration: 5
      });
    }

    // 重置状态
    setValue('');
    setEditBundleList([]);
  };

  const handleDelete = () => {
    let newBundleList = [...new Set(defaultList)].filter(v => v);
    const notFound = editBundleList.filter(bundle => {
      return !newBundleList.includes(bundle);
    });
    newBundleList = newBundleList.filter(bundle => {
      return !editBundleList.includes(bundle);
    });
    const diff = defaultList.filter(bundle => {
      return !newBundleList.includes(bundle);
    });
    handleOk(newBundleList);

    // 关闭弹窗
    handleCancel();
    if (diff.length > 0 || notFound.length > 0) {
      const args = {
        message: 'Delete',
        description: (
          <>
            {diff.length > 0 && (
              <div>
                <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {diff.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {notFound.length > 0 && (
              <div>
                <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
                <span>{`not found failure: `}</span>
                <div className={styles['tips-content']}>
                  {notFound.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        duration: 5
      };
      notification.open(args);
    }
    setValue('');
    setEditBundleList([]);
  };

  const handleOverWrite = () => {
    // 去重和过滤无效项
    const newBundleList = uniqueAndFilterEmpty(editBundleList);

    // 分类有效和无效的bundle
    const { valid: validBundle, invalid: invalidBundle } = classifyBundles(newBundleList, validator);

    // 检查是否超过限制
    if (limitCreateNum && validBundle.length > limitCreateNum) {
      notification.open({
        message: 'Warning',
        description: 'The total number of items exceeds the limit. Please reduce the number of items before saving.',
        duration: 5
      });
      return;
    }

    // 处理结果
    handleOk(validBundle);
    // 关闭弹窗
    handleCancel();

    // 通知处理
    if (invalidBundle.length > 0) {
      const getNotificationContent = () => (
        <>
          {validBundle.length > 0 && (
            <div>
              <CheckCircleOutlined style={{ color: 'var(--primary-color)', fontSize: '16px', paddingRight: '5px' }} />
              <span>succeed:</span>
              <div className={styles['tips-content']}>
                {validBundle.map((v, index) => (
                  <p key={index} style={{ marginBottom: 0 }}>
                    {v}
                  </p>
                ))}
              </div>
            </div>
          )}
          {invalidBundle.length > 0 && (
            <div>
              <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
              <span>invalid:</span>
              <div className={styles['tips-content']}>
                {invalidBundle.map((v, index) => (
                  <p key={index} style={{ marginBottom: 0 }}>
                    {v}
                  </p>
                ))}
              </div>
            </div>
          )}
        </>
      );

      notification.open({
        message: 'Overwrite',
        description: getNotificationContent(),
        duration: 5
      });
    }

    // 重置状态
    setValue('');
    setEditBundleList([]);
  };

  const handleSaveSingle = () => {
    const newBundleList = [...new Set(defaultList)].filter(v => v);
    const isValid = validator?.(singleBundle);
    if (!isValid) {
      const args = {
        message: 'Edit',
        description: (
          <div>
            <CloseCircleOutlined style={{ color: '#F75941', fontSize: '16px', paddingRight: '5px' }} />
            <span>{`invalid: `}</span>
            <div className={styles['tips-content']}>
              <p style={{ marginBottom: 0 }}>{singleBundle}</p>
            </div>
          </div>
        ),
        duration: 5
      };
      notification.open(args);
    } else {
      const findIndex = newBundleList.findIndex(bundle => bundle === currentEditBundle);
      if (findIndex > -1) {
        newBundleList[findIndex] = singleBundle;
      }
    }
    handleOk(newBundleList);
    handleCancel();
    setValue('');
    setEditBundleList([]);
  };

  const handleChangeValue = (val: { target: { value: string } }) => {
    if (isEdit) {
      setSingleBundle(val.target.value);
    } else {
      const tmp = val.target.value.split('\n').map(bundle => {
        const val = bundle.trim();
        return val.replaceAll(',', '');
      });
      const res = tmp.filter((item, index) => {
        return item !== '' || (!item && index === tmp.length - 1);
      });
      // 解决空值问题
      setValue(res.join('\n'));
      setEditBundleList(tmp.filter(bundle => bundle !== ''));
    }
  };

  return (
    <Modal
      title={title || 'Edit app ads List'}
      open={visible || isEdit}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={maskClosable}
      keyboard={keyboard}
      footer={
        <>
          {!isEdit && (
            <>
              <Button type="primary" onClick={handleAdd}>
                Add
              </Button>
              <Button type="primary" onClick={handleDelete}>
                Delete
              </Button>
              <Button type="primary" onClick={handleOverWrite}>
                OverWrite
              </Button>
            </>
          )}
          {isEdit && (
            <Button type="primary" onClick={handleSaveSingle}>
              Save
            </Button>
          )}
        </>
      }
    >
      {!isEdit && (
        <Input.TextArea
          rows={4}
          placeholder={tips || 'Enter the app ads (ad_domain,pub_acc_id,name)'}
          value={value}
          onChange={handleChangeValue}
        />
      )}
      {isEdit && <Input value={singleBundle} onChange={handleChangeValue}></Input>}
    </Modal>
  );
}

export default BundleInput;
