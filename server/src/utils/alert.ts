import moment from 'moment';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import request from './request';
import { getConfig } from '@/utils';

const { NotifyFeishuUrl: StaticUrl } = getConfig();

interface JSONValue {
  [key: string]: string | number | boolean | Array<JSONValue> | JSONValue,
}

type LarkData = string | number | boolean | Error | Array<unknown> | JSONValue | null | undefined;

function formatLarkData(key: string | number, value: LarkData) {
  let formatted: string;
  let str = key;
  if (['string', 'number', 'boolean'].includes(typeof value) || value === null || value === undefined) {
    formatted = String(value);
  } else if (value instanceof Error) {
    formatted = `\n\`\`\`\nError Message: ${value.message}${value.stack ? `\n${value.stack}` : ''}\n\`\`\``;
  } else if (Array.isArray(value) || Object.getPrototypeOf(value).constructor.name === 'Object') {
    const v = JSON.stringify(value, null, 2);
    formatted = `\n\`\`\`json\n${v}\n\`\`\``;
  } else {
    const typeName = typeof value === 'object' ? Object.getPrototypeOf(value).constructor.name : typeof value;
    str = `${key} (Unsupported type ${typeName})`;
    formatted = String(value);
  }
  return `**${str}:** ${formatted}`;
}

export async function sendLarkAlertCard({
  title,
  data,
  env = process.env.NODE_ENV || 'test',
  title_type = 'error'
}: {
  title: string;
  data: Record<string, LarkData>,
  env?: string;
  title_type?: 'success' | 'info' | 'warning' | 'error',
}) {
  if (env === 'dev' || env === 'development') return;
  const e = new Error();
  const result = e.stack?.split('\n')[2].match(/\(([\/\w.-]+):(\d+):(\d+)\)/);
  const filePath = path.relative(path.join(__dirname, '../../../'), result?.[1]!);
  const line = result?.[2];
  const header_color = {
    success: 'green',
    info: 'blue',
    warning: 'yellow',
    error: 'red'
  }[title_type];

  return promisify(exec)('git rev-parse --short HEAD').then(({ stdout }) => stdout.trim() || 'master').catch(() => 'master').then((branch) => {
    const body = {
      msg_type: 'interactive',
      card: {
        elements: [
          {
            tag: 'markdown',
            content: Object.entries(data).map(([k, v]) => formatLarkData(k, v)).join('\n'),
            text_align: 'left',
            text_size: 'normal'
          },
          {
            tag: 'column_set',
            flex_mode: 'stretch',
            background_style: 'default',
            horizontal_spacing: '8px',
            horizontal_align: 'left',
            columns: [
              {
                tag: 'column',
                width: 'weighted',
                vertical_align: 'top',
                elements: [
                  {
                    tag: 'note',
                    elements: [
                      {
                        tag: 'standard_icon',
                        token: 'link-copy_outlined'
                      },
                      {
                        tag: 'lark_md',
                        content: `[${filePath}#${line}](https://gitlab.algorix.co/fe-saas/saas.rix-platform/-/tree/${branch}/${filePath}#L${line})`
                      }
                    ]
                  }
                ],
                weight: 1
              },
              {
                tag: 'column',
                width: 'weighted',
                vertical_align: 'top',
                elements: [
                  {
                    tag: 'note',
                    elements: [
                      {
                        tag: 'standard_icon',
                        token: 'time_outlined'
                      },
                      {
                        tag: 'plain_text',
                        content: moment().format('YYYY-MM-DD HH:mm:ss')
                      }
                    ]
                  }
                ],
                weight: 1
              }
            ],
            margin: '8px 0px 0px 0px'
          }
        ],
        header: {
          title: {
            tag: 'plain_text',
            content: `[Env: ${env}] ${title}`
          },
          template: header_color
        }
      }
    };
    return request.post(StaticUrl, body, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  })
    .then(() => { });
}
