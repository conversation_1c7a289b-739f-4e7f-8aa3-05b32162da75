/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 15:11:59
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 19:05:18
 * @Description:
 */
import { Context } from 'koa';
import { blAndWlService } from '@/services';
import { BlAndWlAPI } from '@/types/bl-wl';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import { AddBlAndWlSchema, UpdateBlAndWlSchema } from '@/schema/strategy/bl-wl';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class BlAndWlCtrl implements BlAndWlAPI.BlAndWlCtrlInterface {
  async getBlAndWlList(ctx: Context) {
    let result = { ...RESULT };

    const { cur_time_zone } = ctx.request.body;
    const list = await blAndWlService.getBlAndWlList(
      ctx.session!.tnt_id,
      cur_time_zone
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddBlAndWlSchema)
  async addBlAndWl(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.op_id = ctx.session!.user_id;
    formData.tnt_id = ctx.session!.tnt_id;
    const data = await blAndWlService.isBlAndWlExists(formData);
    if (data && data.length) {
      result = getCtxResult('BL_WL_EXISTS', data);
    } else {
      const list = await blAndWlService.addBlAndWl(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addBlAndWl success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }

  @validateBody(UpdateBlAndWlSchema)
  async updateBlAndWl(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const data = await blAndWlService.isBlAndWlExists(formData);
    formData.op_id = ctx.session!.user_id;
    if (data && data.length) {
      result = getCtxResult('BL_WL_EXISTS', data);
    } else {
      const list = await blAndWlService.updateBlAndWl(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateBlAndWl success formData=[${JSON.stringify(formData)}]`
        );
      }
    }
    ctx.body = result;
  }
}

export default new BlAndWlCtrl();
