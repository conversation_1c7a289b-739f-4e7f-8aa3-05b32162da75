/**
 * 路由信息接口
 */
export interface RouteInfo {
  path: string;
  name: string;
  search: string;
}

/**
 * 路由切换信息接口
 */
export interface RouteChangeInfo {
  next: RouteInfo;
  cur: RouteInfo | null;
}

/**
 * 路由管理工具类
 */
export class RouteManager {
  private static currentRoute: RouteInfo | null = null;
  private static previousRoute: RouteInfo | null = null;
  private static debug: boolean = false;

  /**
   * 从路由配置中获取路由名称
   * @param path 路由路径
   * @param routes 路由配置
   * @returns 路由名称
   */
  static getRouteName(path: string, clientRoutes: any[]): string {
    const findRouteName = (routeList: any[], targetPath: string): string => {
      for (const route of routeList) {
        if (route.path === targetPath) {
          return route.name || targetPath;
        }
        if (route.routes) {
          const found = findRouteName(route.routes, targetPath);
          if (found) return found;
        }
      }
      return '';
    };

    return findRouteName(clientRoutes, path);
  }

  /**
   * 从location对象中获取路由信息
   * @param location 位置对象
   * @param routes 路由配置
   * @returns 路由信息
   */
  static getRouteFromLocation(location: any, clientRoutes: any[]): RouteInfo {
    const path = location?.pathname || '/';
    const search = location?.search || '';
    const name = this.getRouteName(path, clientRoutes);
    return { path, name, search };
  }

  /**
   * 处理路由变化
   * @param props onRouteChange的参数
   * @returns 路由切换信息
   */
  static handleRouteChange(props: any, debug: boolean = false): RouteChangeInfo | null {
    this.debug = debug;
    try {
      const { location, clientRoutes } = props;
      if (!location || !clientRoutes) {
        if (this.debug) {
          console.warn('[RouteManager] Missing required props for route change');
        }
        return null;
      }

      // 获取当前路由信息
      const currentRoute = this.getRouteFromLocation(location, clientRoutes);

      // 保存前一个路由
      this.previousRoute = this.currentRoute;
      this.currentRoute = currentRoute;

      // 构建路由切换信息
      const routeChangeInfo: RouteChangeInfo = {
        next: currentRoute,
        cur: this.previousRoute
      };

      if (this.debug) {
        console.log('[RouteManager] Route change detected:', routeChangeInfo);
      }
      return routeChangeInfo;
    } catch (error) {
      if (this.debug) {
        console.error('[RouteManager] Error handling route change:', error);
      }
      return null;
    }
  }

  /**
   * 获取当前路由信息
   * @returns 当前路由信息
   */
  static getCurrentRoute(): RouteInfo | null {
    return this.currentRoute;
  }

  /**
   * 获取前一个路由信息
   * @returns 前一个路由信息
   */
  static getPreviousRoute(): RouteInfo | null {
    return this.previousRoute;
  }

  /**
   * 重置路由状态
   */
  static reset(): void {
    this.currentRoute = null;
    this.previousRoute = null;
  }

  /**
   * 检查是否为有效路由
   * @param routeInfo 路由信息
   * @returns 是否有效
   */
  static isValidRoute(routeInfo: RouteInfo): boolean {
    return (
      routeInfo &&
      typeof routeInfo.path === 'string' &&
      routeInfo.path.length > 0 &&
      typeof routeInfo.name === 'string' &&
      routeInfo.name.length > 0
    );
  }

  /**
   * 格式化路由信息为字符串
   * @param routeInfo 路由信息
   * @returns 格式化后的字符串
   */
  static formatRouteInfo(routeInfo: RouteInfo): string {
    if (!this.isValidRoute(routeInfo)) {
      return 'Invalid Route';
    }
    return `${routeInfo.name} (${routeInfo.path})`;
  }
}

/**
 * 便捷函数：处理路由变化并返回格式化信息
 * @param props onRouteChange的参数
 * @returns 路由切换信息
 */
export function handleRouteChange(props: any): RouteChangeInfo | null {
  return RouteManager.handleRouteChange(props);
}

/**
 * 便捷函数：获取当前路由信息
 * @returns 当前路由信息
 */
export function getCurrentRoute(): RouteInfo | null {
  return RouteManager.getCurrentRoute();
}

/**
 * 便捷函数：获取前一个路由信息
 * @returns 前一个路由信息
 */
export function getPreviousRoute(): RouteInfo | null {
  return RouteManager.getPreviousRoute();
}

export default RouteManager;
