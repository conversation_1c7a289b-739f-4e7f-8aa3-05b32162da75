# IframeManager 使用指南

更新后的 `IframeManager` 类现在支持与父页面的消息通信功能。

## 功能特性

1. **IframeMessageCommunicator 实例**: 内置消息通信器实例
2. **发送消息**: 向父页面发送类型安全的消息
3. **监听消息**: 接收来自父页面的消息
4. **类型安全**: 基于 TypeScript 泛型的类型安全消息系统
5. **错误处理**: 完善的错误处理和日志记录

## 基本使用方法

### 1. 初始化消息通信器

```typescript
import { iframeManager } from './iframe-manager';

// 初始化消息通信器
iframeManager.initMessageCommunicator(
  ['https://parent-domain.com', 'https://another-domain.com'], // 白名单域名
  true // 开启调试模式
);
```

### 2. 发送消息到父页面

```typescript
import { IframeMessage } from '../hooks/useIframe/type';

// 发送注册消息
const success = iframeManager.sendMessage(IframeMessage.REGISTER, {
  account_name: 'username',
  password: 'password123',
  time: new Date().getTime()
});

if (success) {
  console.log('消息发送成功');
} else {
  console.log('消息发送失败');
}
```

### 3. 监听来自父页面的消息

```typescript
// 监听登录请求消息
const unsubscribe = iframeManager.onMessage(IframeMessage.LOGIN_REQUEST, (data) => {
  console.log('收到登录请求:', data);
  
  // 处理登录逻辑
  // ...
  
  // 发送登录成功消息
  iframeManager.sendMessage(IframeMessage.LOGIN, {
    message: 'success'
  });
});

// 在组件卸载时取消监听
// unsubscribe();
```

## 完整示例

```typescript
import { iframeManager } from './iframe-manager';
import { IframeMessage } from '../hooks/useIframe/type';

class MyIframeApp {
  private unsubscribeFunctions: (() => void)[] = [];

  init() {
    // 检查是否在 iframe 中
    if (!iframeManager.isInIframe()) {
      console.log('不在 iframe 环境中');
      return;
    }

    // 初始化消息通信器
    iframeManager.initMessageCommunicator(
      ['https://parent-domain.com'],
      true // 开启调试模式
    );

    // 设置消息监听器
    this.setupMessageListeners();
  }

  private setupMessageListeners() {
    // 监听登录请求
    const unsubscribeLogin = iframeManager.onMessage(
      IframeMessage.LOGIN_REQUEST, 
      this.handleLoginRequest.bind(this)
    );
    this.unsubscribeFunctions.push(unsubscribeLogin);
  }

  private async handleLoginRequest(data: { account_name: string; password: string; time: number }) {
    try {
      console.log('处理登录请求:', data);
      
      // 执行登录逻辑
      const loginResult = await this.performLogin(data.account_name, data.password);
      
      // 发送登录结果
      iframeManager.sendMessage(IframeMessage.LOGIN, {
        message: loginResult.success ? 'success' : 'failed'
      });
    } catch (error) {
      console.error('登录处理失败:', error);
      iframeManager.sendMessage(IframeMessage.LOGIN, {
        message: 'error'
      });
    }
  }

  private async performLogin(username: string, password: string) {
    // 模拟登录逻辑
    return { success: true };
  }

  // 发送注册数据
  sendRegistration(accountName: string, password: string) {
    return iframeManager.sendMessage(IframeMessage.REGISTER, {
      account_name: accountName,
      password: password,
      time: new Date().getTime()
    });
  }

  // 清理资源
  destroy() {
    // 取消所有消息监听
    this.unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    this.unsubscribeFunctions = [];
    
    // 销毁消息通信器
    iframeManager.destroy();
  }
}

// 使用示例
const app = new MyIframeApp();
app.init();
```

## API 参考

### initMessageCommunicator(whiteList, debug?)
初始化消息通信器
- `whiteList`: 允许通信的域名白名单数组
- `debug`: 是否开启调试模式（可选，默认 false）

### sendMessage<K>(type, data, options?)
发送消息到父页面
- `type`: 消息类型（必须是 TypedMessageMap 中定义的类型）
- `data`: 消息数据（类型安全）
- `options`: 发送选项（可选）
- 返回: boolean - 是否发送成功

### onMessage<K>(type, handler)
监听来自父页面的消息
- `type`: 消息类型
- `handler`: 处理函数
- 返回: () => void - 取消监听的函数

### destroy()
停止消息监听并清理资源

### getMessageCommunicator()
获取消息通信器实例
- 返回: IframeMessageCommunicator | null

### isMessageCommunicatorInitialized()
检查消息通信器是否已初始化
- 返回: boolean

## 注意事项

1. **初始化顺序**: 必须先调用 `initMessageCommunicator` 才能使用消息功能
2. **iframe 环境**: 消息通信功能只在 iframe 环境中有效
3. **白名单安全**: 确保白名单中只包含可信任的域名
4. **资源清理**: 在组件卸载时记得调用 `destroy()` 方法清理资源
5. **错误处理**: 所有方法都包含错误处理，建议检查返回值和控制台日志
