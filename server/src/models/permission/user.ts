/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:09:49
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 17:35:48
 * @Description: 权限用户
 */

import { getLogger } from '@/config/log4js';
import { UserType } from '@/constants';
import { StatusMap } from '@/constants/strategy';
import dbUtils, { PoolConnection } from '@/db/mysql';
import { UserAPI } from '@/types/user';
import { genEnCode, md5 } from '@/utils';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class UserModel {
  async addOneUser(options: UserAPI.addUserParams, operator: number): Promise<number> {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();

      const { account_name, password, role_id, user_type, email, tnt_id } =
        options;

      const token = md5(
        `${new Date().getTime()}${account_name}${tnt_id}${genEnCode(6)}`
      );
      const sql = `insert into user (account_name, password,type,email,role,tnt_id,token) values ?`;

      const row = await query({
        sql,
        values: [
          [
            [
              account_name,
              password,
              user_type || 1,
              email || '',
              2,
              tnt_id,
              token
            ]
          ]
        ]
      });
      const sql1 = `insert into user_role_rl (user_id, role_id, op_user_id,tnt_id) values ?`;
      await query({
        sql: sql1,
        values: [[[row.insertId, role_id, operator, tnt_id]]]
      });
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return row.insertId;
    } catch (e: any) {
      console.log('err', e);
      getLogger('error').error(`addOneUser failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async getUserByIds(ids: number[], tnt_id: number) {
    if (!ids.length) {
      return [];
    }
    const sql = `
      select
        user_id,
        status,
        account_name
      from user where user_id in (?) and tnt_id=?
    `;
    return await dbUtils.query(sql, [ids, tnt_id]);
  }

  async getRoleByIds(ids: number[], tnt_id: number) {
    if (!ids.length) {
      return [];
    }
    if (!tnt_id) {
      return [];
    }
    const sql = `
      select
        r.id as role_id,
        r.role_name,
        group_concat(rpl.rsc_id) as permissions
      from role as r
      left join role_permission_rl as rpl on rpl.role_id = r.id
      where r.tnt_id in (?) and r.id in (?)
      group by r.id
    `;
    return await dbUtils.query(sql, [[tnt_id, 0], ids]);
  }

  async getUserList(params: UserAPI.UserListItem) {
    // 除开自己之外的用户
    const sql = `
      select 
        u.user_id,
        u.account_name,
        u.role,
        u.status,
        u.email,
        ur.op_user_id,
        ur.role_id
      from user as u
      left join user_role_rl as ur on ur.user_id=u.user_id
      where u.role=2 and u.type = ? and u.status != ? and u.user_id != ? and u.tnt_id=? 
      order by u.user_id asc
    `;
    return await dbUtils.query(sql, [
      UserType.Tenant,
      StatusMap.Deleted,
      params.user_id,
      params.tnt_id
    ]);
  }

  async getUserListCount(tnt_id: number) {
    const sql = `select count(1) as count from user where type=? and role = 2 and status != 3 and tnt_id=? `;
    const data = await dbUtils.query(sql, [UserType.Tenant, tnt_id]);
    if (Array.isArray(data) && data.length) {
      return data[0].count;
    }
    return 0;
  }

  async editUser(params: UserAPI.EditUserParams) {
    const { user_id, status, tnt_id, new_password, email } = params;

    const userObj = buildSQLSetClause([
      ['status', status],
      ['email', email || ''],
      ['password', new_password ? new_password : null]
    ]);

    if (!userObj) {
      return false;
    }

    const sql = `update user set ? where user_id = ? and tnt_id = ?`;

    return !!(await dbUtils.query(sql, [userObj, user_id, tnt_id]));
  }

  async editUserTransaction(params: UserAPI.EditUserParams) {
    const { user_id, status, tnt_id, new_password, email, role_id } = params;

    const userObj = buildSQLSetClause([
      ['status', status],
      ['email', email || ''],
      ['password', new_password]
    ]);

    await dbUtils.execWaterfallTransaction([
      {
        // 更新用户信息
        sql: `update user set ? where user_id = ? and tnt_id = ?`,
        values: [userObj, user_id, tnt_id]
      },
      {
        // 更新用户角色
        sql: `update user_role_rl set role_id = ? where user_id = ? and tnt_id = ?`,
        values: [role_id, user_id, tnt_id]
      }
    ]);
    return Promise.resolve(true);
  }

  async deleteUser(params: UserAPI.DeleteUserParams) {
    const { user_id, tnt_id, account_name } = params;
    const sql = `update user set status = 3, account_name = "bak_name_${new Date().getTime()}_${account_name}" where user_id = ? and tnt_id = ?`;
    return !!(await dbUtils.query(sql, [user_id, tnt_id]));
  }
}

export const pmsUserModel = new UserModel();
