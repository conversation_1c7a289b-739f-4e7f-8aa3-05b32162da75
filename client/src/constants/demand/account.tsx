/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-06 23:33:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-11 14:38:24
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import { Typography } from 'antd';

import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';
const { Paragraph } = Typography;

const reportingDoc = '/help/demandapi-v2';

export const DemandColumns: ColumnProps<DemandAPI.DemandUser>[] = [
  {
    title: 'Sid',
    dataIndex: 'buyer_id',
    width: 80
  },
  {
    title: 'Token',
    dataIndex: 'token',
    width: 220,
    ellipsis: { showTitle: false },
    render: (txt, params) => {
      return (
        <span className="copy-container">
          <HoverToolTip title={txt}>
            <span className="info">{txt}</span>
          </HoverToolTip>
          <Paragraph
            copyable={{
              tooltips: false,
              text: txt,
              icon: <RixEngineFont type="rix-copy" />
            }}
          ></Paragraph>
        </span>
      );
    }
  },
  {
    title: 'Doc',
    dataIndex: 'doc',
    width: 150,
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverToolTip title={txt}>
        <a target="_blank" href={reportingDoc} rel="noreferrer">
          Demand Report API
        </a>
      </HoverToolTip>
    )
  }
];
