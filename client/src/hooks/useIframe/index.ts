import { useCallback, useEffect, useMemo, useRef } from 'react';
import {
  IframeMessageCommunicator,
  ParentMessageCommunicator,
} from './message-comm';
import { IframeCommOptions, TypedMessageMap } from './type';

/**
 * 通用iframe通信hook
 * 同时支持父页面和iframe页面
 *
 * @param options 通信配置选项
 * @returns 通信相关方法和状态
 */
export const useIframe = (options: IframeCommOptions) => {
  const { sendWhiteList, listenWhiteList, iframeRef = null, debug = false } = options;

  // 判断当前是否在iframe中
  const isInIframe = useMemo(() => window !== window.parent, []);

  // 通信器引用
  const communicatorRef = useRef<
    IframeMessageCommunicator | ParentMessageCommunicator | null
  >(null);

  // 初始化通信器
  useEffect(() => {
    if (isInIframe) {
      // 在iframe中，初始化iframe通信器
      communicatorRef.current = new IframeMessageCommunicator(sendWhiteList, listenWhiteList, debug);
    } else {
      // 在父页面中，初始化父页面通信器
      communicatorRef.current = new ParentMessageCommunicator(
        iframeRef?.current || null,
        sendWhiteList,
        listenWhiteList,
        debug,
      );
    }

    // 开始监听消息
    if (communicatorRef.current) {
      communicatorRef.current.listen();
    }

    return () => {
      // 清理监听器
      communicatorRef.current?.stopListening();
    };
  }, [sendWhiteList, listenWhiteList, isInIframe, debug, iframeRef]);

  // 当iframe引用变化时更新父页面通信器
  useEffect(() => {
    if (
      !isInIframe &&
      communicatorRef.current instanceof ParentMessageCommunicator
    ) {
      communicatorRef.current.setIframe(iframeRef?.current || null);
    }
  }, [iframeRef, isInIframe]);

  /**
   * 发送消息
   * @param type 消息类型
   * @param data 消息数据
   * @returns 是否发送成功
   */
  const send = useCallback(
    <K extends keyof TypedMessageMap>(
      type: K,
      data: TypedMessageMap[K],
    ): boolean => {
      if (communicatorRef.current) {
        return communicatorRef.current.send(type, data);
      }
      return false;
    },
    [],
  );

  /**
   * 注册消息处理函数
   * @param type 消息类型
   * @param handler 处理函数
   * @returns 取消注册的函数
   */
  const on = useCallback(
    <K extends keyof TypedMessageMap>(
      type: K,
      handler: (data: TypedMessageMap[K]) => void | Promise<void>,
    ): (() => void) => {
      if (communicatorRef.current) {
        return communicatorRef.current.on(type, handler);
      }
      return () => {};
    },
    [],
  );

  /**
   * 发送就绪信号
   * 仅在iframe中有效，在组件挂载后发送就绪通知
   */
  useEffect(() => {
    if (
      isInIframe &&
      communicatorRef.current instanceof IframeMessageCommunicator
    ) {
      communicatorRef.current.sendReadySignal();
    }
  }, [isInIframe]);

  return {
    isInIframe,
    send,
    on,
  };
};

// 导出类型和常量，方便使用
export * from './type';
