import NormalModal from '@/components/Modal/NormalModal';
import { compareItemsAndGetLabels } from '@/pages/demand/pretargeting/helper/compareItemsAndGetLabels';
import {
  extractFormDefaultValues,
  generatePretargetFormItemSchemas,
  renderFormItemsFromSchema
} from '@/pages/demand/pretargeting/helper/form-item-schema';
import { transformFormValuesToBackend } from '@/pages/demand/pretargeting/helper/transformFormValuesToBackend';
import { usePretargetingState, usePretargetingStore } from '@/pages/demand/pretargeting/store';
import { addPretargetCampaign, updatePretargetCampaign } from '@/services/demand';
import { fetchData as httpFetchData } from '@/utils';
import { ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { Form, message, Space, Spin, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

const { Text } = Typography;

interface PretargetModalProps {
  fetchData: () => void;
  loading: boolean;
  supplyOptions: { label: string; value: string }[];
  adSizeOptions: { label: string; value: string }[];
}

const PretargetModal: React.FC<PretargetModalProps> = ({
  // 重新拉取数据
  fetchData,
  // 动画
  loading: defaultLoading,
  supplyOptions,
  adSizeOptions
}) => {
  const state = usePretargetingState();
  const { currentDemand, pretargetModalOp, pretargetModalVisible, onePretargetingData } = usePretargetingStore();

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const handleCancel = () => {
    state.pretargetModalVisible = false;
    state.pretargetModalOp = 1;
    state.onePretargetingData = null;
    form.resetFields();
  };

  const handleSubmit = (values: ReturnType<typeof transformFormValuesToBackend>) => {
    const { baseInfo, items } = values;
    const params = {
      ...baseInfo,
      buyer_id: currentDemand?.buyer_id,
      op: pretargetModalOp,
      items: JSON.stringify(items),
      campaign_id: onePretargetingData?.campaign_id
    };

    const onSuccess = () => {
      message.success('success');
      fetchData();
      handleCancel();
    };

    const request = +params.op === 1 ? addPretargetCampaign : +params.op === 2 ? updatePretargetCampaign : null;
    if (request) {
      httpFetchData({ setLoading, request, params, onSuccess });
    }
  };

  const modifyRemind = (diffs: string[], onOk: () => void) => {
    const names = diffs.join(', ');
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <Space direction="vertical">
            <div>The following radio button field values have been changed, continue?</div>
            <Text type="secondary">{names}</Text>
          </Space>
        </div>
      ),
      okText: 'Continue',
      cancelText: 'Cancel',
      onOk
    });
  };

  const handleSave = async () => {
    const values = await form.validateFields();

    const oldBackendValues = transformFormValuesToBackend(onePretargetingData || {});
    const backendValues = transformFormValuesToBackend(values);

    if (pretargetModalOp === 1) {
      handleSubmit(backendValues);
    } else if (pretargetModalOp === 2) {
      // 比较两者的差异
      const changedLabels = compareItemsAndGetLabels(oldBackendValues.items || [], backendValues.items || []);

      if (changedLabels.length > 0) {
        modifyRemind(changedLabels, () => {
          handleSubmit(backendValues);
        });
      } else {
        // 如果没有变化，直接提交
        handleSubmit(backendValues);
      }
    }
  };

  const [formItems, setFormItems] = useState<React.ReactNode[]>([]);

  useEffect(() => {
    if (!pretargetModalVisible || !form || !supplyOptions || !adSizeOptions) return;

    const schemas = generatePretargetFormItemSchemas({ supplyOptions, adSizeOptions });
    // 提取默认值并设置表单初始值
    const initValues = extractFormDefaultValues(schemas);
    // 如果有默认值则设置到表单
    if (Object.keys(initValues).length) {
      form.setFieldsValue({
        ...initValues,
        ...onePretargetingData
      });
    }

    setFormItems(renderFormItemsFromSchema(schemas));
  }, [form, onePretargetingData, supplyOptions, adSizeOptions, pretargetModalVisible]);

  const modalTitle =
    pretargetModalOp === 1
      ? `Create Pretargeting (${currentDemand?.buyer_name})`
      : `Edit Pretargeting (${currentDemand?.buyer_name})`;

  return (
    <NormalModal
      title={modalTitle}
      open={pretargetModalVisible}
      maskClosable={false}
      keyboard={false}
      width={1300}
      className={`${styles['pretarget-modal']}`}
      centered
      destroyOnClose
      onCancel={handleCancel}
      okText="Save"
      cancelText="Cancel"
      onOk={handleSave}
      confirmLoading={(defaultLoading || loading) && pretargetModalVisible}
    >
      <Spin indicator={<LoadingOutlined />} spinning={(defaultLoading || loading) && pretargetModalVisible}>
        {/* 优化目标：采用 columns 实现多列布局，同时使用 form 集中管理 表单状态 */}
        <div className={styles['pretarget-edit-container']}>
          <Form form={form} layout="vertical">
            <div className={styles['pretarget-multi-rows']}>{formItems}</div>
          </Form>
        </div>
      </Spin>
    </NormalModal>
  );
};

export default PretargetModal;
