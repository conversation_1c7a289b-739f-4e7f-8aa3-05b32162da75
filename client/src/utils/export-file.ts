export interface FormatExportValueMapType {
  [key: string]: (value: string, row: Record<string, any>) => string;
}

/**
 * 格式化导出数据
 * @param data 数据
 * @param FormatExportValueMap 格式化映射逻辑
 */
export function formatExportData(
  data: Record<string, any>[],
  FormatExportValueMap: FormatExportValueMapType,
  batchSize: number = 1000
): Promise<Record<string, any>[]> {
  return new Promise(resolve => {
    const result = new Array(data.length);
    const formatKeys = Object.keys(FormatExportValueMap);
    const keysLength = formatKeys.length;

    const processBatch = (startIndex: number) => {
      const endIndex = Math.min(startIndex + batchSize, data.length);

      for (let i = startIndex; i < endIndex; i++) {
        const item = data[i];
        // 只有当确实需要格式化时才创建新对象
        let formattedItem = item;

        for (let j = 0; j < keysLength; j++) {
          const key = formatKeys[j];
          if (key in item) {
            // 延迟创建新对象，直到真正需要修改时
            if (formattedItem === item) {
              formattedItem = { ...item };
            }
            const formatter = FormatExportValueMap[key];
            formattedItem[key] = formatter(item[key], item);
          }
        }

        result[i] = formattedItem;
      }

      if (endIndex < data.length) {
        requestAnimationFrame(() => processBatch(endIndex));
      } else {
        resolve(result);
      }
    };

    processBatch(0);
  });
}
