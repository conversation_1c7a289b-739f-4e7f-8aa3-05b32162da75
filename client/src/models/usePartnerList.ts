/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 16:19:42
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-11 17:43:00
 * @Description:
 */
import { PartnerType } from '@/constants/partner';
import useCustomRequest from '@/hooks/useCustomRequest';
import { getPartnerList } from '@/services/api';

interface PartnerListProps {
  dataSource: PartnerAPI.PartnerListItem[];
  reload: () => void;
  loading: boolean;
  buyerPartnerList: PartnerAPI.PartnerListItem[];
  sellerPartnerList: PartnerAPI.PartnerListItem[];
}

export default function usePartnerList(): PartnerListProps {
  const { data, run, loading, error } = useCustomRequest(getPartnerList, {
    cacheKey: 'partner-all-list'
  });
  if (error) {
    console.error(error);
  }
  const tmp = Array.isArray(data) ? data : [];
  const seller = tmp.filter(v => v.type !== PartnerType.Advertiser);
  const buyer = tmp.filter(v => v.type !== PartnerType.Publisher);
  return {
    dataSource: data || [],
    reload: run,
    loading,
    buyerPartnerList: buyer,
    sellerPartnerList: seller
  };
}
