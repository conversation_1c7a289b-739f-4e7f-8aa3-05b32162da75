---
description: application features and modules
globs: 
alwaysApply: false
---
# Application Features and Modules

## Core Modules

### Authentication and User Management
- Login: [client/src/pages/login](mdc:client/src/pages/login)
- My Profile: [client/src/pages/my-profile](mdc:client/src/pages/my-profile)
- Permission Management: [client/src/pages/my-profile/components/Permission](mdc:client/src/pages/my-profile/components/Permission)

### Supply Side Platform
- Publisher Management: [client/src/pages/supply/publisher](mdc:client/src/pages/supply/publisher)
- App Management: [client/src/pages/developer/app-list](mdc:client/src/pages/developer/app-list)
- Placement Management: [client/src/pages/developer/components/AppPlacement](mdc:client/src/pages/developer/components/AppPlacement)

### Demand Side Platform
- Advertiser Management: [client/src/pages/demand/advertiser](mdc:client/src/pages/demand/advertiser)
- Campaign Management: [client/src/pages/demand/account](mdc:client/src/pages/demand/account)
- Pretargeting: [client/src/pages/demand/pretargeting](mdc:client/src/pages/demand/pretargeting)

### Data Reporting
- Advertiser Reports: [client/src/pages/data-report/AdvReport](mdc:client/src/pages/data-report/AdvReport)
- Publisher Reports: [client/src/pages/data-report/PubReport](mdc:client/src/pages/data-report/PubReport)
- Dashboard: [client/src/pages/data-report/dashboard](mdc:client/src/pages/data-report/dashboard)
- Billing Reports: [client/src/pages/data-report/billing-report](mdc:client/src/pages/data-report/billing-report)

### Strategy Management
- A/B Testing: [client/src/pages/strategy/ab-test](mdc:client/src/pages/strategy/ab-test)
- Floor Price: [client/src/pages/strategy/floor](mdc:client/src/pages/strategy/floor)
- Creative Management: [client/src/pages/strategy/creative](mdc:client/src/pages/strategy/creative)
- PMP Deals: [client/src/pages/strategy/pmp](mdc:client/src/pages/strategy/pmp)

### AI Features
- AI Board: [client/src/pages/ree-ai/ai-board](mdc:client/src/pages/ree-ai/ai-board)
- AI Guide: [client/src/pages/help/ai-guide](mdc:client/src/pages/help/ai-guide)

### Transparency Features
- Supply Chain: [client/src/pages/transparency/supply-chain-v2](mdc:client/src/pages/transparency/supply-chain-v2)

### Troubleshooting
- Sample Trace: [client/src/pages/troubleshooting/sample-trace](mdc:client/src/pages/troubleshooting/sample-trace)

## API Structure

### Client-side Services
- API services are organized by feature modules in [client/src/services](mdc:client/src/services)
- Each feature has its own service file for API interactions

### Server-side Endpoints
- API routes are defined in [server/src/routers/api](mdc:server/src/routers/api)
- Backend API routes are in [server/src/routers/backend-api](mdc:server/src/routers/backend-api)
- Controllers implement the business logic for each endpoint

## Help Center
The application includes a comprehensive help center:
- Documentation: [client/src/pages/help](mdc:client/src/pages/help)
- API References: Various API documentation pages
- FAQ: [client/src/pages/help/faq](mdc:client/src/pages/help/faq)
- Operational Guide: [client/src/pages/help/operational-guide](mdc:client/src/pages/help/operational-guide)
