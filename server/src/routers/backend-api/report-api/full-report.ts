/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-28 15:09:17
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-21 16:38:46
 * @Description:
 */

import Router from 'koa-router';
import { beFullReportCtrl } from '@/controllers';
import { setInterfaceTimeOut, setTimeOutMiddler } from '@/utils/timeout';
import { parseDateParams } from '@/utils/report';
import {
  validateApiToken,
  initApiParams,
  validVisitDayLimit,
  validateTntAuth
} from '@/middleware/backend-api';

const router = new Router({ prefix: '/full-report' })
  .use(setTimeOutMiddler(60 * 60 * 1000))
  .use(validateApiToken)
  .use(validVisitDayLimit)
  .use(validateTntAuth);

const routers = router.post(
  '/getFullReport',
  setInterfaceTimeOut(15 * 60 * 1000),
  initApiParams,
  parseDateParams,
  beFullReportCtrl.getFullReport
);

export default routers;
