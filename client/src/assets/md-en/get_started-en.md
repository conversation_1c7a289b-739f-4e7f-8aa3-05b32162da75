# 「RixEngine」🌟Management Console User Manual🌟

# **1. Get Started**

![img](https://static.rixengine.com/a/platform/help/manual-en/1-1-1-1.png)

# **2. Main Features**

The **SaaS** platform provides one-stop services for the ad ecosystem, including:

- **Data Report**: Provide data reports for both publishers and advertisers. Through data analysis in different dimensions, you can increase publishers' revenue and help advertisers purchase inventories;
- **Supply**: Provide unified management of downstream supply partners to facilitate quick integration;
- **Developer**: For publishers with more granular optimization strategies, Apps can be added and corresponding ad placements can be managed on the RixEngine SaaS console;
- **Demand**: Provide unified management of upstream demand partners to facilitate quick integration;
- **Strategy Config**: Provide unified management and configuration for upstream and downstream partners including traffic forwarding, profit strategies, QPS and other strategies;

# **3. Product User Guide**

## **「Dashboard」**

### **1. Dashboard**

​                  **Real-time data comparison chart (vs. yesterday), with filtering of upstream and downstream dimensions and different data indicators (eg: Revenue, Profit).**

![image-20230915173941233](https://static.rixengine.com/a/platform/help/manual-en/3-1-1-1.png)

### **2. Data Report**

#### 2.1 Full Reporting

​		**Conduct business metric analysis based on the selection of data dimensions and metric dimensions.**

![image-20230918101209293](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-1.png)

#### 2.2 Billing Reporting

​		**This module is mainly used for monthly settlement and can view summary data reports in different time intervals (including weekly reports, monthly reports, quarterly reports, etc.) according to needs.**

![image-20230915184012276](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-2.png)
<!-- 
#### 2.3 Pixalate Reporting

   **Pixalate is a traffic quality measurement tool. This is an add-on module that is separately charged. Please reach out to your contact person in RixEngine to enable this function.** 

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-3.png)

#### 2.4 HUMAN Reporting

   **HUMAN is a traffic quality measurement tool (Usually used for CTV inventory). This is an add-on module that is separately charged. Please reach out to your contact person in RixEngine to enable this function.** 

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-4.png) -->

#### 2.3 Export Log

   **Module used for asynchronously exporting reports, especially when exporting large amount of data.**

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-5.png)
![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-6.png)

### **3. Partner**

   **When your partner has multiple advertiser or publisher accounts, Partner module can be used to manage these accounts easily. You can also check the reporting data on Partner level.**

- **Create Partner**

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-7.png)

- **Assign Advertiser to Partner**

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-8.png)
![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-9.png)

- **Assign Publisher to Partner**

![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-10.png)
![img](https://static.rixengine.com/a/platform/help/manual-en/3-1-2-11.png)


## **「Supplier Integration」**

### **1. Supply**

#### 1.1 Create a Publisher

​		To integrate a supply partner, you need to create publisher-related information in this module to prepare for subsequent integration authentication.

**Step1**: Log into the module

* **Steps to log in to the module：**【Supply】-->【Publisher List】

* **Screenshot：**

  ![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.1-1.png)



**Step2**: Create a Publisher

- **Entrance to create a new publisher：【+ Create Publisher 】**

- **Screenshot：**

![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.1-2.png)

- **Steps to create a new publisher：**

  - **Step 1** :

    Fill in the Publisher name, select the integration method (i.e. technical integration method), media type and whether the integrating partner is a direct publisher or reseller, and finally select the type of device.

  - **Step 2** :

    1. Choose whether to customize ad placements ([Custom Placement])
     * If the ad placement information in the supply partner's platform is completed, there is no need to manage ad placements on the RixEngine console. Select the [Paused] state to save manual operational efforts.
    
     * If the supply partner's system is connected to the SaaS platform and needs to create and manage the ad placements on the RixEngine console, select the [Active] state.
    
       **Tips** :It should be noted that if this is set to Active state but **App** and **ad placements** are not added to the RixEngine console, the traffic will be **rejected** by the RixEgine system.）
    
       **Note** :When selecting the SDK method to integrate, the custom ads placement cannot be turned off (Paused), App and ad placements need to be added and created on the RixEngine SaaS console.
    
    2. Publisher TagID is used to monitor performance metrics on the RixEngine SaaS console and display tagid level data on the Dashboard page. (Precondition: This field is visible only when Custom Placement is in the Paused state).
    
       **a. Use Case**: If the supply partner has not defined ad placements on the RixEngine SaaS console but still wants to view the ad placement level metrics, select the [Active] state, otherwise select the [Paused] state.

  - **Step 3** :Profit Configuration

1. Profit: Based on the ad revenue paid by upstream demand partners, you keep Profit Ratio (%) as the your own net profit.

- **Steps to create a new Publisher：**
![img](https://static.rixengine.com/a/platform/help/manual-en/3-2-1-1-3.png)


#### 1.2 Inventory Authorization

​		Authorize Publisher's inventory to certain Advertisers for traffic distribution.

**Step 1** : Authorize Advertiser

* **Detail page displays Publisher's basic information, authorization of advertisers, and app and ad placement level authorization.**

  1. Basic information of Publisher

  2. Publisher level authorization. Authorization of advertisers on Publisher level.

  3. App and ad placement level authorization. Apps and ad placements can be separately authorized to advertisers. (**Note**: Authorization on the app level and ad placement level must be created on the RixEngine SaaS console.)

* **Note** :Inventory authorization priority is Ad Placement > App > Publisher. Note:

  1. If authorization is done on ad placement level, inventory corresponding to the authorized ad placements is distributed to the authorized advertisers (Demand side).
  2. If authorization is done on app level, all inventory from ad placements that do not have ad placement level authorization is distributed to the authorized advertisers (Demand side) of the app.
  3. If authorization is not done on either ad placement or app level, then all inventory will be distributed to the authorized party (Demand side) of the Publisher.

  - **Example**: As shown below![img](https://static.rixengine.com/a/platform/help/manual-en/3-2-1-2-1.png)

- **Steps of advertiser authorization：**

  **Entrance of advertiser authorization:**![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.2-2.png)

  **Steps of advertiser authorization:**![img](https://static.rixengine.com/a/platform/help/manual-en/3-2-1-2-3.png)




#### 1.3 Inventory Reception Endpoint

​		Publishers send inventory to the traffic reception endpoint of RixEngine SaaS system.

**Step 1** : Retrieve the inventory reception Endpoints, perform necessary technical configuration to complete the integration.

* **Step 1** : Obtain the inventory reception endpoint (automatically generated by the system).

* **Step 2**: Publisher completes the endpoint configurations and starts technical integration. (For technical team of both parties to conduct joint debugging and testing)

* **Screenshots**：

  **Entrance for retrieving endpoints** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.3-1.png)

  **Screenshot** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.3-2-1.png)



#### 1.4 Publisher Account Management

​		Main purpose of this module is to solve the problem of data comparison between RixEngine and your publishers. There are two methods of conducting data comparison: 1. Provide a login account for your partner to compare data. 2. Provide API Reporting to your partner. 

**Step 1**: Create a dedicated Publisher login account and send account information by email

* **Create a publisher account password and change account login username**：
  
  - **Step 1** : You need to create a password when entering the page for the first time. After the creation is completed, you can show the Report Data and Developer modules to specific publishers.
    1. **New Password**: This is the new password.
    2. **Repeat Password**: This is to confirm the password.
    3. **Sent to email**: Send the login URL and account password information to the email address (To Yourself: whether to copy the email address linked to the RixEngine SaaS console login account)
    
  - **Step 2**: Change the login username if needed
    1. **User Name**: Default prefix of the username starts with "Pub_"
    2. **Sent to email**: Same as above
  
  - **Step 3** :If you want to stop using the account, click the Status button on the page.
    
    (**Note** : Before logging in, you need to confirm whether the account's Status is turned on.)
  
* **Screenshots of publisher account management**：
	**Publisher account information management entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-1.png)
	**Screenshot** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-2.png)
	**Screenshot of creating password** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-3.png)
	**Screenshot of user name information** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-1.4-4.png)

**Step2**：Retrieving Token, account ID and Report API

![image-*****************](https://static.rixengine.com/a/platform/help/manual-en/3-2-1-4-5.png)

![image-*****************](https://static.rixengine.com/a/platform/help/manual-en/3-2-1-4-6.png)

### **2. Developer**

​		Use Case: Developers need to use the RixEngine SaaS console to create and manage basic information about ad placement.

#### 2.1 Create App

**Step 1** : Log in to the App List page and select the corresponding Publisher

**Management of developer apps and ad placement**

-Only when ad placements are defined on the RixEngine SaaS console (Precondition: [Custom Placement] needs to be set to [Active] on the Publisher List page)

* **Step 1 Screenshot**：

  **Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-1.png)

  **Choose Publisher (Only when [Custom Placement] status is [Active])**![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-2.png)

**Step 2** : Create app and fill in app information

* **Create app and edit app information**
  1. **App Name** :Required, you need to enter the actual name of the App
  2. **Bundle** :Required, you need to enter the actual package bundle name of the App
  3. **StoreURL** :Required, you need to enter the corresponding app store URL where the App is published
  4. **Category** :Optional, select the category where the App belongs to
  5. **Platform** :Select the platform corresponding to the app
  6. **Screen Orientation**: Select the screen orientation the app is compatible with, portrait or landscape
  
* **Step 2 Screenshot**：

  **Entrance of creating app** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-3.png)

  **Fill in basic app information** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-4.png)

  **Entrance of editing app information** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.1-5.png)



#### 2.2 Create Ad Placement

**Step 1** : Add ad placement information

* **Create and edit ad placements**

  The following is a description of the fields for each Ad Format:

1. **Banner:**
   * Ad Size: Required, select the size of the ad placement
   * Bid Floor: Required, setting of bidding floor price
   * Ad Position: Optional, select the location of the ad placement
2. **Medium Rectangle: A medium rectangular sized ad**
   * Ad Size: Required, select the size of the ad placement
   * Bid Floor: Required, setting of bidding floor price
   * Ad Position: Optional, select the location of the ad placement
3. **Interstitial: A form of full-screen ads that usually appears after the end user completes certain actions, such as loading an application, completing a game level, etc.**
   * Ad Size: Required, select the size of the ad placement
   * Support MIME Types: Required. If Video is ticked, video interstitial ads may be served. Otherwise, only Banner interstitial ad will be served.
   * Allow Skip: Whether to allow video ads to be skipped
   * Mute: Whether to play video ads silently
   * Bid Floor: Required, setting of bidding floor price
   * Show Skip Button After: Required, how long the ad [Skip] button should be displayed after the video starts to play
   * Max Duration: Maximum duration of video ads
4. **Rewarded Video:**
   * Ad Size: Required, select the size of the ad placement
   * Bid Floor: Required, setting of bidding floor price
   * Min Duration: Minimum duration of video ads
   * Max Duration: Maximum duration of video ads
5. **Native:**
   * Assets: Required, elements of Native ad content
   * Bid Floor: Required, setting of bidding floor price

（**Note: If you are running first price auction, when Bid Floor is set to 0, it means no floor price is set.**）

* **Screenshot of creating ad placement**：

  **Entrance of creating ad placement** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-1.png)
  **Fill in basic information of ad placement** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-2.png)
  **Entrance of editing ad placement** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-3.png)
  **Edit page (ad format cannot be modified)** :![img](https://static.rixengine.com/a/platform/help/manual/3.2-2.2-4.png)



## **「Demand Side Integration」**

### **1. Demand**

#### **1.1 Create Advertiser**

​		Basic configurations needed for demand (Advertiser) integration

**Step 1** :Steps of creating advertiser

* **Adding basic information to Advertiser** :

  1. **Advertiser Name** : Required, enter the name of the Advertiser
  2. **Integration Type** : Type of integration protocol, the default is RTB, Online means other API protocols, please contact the technical team for integration evaluation.
  3. **Auction Type** : Bidding type, first price auction and second price auction, default is First Price.
  4. **Profit**: Based on the ad revenue paid by upstream demand partners, you keep Profit Ratio (%) as the your own net profit.
  5. **Profit Model** : Default is net (no revenue sharing with the advertiser).
  6. **Pass Supply Chain** : Whether demand partners require supply chain to be passed in the requests. Default is Paused.
  7. **Mraid Traffic Filter** : Whether demand partners require inventory to support MRAID. Default is Paused.
  8. **Pixalate IVT Block** : Whether demand partners require inventory's IVT to be below certain level based on Pixalate detection. Default is Unlimited. This feature is only available when you enable Pixalate scanning. 
  9. **Human IVT Block** : Whether demand partners require inventory's IVT to be below certain level based on HUMAN detection. Default is Unlimited. This feature is only available when you enable HUMAN scanning. 
  10. **IFA Required** : Whether demand partners require device IDs to be passed in the requests. Default is Paused. If set to Active, inventory that has empty device ID will not be sent.



* **Screenshot of creating advertiser**：
**Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-1.png)
**Fill in Advertiser basic information** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-2.png)
**Entrance of editing advertiser** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.1-3.png)



#### **1.2 Traffic Endpoint Configuration**

​		After filling in basic information, configure the traffic endpoint for each ad format (Native/Banner/Video/Reward Video)

**Step 1** : Configure endpoint for demand partners to receive traffic

* **Can be configured in the US East (USE) data center or Singapore (APSE) data center.**
  1. **Endpoint** : Endpoint URL where demand partners receive traffic
  2. **Socket Timeout** : Used to control the connection time between servers. When the server does not receive a response within the set time, the connection will be interrupted.
  3. **Gzip** : Whether to enable bidding data compression. Enabling compression can reduce server bandwidth costs.

* **Step 1 Screentshot**: 
**Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.2-1.png)
**Add endpoint basic information** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.2-2.png)



#### **1.3 Inventory Authorization**

​		Authorization of publishers' inventory to advertisers

**Step 1** : Authorizing publishers

* **Note**: Authorizing publishers based on advertisers' requirements.

* **Screenshot of authorizing publisher inventory**：
**Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.3-1.png)
**Authorize publishers or cancel authorization information** :![img](https://static.rixengine.com/a/platform/help/manual-en/3-3-1-3-2.png)



#### **1.4 Configuration of inventory pretargeting**

​		You can configure pretargeting for more granular inventory filtering in order to better meet the requirements of demand partners (Advertiser).

**Step 1**: Configure inventory pretargeting

* Description：
  1. **Campaign Name** : Name of the inventory pretargeting rule
  2. **Publisher** : Create Publisher black list or white list. Multiple selection is allowed. Default is white list.
      * General requirements: Selected Publisher must be an authorized Publisher under the "Advertiser List -> More -> Authorization". If no Publisher is selected, this pretargeting rule will take effect on all authorized Publishers.
      * You can configure inventory pretargeting rule for individual Publisher, or for multiple Publishers.
  3. **Country** : Create country black list or white list. Multiple selection is allowed. Default is white list.
  4. **Category** : Create app category black list or white list. Multiple selection is allowed. Default is white list.
  5. **Network**: Create device network type black list or white list. Multiple selection is allowed. Default is white list.
  6. **Delivery Time Slot** : Define time slots of the day (In UTC+0 time zone) when inventory is enabled.
  7. **OS** : Filter based on device operating system.
  8. **Device** : Filter based on device type.
  9. **Inventory** : Filter based on inventory type. 
  10. **Ad Format** : Filter based on ad formats, multiple selection is allowed.
  11. **Ad Size** : Create ad size black list or white list. Multiple selection is allowed. Default is white list.
  12. **Bundle**: Create app bundle or domain black list or white list. Multiple selection is allowed. Default is white list.
  13. **Maximum Floor Price** : Filter the inventory based on the floor price of the ad placement. If the floor price exceeds the configured value, the inventory will be filtered out.
  14. **Server Region** : Select the endpoint of receiving inventory of this pretargeting rule, APAC (Asia Pacific) or USE (North America).
       * Selecting Default means our system will send inventory based on the same regional endpoint configured. For example, inventory from APAC region will be sent to advertisers' APAC endpoint.

  （**Note: If certain fields above are not configured, there will be no restrictions on inventory. However, each advertiser must have a default pretargeting rule, otherwise advertiser cannot receive any inventory.**）

  <font color=red>**Notes on configuring inventory pretargeting:**</font>
  
  Pretargeting supports multiple rule configurations, inventory will be sent to Advertiser when at least one of the Pretargeting rules is met.
  
* **Inventory pretargeting configuration screenshot**:
**Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.4-1.png)
**Entrance of creating inventory filtering configuration** :![img](https://static.rixengine.com/a/platform/help/manual-en/3-3-1-4-2.png)
**Add pretargeting configuration information** :![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.4-3.png)
**After configuration is completed, click the "Save" button to save it.**

#### 1.5 Advertiser Account Management

​		Main purpose of this module is to solve the problem of data comparison between RixEngine and your demand partners. There are two methods of conducting data comparison: 1. Provide a login account for your partner to compare data. 2. Provide API Reporting to your partner. 

**Step 1**: Create a dedicated Advertiser login account and send account information by email

* Create an advertiser account password and change the account login username

  **Step 1** : You need to create a password when entering the page for the first time. After the creation is completed, you can show the Report Data module to specific advertisers.
  1. **New Password**: This is the new password.
  2. **Repeat Password**: This is used to confirm the password.
  3. **Sent to email**: Send the login URL and account password information to the email address (To Yourself: whether to copy the email address linked to the RixEngine SaaS console login account)

  **Step 2**: Change the login username if needed
  1. **User Name**: Default prefix of username starts with "Adv_"
  2. **Sent to email**: Same as above.

  **Step 3** :If you want to stop using the account, click the Status button on the page.

  (**Note** : Before logging in, you need to confirm whether the account's Status is turned on.)

* **Screenshots of advertiser account management**：

**Advertiser account management entrance** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-1.png)

**Screenshot** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-2.png)

**Screenshot of creating password** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-3.png)

**Screenshot of user name information** :

![img](https://static.rixengine.com/a/platform/help/manual/3.3-1.5-4.png)

**Step 2**: Retrieving Token, account ID and Report API

![image-*****************](https://static.rixengine.com/a/platform/help/manual-en/3-3-1-5-5.png)

![image-*****************](https://static.rixengine.com/a/platform/help/manual-en/3-3-1-5-6.png)

## **「Operational Strategy Configuration」**

### **1. Strategy Config**

​		Configuring optimization rules and strategies related to the publishers and advertisers, such as restrictions on publisher and advertiser's revenue cap, impression volume cap, and management of inventory black and white lists.

#### **1.1 BL&WL**

​		You can configure black lists or white lists for one or more publishers and advertisers in different dimensions.

* **Operational steps: Strategy Config —> BL&WL —> "+Create BL&WL"**, you can set black lists or white lists among publishers and advertisers in dimensions like Bundle, Country and Ad Format.

* **Add Configuration Information** :
  
  1. **Publisher** : Select the Publisher you want to add to the black list or white list
  
  2. **Advertiser** :Select the Advertiser you want to add to the black list or white list
  
  3. **Type** : Select which type black list or white list you want to apply
  
  4. **Content**: Detailed settings based on the selection of Type
  
     * If you select Type Bundle, you need to add Bundle Names.
  
     * For other Type values, you can select any enumeration value from the drop-down box. Multiple selections is possible.
  
* **Screenshot of adding configuration information**:
  **Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-1.png)
  **Add black list and white list configuration information** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-2.png)
  **Entrance of editing** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.1-3.png)

* **Note** :

Once a white list is created, only inventory matching the white list configuration will be sent to advertisers.

![image-20230921141037747](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-1-4.png)

#### **1.2 Cap**

​		You can set daily revenue cap or daily ad impression cap for Advertisers and Publishers. This feature is helpful when you are doing a controlled live test with newly onboarded publisher or advertiser. 

* **Operational steps: Strategy Config --> Cap**, You can limit daily revenue and daily impressions for the Advertiser and Publisher.

* **Create Cap** :
  1. **Daily Revenue Cap** : The maximum daily revenue of a Publisher from a specific Advertiser
  2. **Daily Impression Cap** : The maximum daily ad impressions for a Publisher from a specific Advertiser
  
* **Screenshots of Creating Cap**：
**Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-1.png)
**Pages of creating cap configuration** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-2.png)
**Entrance of editing cap configuration** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.2-3.png)



#### **1.3 Profit**

​		You can configure profit rate according to your needs. Our dynamic model algorithm will allocate traffic based on your profit rate configuration.

* **Profit related configuration:**
* **Profit configuration entrance: Strategy Config-->Profit**

* Description：

  1. **Publisher Profit**: Profit rate can be added for specific advertiser(s).
     * Priority of profit rate configurations from high to low: publisher-advertiser > Advertiser > Publisher.
     
     * Use case: When developer (Publisher) has the need to set up dedicated profit rate for a specific advertiser.
     
  2. **Advertiser Profit** : Profit rate can be added for specific publisher(s).
     * Priority of profit rate configurations from high to low: Publisher-Advertiser > Advertiser > Publisher.

     * Use case: When Advertiser has the need to set up dedicated profit rate for a specific Publisher.

* **Profit Rate Priority Examples**
  
  ![image-20231219110215724](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-6.png)
  
  ![image-20231219110235097](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-7.png)
  
  **Publisher-Advertiser level profit rate has the highest priority：**Advertiser test-adv(30604) has a profit rate of 33% for all Publishers, and 50% for Publisher test-pub(36048) only. In this case, Advertiser test-adv(30604) has an effective profit rate of 50% with Publisher test-pub(36048). Other Publishers have an effective profit rate of 33% with Advertiser test-adv(30604). Meanwhile, Publisher test-pub(36048) has an effective profit rate of 50% with test-adv(30604). Other Advertisers have an effective profit rate of 35% with Publisher test-pub(36048).

  ![image-20231219110258039](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-8.png)
  
  ![image-20231219110316593](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-9.png)
  
  **Profit rate set on Advertiser level has higher priority than Publisher level****：**Advertiser test1-adv(30608) has a profit rate of 30% with all Publishers, and does not have a dedicated profit rate for Publisher test1-pub(36053). Meanwhile, Publisher test1-pub(36053) has a profit rate of 50% with all Advertisers, and does not have a dedicated profir rate with Advertiser test1-adv(30608). In this case, between Advertiser test1-adv(30608) and Publisher test1-pub(36053), the profit rate will follow the setting of Advertiser test1-adv(30608), which is 30%.
  
* **Profit configuration screenshot** ：
    **Publisher and Advertiser Profit Entrance** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-1.png)
    **Entrance to add mapping between Publisher and Advertiser profit rate** :![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-2.png)
  
  **Add profit margin information**: 
  1. **Add Publisher profit**![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-3.png)
  2. **Add Advertiser profit**![img](https://static.rixengine.com/a/platform/help/manual/3.4-1.3-4.png)

  **Screenshot after adding** :![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-5.png)

#### **1.4 QPS**

We support dynamic QPS adjustment to save IDC cost for you. 

**Note：**

- QPS value should be set based on eCPR and QPS (Real) per region:
- Default QPS for Advertiser is 100/Region, default QPS for Publisher is 1000/Region.
- When eCPR is equal or less than 1, QPS value should not be set higher than regional QPS (Real).
- When eCPR is between 1 to 2, QPS value can be set to 1.5 times of QPS (Real).
- When eCPR is equal to 2 or above, QPS value can be set to 2 times of QPS (Real).
- For Publishers who have even higher eCPR, QPS can be set to higher value but still need to be capped appropriately in case certain Publisher increases inventory volume abruptly.

**Screenshot of QPS Configuration** ：

![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-6.png)

![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-7.png)

#### **1.5 Creative**

This module is used to block certain creatives served by Advertisers, according to Publisher's requirements. You can configure creative blocking in「Adomain」/「Crid」dimension. 

![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-8.png)

![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-9.png)

## **「Inventory Seller Authorization」**

### 1. App-ads.txt

​     This module is used to check the relationship between Publisher and authorized sellers. It helps to ensure demand partners to buy through authorized sellers. You can check if App-ads.txt lines have been added per app bundle. You can search for lines following the format of “rixengine.com” or “rixengine.com,36069, RESELLER”.

![img](https://static.rixengine.com/a/platform/help/manual-en/3-4-1-3-10.png)


## **「Permission Configuration」**

### 1. My Profile

​	Super administrator can use this module to manage roles, users and accesses.

- **Entrance of permission configuration:**

![image-20230918143444002](https://static.rixengine.com/a/platform/help/manual-en/3-5-1-1.png)

#### **1.1 Role Permission**

​	This module is used to create roles and configure specific permissions for different roles. The Administrator role is defined by default, and administrators can create other roles when needed.

![image-20230918143504682](https://static.rixengine.com/a/platform/help/manual-en/3-5-1-1-1.png)

#### **1.2 Manage Users**

​	This module is used to manage new users and existing users.

- **Manage existing users**

  ![image-20230918143521769](https://static.rixengine.com/a/platform/help/manual-en/3-5-1-2-1.png)

- **Adding users**

  ![image-20230918143557813](https://static.rixengine.com/a/platform/help/manual-en/3-5-1-2-2.png)
