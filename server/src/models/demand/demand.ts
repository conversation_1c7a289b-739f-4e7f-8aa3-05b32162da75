/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-06 10:11:42
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 19:01:21
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { ProfitType, StatusMap, UserType } from '@/constants';
import { AuthLevel } from '@/constants/demand';
import { RoleTypeToNumber } from '@/constants/permission';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil, { PoolConnection, QueryParamType } from '@/db/mysql';
import { DemandAPI } from '@/types/demand';
import { genEnCode, md5 } from '@/utils';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class DemandModel implements DemandAPI.DemandModel {
  async getDemandList(
    tnt_id: number,
    cur_time_zone: string,
    isTesting?: boolean
  ): Promise<DemandAPI.DemandListItem[]> {
    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['status != ?', isTesting ? null : StatusMap.Testing]
    ]);

    const sql = `
        select
            byr.buyer_id as buyer_id,
            byr.buyer_name as buyer_name,
            byr.integration_type as integration_type,
            byr.status as status,
            ${getTzTimeSql('byr.create_time', cur_time_zone)} as create_time,
            ${getTzTimeSql('byr.update_time', cur_time_zone)} as update_time,
            byr.profit_model as profit_model,
            byr.rev_share_ratio as rev_share_ratio,
            byr.auction_type as auction_type,
            byr.imp_track_type as imp_track_type,
            byr.user_id as user_id,
            byr.schain_required as schain_required,
            byr.schain_complete as schain_complete,
            byr.schain_hops as schain_hops,
            byr.pass_display_manager as pass_display_manager,
            byr.display_manager_filter as display_manager_filter,
            byr.filter_mraid as filter_mraid,
            byr.max_hm_ivt_ratio as max_hm_ivt_ratio,
            byr.max_pxl_ivt_ratio as max_pxl_ivt_ratio,
            byr.idfa_required as idfa_required,
            byr.multi_format as multi_format,
            byr.banner_multi_size as banner_multi_size,
            byr.native_format as native_format,
            byr.native_version as native_version,
            bit.itg_name as integration_type_desc,
            byr.token as token,
            byr.omid_track as omid_track,
            p.id as profit_id,
            p.profit_ratio as profit_ratio,
            p.status as profit_status,
            u.account_name as demand_account_name,
            u.status as demand_account_status,
            u.api_status as api_status,
            coalesce(bpt.dp_name, '') as partner_name,
            coalesce(bpt.partner_id, 0) as partner_id,
            coalesce(bpt.dp_id, 0) as dp_id
        from (select * from buyer ${clause}) as byr
        left join buyer_parent as bpt on bpt.dp_id=byr.dp_id
        left join buyer_integration_type as bit on byr.integration_type = bit.id
        left join profit as p on byr.buyer_id = p.buyer_id and p.type = ?
        left join user as u on byr.user_id = u.user_id
        order by byr.update_time desc
    `;
    return await dbUtil.query(sql, [...values, ProfitType.Demand]);
  }

  async addDemand(params: DemandAPI.AddDemandParams) {
    const {
      buyer_name,
      demand_account_name,
      integration_type,
      status = 1,
      tnt_id = 0,
      profit_model,
      profit_ratio = 0,
      rev_share_ratio = 100,
      op_id,
      profit_status,
      auction_type,
      imp_track_type = 1,
      schain_required = 2,
      schain_hops = 0,
      schain_complete = 0,
      pass_display_manager = 2,
      display_manager_filter = 2,
      idfa_required = 2,
      filter_mraid = 2,
      max_hm_ivt_ratio = -1,
      max_pxl_ivt_ratio = -1,
      dp_id = 0,
      omid_track = 2,
      multi_format = 2,
      banner_multi_size = 2,
      native_format = 1,
      native_version = 2
    } = params;
    const token = md5(`${new Date().getTime()}Z8P5-BY${genEnCode(4)}`);
    const userToken = md5(
      `${new Date().getTime()}${demand_account_name}${tnt_id}${genEnCode(6)}`
    );

    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtil.getConnectionParams();
      conn = connection;
      await beginTransaction();

      const [userRow] = await Promise.all([
        query({
          sql: 'insert into user (account_name,type,role,status,tnt_id,token) values ?',
          values: [
            [
              [
                `Adv_${demand_account_name}`,
                UserType.Demand,
                2,
                1,
                tnt_id,
                userToken
              ]
            ]
          ]
        })
      ]);

      const userInsertId = userRow.insertId;

      const rows = await query({
        sql: 'insert into buyer (buyer_name, integration_type, status,schain_required,schain_hops,schain_complete,pass_display_manager,display_manager_filter,filter_mraid,max_hm_ivt_ratio,max_pxl_ivt_ratio,idfa_required, multi_format, banner_multi_size, tnt_id, profit_model, rev_share_ratio,imp_track_type,omid_track,token, auction_type,user_id, dp_id, native_format, native_version) values ?',
        values: [
          [
            [
              buyer_name,
              integration_type,
              status,
              schain_required,
              schain_hops,
              schain_complete,
              pass_display_manager,
              display_manager_filter,
              filter_mraid,
              max_hm_ivt_ratio,
              max_pxl_ivt_ratio,
              idfa_required,
              multi_format,
              banner_multi_size,
              tnt_id,
              profit_model,
              rev_share_ratio,
              imp_track_type,
              omid_track,
              token,
              auction_type,
              userInsertId,
              dp_id,
              native_format,
              native_version
            ]
          ]
        ]
      });

      const buyerInsertId = rows.insertId;

      await Promise.all([
        await query({
          sql: 'insert into user_role_rl (user_id,role_id,op_user_id,tnt_id) values ?',
          values: [
            [[userInsertId, RoleTypeToNumber['Demand User'], op_id, tnt_id]]
          ]
        }),
        await query({
          sql: 'insert into profit(type, buyer_id, profit_ratio, op_id, tnt_id, status) values ?',
          values: [
            [
              [
                ProfitType.Demand,
                buyerInsertId,
                profit_ratio,
                op_id,
                tnt_id,
                profit_status
              ]
            ]
          ]
        })
      ]);

      // if (Array.isArray(permissions) && permissions.length > 0) {
      //   const pms = permissions.map((item: any) => {
      //     return [userRow.insertId, item.type, item.rsc_id, tnt_id];
      //   });
      //   await query({ sql: sqls.addPermission, values: [pms] });
      // }

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      console.log(e);
      getLogger('error').error(`addDemand failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(e);
    }
  }

  async updateDemand(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  async isDemandNameExists(
    buyer_name: string,
    tnt_id: number,
    buyer_id?: number
  ): Promise<any[]> {
    const { clause, params: values } = buildSQLWhereClause([
      ['buyer_name = ?', buyer_name],
      ['tnt_id = ?', tnt_id],
      ['buyer_id != ?', buyer_id ? buyer_id : null]
    ]);

    const sql = `select buyer_name,status,buyer_id from buyer ${clause}`;

    return await dbUtil.query(sql, values);
  }

  async isTestingDemand(buyer_id: number, tnt_id: number): Promise<any[]> {
    const sql = `select status from buyer where buyer_id = ? and tnt_id = ? and status = ? limit 1`;
    return await dbUtil.query(sql, [buyer_id, tnt_id, StatusMap.Testing]);
  }

  async getDemandEndpoint(tnt_id: number, buyer_id?: number) {
    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['buyer_id = ?', buyer_id ? buyer_id : null]
    ]);

    const sql = `select * from buyer_endpoint ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async setDemandEndpoint(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  async getPretargetCampaign(
    buyer_id: number,
    tnt_id: number,
    campaign_id?: number
  ) {
    const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];

    const { clause, params: values } = buildSQLWhereClause([
      ['buyer_id = ?', buyer_id],
      ['tnt_id = ?', tnt_id],
      ['campaign_id = ?', campaign_id ? campaign_id : null],
      ['status != ?', 3]
    ]);

    const sql = `select 
      ptc.campaign_id,
      ptc.campaign_name,
      ptc.buyer_id,
      ptc.status,
      ptc.pt_flag,
      ptc.op_id,
      ptc.update_time,
      case when u.type in (?) then "System" else u.account_name end op_name
    from (
      select campaign_id,campaign_name,buyer_id,status,pt_flag,op_id,update_time
      from pt_campaign ${clause}
      ) as ptc
    left join user as u on ptc.op_id = u.user_id`;
    return await dbUtil.query(sql, [SystemType, ...values]);
  }

  async getPretargetCampaignItem(
    buyer_id: number,
    tnt_id: number,
    campaign_id?: number
  ) {
    const { clause, params: values } = buildSQLWhereClause([
      ['buyer_id = ?', buyer_id],
      ['tnt_id = ?', tnt_id],
      ['campaign_id = ?', campaign_id ? campaign_id : null]
    ]);
    const sql = `select 
      id,
      campaign_id,
      buyer_id,
      level,
      content,
      op_id,
      update_time
    from pt_item ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async updatePretargetCampaign(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  async addPretargetCampaign(params: DemandAPI.PretargetUpdateItem) {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtil.getConnectionParams();
      conn = connection;
      await beginTransaction();

      const { campaign_name, buyer_id, items, tnt_id, op_id } = params;
      const pt_flag = Array.from({ length: 20 }, () => 0);

      for (const item of items) {
        pt_flag[item.level - 1] = 1;
      }

      const rows = await query({
        sql: 'insert into pt_campaign(campaign_name, buyer_id, status, pt_flag, tnt_id, op_id) values (?, ?, ?, ?, ?, ?)',
        values: [campaign_name, buyer_id, 1, pt_flag.join('|'), tnt_id, op_id]
      });

      if (items.length) {
        const campaign_id = rows.insertId;
        const content = items.map(({ level, content }) => [
          campaign_id,
          buyer_id,
          level,
          content,
          tnt_id,
          op_id
        ]);
        await query({
          sql: 'insert into pt_item(campaign_id, buyer_id, level, content, tnt_id, op_id) values ?',
          values: [content]
        });
      }
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (error: any) {
      getLogger('error').error(`addPretargetCampaign failed ${error.message}`);
      console.log('xx丫丫error', error);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(error);
    }
  }

  async setDemandAuth(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  async getDemandAuth(buyer_id: number, tnt_id: number) {
    const sql = `
            select
                ca.id as id,
                ca.level as level,
                ca.pub_id as seller_id,
                ca.buyer_id as buyer_id,
                ssp.seller_name as seller_name,
                ssp.status as seller_status,
                ssp.integration_type as integration_type
            from (select * from config_auth where tnt_id=?) as ca
            left join seller as ssp on ca.pub_id = ssp.seller_id
            where level = ? and buyer_id = ?
        `;
    return await dbUtil.query(sql, [tnt_id, AuthLevel.Supply, buyer_id]);
  }

  async getDemandByBuyerId(buyer_id: number, tnt_id: number) {
    const sql = `select buyer_name from buyer where buyer_id=? and tnt_id=?`;
    return await dbUtil.query(sql, [buyer_id, tnt_id]);
  }

  // partner获取关联的上游
  async getPartnerDemand(tnt_id: number) {
    const sql = `
      select
        buyer_name,
        buyer_id,
        dp_id
      from buyer where tnt_id=? and dp_id > 0
    `;
    return await dbUtil.query(sql, [tnt_id]);
  }

  async getDownloadDemandList(params: { tnt_id: number; buyer_id?: number[] }) {
    const { tnt_id, buyer_id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['buyer_id in (?)', buyer_id?.length ? buyer_id : null]
    ]);
    const sql = `select buyer_id,buyer_name from buyer ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async getTestingDemand(tnt_id: number) {
    const sql = `select buyer_id from buyer where tnt_id=? and status=?`;
    return await dbUtil.query(sql, [tnt_id, StatusMap.Testing]);
  }
}

export default new DemandModel();
