import { Context } from 'koa';
import moment from 'moment';
import { APIRESULT } from '@/constants/backend-api/common';
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { BackendAPI } from '@/types/backend-api';
import { BeDashboardMetrics } from '@/constants/backend-api/full-report';

/**
 * get token info form request headers
 * @param ctx
 * @returns user_id, token, cs_domain
 */
export function getTokenInfo(ctx: Context) {
  let user_id = 0;
  let token = '';
  const { cs_domain } = ctx.state;
  if (ctx.request.method === 'POST') {
    user_id = +(ctx.request.header['x-user-id'] || 0);
    token = (ctx.request.header['x-authorization'] || '') as string;
  }
  return { user_id, token, cs_domain };
}

/**
 * Handle invalid token request,
 * @returns {void}
 */
export function handleInvalidToken(
  ctx: Context,
  user_id: number,
  token: string,
  cs_domain: string
) {
  const result = { ...APIRESULT };
  ctx.response.status = 200;
  result.timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
  result.data = null;
  result.status = {
    code: Code.API_TOKEN_INVALID,
    msg: Message.API_TOKEN_INVALID
  };
  getLogger('error').error(
    `[Saas Std API Authroization Error] user_id=[${user_id}],token=[${token}],cs_domain=[${cs_domain}] 'x-user-id' or 'x-authorization' is invalid.`
  );
  ctx.body = result;
}

/**
 * Transform the api params to the dashboardService params
 */
export function transformApiParams(params: BackendAPI.FullReportPaeams) {
  const {
    dimensions: columns,
    order,
    order_key,
    start,
    end,
    adv_id,
    pub_id,
    bundle,
    unit_id,
    ...other
  } = params;
  let split_time = 1;
  if (
    !columns ||
    !Array.isArray(columns) ||
    !columns.length ||
    !columns?.includes('day')
  ) {
    split_time = 0;
  }
  if (Array.isArray(columns) && columns.length) {
    const sIndex = columns?.findIndex((item) => item === 'pub_id');
    const bIndex = columns?.findIndex((item) => item === 'adv_id');
    const bundleIndex = columns?.findIndex((item) => item === 'bundle');
    const unitIndex = columns?.findIndex((item) => item === 'unit_id');
    if (sIndex !== -1) {
      columns[sIndex] = 'seller_id';
    }
    if (bIndex !== -1) {
      columns[bIndex] = 'buyer_id';
    }
    if (bundleIndex !== -1) {
      columns[bundleIndex] = 'app_bundle_id';
    }
    if (unitIndex !== -1) {
      columns[unitIndex] = 'placement_id';
    }
  }
  if (Array.isArray(order_key) && order_key.length) {
    const sIndex = order_key?.findIndex((item) => item === 'pub_id');
    const bIndex = order_key?.findIndex((item) => item === 'adv_id');
    if (sIndex !== -1) {
      order_key[sIndex] = 'seller_id';
    }
    if (bIndex !== -1) {
      order_key[bIndex] = 'buyer_id';
    }
  }
  return {
    ...other,
    columns,
    metrics: BeDashboardMetrics,
    split_time,
    order: order || 'desc',
    order_key: order_key || ['date'],
    start: start >= 0 ? start : 0,
    end: end >= 0 ? end : 50,
    buyer_id: adv_id,
    seller_id: pub_id,
    app_bundle_id: bundle,
    placement_id: unit_id
  };
}

/**
 * Handle invalid token request,
 * @returns {void}
 */
export function handleInvalidCsDomain(
  ctx: Context,
  user_id: number,
  token: string,
  cs_domain: string
) {
  const result = { ...APIRESULT };
  ctx.response.status = 200;
  result.timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
  result.data = null;
  result.status = {
    code: Code.CS_DOMAIN_NOT_ALLOWED,
    msg: Message.CS_DOMAIN_NOT_ALLOWED
  };
  getLogger('error').error(
    `[Saas Std API Authroization Error] user_id=[${user_id}],token=[${token}],cs_domain=[${cs_domain}] cs_domain is not allowed.`
  );
  ctx.body = result;
}
