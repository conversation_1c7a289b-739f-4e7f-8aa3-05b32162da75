/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-17 18:56:42
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getSupplyList } from '@/services/api';

export default function useSupplyList() {
  const { data, run, loading, error } = useCustomRequest(getSupplyList, {
    cacheKey: 'supply-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    supplyOptions:
      data?.map((item: SupplyAPI.SupplyListItem) => ({
        label: `${item.seller_name}(${item.seller_id})`,
        value: item.seller_id
      })) || [],
    supplyMapByValue:
      data?.reduce((acc: Record<string, string>, curr: SupplyAPI.SupplyListItem) => {
        acc[curr.seller_id] = `${curr.seller_name}(${curr.seller_id})`;
        return acc;
      }, {}) || {},
    reload: run,
    loading
  };
}
