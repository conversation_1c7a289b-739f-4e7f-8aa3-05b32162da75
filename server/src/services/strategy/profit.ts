/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 11:08:58
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:30:55
 * @Description:
 */
import { profitModel } from '@/models';
import { ProfitAPI } from '@/types/profit';
import {
  processHierarchicalData,
  ProcessStrategies,
  TabTypeToProfitTypes
} from '@/utils/profit';

import { ProfitTabType } from '@/utils/profit';
class ProfitService implements ProfitAPI.Profit {
  async updateBundleProfit(
    formData: ProfitAPI.UpdateProfitParams
  ): Promise<boolean> {
    return profitModel.updateProfit(formData);
  }
  async addBundleProfit(
    params: ProfitAPI.AddBundleProfitParams
  ): Promise<boolean> {
    return profitModel.addBundleProfit(params);
  }

  async getProfitList(
    tnt_id: number,
    cur_time_zone: string,
    type: ProfitTabType
  ): Promise<ProfitAPI.ProfitListItem[]> {
    // 获取对应类型的利润类型数组
    const profit_types = TabTypeToProfitTypes[type];
    if (!profit_types) {
      return [];
    }

    // 从数据库获取原始数据
    const list = await profitModel.getProfitList(
      tnt_id,
      cur_time_zone,
      profit_types
    );

    // bundle 类型直接返回原始数据，无需层级处理
    if (type === 'bundle') {
      return list;
    }

    const strategy =
      ProcessStrategies[type as Exclude<ProfitTabType, 'bundle'>];
    if (!strategy) {
      return [];
    }

    return processHierarchicalData(list, strategy);
  }

  async addProfit(params: ProfitAPI.AddProfitParams): Promise<boolean> {
    return profitModel.addProfit(params);
  }

  async updateProfit(params: ProfitAPI.UpdateProfitParams): Promise<boolean> {
    return profitModel.updateProfit(params);
  }
}

export default new ProfitService();
