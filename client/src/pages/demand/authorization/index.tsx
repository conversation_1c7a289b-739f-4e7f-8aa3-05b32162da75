import React, { useEffect, useState } from 'react';
import { useModel, useLocation, useAccess } from 'umi';
import { Card, message, Button, Tooltip } from 'antd';
import styles from './index.less';
import { getDemandAuth, setDemandAuth } from '@/services/api';
import { parse } from 'query-string';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import AuthorizationModal from '@/components/TransferModal';
import { DemandAndSupplyStatusMap, StatusMap } from '@/constants';
import { AuthBreadOptions } from '@/constants/demand';
import { leftTableColumns, rightTableColumns, AuthColumns } from '@/constants/demand/demand-columns';
import useConstantHook from '@/hooks/useModelConstant';
import { fetchData } from '@/utils';
import PageContainer from '@/components/RightPageContainer';
import OriginalTable from '@/components/Table/OriginalTable';
import Modal from '@/components/Modal/NormalModal';
import NormalTitle from '@/components/NormalTitle';
import InputSearch from '@/components/Input/InputSearch';

const AuthDetail: React.FC = () => {
  const access = useAccess();
  const {
    selectedRowKeys,
    setSelectedRowKeys,
    setSelectedRow,
    dataSource,
    reload,
    loading: supplyLoading,
    reset,
    handleRowChange,
    handleRowClick
  } = useConstantHook<SupplyAPI.SupplyListItem>({
    modelName: 'useSupplyListWithTesting',
    key: 'seller_id'
  });
  const [loading, setLoading] = useState(false);
  const location: any = useLocation();
  const { buyer_id } = parse(location.search);
  const [dspSupplyList, setDspSupplyList] = useState<SupplyAPI.SupplyListItem[]>([]);
  const [visible, setVisible] = useState(false);
  const [dspData, setDspData] = useState<DemandAPI.DemandListItem | undefined>(undefined);
  const { dataSource: demandList, reload: reloadDemand, loading: dspLoading } = useModel('useDemandListWithTesting');
  const [dspAuth, setDemandAuthList] = useState<string[]>([]);
  const [supplyList, setSupplyList] = useState<any[]>([]);
  const [authorizeList, setAuthorizeList] = useState<string[]>([]);
  const [clearAllTag, setClearAllTag] = useState(false);
  const [filterList, setFilterList] = useState<SupplyAPI.SupplyListItem[]>([]);

  useEffect(() => {
    if (buyer_id) {
      handleGetDspAuth(+buyer_id);
    }
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!dataSource || !dataSource.length) {
      reload();
    }
  }, []);

  useEffect(() => {
    const selecteds: any[] = selectedRowKeys.filter(v => {
      return filterList.some(item => item.seller_id === v);
    });
    setSelectedRowKeys(selecteds);
    if (selecteds.length === 0) {
      setSelectedRow(selecteds[0]);
    }
  }, [filterList]);

  useEffect(() => {
    if (dspSupplyList && dspSupplyList.length) {
      setFilterList(dspSupplyList);
    } else {
      setFilterList([]);
    }
  }, [dspSupplyList]);

  useEffect(() => {
    if (demandList && demandList.length) {
      const item = demandList.find((val: DemandAPI.DemandListItem) => buyer_id && +val.buyer_id === +buyer_id);
      if (item) {
        setDspData(item);
      }
    }
  }, [demandList]);

  useEffect(() => {
    if (dataSource && dataSource.length) {
      const list = dataSource.filter(
        (item: SupplyAPI.SupplyListItem) =>
          dspAuth.indexOf(`${item.seller_id}`) !== -1 && item.status !== StatusMap.Paused
      );

      setDspSupplyList(list);
      const result = list.map((item: SupplyAPI.SupplyListItem) => `${item.seller_id}`);
      setAuthorizeList(result);
      // 过滤paused状态上游
      const supplys = dataSource
        .filter((item: SupplyAPI.SupplyListItem) => item.status !== StatusMap.Paused)
        .map((item: SupplyAPI.SupplyListItem) => {
          return {
            ...item,
            key: `${item.seller_id}`,
            seller_id: `${item.seller_id}`,
            disabled: item.status === DemandAndSupplyStatusMap.Testing,
            tooltip: item.status === DemandAndSupplyStatusMap.Testing ? 'Testing' : ''
          };
        });
      setSupplyList(supplys);
    }
  }, [dspAuth, dataSource]);

  const handleSearch = (val: string) => {
    if (val) {
      const list = dspSupplyList.filter((item: SupplyAPI.SupplyListItem) => {
        return (
          item.seller_name.toLowerCase().indexOf(val.toLowerCase()) !== -1 ||
          item.seller_id.toString().indexOf(val) !== -1
        );
      });
      setFilterList(list);
    } else {
      setFilterList(dspSupplyList);
    }
  };

  const onCancel = () => {
    setVisible(false);
  };

  const onSave = (seller_ids: string[]) => {
    setVisible(false);
    handleSupplyChange(seller_ids);
  };

  const handleGetDspAuth = (buyer_id: number) => {
    const onSuccess = (data: any) => {
      setDemandAuthList(data.map((item: { seller_id: any }) => `${item.seller_id}`));
    };
    fetchData({ setLoading, request: getDemandAuth, params: { buyer_id }, onSuccess });
  };

  const handleOpen = () => {
    setVisible(true);
  };

  const handleUnauthorize = () => {
    const authIds = dspSupplyList
      .filter(item => selectedRowKeys.indexOf(item.seller_id) === -1)
      .map(item => `${item.seller_id}`);
    Modal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure to deauthorize selected publishers to ${dspData?.buyer_name}?`,
      okText: 'Deauthorize',
      cancelText: 'Cancel',
      onOk: () => {
        handleSupplyChange(authIds);
        setSelectedRow(undefined);
        setSelectedRowKeys([]);
        setClearAllTag(!clearAllTag);
      },
      okButtonProps: {
        danger: true
      }
    });
  };

  const handleSupplyChange = (authIds: string[]) => {
    const ori_seller_ids = (dspSupplyList && dspSupplyList.length && dspSupplyList.map(item => item.seller_id)) || [];
    const params: any = {
      buyer_id: buyer_id,
      authIds: authIds,
      ori_seller_ids
    };
    const onSuccess = () => {
      message.success('success');
      reset();
    };

    const onFinally = () => {
      handleGetDspAuth(+buyer_id!);
    };
    fetchData({ setLoading, request: setDemandAuth, params, onSuccess, onFinally });
  };

  return (
    <PageContainer options={AuthBreadOptions} isBack handleGoBack={() => history.go(-1)}>
      <Card className={styles['card-container']} loading={loading}>
        <div className={styles['container']}>
          <NormalTitle
            blackName="Authorization"
            grayName={`${dspData?.buyer_name}(${dspData?.buyer_id})`}
            isTitle={true}
            bottom={20}
            top={20}
            num={
              selectedRowKeys.length
                ? `${selectedRowKeys.length} / ${filterList.length || 0}`
                : `${filterList.length || 0}`
            }
          />
          <div className={styles['top']}>
            <div className={styles['top-left']}>
              <InputSearch placeholder="Please input publisher name or publisher id" handleSearch={handleSearch} />
            </div>
            <div className={styles['top-right']}>
              <div>
                <Button
                  danger
                  type="primary"
                  disabled={
                    access.DisabledButton('DemandAuthorizeAuth') ||
                    !selectedRowKeys ||
                    !selectedRowKeys.length ||
                    dspData?.status === StatusMap.Paused ||
                    selectedRowKeys.length === 0
                  }
                  onClick={handleUnauthorize}
                >
                  Deauthorize
                </Button>
                {dspData?.status === StatusMap.Paused ||
                  (selectedRowKeys.length === 0 && (
                    <Tooltip
                      title={
                        dspData?.status === StatusMap.Paused
                          ? `You cannot operate deauthorization because the advertiser status is "Paused"`
                          : `Please select publisher before deauthorization`
                      }
                      trigger="hover"
                    >
                      <ExclamationCircleOutlined
                        style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
                      />
                    </Tooltip>
                  ))}
              </div>
              <div>
                <Button
                  type="primary"
                  disabled={
                    access.DisabledButton('DemandAuthorizeAuth') ||
                    dspData?.status === DemandAndSupplyStatusMap.Paused ||
                    dspData?.status === DemandAndSupplyStatusMap.Testing
                  }
                  onClick={handleOpen}
                >
                  Authorize
                </Button>
                {dspData?.status === StatusMap.Paused && (
                  <Tooltip
                    title='You cannot operate authorization because the advertiser status is "Paused"'
                    trigger="hover"
                  >
                    <ExclamationCircleOutlined
                      style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
                    />
                  </Tooltip>
                )}
              </div>
            </div>
          </div>
          <OriginalTable
            loading={loading || supplyLoading || dspLoading}
            columns={AuthColumns}
            dataSource={filterList}
            rowKey="seller_id"
            rowSelection={{
              onChange: handleRowChange,
              selectedRowKeys,
              getCheckboxProps: (record: SupplyAPI.SupplyListItem) => ({
                disabled: record.status === DemandAndSupplyStatusMap.Testing
              })
            }}
            onRow={(record: SupplyAPI.SupplyListItem) => ({
              onClick: () => (record.status === DemandAndSupplyStatusMap.Testing ? null : handleRowClick(record))
            })}
            scroll={{ y: 'calc(100vh - 330px)' }}
          />
        </div>
      </Card>
      <AuthorizationModal<SupplyAPI.SupplyListItem>
        rowKey="seller_id"
        visible={visible}
        onCancel={onCancel}
        onSave={onSave}
        authorizeList={authorizeList}
        dataList={supplyList}
        leftTableColumns={leftTableColumns}
        rightTableColumns={rightTableColumns}
        leftTitle={'Unauthorized Publishers'}
        rightTitle={'Authorized Publishers'}
        searchKeys={['seller_id', 'seller_name']}
        placeholder="Please Input Publisher Name or ID"
      ></AuthorizationModal>
    </PageContainer>
  );
};

export default AuthDetail;
