/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-09 16:58:2.
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 15:59:38
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import StatusTag from '@/components/Tag/StatusTag';
import {
  ProfitModelType,
  ProfitModelTypeToString,
  StatusDesc,
  DemandAndSupplyStatusDesc,
  DemandAndSupplyStatusMap
} from '@/constants';
import {} from '@/constants';
import {
  AuctionTypeToString,
  ImpTrackingTypeToString,
  NativeFormatTypeToString,
  NativeVersionTypeToString,
  SchainCompleteMapByValueKey
} from '@/constants/demand';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { type ColumnsType } from '@/components/Table/FrontTable';

export const leftTableColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id'
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc'
  }
];

export const rightTableColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id'
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc'
  }
];

export const AuthColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id'
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc'
  }
];

export const DemandColumns: ColumnsType<DemandAPI.DemandListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    fixed: 'left',
    width: 220,
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isTesting = row.status === DemandAndSupplyStatusMap.Testing;
      return isTesting ? (
        <div>
          <span>{_}</span>
          <Tooltip title={<span>Created By system, only for testing</span>}>
            <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'ID',
    width: 100,
    dataIndex: 'buyer_id'
  },
  {
    title: 'Partner',
    width: 150,
    dataIndex: 'partner_name',
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Integration Type',
    width: 150,
    dataIndex: 'integration_type_desc'
  },
  {
    title: 'Auction Type',
    width: 150,
    dataIndex: 'auction_type',
    render: text => <>{AuctionTypeToString[text]}</>
  },
  {
    title: 'Profit Model',
    width: 120,
    dataIndex: 'profit_model',
    render: text => <>{ProfitModelTypeToString[text]}</>
  },
  {
    title: 'Profit(%)',
    width: 100,
    dataIndex: 'profit_ratio',
    render: (txt, params) => <span>{params.profit_status === DemandAndSupplyStatusMap.Active ? txt : '-'}</span>
  },
  {
    title: 'Rev Share(%)',
    width: 120,
    dataIndex: 'rev_share_ratio',
    render: (text, row) => (text && row.profit_model === ProfitModelType['Rev Share'] ? `${text}` : '-')
  },
  {
    title: 'Impression Track Type',
    width: 200,
    dataIndex: 'imp_track_type',
    render: text => <>{ImpTrackingTypeToString[text]}</>
  },
  {
    title: 'Max Px IVT(%)',
    width: 180,
    access: 'isPixalateHuman',
    dataIndex: 'max_pxl_ivt_ratio',
    render: (txt, params) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>
  },
  {
    title: 'Max Hm IVT(%)',
    width: 180,
    dataIndex: 'max_hm_ivt_ratio',
    access: 'isPixalateHuman',
    render: (txt, params) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>
  },
  {
    title: 'Native Format',
    width: 150,
    dataIndex: 'native_format',
    render: text => <>{NativeFormatTypeToString[text]}</>
  },
  {
    title: 'Native Version',
    width: 150,
    dataIndex: 'native_version',
    render: text => <>{NativeVersionTypeToString[text] || '-'}</>
  },
  {
    title: 'OMID Track',
    width: 200,
    dataIndex: 'omid_track',
    render: (_, row) => (
      <StatusTag
        type={(row['omid_track'] && StatusDesc[row['omid_track']] && StatusDesc[row['omid_track']].toLowerCase()) || ''}
      >
        {row['omid_track'] && StatusDesc[row['omid_track']]}
      </StatusTag>
    )
  },
  {
    title: 'Pass Supply Chain',
    width: 150,
    dataIndex: 'schain_required',
    render: (_, row) => (
      <StatusTag
        type={
          (row['schain_required'] &&
            StatusDesc[row['schain_required']] &&
            StatusDesc[row['schain_required']].toLowerCase()) ||
          ''
        }
      >
        {row['schain_required'] && StatusDesc[row['schain_required']]}
      </StatusTag>
    )
  },
  // add two columns: schain_complete and schain_hops
  {
    title: 'Schain Complete Required',
    width: 240,
    dataIndex: 'schain_complete',
    render: (_, row) => {
      return (
        <StatusTag type={SchainCompleteMapByValueKey[row['schain_complete']]?.toLowerCase() || ''}>
          {SchainCompleteMapByValueKey[row['schain_complete']]}
        </StatusTag>
      );
    }
  },
  {
    title: 'Schain Hops Filter',
    width: 150,
    dataIndex: 'schain_hops',
    render: schain_hops => <>{schain_hops || 'Unlimited'}</>
  },
  {
    title: 'Pass Display Manager',
    width: 180,
    dataIndex: 'pass_display_manager',
    render: _ => {
      return <StatusTag type={StatusDesc[_]?.toLowerCase() || ''}>{StatusDesc[_]}</StatusTag>;
    }
  },
  {
    title: 'Display Manager Filter',
    width: 180,
    dataIndex: 'display_manager_filter',
    render: _ => {
      return <StatusTag type={StatusDesc[_]?.toLowerCase() || ''}>{StatusDesc[_]}</StatusTag>;
    }
  },
  {
    title: 'Mraid Traffic Filter',
    width: 150,
    dataIndex: 'filter_mraid',
    render: (_, row) => (
      <StatusTag
        type={
          (row['filter_mraid'] && StatusDesc[row['filter_mraid']] && StatusDesc[row['filter_mraid']].toLowerCase()) ||
          ''
        }
      >
        {row['filter_mraid'] && StatusDesc[row['filter_mraid']]}
      </StatusTag>
    )
  },
  {
    title: 'IFA Required',
    width: 150,
    dataIndex: 'idfa_required',
    render: (_, row) => (
      <StatusTag
        type={
          (row['idfa_required'] &&
            StatusDesc[row['idfa_required']] &&
            StatusDesc[row['idfa_required']].toLowerCase()) ||
          ''
        }
      >
        {row['idfa_required'] && StatusDesc[row['idfa_required']]}
      </StatusTag>
    )
  },

  {
    title: 'Multi Format',
    width: 150,
    dataIndex: 'multi_format',
    render: (_, row) => (
      <StatusTag
        type={
          (row['multi_format'] && StatusDesc[row['multi_format']] && StatusDesc[row['multi_format']].toLowerCase()) ||
          ''
        }
      >
        {row['multi_format'] && StatusDesc[row['multi_format']]}
      </StatusTag>
    )
  },
  {
    title: 'Banner Multi Size',
    width: 150,
    dataIndex: 'banner_multi_size',
    render: (_, row) => <StatusTag type={StatusDesc[_]?.toLowerCase() || ''}>{StatusDesc[_]}</StatusTag>,
  },
  {
    title: 'Status',
    width: 100,
    dataIndex: 'status_desc',
    render: (_, row) => (
      <StatusTag
        type={
          (row['status'] &&
            DemandAndSupplyStatusDesc[row['status']] &&
            DemandAndSupplyStatusDesc[row['status']].toLowerCase()) ||
          ''
        }
      >
        {row['status'] && DemandAndSupplyStatusDesc[row['status']]}
      </StatusTag>
    )
  }
];
