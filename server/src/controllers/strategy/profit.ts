/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 11:33:30
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 19:27:10
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import {
  AddBundleProfitSchema,
  AddProfitSchema,
  GetProfitListSchema,
  UpdateBundleProfitSchema,
  UpdateProfitSchema
} from '@/schema/strategy/profit';
import { profitService } from '@/services';
import { ProfitAPI } from '@/types/profit';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class ProfitCtrl implements ProfitAPI.ProfitCtrlInterface {
  @validateBody(GetProfitListSchema)
  async getProfitList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone, type } = ctx.request.body;
    const list = await profitService.getProfitList(
      ctx.session!.tnt_id,
      cur_time_zone,
      type
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddProfitSchema)
  async addProfit(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const list = await profitService.addProfit(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `addProfit success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  @validateBody(UpdateProfitSchema)
  async updateProfit(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const list = await profitService.updateProfit(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `updateProfit success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  // 新增 bundle 的接口
  @validateBody(AddBundleProfitSchema)
  async addBundleProfit(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const created = await profitService.addBundleProfit(formData);
    if (created) {
      result = getCtxResult('SUCCESS', created);
      getLogger('app').info(
        `addBundleProfit success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  // 修改 bundle 的接口（只能改 profit，数组结构）
  @validateBody(UpdateBundleProfitSchema)
  async updateBundleProfit(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const updated = await profitService.updateBundleProfit(formData);
    if (updated) {
      result = getCtxResult('SUCCESS', updated);
      getLogger('app').info(
        `updateBundleProfit success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }
}

export default new ProfitCtrl();
