/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-14 19:11:14
 * @Description:
 */
import { <PERSON><PERSON>, Drawer, Spin } from 'antd';
import type { DrawerProps } from 'antd';
import styles from './index.less';
import { CloseOutlined } from '@ant-design/icons';
import NormalTitle from '@/components/NormalTitle';

type NormalDrawerProps = DrawerProps & {
  onConfirm?: () => void;
  loading: boolean;
  blackName: string;
  grayName?: string;
  showFooter?: boolean;
  titleMaxWidth?: number;
  contentMaxHeight?: string;
};

function NormalDrawer({
  onConfirm,
  onClose,
  blackName,
  grayName,
  children,
  loading,
  showFooter = true,
  titleMaxWidth = 424,
  contentMaxHeight = 'calc(100vh - 100px)',
  ...rest
}: NormalDrawerProps): JSX.Element {
  return (
    <Drawer
      maskClosable={false}
      width={424} // 默认抽屉宽度
      {...rest}
      onClose={onClose}
      closable={false}
      className={rest.className || styles['normal-drawer-container']}
      title=""
    >
      <Spin spinning={loading}>
        <div className={styles['drawer-content-container']}>
          <div className={styles['header']}>
            <NormalTitle
              blackName={blackName}
              grayName={grayName}
              isTitle
              bottom={0}
              top={0}
              maxWidth={titleMaxWidth}
            />
            <Button
              icon={<CloseOutlined />}
              onClick={onClose}
              style={{ background: 'none', boxShadow: 'none' }}
              disabled={loading}
            ></Button>
          </div>
          <div className={styles['content']} id="drawer-content" style={{ maxHeight: contentMaxHeight }}>
            {children}
          </div>
          {showFooter && (
            <div className={styles['footer']}>
              <Button onClick={onClose} style={{ marginRight: 12 }}>
                Cancel
              </Button>
              <Button type="primary" onClick={onConfirm}>
                Confirm
              </Button>
            </div>
          )}
        </div>
      </Spin>
    </Drawer>
  );
}

export default NormalDrawer;
