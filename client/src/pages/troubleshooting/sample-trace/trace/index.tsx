/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-21 15:47:33
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-26 14:48:27
 * @Description:
 */

import NormalTitle from '@/components/NormalTitle';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import { TopBarSearchItem } from '@/components/TopBar';
import { SampleTab, SampleTabOptions, TaskAllColumns, treeTheme } from '@/constants/troubleshooting';
import {
  TraceBreadOptions,
  TraceColumnKeysMap,
  TraceDemandSearchOption
} from '@/constants/troubleshooting/sampletrace-trace';
import useCustomRequest from '@/hooks/useCustomRequest';
import { getSampleTraceList } from '@/services/troubleshooting';
import { ColumnProps } from 'antd/es/table';
import Paragraph from 'antd/lib/typography/Paragraph';
import React, { useEffect, useState } from 'react';
import { JSONTree } from 'react-json-tree';
import { history, useModel, useSearchParams } from 'umi';
import styles from './index.less';

const Page: React.FC = () => {
  const [searchParams] = useSearchParams();
  const { dataSource: tasklist, reload: reloadTaskList, loading: taskListLoading } = useModel('useSampleTraceTaskList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandListWithTesting');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');

  const [searchOptions, setSearchOptions] = useState<TopBarSearchItem[]>([]);
  const [currentTab, setCurrentTab] = useState<number>(SampleTab.SupplyRequest);
  const [dataSource, setDataSource] = useState<TroubleShootingAPI.TraceListItem[]>([]);
  const [columns, setColumns] = useState<ColumnProps<TroubleShootingAPI.TraceListItem>[]>([]);
  const {
    run,
    loading,
    data: tracList
  } = useCustomRequest(getSampleTraceList, {
    manual: true
  });

  // 判断用户是否有权限查看当前页面
  useEffect(() => {
    if (!taskListLoading && tasklist && tasklist.length > 0) {
      const task = tasklist.find(item => item.tag_id === searchParams.get('tag_id'));
      if (!task) {
        history.push('/403');
      }
    }
  }, [tasklist, taskListLoading]);

  // 初始化
  // 1. 获取tasklist，入参 tag_id
  // 2. 获取demandList（搜索选项）
  // 3. 获取supplyList（搜索选项）
  // 4. 设置搜索项和搜索项的选项
  useEffect(() => {
    if (!searchParams.get('tag_id')) return;
    if (searchParams.get('type') && !isNaN(Number(searchParams.get('type')))) {
      setCurrentTab(Number(searchParams.get('type')));
    }
    reloadTaskList();
    !loading && run({ tag_id: searchParams.get('tag_id') });
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }

    handleSearchOptions();
  }, []);
  // 当tracList/demandList/supplyList变化时
  // 1. 更新dataSource
  useEffect(() => {
    if (!tracList || !demandList || !supplyList) return;
    // TODO 不应该直接改变原数据，应该在 column 处理
    const data = tracList.map((item: TroubleShootingAPI.TraceListItem) => {
      if (!item.buyer_id) {
        item.buyer = '-';
      } else if (item.buyer_id && Array.isArray(demandList)) {
        const buyer = demandList.find((v: any) => v.buyer_id === +item.buyer_id);
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
      }
      if (!item.seller_id) {
        item.seller = '-';
      } else if (item.seller_id && Array.isArray(supplyList)) {
        const seller = supplyList.find((v: { seller_id: number }) => v.seller_id === +item.seller_id);
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
      }
      return item;
    });
    setDataSource(data);
  }, [tracList, demandList, supplyList]);

  useEffect(() => {
    if (!Array.isArray(demandList) || !Array.isArray(supplyList) || !currentTab) return;
    const columns = TaskAllColumns.filter(item => TraceColumnKeysMap[currentTab].includes(item.dataIndex as string));
    console.log(columns);
    setColumns(columns);

    handleSearchOptions();
  }, [currentTab, demandList, supplyList]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(TraceDemandSearchOption));
    const sindex = options.findIndex(item => item.key === 'tag_id');
    if (sindex !== -1) {
      options[sindex].value = searchParams.get('tag_id') || '';
      options[sindex].disabled = true;
    }
    if (Array.isArray(supplyList)) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');

      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    if ([SampleTab.DemandResponse, SampleTab.DemandRequest].includes(currentTab)) {
      if (Array.isArray(demandList)) {
        const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
          return {
            label: `${item.buyer_name}(${item.buyer_id})`,
            value: item.buyer_id
          };
        });
        const dIndex = options.findIndex(item => item.key === 'buyer_id');
        if (dIndex !== -1) {
          options[dIndex].options = dOptions;
        }
      }

      setSearchOptions(options);
    } else {
      const supplyOptions = options.filter(item => item.key !== 'buyer_id');
      setSearchOptions(supplyOptions);
    }
  };

  const expandedRowRender = (record: any) => {
    let data;
    let isJson = false;
    try {
      data = JSON.parse(record.data);
      isJson = true;
    } catch (error) {
      data = record.data;
    }
    return (
      <div style={{ fontFamily: 'monospace' }}>
        <div className={styles['child-row-container']}>
          {!isJson && (
            <span className={styles['format-error']}>
              The body data is not in JSON format. Following is the raw data.
            </span>
          )}
        </div>
        {isJson ? (
          <JSONTree
            data={data}
            invertTheme={false}
            theme={treeTheme}
            labelRenderer={(keyPath: readonly (string | number)[], nodeType: string) => {
              if (['String', 'Object'].includes(nodeType)) {
                let value = data;
                let len = keyPath.length;

                for (let index = len - 2; index >= 0; index--) {
                  value = value[keyPath[index]];
                }
                return (
                  <span style={{ display: 'inline-flex', fontFamily: 'inherit' }}>
                    <span style={{ width: 'max-content' }}>{keyPath[0]}</span>
                    <Paragraph
                      copyable={{
                        tooltips: false,
                        text: nodeType === 'Object' ? JSON.stringify(value) : value,
                        icon: <RixEngineFont type="rix-copy" style={{ fontSize: '16px' }} />
                      }}
                      style={{ display: 'flex', marginBottom: 0, textIndent: 'initial' }}
                    ></Paragraph>
                  </span>
                );
              }
              return keyPath[0];
            }}
          />
        ) : (
          <span>{data}</span>
        )}
      </div>
    );
  };

  return (
    <PageContainer
      flexDirection="column"
      options={TraceBreadOptions}
      backUrl="/troubleshooting/sample-trace/task"
      isBack
      className={styles['trace-container']}
    >
      <FrontTable<TroubleShootingAPI.TraceListItem>
        pageTitle={
          <NormalTitle
            blackName={SampleTabOptions.find(item => item.value === currentTab)?.label || ''}
            isTitle={true}
          />
        }
        searchOptions={searchOptions}
        loading={dataSource && !dataSource.length && loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={run}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={120}
        emptyRender={dataSource && dataSource.length ? () => <span></span> : undefined}
        expandable={{ expandedRowRender }}
        initialValues={{ tag_id: searchParams.get('tag_id') }}
        isFold={true}
      />
    </PageContainer>
  );
};

export default Page;
