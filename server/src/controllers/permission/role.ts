/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 15:34:16
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-22 17:13:10
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import {
  AddRoleSchema,
  DeleteRoleSchema,
  EditRolePmsSchema,
  UpdateRoleSchema
} from '@/schema/permission/role';
import { roleService } from '@/services';
import { PermissionAPI } from '@/types/permission';
import { getCtxResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

class RoleController implements PermissionAPI.RoleCtrl {
  @validateBody(AddRoleSchema)
  async addRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { role_name, role_type = 3 } = options;
    const { tnt_id } = ctx.session!;
    options.tnt_id = tnt_id;
    const repeat = await roleService.isRoleExist(role_name, role_type, tnt_id);
    if (Array.isArray(repeat) && repeat[0].count === 0) {
      const data = await roleService.addRole(options);
      if (data) {
        getLogger('app').info(
          `addRole success formData=[${JSON.stringify(options)}]`
        );
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROLE_NAME_EXISTS', repeat);
    }
    ctx.body = result;
  }

  @validateBody(UpdateRoleSchema)
  async updateRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { tnt_id } = ctx.session!;
    options.tnt_id = tnt_id;
    const { role_name, role_type = 3 } = options;
    const repeat = await roleService.isRoleExist(role_name, role_type, tnt_id);
    if (Array.isArray(repeat) && repeat[0].count === 0) {
      const data = await roleService.updateRole(options);
      if (data) {
        getLogger('app').info(
          `addRole success formData=[${JSON.stringify(options)}]`
        );
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROLE_NAME_EXISTS', repeat);
    }
    ctx.body = result;
  }

  @validateBody(EditRolePmsSchema)
  async editRolePms(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { tnt_id } = ctx.session!;
    options.tnt_id = tnt_id;
    const data = await roleService.editRolePms(options);
    if (data) {
      getLogger('app').info(
        `updateRole success formData=[${JSON.stringify(options)}]`
      );
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getAllRole(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const data = await roleService.getAllRole(tnt_id);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateBody(DeleteRoleSchema)
  async deleteRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { tnt_id } = ctx.session!;
    options.tnt_id = tnt_id;
    const data = await roleService.deleteRole(options);

    if (Array.isArray(data) && data.length) {
      result = getCtxResult('ROLE_HAS_USER', data);
    } else {
      getLogger('app').info(
        `deleteRole success formData=[${JSON.stringify(options)}]`
      );
      result = getCtxResult('SUCCESS', data);
    }

    ctx.body = result;
  }
}

export default new RoleController();
