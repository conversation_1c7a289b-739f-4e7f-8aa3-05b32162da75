/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-26 18:36:05
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 12:10:42
 * @Description:
 */

import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import NormalSelect from '../Select/NormalSelect';
import moment from 'moment-timezone';
type props = {
  dates?: number | [number, number];
  format?: string;
  onChange?: (values: string[]) => void;
  defaultValue?: string[];
};
/**
 * @description: 日期多选
 * @param {dates} 需要生成的日期,可以是单个日期或者日期区间,左区间应小于右区间,
 *                以当前日期为基准生成日期区间,默认为当前日期
 *                eg:[-1,1] 生成当前日期前一天到后一天的日期，共三天
 *                eg:1 生成当前日期到后一天的日期，共两天
 * @params {format} 日期格式化格式,默认为'YYYY-MM-DD'
 */
const SelectDatesPicker = ({ dates, format = 'YYYY-MM-DD', onChange, defaultValue }: props) => {
  if (Array.isArray(dates)) {
    if (dates.length > 2) throw new Error('dates should be an array with length of 2');
    if (dates[0] > dates[1]) throw new Error('The left interval must be greater than the right interval');
  }

  const [values, setValues] = useState<string[]>([]);
  const [firstDatesOptions, setFist] = useState<{ label: string; value: string; disabled?: boolean }[]>([]);
  const [secondDatesOptions, setSecond] = useState<{ label: string; value: string; disabled?: boolean }[]>([]);

  const calculateDate = (num: number, format: string) => {
    return num >= 0 ? moment().add(num, 'days').format(format) : moment().subtract(-num, 'days').format(format);
  };

  const getDatesOptions = (dates: number | [number, number] = 0) => {
    if (dates === 0) return [{ label: calculateDate(0, format), value: calculateDate(0, 'YYYYMMDD') }];
    const options = [];
    const rang = [];
    if (Array.isArray(dates)) {
      const start = dates[0];
      const end = dates[1] || 0;
      start < end ? rang.push(start, end) : rang.push(end, start);
    } else {
      dates < 0 ? rang.push(dates + 1, 0) : rang.push(0, dates - 1);
    }
    for (let i = rang[0]; i <= rang[1]; i++) {
      options.push({
        label: calculateDate(i, format),
        value: calculateDate(i, 'YYYYMMDD')
      });
    }
    return options;
  };

  useEffect(() => {
    if (dates) {
      setFist(getDatesOptions(dates));
      setSecond(getDatesOptions(dates));
    }
  }, [dates]);

  useEffect(() => {
    onChange?.(values.filter(Boolean));
  }, [values]);
  useEffect(() => {
    if (defaultValue && !values?.length) {
      setValues(defaultValue);
    }
  }, [defaultValue]);

  const handleChange = (value: string, selector: 'first' | 'second') => {
    const options = selector === 'first' ? secondDatesOptions : firstDatesOptions;
    options.forEach(item => {
      if (item.value === value) {
        item.disabled = true;
      } else {
        item.disabled = false;
      }
    });
    selector === 'first' ? setSecond([...options]) : setFist([...options]);
    setValues(selector === 'first' ? [value, values[1] || ''] : [values[0] || '', value]);
  };
  return (
    <div className={styles['container']}>
      <NormalSelect options={firstDatesOptions} onChange={value => handleChange(value, 'first')} value={values?.[0]} />
      <NormalSelect
        options={secondDatesOptions}
        onChange={value => handleChange(value, 'second')}
        value={values?.[1]}
      />
    </div>
  );
};
export default SelectDatesPicker;
