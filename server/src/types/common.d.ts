/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:30:45
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 10:48:04
 * @Description:
 */

import { Context } from 'koa';
import { type } from 'os';

declare namespace CommonAPI {
  type UploadParams = {
    fileName: string; // gcs文件名，可包含路径，eg: 'test/test.txt'上传到bucket 桶的 test目录下
    filePath: string; // 本地文件路径
    bucket?: string; // 桶名
    clearLocal?: boolean; // 是否清除本地文件，默认清除
  };
  type DownloadParams = {
    fileName: string;
    bucket?: string;
  };
  type IntegrationTypeItem = {
    id: number;
    itg_name: string;
    itg_key: string;
    create_time: string;
    update_time: string;
  };

  type SessionItem = {
    user_id: number;
    account_name: string;
    tnt_id: number;
    email: string;
    tnt_name: string;
    token: string;
    cs_domain: string;
    role: number;
    session_id: string;
    isLogin: boolean;
  };

  type NotificationListItem = {
    id: number;
    title: string;
    content: string;
    unread: number;

    create_time: string;
    update_time: string;
  };

  type UpdateNotificationParams = {
    msg_id: number | number[];
    user_id: number;
    tnt_id: number;
  };

  type AddNotificationParams = {
    title: string;
    content: string;
    rule_id: number; // 规则
    tnt_id: number; // 当前租户
    tnt_ids: number[]; // 需要插入哪些租户
    user_id: number; // 操作人
    ext: number; // 是否发送ads.txt
  };

  type ReadLogListItem = {
    id: number;
    msg_id: number;
    tnt_id: number;
    user_id: number;
  };

  type GetFaviconParams = {
    cs_domain: string;
    tnt_id?: number;
  };
  interface Result {
    code: number;
    message: string;
    data: any;
  }

  interface Common {
    getBuyerIntegrationType(): Promise<IntegrationTypeItem[]>;
    getSellerIntegrationType(): Promise<IntegrationTypeItem[]>;
  }

  interface CommonCtrl {
    getBuyerIntegrationType(ctx: Context): void;
    getSellerIntegrationType(ctx: Context): void;
  }
}
