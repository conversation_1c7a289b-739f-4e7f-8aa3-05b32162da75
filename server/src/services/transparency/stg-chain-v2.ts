/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:56:21
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:23:18
 * @Description:
 */

import { stgModelV2 } from '@/models';
import { TransparencyAPI } from '@/types/transparency';
class StgChainServicesV2 implements TransparencyAPI.StgChainServices {
  async isStgChainExist(params: any) {
    return await stgModelV2.isStgChainExist(params);
  }

  async getStgChainList(tnt_id: number) {
    return await stgModelV2.getStgChainList(tnt_id);
  }

  async addStgChain(params: TransparencyAPI.AddStgParams): Promise<Boolean> {
    return await stgModelV2.addStgChain(params);
  }

  async updateStgChain(
    params: TransparencyAPI.UpdateStgParams
  ): Promise<Boolean> {
    return await stgModelV2.updateStgChain(params);
  }
}
export const stgChainServiceV2 = new StgChainServicesV2();
