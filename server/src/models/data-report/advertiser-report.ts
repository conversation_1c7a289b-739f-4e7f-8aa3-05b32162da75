/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-03 21:37:59
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-21 18:48:58
 * @Description:
 */
// ?types

// function getReportSql(options: DashboardAPI.ParseParams, isAll: boolean) {
//   const { dimension, metric, conditions, table, limit, group_by, order_by } =
//     options;
//   const baseSQL = `
//       select
//           ${dimension ? `${dimension},` : ''}
//           ${metric ? `${metric}` : ''}
//           from ${table} where ${conditions}
//           ${group_by}
//           ${order_by}
//           ${isAll ? 'limit 10000' : limit}
//   `;
//   return baseSQL;
// }

class AdvReportModel {
  // async getDashboardList(options: any, api_url: string): Promise<any> {
  //   const reprotSql = getReportSql(options, false);
  //   return await queryStackOverflow(reprotSql, { tag: api_url });
  // }
  // async countAdvReportList(options: any, api_url: string): Promise<number> {
  //   const reprotSql = getReportSql(options, true);
  //   const countSql = `select count(*) as count from (${reprotSql}) as t`;
  //   const res = await queryStackOverflow(countSql, { tag: api_url });
  //   if (Array.isArray(res) && res.length) {
  //     return res[0].count;
  //   }
  //   return 0;
  // }
  // // 下载全部
  // async downloadAllReport(options: any, api_url: string) {
  //   const reprotSql = getReportSql(options, true);
  //   return await bigqueryStream(reprotSql, { tag: api_url });
  // }
}

export default new AdvReportModel();
