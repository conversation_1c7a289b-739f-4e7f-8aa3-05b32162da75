/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-20 11:07:24
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-12 15:49:33
 * @Description:
 */
// ?libs
import React, { useEffect, useState } from 'react';
import { useModel, useAccess } from 'umi';

// ?components
import FrontTable from '@/components/Table/FrontTable';
import PageContainer from '@/components/RightPageContainer';
import { PlusOutlined } from '@ant-design/icons';
import OperateRender, { OperateRenderItem } from '@/components/OperateRender';

// ?constants
import { CreativeSearchOption, CreativeBreadOptions, CreativeColumns } from '@/constants/strategy/creative';
import { StatusMap } from '@/constants';

// ?type
import { TopBarSearchItem } from '@/components/TopBar';
import AddCreativeDrawer from '../components/AddCreativeDrawer';
import { ColumnProps } from 'antd/lib/table';

export default function Creative() {
  const { data: creativeList, reload, loading } = useModel('useCreativeList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');

  const [dataSource, setDataSource] = useState<StrategyAPI.CreativeListItem[]>([]);
  const [searchOptions, setSearchOptions] = useState(CreativeSearchOption);
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [buyerId, setBuyerId] = useState<number | undefined>(undefined);
  const [creative, setCurrentCreative] = useState<StrategyAPI.CreativeListItem>();
  const handleEditCreative = (params: StrategyAPI.CreativeListItem) => {
    setCurrentCreative(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = (buyer_id?: number) => {
    if (buyer_id) {
      setBuyerId(buyer_id);
    }
    setIsEdit(false);
    setVisible(true);
    setCurrentCreative(undefined);
  };
  const btnOptions: OperateRenderItem[] = [
    // {
    //   label: 'Add',
    //   icon: <PlusOutlined />,
    //   onClick: params => handleClickCreate(params.buyer_id),
    //   hide: (params: any) => !params.isParent,
    //   accessCode: 'addCreativeAuth'
    // },
    {
      label: 'Edit',
      onClick: handleEditCreative,
      accessCode: 'updateCreativeAuth',
      hide: (params: any) => params.isParent
    }
  ];

  const tmpColumns: ColumnProps<StrategyAPI.CreativeListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 90,
      fixed: 'right',
      render: (txt, params) => <OperateRender btnOptions={btnOptions} params={params} isExpendTable={true} />
    }
  ];
  const columns = [...CreativeColumns, ...tmpColumns];

  useEffect(() => {
    reload();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    handleSearchOptions();
  }, []);

  useEffect(() => {
    if (!creativeList || !Array.isArray(creativeList)) return;
    setDataSource(creativeList);
  }, [creativeList]);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList]);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    if (demandList) {
      const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      dOptions.unshift({ label: `All Advertisers`, value: 0 });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      sOptions.unshift({ label: `All Publishers`, value: 0 });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    setSearchOptions(options);
  };

  const handleClose = () => {
    setIsEdit(false);
    setVisible(false);
    setCurrentCreative(undefined);
  };
  const normalEmptyRender = () => <span></span>;
  return (
    <PageContainer flexDirection="column" options={CreativeBreadOptions}>
      <FrontTable<StrategyAPI.CreativeListItem>
        pageTitle="Creative"
        isFold
        searchOptions={searchOptions}
        loading={dataSource && !dataSource.length && loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create Creative Block',
            type: 'primary',
            size: 'small',
            onClick: () => handleClickCreate(),
            icon: <PlusOutlined />,
            accessCode: 'addCreativeAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={80}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        initialValues={{ status: StatusMap.Active }}
      />
      <AddCreativeDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reload={reload}
        item={creative}
        demandList={demandList || []}
        supplyList={supplyList || []}
      />
    </PageContainer>
  );
}
