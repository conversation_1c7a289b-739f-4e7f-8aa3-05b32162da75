/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-21 18:55:30
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 19:32:01
 * @Description:
 */
import {
  AbTestReportDimension,
  AbTestReportDimensionOrder,
  AbTestReportMetrics
} from '@/constants/report/abtest-report';
import Joi from 'joi';

const metricsValidation = Joi.array()
  .min(1)
  .items(
    Joi.string().custom((value, helpers) => {
      // 防止 SQL 注入攻击
      const isRight = AbTestReportMetrics.includes(value);
      if (!isRight) {
        return helpers.error('metricsValidation invalid');
      }
      return value;
    })
  )
  .required();
const dimensionValidation = Joi.array().items(
  Joi.string().custom((value, helpers) => {
    const isRight = AbTestReportDimension.includes(value);
    if (!isRight) {
      return helpers.error('dimensionValidation invalid');
    }
    return value;
  })
);

const abTestReportScheme = {
  region: Joi.array().items(Joi.string().valid('USE', 'APAC', 'EUW')),
  country: Joi.array().items(Joi.string().max(3)),
  ad_size: Joi.array().items(Joi.string().pattern(/^\d+$/)),
  ad_format: Joi.array().items(Joi.number().integer().valid(1, 2, 3, 4)),
  buyer_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer().required())
  ),
  seller_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer().required())
  ),
  metrics: metricsValidation,
  columns: dimensionValidation,
  start_date: Joi.string()
    .pattern(/^\d{8}$/)
    .required(), // 8位的数字 默认当天
  end_date: Joi.string()
    .pattern(/^\d{8}$/)
    .required(), // 8位的数字 默认当天
  start: Joi.number().integer().min(0).required(),
  end: Joi.number()
    .integer()
    .min(1)
    .required()
    .when('start', {
      // 必须大于start
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .max(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: relation => relation + 500
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    })
    .when('start', {
      is: Joi.number().integer(),
      then: Joi.number()
        .integer()
        .min(
          Joi.ref('start', {
            // 限制最大查询数量
            adjust: relation => relation + 1
          })
        )
        .message(
          'end must be larger then start,and less then or equal start + 500'
        )
    }),
  order: Joi.string().valid('desc', 'asc'),
  order_key: Joi.array().items(
    Joi.string().valid(...AbTestReportMetrics, ...AbTestReportDimensionOrder)
  ),
  test_tag_a: Joi.alternatives().try(
    Joi.string().valid(
      'op_bidfloor',
      'op_profit_ratio',
      'op_banner_transfer_format'
    ),
    Joi.array().items(
      Joi.string().valid(
        'op_bidfloor',
        'op_profit_ratio',
        'op_banner_transfer_format'
      )
    )
  )
};
export const AbTestReportScheme = Joi.object({ ...abTestReportScheme });
