import { IframeMessageCommunicator } from '@/hooks/useIframe/message-comm';
import { SendMessageOptions, TypedMessageMap } from '@/hooks/useIframe/type';

class IframeManager {
  private messageCommunicator: IframeMessageCommunicator | null = null;
  private isInitialized = false;
  private debug: boolean = false;

  public isInIframe() {
    try {
      return window.self !== window.top;
    } catch (e) {
      return false; // If an error occurs, assume we are not in an iframe
    }
  }

  /**
   * 判断是否为 topon 租户，在 iframe 中，tnt_type 为 2
   * 如果 tnt_type 为 undefined，则判断当前域名是否为 topon 域名
   * @param tnt_type 租户类型
   * @returns 是否为 topon 租户
   */
  public isToponTenant(tnt_type?: number) {
    if (!this.isInIframe()) {
      return false;
    }

    if (tnt_type !== undefined) {
      return tnt_type === 2;
    }

    const enumDomains = [
      /^https:\/\/127\.0\.0\.1:8000/,
      /^https:\/\/office\.local:8000/,
      /^https:\/\/topon-[\w-]+\.console(-t)?\.rixengine\.com/
    ];

    return enumDomains.some(pattern => pattern.test(location.href));
  }

  /**
   * 自定 topon 的样式，在 iframe 中，tnt_type 为 2
   * @param tnt_type 租户类型
   */
  public setToponStyle(tnt_type?: number) {
    if (this.isToponTenant(tnt_type)) {
      document.body.classList.add('topon');
    }
  }

  /**
   * 初始化消息通信器
   * @param sendWhiteList 发送消息的域名白名单，支持完整URL
   * @param listenWhiteList 监听消息的域名白名单，支持完整URL和正则表达式
   * @param debug 是否开启调试模式
   */
  public initMessageCommunicator(debug: boolean = false): void {
    const sendWhiteList = ToponSendWhiteList;
    const listenWhiteList = ToponListenWhiteList;
    this.debug = debug;

    if (this.isInitialized) {
      if (this.debug) {
        console.warn('[IframeManager] Message communicator already initialized');
      }
      return;
    }

    if (!this.isInIframe()) {
      if (this.debug) {
        console.warn('[IframeManager] Not in iframe, message communicator initialization skipped');
      }
      return;
    }

    try {
      this.messageCommunicator = new IframeMessageCommunicator(sendWhiteList, listenWhiteList, this.debug);
      this.messageCommunicator.listen();
      this.isInitialized = true;

      // 发送就绪信号
      this.messageCommunicator.sendReadySignal();

      if (this.debug) {
        console.log('[IframeManager] Message communicator initialized successfully');
      }
    } catch (error) {
      console.error('[IframeManager] Failed to initialize message communicator:', error);
    }
  }

  /**
   * 发送消息到父页面
   * @param type 消息类型
   * @param data 消息数据
   * @param options 发送选项
   * @returns 发送是否成功
   */
  public sendMessage<K extends keyof TypedMessageMap>(
    type: K,
    data: TypedMessageMap[K],
    options?: SendMessageOptions
  ): boolean {
    if (!this.messageCommunicator) {
      if (this.debug) {
        console.error('[IframeManager] Message communicator not initialized. Call initMessageCommunicator first.');
      }
      return false;
    }

    if (!this.isInIframe()) {
      if (this.debug) {
        console.warn('[IframeManager] Not in iframe, message sending skipped');
      }
      return false;
    }

    try {
      return this.messageCommunicator.send(type, data, options);
    } catch (error) {
      if (this.debug) {
        console.error('[IframeManager] Failed to send message:', error);
      }
      return false;
    }
  }

  /**
   * 监听来自父页面的消息
   * @param type 消息类型
   * @param handler 处理函数
   * @returns 取消监听的函数
   */
  public onMessage<K extends keyof TypedMessageMap>(
    type: K,
    handler: (data: TypedMessageMap[K]) => void | Promise<void>
  ): () => void {
    if (!this.messageCommunicator) {
      if (this.debug) {
        console.error('[IframeManager] Message communicator not initialized. Call initMessageCommunicator first.');
      }
      return () => {};
    }

    try {
      return this.messageCommunicator.on(type, handler);
    } catch (error) {
      if (this.debug) {
        console.error('[IframeManager] Failed to register message handler:', error);
      }
      return () => {};
    }
  }

  /**
   * 停止消息监听并清理资源
   */
  public destroy(): void {
    if (this.messageCommunicator) {
      this.messageCommunicator.stopListening();
      this.messageCommunicator = null;
      this.isInitialized = false;
    }
  }

  /**
   * 获取消息通信器实例
   * @returns 消息通信器实例或null
   */
  public getMessageCommunicator(): IframeMessageCommunicator | null {
    return this.messageCommunicator;
  }

  /**
   * 检查消息通信器是否已初始化
   * @returns 是否已初始化
   */
  public isMessageCommunicatorInitialized(): boolean {
    return this.isInitialized && this.messageCommunicator !== null;
  }
}

// topon 发送白名单，仅支持具体域名
export const ToponSendWhiteList: string[] = [
  // 'http://127.0.0.1:8001',
  // 'http://localhost:8001',
  // 'http://***************:8001',
  // 'https://office.local:8000',
  // 'https://platform-iframe.rixfe.com',
  '*'
];

// topon 监听白名单，支持具体域名和正则表达式
export const ToponListenWhiteList: string[] = [
  '*',
  // 'http://127.0.0.1:8001',
  // 'http://localhost:8001',
  // 'http://***************:8001',
  // 'https://office.local:8000',
  // 'https://platform-iframe.rixfe.com',
  // 'regex:^https:\\/\\/topon-[\\w-]+\\.console(-t)?\\.rixengine\\.com'
];

const iframeManager = new IframeManager();

export { iframeManager };
