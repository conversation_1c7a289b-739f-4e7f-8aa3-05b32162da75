import { LevelConfigMap, ValueType } from './level-mapping';

// 定义转换后的数据项类型
export type PretargetingDataItem = Omit<DemandAPI.PretargetCampaignItem, 'items'> & {
  
  [key: string]: any;
};

/**
 * 将后端预定位数据转换为扁平化的数据结构
 * 后续逻辑迁移到后端处理
 * @param campaigns 后端返回的预定位数据
 * @returns 转换后的扁平化数据
 */
export const transformPretargetListData = (campaigns: DemandAPI.PretargetCampaignItem[]): PretargetingDataItem[] => {
  return campaigns.map(campaign => {
    const { items, ...rest } = campaign;
    // 创建基础数据结构
    const result: PretargetingDataItem = { ...rest };

    if (!items || !Array.isArray(items)) {
      return result;
    }

    // 遍历 items 数据，进行扁平化处理
    for (const { level, content } of items) {
      // 获取 level 对应的配置
      const levelIndex = level - 1;
      const config = LevelConfigMap[levelIndex];
      if (!config) {
        continue;
      }

      // 根据配置的 valueType 处理内容
      // 根据 valueType 对 content 进行类型转换
      let convertedContent: any;

      switch (config.valueType) {
        case ValueType.STRING:
          convertedContent = content;
          break;
        case ValueType.NUMBER:
          convertedContent = Number(content);
          break;
        case ValueType.STRING_ARRAY:
          convertedContent = content.split(',');
          break;
        case ValueType.NUMBER_ARRAY:
          convertedContent = content.split(',').map((val: string) => Number(val.trim()));
          break;
        default:
          convertedContent = content;
      }

      // 将转换后的内容添加到结果对象中
      result[config.name] = convertedContent;
      // 同时添加 level 信息
      result[`${config.levelName}`] = level;
      // 添加状态
      result[config.statusName] = config.status;
    }

    return result;
    // 排序：根据 update_time 倒序，数据为 "2025-06-04 11:45:10"
  }).sort((a, b) => {
    const aTime = new Date(a.update_time).getTime();
    const bTime = new Date(b.update_time).getTime();
    return bTime - aTime;
  });
};
