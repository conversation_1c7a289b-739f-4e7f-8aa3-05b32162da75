/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-22 18:59:59
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-28 20:34:13
 * @Description:
 */

const config = Object.seal({
  port: 3001,
  database: {
    host: '**********',
    port: 8306,
    user: 'test',
    password: '%xOakkb3',
    database: 'db_saas',
    // database: 'db_saas_prod',
    timezone: 'UTC'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  encryptionStr: '8rL5Mbp5-', // 加密密钥
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  redisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
    optArray: [
      {
        port: 8379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 8379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
    BE_API_DAILY_LIMIT: 50
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 8379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 8379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  UpdateUserInfo_KEY: 'needUpdateInfo',
  NotifyFeishuUrl:
    'https://open.feishu.cn/open-apis/bot/v2/hook/60030935-5897-491d-bd6f-9f39145b9787',
  BoardCaCheTime: 60 * 10
});

export default config;
