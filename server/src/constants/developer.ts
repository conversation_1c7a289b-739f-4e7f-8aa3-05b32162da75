/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-19 16:07:44
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-25 11:16:43
 * @Description:
 */

type Type = {
  [key: string | number]: string;
};

export const PlatformTypeToLabel: Type = {
  0: 'Unknown',
  1: 'IOS',
  2: 'Android',
  3: 'Others'
};

export const PlatformType = [
  1, 2, 3, 4, 5, 6, 11, 12, 13, 14, 15, 16, 17, 19, 50, 51
];
export const ScreenOrientation = [1, 2];
export const Category = [
  'IAB1',
  'IAB2',
  'IAB3',
  'IAB4',
  'IAB5',
  'IAB6',
  'IAB7',
  'IAB8',
  'IAB9',
  'IAB10',
  'IAB11',
  'IAB12',
  'IAB13',
  'IAB14',
  'IAB15',
  'IAB16',
  'IAB17',
  'IAB18',
  'IAB19',
  'IAB20',
  'IAB21',
  'IAB22',
  'IAB23',
  'IAB24',
  'IAB25',
  'IAB26'
];
export const minDuration = [5, 10, 15];
export const maxDuration = [30, 60, 120];
export const AdPosition = [1, 3, 4, 5];
export const BannerAdSize = {
  AdWidth: [320, 728],
  AdHeight: [50, 90]
};
export const RectangleAdSize = {
  AdWidth: [300],
  AdHeight: [250]
};
export const InterstitialAdSize = {
  AdWidth: [480, 1280, 1024, 320, 720, 768, 1920, 1080],
  AdHeight: [320, 720, 768, 480, 1280, 1024, 1080, 1920]
};

export const RewardedAdSize = {
  AdWidth: [480, 1280, 1024, 320, 720, 768, 1920, 1080],
  AdHeight: [320, 720, 768, 480, 1280, 1024, 1080, 1920]
};

export const PlacementType = [1, 2, 3, 4, 5];
export const AdFormat = [1, 2, 3, 4];
