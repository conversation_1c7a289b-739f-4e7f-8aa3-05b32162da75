/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:14:08
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-04 17:24:40
 * @Description:
 */
declare namespace StrategyAPI {
  type BlAndWlListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    status: number;
    account_name: string;
    account_status: number;
    create_time: string;
    update_time: string;
  };

  type AddBlAndWlParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
  };
  type updateBlAndWlParams = {
    id: number;
    content: string;
    status: number;
  };

  type CapListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    cur_imp: number;
    cur_rev: number;
    status: number;
    cap_status: number;
    seller_name: string;
    buyer_name: string;
    account_name: string;
    account_status: number;
    bundle: string;
    op_id: number;
    create_time: string;
    update_time: string;
    sys_update_time: string;
  };

  type AddCapParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
  };
  type updateCapParams = {
    id: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
  };
  type QpsListItem = {
    id: number;
    // pub_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    app_name: string;
    plm_name: string;
    create_time: string;
    update_time: string;
    app_id: number;
    plm_id: number;
    seller_id: number;
    account_status: number;
    ots_id: string;
    region: number;
  };
  type AddQpsParams = {
    pub_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
  };
  type UpdateQpsParams = {
    id: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
    op_id: number;
  };
  type ProfitListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    profit_ratio: number;
    status: number;
    op_id: number;
    account_name: string;
    create_time: string;
    update_time: string;
    name: string;
    tmp_id: number;
    account_status: number;
    seller_status?: number;
    bundle?: string;
    children?: ProfitListItem[];
    exist_higher_priority?: boolean;
  };

  type PublisherAdvertiserProfitListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    profit_ratio: number;
    status: number;
    op_id: number;
    account_name: string;
    create_time: string;
    update_time: string;
    name: string;
    tmp_id: number;
    children?: ProfitListItem[];
    account_status: number;
    seller_status?: number;
  };
  type AddProfitParams = {
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
    type: number;
    seller_id: number;
    buyer_id: number;
  };

  type UpdateProfitParams = {
    id: number;
    status: number;
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
  };

  type GetProfitCheckListParams = {
    type: number;
    buyer_id?: number[];
    seller_id?: number[];
    bundle?: string[];
  };

  type AddBundleProfitParams = {
    profit_ratio: number;
    type: number;
    seller_id?: number;
    buyer_id: number;
    bundle: string[];
    // tnt_id: number;
    // op_id: number;
  };

  type UpdateBundleProfitParams = {
    id: number;
    status: number;
    profit_ratio: number;
    bundle: string;
    type: number;
    seller_id?: number;
    buyer_id: number;
    // tnt_id: number;
    // op_id: number;
  };

  type SelectOptionsType = {
    label: string;
    value: number | string;
  };

  type FloorListItem = {
    id: number;
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    plm_name: string;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
    account_status: number;
    account_name: string;
    seller_name: string;
    buyer_name: string;
    children?: FloorListItem[];
    name: string;
    tmp_id: number;
    key: number | string;
    update_time: string;
    isParent?: boolean;
  };
  type FloorPlmItem = {
    plm_id: number;
    plm_name: string;
    app_id: number;
    app_name: string;
    seller_id: number;
  };
  type AddFloorParams = {
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type UpdateFloorParams = {
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };
  type DeleteFloorParams = {
    id: number;
    tnt_id: number;
  };

  type CreativeListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    content: string[] | string;
    expire: string;
    remark: string;
    op_name: string;
    op_status: number;
    update_time: string;
    status: number;
  };

  type AddCreativeParams = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
    content: string;
  };

  type UpdateCreativeParams = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
    content: string;
  };

  type IvtConfigItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    seller_id: number;
    seller_name: string;
    buyer_id: number;
    buyer_name: string;
    type: number;
    ratio: number;
    bundle: string;
    status: number;
    op_id: number;
    op_name: string;
    create_time: string;
    update_time: string;
  };
  type AddIvtParams = {
    tnt_id: number;
    seller_id?: number;
    buyer_id?: number;
    op_id: number;
    status?: number;
    type: number;
    ratio: number;
    bundle?: string;
  };

  type UpdateIvtParams = {
    id: number;
    tnt_id: number;
    seller_id?: number[];
    buyer_id?: number[];
    op_id: number;
    status?: number;
    isChange?: boolean;
    type: number;
    ratio: number;
    bundle?: string;
  };
  type AtcListItem = {
    id: number;
    model: number;
    op_id: number;
    create_time: string;
    update_time: string;
  };
  type UpdateAtcModelParams = {
    id: number;
    model: number;
    op_id: number;
    tnt_id: number;
  };

  type ABTestListItem = {
    id: number;
    type: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    country?: string;
    ad_format?: string;
    ad_size?: string;
    default_profit?: number;
    content: string;
    op_id: number;
    account_name: string;
    account_status: number;
    create_time: string;
    update_time: string;
    expire_time: string | string[];
  };

  type AddABTestParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
  };
  type UpdateABTestParams = {
    id: number;
    content: string;
  };

  type AddPmpDealParams = {
    name: string;
    pmp_id: number;
    buyer_id: number;
    deal_id: string;
    bidfloor: number;
    auction_type: number;
    status?: number;
  };

  type AddPmpInventoryParams = {
    name: string;
    seller_id: number;
    inventory_type: number[];
    bundle: string[];
    ad_format: number[];
    country: string[];
    ad_size: number[];
    seller_deal_id: string;
    remark: string;
    status?: number;
  };

  type UpdatePmpDealParams = {
    id: number;
    auction_type: number;
    bidfloor: number;
    deal_id: string;
    name: string;
    status: number;
  };

  type UpdatePmpInventoryParams = {
    id: number;
    name: string;
    seller_id: number;
    inventory_type: number[];
    bundle: string[];
    ad_format: number[];
    country: string[];
    ad_size: number[];
    seller_deal_id: string;
    remark: string;
    status: number;
  };

  type PmpDealListItem = {
    id: number;
    name: string;
    pmp_id: number;
    pmp_internal_name: string;
    buyer_id: number;
    buyer_name: string;
    deal_id: string;
    bidfloor: number;
    auction_type: number;
    status: number;
    update_time: string;
    u_status: number; // 账户状态
    op_id: number;
    op_name: string;
    tnt_id: number;
  };

  type PmpInternalListItem = {
    id: number;
    name: string;
    seller_id: number[]; // 多个值
    sellers?: { seller_id: number; seller_name: string }[];
    inventory_type: number[];
    bundle: string[];
    ad_format: number[];
    country: string[];
    ad_size: number[];
    seller_deal_id: string;
    remark: string;
    status?: number;
    update_time: string;
    u_status: number; // 账户状态
    op_id: number;
    op_name: string;
    tnt_id: number;
  };

  type AddGeoPolicySchemaType = {
    policy_key: string;
    remark: string;
    seller_ids: number[];
  };

  type UpdateGeoPolicySchemaType = AddGeoPolicySchemaType & {
    id: number;
  };

  /**
   * 后端接口返回类型：policy key list
   */
  type GeoPolicyKeyListItem = {
    id: number;
    unique_id: string;
    policy_key: string;
    remark: string;
    op_id: number;
    op_name: string;
    op_status: number;
    status: number;
    is_default: number;
    update_time: number;
  };

  /**
   * 添加 policy key
   */
  type AddGeoPolicyKeySchema = {
    policy_key: string;
    remark: string;
    is_default: number;
  };

  /**
   * 更新 policy key
   */
  type UpdateGeoPolicyKeySchema = {
    id: number;
    policy_key: string;
    remark: string;
    status: number;
    is_default: number;
  };

  /**
   * 后端接口返回类型：policy key relation list
   */
  type GeoPolicyKeyRelationListItem = {
    id: number;
    unique_id: string;
    policy_key_id: number;
    policy_key: string;
    policy_key_status: number;
    seller_id: number;
    seller_name: string;
    op_id: number;
    op_name: string;
    op_status: number;
    status: number;
    is_default: number;
    update_time: number;
  };

  /**
   * 后端接口参数类型
   */
  type AddGeoPolicyKeyRelationSchema = {
    policy_key_id: number;
    seller_id: number;
  };

  type UpdateGeoPolicyKeyRelationSchema = {
    id: number;
    status: number;
    policy_key_id: number;
    seller_id: number;
  };
}
