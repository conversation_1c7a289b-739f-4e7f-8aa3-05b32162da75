/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:16:35
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-05 19:29:41
 * @Description:
 */

import { QpsLevel } from '@/constants/strategy';
import { bigqueryStream, queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { concatSQLFragments, LabelGenerationParams } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

// /**
//  * @deprecated 使用 utils/report/dash-report 中的方法
//  */
// export function getReportSql(
//   options: DashboardAPI.ParseParams,
//   isAll: boolean,
//   isCount?: boolean
// ) {
//   const {
//     dimension,
//     metric,
//     conditions,
//     table,
//     limit,
//     group_by,
//     order_by,
//     tnt_id
//   } = options;
//   const allow_5w_Rows = tnt_id && [1075, 1047].includes(tnt_id);
//   const limit_str = allow_5w_Rows ? 'limit 50000' : 'limit 10000';
//   const reportSql = `
//     select
//       ${dimension ? `${dimension},` : ''}
//       ${metric ? `${metric}` : ''}
//       from ${table} where ${conditions}
//       ${group_by}
//       ${order_by}
//       ${isAll ? (isCount ? '' : `${limit_str}`) : limit}
//   `;

//   return reportSql;
// }
class DashboardModel {
  async getNewHoursToday(
    tnt_id: number,
    table: string,
    timeZone: string,
    labels: LabelGenerationParams
  ): Promise<any> {
    const utc0_today = moment().tz('Etc/UTC').format('YYYY-MM-DD');
    const sql = concatSQLFragments({
      select: [
        `EXTRACT(HOUR FROM TIMESTAMP(FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%S", max(day_hour), '${timeZone}'))) + 1 as hours`
      ],
      from: table,
      where: [`tnt_id = ${tnt_id}`, `date = '${utc0_today}'`],
      limit: '1'
    });
    const data = await queryStackOverflow(sql, {
      cacheTime: 60 * 5,
      ...labels
    });
    return data[0].hours;
  }

  async getNewDashboardList(
    sql: string,
    labels: LabelGenerationParams
  ): Promise<any> {
    return await queryStackOverflow(sql, {
      cacheTime: 60 * 5,
      ...labels
    });
  }

  async newDownloadAllReport(
    sql: string,
    labels: LabelGenerationParams
  ): Promise<any> {
    return await bigqueryStream(sql, { ...labels });
  }

  async getConfigQps(tnt_id: number): Promise<any> {
    const sql = `
      select
        q.server_region as region,
        q.ots_id as ots_id,
        q.seller_id as pub_id,
        q.buyer_id as buyer_id,
        q.id as id,
        q.level as level,
        q.qps as qps,
        b.buyer_name as buyer_name,
        s.seller_name as seller_name,
        s.seller_id as seller_id,
        a.account_name as account_name,
        a.status as account_status,
        sa.app_name as app_name,
        sa.app_id as app_id,
        sp.plm_name as plm_name,
        sp.plm_id as plm_id
      from (select * from qps where tnt_id=${tnt_id} and level not in (${QpsLevel['supply + ad_format']},${QpsLevel['supply + bundle,']},${QpsLevel['supply + country']})) as q
      left join (select buyer_name, buyer_id from buyer) as b on b.buyer_id=q.buyer_id and q.level != ${QpsLevel.supply}
      left join (select plm_name, plm_id, app_id from seller_placement) as sp on sp.plm_id = q.seller_id and q.level = ${QpsLevel['demand + supply-placement']}
      left join (select app_name, app_id, seller_id from seller_app) as sa on ((sa.app_id = q.seller_id and q.level = ${QpsLevel['demand + supply-app']}) or 
      (q.level = ${QpsLevel['demand + supply-placement']} and sa.app_id = sp.app_id))
      left join (select seller_name, seller_id from seller) as s on ((s.seller_id=q.seller_id and 
        (q.level != ${QpsLevel.demand} or
        q.level != ${QpsLevel['demand + ad_format']} or
        q.level != ${QpsLevel['demand + country']})) or 
      (q.level = ${QpsLevel['demand + supply-app']} and (sa.seller_id = s.seller_id)) or 
      (q.level = ${QpsLevel['demand + supply-placement']} and sa.seller_id = s.seller_id))
      left join user as a on a.user_id=q.op_id 
      order by q.update_time desc;
    `;
    return await dbUtils.query(sql);
  }

  async getSupplyAndDemandConfigQps(tnt_id: number): Promise<any> {
    const sql = `select qps,server_region region,seller_id,buyer_id,level from qps where level in (?) and tnt_id=?`;
    return await dbUtils.query(sql, [[QpsLevel.demand, QpsLevel.supply], tnt_id]);
  }

  // /**
  //  * @deprecated 废弃，直接在 sql 请求数量
  //  */
  // async countNewDashboardList(
  //   sql: string,
  //   labels: LabelGenerationParams
  // ): Promise<number> {
  //   const countSql = `select count(*) as count from (${sql}) as t`;
  //   const res = await queryStackOverflow(countSql, {
  //     cacheTime: 60 * 5,
  //     ...labels
  //   });

  //   if (Array.isArray(res) && res.length) {
  //     return res[0].count;
  //   }

  //   return 0;
  // }
  // /**
  //  * @deprecated 使用 countNewDashboardList 方法
  //  */
  // async countDashboardList(
  //   options: any,
  //   labels: LabelGenerationParams
  // ): Promise<number> {
  //   const reprotSql = getReportSql(options, true, true);
  //   const countSql = `select count(*) as count from (${reprotSql}) as t`;
  //   const res = await queryStackOverflow(countSql, {
  //     cacheTime: 60 * 5,
  //     ...labels
  //   });

  //   if (Array.isArray(res) && res.length) {
  //     return res[0].count;
  //   }

  //   return 0;
  // }

  // /**
  //  * @deprecated 废弃，使用 newDownloadAllReport 查询数据
  //  */
  // async downloadAllReport(
  //   options: DashboardAPI.ParseParams,
  //   labels: LabelGenerationParams
  // ) {
  //   const reprotSql = getReportSql(options, true);
  //   return await bigqueryStream(reprotSql, { ...labels });
  // }

  // /**
  //  * @deprecated 使用 getNewHoursToday 方法
  //  */
  // async getHoursToday(
  //   tnt_id: number,
  //   table: string,
  //   timeZone: string,
  //   labels: LabelGenerationParams
  // ): Promise<any> {
  //   const utc0_today = moment().tz('Etc/UTC').format('YYYYMMDD');
  //   const sql = `select FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', max(day_hour), '${timeZone}') as last_hour from ${table} where tnt = ${tnt_id} and day = '${utc0_today}' limit 1`;
  //   const data = await queryStackOverflow(sql, {
  //     cacheTime: 60 * 5,
  //     ...labels
  //   });
  //   const { last_hour } = data[0];
  //   // 小时格式为00:00-23:00，需要加1
  //   const hours = moment(last_hour).hours() + 1;
  //   return hours;
  // }

  // /**
  //  * @deprecated 使用 getNewDashboardList 方法
  //  */
  // async getDashboardList(
  //   options: any,
  //   labels: LabelGenerationParams,
  //   all: boolean = false
  // ): Promise<any> {
  //   const reprotSql = getReportSql(options, all);
  //   return await queryStackOverflow(reprotSql, {
  //     cacheTime: 60 * 5,
  //     ...labels
  //   });
  // }
}

export default new DashboardModel();
