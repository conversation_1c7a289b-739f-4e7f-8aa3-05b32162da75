/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-03-18 17:48:44
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-19 10:20:37
 * @Description:
 */
import { useEffect, MutableRefObject } from 'react';
type ChartRef<T> = MutableRefObject<{ getChart: () => T }>;
type ChartInstance = {
  update: (options: any) => void;
};
type Options<T> = {
  chartRef: ChartRef<T>;
  screenWidth: number;
  smallScreenConfig: { [key: string]: any };
  largeScreenConfig: { [key: string]: any };
};
const useResponsiveChart = <T extends ChartInstance>(options: Options<T>[]) => {
  useEffect(() => {
    const handleResize = () => {
      options.forEach(option => {
        const { chartRef, screenWidth, smallScreenConfig, largeScreenConfig } = option;
        const chart = chartRef?.current?.getChart();
        const config = window.innerWidth < screenWidth ? smallScreenConfig : largeScreenConfig;
        if (chart) {
          chart.update(config);
        }
      });
    };

    handleResize();

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [options]);
};

export default useResponsiveChart;
