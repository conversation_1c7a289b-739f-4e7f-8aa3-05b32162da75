/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 12:22:20
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-26 18:27:21
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-18 20:52:13
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:06:14
 * @Description:
 */
import { UserType } from '@/constants';
import dbUtils from '@/db/mysql';
import { TransparencyAPI } from '@/types/transparency';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class StgModelV2 implements TransparencyAPI.StgChainModel {
  async isStgChainExist(params: TransparencyAPI.CheckStgParams) {
    const { tnt_id, developer_website_domain, id, type } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['id != ?', id ? id : null],
      ['tnt_id = ?', tnt_id],
      ['developer_website_domain = ?', developer_website_domain],
      ['type = ?', type]
    ]);

    const sql = `select count(id) as count from stg_chain_map_v2 ${clause} limit 1`;
    const res = await dbUtils.query(sql, values);
    if (Array.isArray(res) && res.length > 0) {
      return { count: res[0].count };
    }
    return { count: 0 };
  }

  async getStgChainList(tnt_id: number) {
    const SystemType = [UserType.Rix_Admin, UserType.Rix_Data_Analyst];
    const sql = `
    select 
      stg.id,
      stg.developer_website_domain,
      stg.publisher_id,
      stg.op_id,
      stg.status,
      stg.tnt_id,
      stg.type,
      stg.update_time,
      (case when u.type in (?) or u.type is null then "System" else u.account_name end) as op_name
      from stg_chain_map_v2 stg
      left join user u on stg.op_id = u.user_id
      where stg.tnt_id = ?
      order by stg.update_time desc`;
    return dbUtils.query(sql, [SystemType, tnt_id]);
  }

  async addStgChain(params: TransparencyAPI.AddStgParams) {
    const {
      op_id,
      status = 1,
      tnt_id,
      developer_website_domain,
      publisher_id,
      type = 1
    } = params;
    const sql = `insert into stg_chain_map_v2 (developer_website_domain,publisher_id,op_id,status,tnt_id,type) values (?, ?, ?, ?, ?, ?)`;
    return !!(await dbUtils.query(sql, [
      developer_website_domain,
      publisher_id,
      op_id,
      status,
      tnt_id,
      type
    ]));
  }

  async updateStgChain(params: TransparencyAPI.UpdateStgParams) {
    const {
      id,
      op_id,
      status,
      tnt_id,
      developer_website_domain,
      publisher_id,
      type = 1
    } = params;

    const stgChainObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['developer_website_domain', developer_website_domain],
      ['publisher_id', publisher_id],
      ['type', type]
    ]);

    const sql = `update stg_chain_map_v2 set ? where id = ? and tnt_id = ?`;
    return !!(await dbUtils.query(sql, [stgChainObj, id, tnt_id]));
  }
}

export const stgModelV2 = new StgModelV2();
