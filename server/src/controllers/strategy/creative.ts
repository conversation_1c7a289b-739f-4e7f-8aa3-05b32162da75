/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-20 14:49:30
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:31:06
 * @Description:
 */
import { Context } from 'koa';
import { creativeService } from '@/services';
import { CreativeAPI } from '@/types/creative';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { RESULT } from '@/constants';
import { validateBody } from '@/utils/validate-params';
import {
  AddCreativeSchema,
  UpdateCreativeSchema
} from '@/schema/strategy/creative';
class CreativeCtrl implements CreativeAPI.CreativeCtrlInterface {
  async getCreativeList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const { cur_time_zone } = ctx.request.body;
    const list = await creativeService.getCreativeList(tnt_id, cur_time_zone);

    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(AddCreativeSchema)
  async addCreative(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const params = ctx.request.body;
    params.tnt_id = tnt_id;
    const res = await creativeService.addCreative(params);
    if (res) {
      result = getCtxResult('SUCCESS', res);
    }
    ctx.body = result;
  }

  @validateBody(UpdateCreativeSchema)
  async updateCreative(ctx: Context) {
    let result = { ...RESULT };
    const params = ctx.request.body;
    const { tnt_id } = ctx.session!;
    params.tnt_id = tnt_id;
    const res = await creativeService.updateCreative(params);
    if (res) {
      result = getCtxResult('SUCCESS', res);
    }
    ctx.body = result;
  }
}

export const creativeController = new CreativeCtrl();
