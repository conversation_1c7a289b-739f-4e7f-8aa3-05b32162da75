/**
 * 基础消息接口
 */
export interface Message<T = any> {
  type: string;
  data: T;
}

/**
 * 消息处理函数类型
 */
export type MessageHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * 消息处理映射
 */
export interface MessageHandlerMap {
  [type: string]: MessageHandler;
}

/**
 * 发送消息选项
 */
export interface SendMessageOptions {
  timeout?: number;
}

/**
 * 通信配置选项
 */
export interface IframeCommOptions {
  // 发送消息的域名白名单，支持完整URL
  // 注意：由于浏览器安全限制，发送消息时只能使用具体的域名，不支持通配符
  sendWhiteList: string[];
  // 监听消息的域名白名单，支持完整URL和正则表达式（以 'regex:' 开头）
  // 例如：['https://example.com', 'regex:^https:\\/\\/.*\\.example\\.com$']
  listenWhiteList: string[];
  iframeRef?: React.RefObject<HTMLIFrameElement>;
  debug?: boolean;
}

/**
 * 消息类型和数据模块
 * 用于定义和扩展消息类型及其对应的数据类型
 */
export namespace IframeMessage {
  // 预定义消息类型
  export const ONLOAD = 'onload';
  export const REGISTER = 'register';
  export const LOGIN = 'login';
  export const LOGIN_REQUEST = 'login_request';
  export const SET_TZ = 'set_tz';
  export const TZ_SUCCESS = 'tz_success';
  export const ROUTE_CHANGE = 'set_menu';
  export const AUTH_EXPIRED = 'expired';
  export const SYSTEM_ERROR = 'system_error';
}

/**
 * 消息类型映射接口
 * 用于约束send和on方法的类型
 * 可以通过模块扩展添加新的消息类型
 */
export interface TypedMessageMap {
  // 系统预定义消息类型映射
  [IframeMessage.ONLOAD]: { timestamp: number };
  [IframeMessage.REGISTER]: { account_name: string; password: string; time: number };
  [IframeMessage.LOGIN]: { message: string };
  [IframeMessage.LOGIN_REQUEST]: { account_name: string; password: string; time: number };
  [IframeMessage.SET_TZ]: { tz: string };
  [IframeMessage.TZ_SUCCESS]: {};
  [IframeMessage.ROUTE_CHANGE]: {
    next: { path: string; name: string };
    cur: { path: string; name: string } | null;
    timestamp: number;
  };
  [IframeMessage.AUTH_EXPIRED]: { code: number; message: string };
  [IframeMessage.SYSTEM_ERROR]: { code: number; message: string };
  // 可以通过模块扩展添加更多类型
  // [string]: any; - 允许任意字符串键
}

/**
 * 类型安全的发送方法类型
 */
export type TypedSend = <K extends keyof TypedMessageMap>(type: K, data: TypedMessageMap[K]) => boolean;

/**
 * 类型安全的监听方法类型
 */
export type TypedOn = <K extends keyof TypedMessageMap>(
  type: K,
  handler: (data: TypedMessageMap[K]) => void | Promise<void>
) => () => void;
