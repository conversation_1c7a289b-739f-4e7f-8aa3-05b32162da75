/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-25 15:07:40
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-12 15:46:28
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { useModel } from 'umi';
import { PlusOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import styles from './index.less';

import { StatusMap } from '@/constants/common';
import {
  FloorSearchOption,
  FloorBreadOptions,
  FloorTabOptions,
  FloorTab,
  FloorColumnsForOverall,
  FloorColumnsForSupply,
  FloorType,
  FloorTypeOptions,
  FloorParentItem
} from '@/constants/strategy/floor';
import OperateRender, { OperateRenderItem } from '@/components/OperateRender';

import FrontTable from '@/components/Table/FrontTable';
import PageContainer from '@/components/RightPageContainer';
import { TopBarSearchItem } from '@/components/TopBar';
import AddFloorDrawer from '../components/AddFloorDrawer';

const Page: React.FC = () => {
  const { dataSource: FloorList, reload, loading } = useModel('useFloorList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const { dataSource: allPlmList, reload: reloadAllPlm } = useModel('useAllPlacementsList');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [floor, setCurrentFloor] = useState<StrategyAPI.FloorListItem | undefined>(undefined);
  const [sellerId, setSellerId] = useState<number | undefined>(undefined);
  const [searchOptions, setSearchOptions] = useState(FloorSearchOption);
  const [floorTypeOptions, setFloorTypeOptions] = useState(FloorTypeOptions);
  const [currentTab, setCurrentTab] = useState(FloorTab['Overall Bidfloor']);
  const [dataSource, setDataSource] = useState<StrategyAPI.FloorListItem[]>([]);
  const [expandedRowKeys, setExpandedRowKeys] = useState<number[]>([]);
  const handleEditFloor = (params: StrategyAPI.FloorListItem) => {
    setCurrentFloor(params);
    setIsEdit(true);
    setVisible(true);
  };
  const handleClickCreate = (seller_id?: number) => {
    if (seller_id) {
      setSellerId(seller_id);
    }
    setIsEdit(false);
    setVisible(true);
    setCurrentFloor(undefined);
  };
  const btnOptions: OperateRenderItem[] = [
    {
      label: 'Add',
      icon: <PlusOutlined />,
      onClick: params => handleClickCreate(params.seller_id),
      hide: (params: any) => !params.isParent,
      accessCode: 'addFloorAuth'
    },
    { label: 'Edit', onClick: handleEditFloor, accessCode: 'updateFloorAuth', hide: (params: any) => params.isParent }
  ];

  const tmpColumns: ColumnProps<StrategyAPI.FloorListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 90,
      fixed: 'right',
      render: (txt, params) => <OperateRender btnOptions={btnOptions} params={params} isExpendTable={true} />
    }
  ];
  const [dynamicColumns, setDynamicColumns] = useState<ColumnProps<StrategyAPI.FloorListItem>[]>([
    ...FloorColumnsForOverall,
    ...tmpColumns
  ]);

  useEffect(() => {
    reload();
    reloadAllPlm();
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }
  }, []);

  useEffect(() => {
    let tmpList: StrategyAPI.FloorListItem[] = [];
    let columns: ColumnProps<StrategyAPI.FloorListItem>[] = [];
    let options: TopBarSearchItem[] = [];
    let typeOptions = floorTypeOptions;

    if (currentTab === FloorTab['Overall Bidfloor']) {
      tmpList = FloorList?.filter(
        (item: StrategyAPI.FloorListItem) => item.type === FloorType['Ad Format + Country']
      ).map((item: StrategyAPI.FloorListItem, index: number) => {
        return {
          ...item,
          key: item.id
        };
      });
      typeOptions = FloorTypeOptions.filter(item => item.value === FloorType['Ad Format + Country']);
      columns = [...FloorColumnsForOverall, ...tmpColumns];
    } else if (currentTab === FloorTab['Supply Bidfloor']) {
      const supplyFloorData: StrategyAPI.FloorListItem[] = FloorList?.filter(
        (item: StrategyAPI.FloorListItem) => item.type !== FloorType['Ad Format + Country'] || item.seller_id
      );
      tmpList = supplyFloorData?.reduce(
        (prev: StrategyAPI.FloorListItem[], cur: StrategyAPI.FloorListItem, index: number) => {
          const tmp = prev.find((val: StrategyAPI.FloorListItem) => val.seller_id === cur.seller_id);
          if (tmp) {
            tmp.children = [...(tmp.children || []), { ...cur, key: `${cur.id}-${index}` }];
          } else {
            console.log(cur);
            const newCol = {
              ...FloorParentItem,
              key: index,
              name: cur.seller_name,
              tmp_id: cur.seller_id,
              seller_id: cur.seller_id,
              children: [{ ...cur, key: `${cur.id}-${index}` }],
              status: cur.status,
              update_time: cur.update_time,
              isParent: true
            };
            prev.push(newCol);
          }
          return prev;
        },
        []
      );
      tmpList.forEach((item: StrategyAPI.FloorListItem) => {
        const status = item.children?.every((val: StrategyAPI.FloorListItem) => val.status === StatusMap.Paused)
          ? StatusMap.Paused
          : StatusMap.Active;
        item.status = status;
        if (!item.isParent) item.isParent = false;
      });
      typeOptions = FloorTypeOptions.filter(item => item.value !== FloorType['Ad Format + Country']);
      columns = [...FloorColumnsForSupply, ...tmpColumns];
    }

    setDataSource(tmpList);
    setDynamicColumns(columns);
    setFloorTypeOptions(typeOptions);
  }, [currentTab, FloorList, expandedRowKeys]);

  useEffect(() => {
    console.log('allPlmList', allPlmList);
    handleSearchOptions();
  }, [demandList, supplyList, allPlmList, currentTab]);
  const handleClose = () => {
    setVisible(false);
    setSellerId(undefined);
  };
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(FloorSearchOption));
    if (currentTab === FloorTab['Supply Bidfloor'] && demandList) {
      const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const dIndex = options.findIndex(item => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (currentTab === FloorTab['Supply Bidfloor'] && supplyList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    if (currentTab === FloorTab['Supply Bidfloor'] && allPlmList) {
      const pOptions = allPlmList.map((item: any) => {
        return {
          label: `${item.plm_name}(${item.plm_id})`,
          value: item.plm_id
        };
      });
      const pIndex = options.findIndex(item => item.key === 'plm_id');
      if (pIndex !== -1) {
        options[pIndex].options = pOptions;
      }
    }
    const searchKeys = ['ad_format', 'country', 'status'];
    currentTab === FloorTab['Supply Bidfloor']
      ? setSearchOptions(options)
      : setSearchOptions(options.filter(item => searchKeys.includes(item.key)));
  };
  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as string);
  };

  const normalEmptyRender = () => <span> </span>;

  const handleExpand = (expanded: any, record: any) => {
    const keys = [...expandedRowKeys];
    if (expanded) {
      keys.push(record.key);
    } else {
      const index = keys.indexOf(record.key);
      if (index !== -1) {
        keys.splice(index, 1);
      }
    }
    setExpandedRowKeys(keys);
  };
  const handleExpandedRowClassName = (record: any, index: any, indent: any) => {
    if (record.isParent) return 'expanded-row';
    return '';
  };
  return (
    <PageContainer flexDirection="column" options={FloorBreadOptions} className={styles['floor-container']}>
      <FrontTable<StrategyAPI.FloorListItem>
        searchOptions={searchOptions}
        loading={loading && dataSource && !dataSource.length}
        columns={dynamicColumns}
        dataSource={dataSource}
        rowKey={'key'}
        request={reload}
        btnOptions={[
          {
            label: currentTab === FloorTab['Overall Bidfloor'] ? 'Create Overall Bidfloor' : 'Create Supply Bidfloor',
            type: 'primary',
            size: 'small',
            onClick: () => handleClickCreate(),
            icon: <PlusOutlined />,
            accessCode: 'addFloorAuth'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }} // 非动态， 需要自己指定
        labelWidth={80}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        tabOptions={FloorTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange}
        isFold={currentTab === FloorTab['Supply Bidfloor']}
        initialValues={{ status: [StatusMap.Active] }}
        expandable={{
          expandRowByClick: true,
          expandedRowKeys: expandedRowKeys,
          rowExpandable: record => (record.isParent ? true : false),
          onExpand: handleExpand
        }}
        rowClassName={handleExpandedRowClassName}
        noFilterParentRow={currentTab === FloorTab['Supply Bidfloor']}
      />
      <AddFloorDrawer
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadFloor={reload}
        floor={floor}
        demandList={demandList}
        supplyList={supplyList}
        FloorTypeOptions={floorTypeOptions}
        allPlmList={allPlmList}
        sellerId={sellerId}
      />
    </PageContainer>
  );
};

export default Page;
