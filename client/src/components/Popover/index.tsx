/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-02 16:48:17
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-02 16:48:19
 * @Description:
 */
import React from 'react';
import { Popover } from 'antd';
import type { PopoverProps } from 'antd';
import styles from './index.less';

function NormalPopover({ overlayClassName, ...props }: PopoverProps): JSX.Element {
  return (
    <Popover
      {...props}
      overlayClassName={`${styles['normal-popover-container']} ${overlayClassName || ''}`}
    />
  );
}
export default NormalPopover;
