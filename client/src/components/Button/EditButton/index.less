.edit-button {
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  padding: 4px 7px;
  &:hover, &:active, &:focus {
    background: none;
    color: var(--primary-color-hover);
  }
  svg {
    font-size: 18px;
  }
  &[disabled],  &[disabled]:hover{
    color: #D7DADB;
    background-color: var(--primary-disabled);
    border: none;
  }
  :global {
    .anticon + span {
      margin-left: 3px;
    }
  }
}
