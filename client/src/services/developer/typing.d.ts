/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:42:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-19 20:22:06
 * @Description:
 */

declare namespace AppAPI {
  type PlacementListItem = {
    plm_id: number;
    plm_name: string;
    app_id: number;
    ad_format: number;
    placement_type: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    mute: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status: number;
    create_time: string;
    update_time: string;
  };
  type AppListItem = {
    app_id: number;
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    category: string;
    screen_orientation: number;
    status: number;
    create_time: string;
    update_time: string;
    adslots: PlacementListItem[];
  };
  type AddAppParams = {
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    category?: string;
    screen_orientation: number;
    status?: number;
  };
  type AddPlacementParams = {
    plm_name: string;
    app_id: number;
    ad_format: number;
    placement_type: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status?: number;
  };

  type UpdateAppParams = {
    app_id: number;
    app_name: string;
    store_url: string;
    category?: string;
  };
  type UpdatePlacementParams = {
    plm_id: number;
    plm_name: string;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status?: number;
  };
}
