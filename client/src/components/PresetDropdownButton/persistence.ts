// 使用 useLocalStorage 来持久化 dropdown 的 items
import type { Dispatch, SetStateAction } from 'react';
import useLocalStorage from '@/hooks/useLocalStorage';

export type PresetDropdownItem = {
  key: string;
  label: string;
  // 序列化的存储值
  value: string;
  // extra 额外信息
  extra?: Record<string, any>;
};

type PresetDropdownReturnType = {
  items: PresetDropdownItem[];
  setItems: Dispatch<SetStateAction<PresetDropdownItem[]>>;
};

export type PresetDropdownStore = PresetDropdownReturnType;

export const usePresetDropdownItems = (key: string, initialValue: PresetDropdownItem[]): PresetDropdownReturnType => {
  const [items, setItems] = useLocalStorage<PresetDropdownItem[]>(key, initialValue);
  return { items, setItems };
};
