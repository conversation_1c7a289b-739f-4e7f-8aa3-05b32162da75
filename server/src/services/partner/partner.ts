/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 15:26:06
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-17 10:40:40
 * @Description:
 */
import moment from 'moment-timezone';
import { PartnerType } from '@/constants/partner';
import { partnerModel, demandModel, supplyModel } from '@/models';
import { PartnerAPI } from '@/types/partner';
import {
  genEnCode, md5, getConfig, sendEmail,
  getCsDomain
} from '@/utils';
import { StatusType } from '@/constants/permission';

const { encryptionStr } = getConfig();

class PartnerService {
  async addPartner(params: PartnerAPI.AddPartnerParams) {
    return await partnerModel.addPartner(params);
  }

  async updatePartner(params: PartnerAPI.UpdatePartnerParams) {
    return await partnerModel.updatePartner(params);
  }

  async getPartnerList(tnt_id: number, timeZone: string) {
    const result = await Promise.all([
      partnerModel.getPartnerList(tnt_id),
      demandModel.getPartnerDemand(tnt_id),
      supplyModel.getPartnerSupply(tnt_id)
    ]);
    const data = Array.isArray(result[0]) ? result[0] : [];
    const demandPartner = Array.isArray(result[1]) ? result[1] : [];
    const supplyPartner = Array.isArray(result[2]) ? result[2] : [];
    const buyerObj: any = {};
    const sellerObj: any = {};
    demandPartner.forEach((v) => {
      if (!buyerObj[v.dp_id]) {
        buyerObj[v.dp_id] = [v];
      } else {
        buyerObj[v.dp_id].push(v);
      }
    });
    supplyPartner.forEach((v) => {
      if (!sellerObj[v.sp_id]) {
        sellerObj[v.sp_id] = [v];
      } else {
        sellerObj[v.sp_id].push(v);
      }
    });
    if (Array.isArray(data) && data.length) {
      return data.map((v) => {
        // eslint-disable-next-line no-nested-ternary
        const type = v.dp_id ? v.sp_id ? PartnerType['Supply & Demand'] : PartnerType.Demand : PartnerType.Supply;
        const update_time = moment(v.update_time).tz(timeZone).format('YYYY-MM-DD HH:mm:ss');
        const buyers = v.dp_id > 0 ? buyerObj[v.dp_id] : [];
        const sellers = v.sp_id > 0 ? sellerObj[v.sp_id] : [];
        return {
          ...v, type, update_time, buyers, sellers
        };
      });
    }
    return data;
  }

  async isPartnerExists(params: PartnerAPI.UpdatePartnerParams) {
    const data = await partnerModel.isPartnerExists(params.tnt_id, params.partner_name);
    if (Array.isArray(data) && data.length) {
      return !params.partner_id || data[0].partner_id !== params.partner_id;
    }
    return false;
  }

  async getPartnerAccount(params: PartnerAPI.QueryPartnerAccountParams) {
    const [buyer, seller] = await Promise.all([
      partnerModel.getBuyerPartnerAccount(params),
      partnerModel.getSellerPartnerAccount(params)
    ]);
    return [...buyer, ...seller];
  }

  async createAccount(params: PartnerAPI.CreatePartnerAccountParams) {
    const {
      account_name, tnt_id, password, send_email = [], brand, host_prefix, pv_domain, user_email = '', to_self
    } = params;
    const p_token = md5(`${new Date().getTime()}QT1T-SL${genEnCode(6)}`);
    const u_token = md5(`${new Date().getTime()}${account_name}${tnt_id}${genEnCode(6)}`);
    const n_password = md5(encryptionStr + password);
    const flag = await partnerModel.createAccount({
      ...params, token: u_token, p_token, password: n_password
    });
    if (flag) {
      const email = [...send_email, to_self === StatusType.Active ? user_email : ''].filter((v) => v && v.trim()).join(',');
      if (email.length) {
        const subject_str = 'New Account';
        const TestEnv = ['dev', 'test', 'Development'];
        const env = TestEnv.includes(process.env.NODE_ENV || 'prod') ? `(${process.env.NODE_ENV})` : '';
        const cs_domain = getCsDomain(pv_domain, host_prefix);
        const domain = `http://${cs_domain}`;
        sendEmail({
          from: `${brand || 'RixEngine'}<<EMAIL>>`,
          to: `${email || ''}`,
          subject: `${env}${brand} ${subject_str} (${account_name})`,
          html: `
                            <p>Congratulations,${brand || 'RixEngine'} Account had been successfully registered!</p>
                            <p>Platform: ${domain}</p>
                            <p>Username: ${account_name}</p>
                            <p>Password: ${password}</p>
                `
        });
      }
    }
    return flag;
  }
}

export const partnerService = new PartnerService();
