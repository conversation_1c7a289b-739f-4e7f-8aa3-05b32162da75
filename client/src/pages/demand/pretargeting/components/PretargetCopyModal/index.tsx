import NormalModal from '@/components/Modal/NormalModal';
import NormalSelect from '@/components/Select/NormalSelect';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import { usePretargetingState, usePretargetingStore } from '@/pages/demand/pretargeting/store';
import { useNavigate } from '@umijs/max';
import { Alert, Form } from 'antd';
import { useEffect } from 'react';
import styles from './index.less';

interface PretargetCopyModalProps {
  demandOptions: { label: string; value: number }[];
}

export default function PretargetCopyModal({ demandOptions = [] }: PretargetCopyModalProps) {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const state = usePretargetingState();
  const { currentDemand, pretargetCopyModalVisible, pretargetCopyModalData } = usePretargetingStore();

  const { campaign_name } = pretargetCopyModalData || {};

  const buyerId = currentDemand?.buyer_id || 0;
  const buyerName = currentDemand?.buyer_name || '';

  useEffect(() => {
    if (pretargetCopyModalVisible && buyerId) {
      form.setFieldsValue({
        buyer_id: buyerId
      });
    }
  }, [pretargetCopyModalVisible, buyerId]);

  const handleCancel = () => {
    state.pretargetCopyModalVisible = false;
    state.pretargetCopyModalData = null;
    form.resetFields();
  };

  const handleOk = async () => {
    const { buyer_id } = await form.validateFields();
    const newCampaignName = `${campaign_name}(copy)`;

    if (buyer_id === buyerId) {
      // Copy to the same advertiser
      state.pretargetModalOp = 1;
      state.pretargetModalVisible = true;
      state.onePretargetingData = {
        ...(pretargetCopyModalData as any),
        campaign_name: newCampaignName
      };
    } else {
      // Copy to a different advertiser
      navigate(`/demand/advertiser/pretargeting?id=${buyer_id}`, {
        state: {}
      });
      state.onePretargetingData = {
        ...(pretargetCopyModalData as any),
        campaign_name: newCampaignName
      };
    }

    // Clean up regardless of destination
    handleCancel();
  };

  return (
    <NormalModal
      title="Copy Pretargeting Configuration"
      open={pretargetCopyModalVisible}
      maskClosable={false}
      keyboard={false}
      centered
      destroyOnClose
      onCancel={handleCancel}
      okText="Copy and Edit"
      cancelText="Cancel"
      onOk={handleOk}
    >
      <Alert
        description={
          <div className={styles['pretarget-copy-modal-description']}>
            You are copying pretargeting{' '}
            <HoverTooltip title={campaign_name} maxWidth={180}>
              <span className={styles['bold']}>{campaign_name}</span>
            </HoverTooltip>{' '}
            from{' '}
            <HoverTooltip title={`${buyerName}(${buyerId})`} maxWidth={180}>
              <span className={styles['bold']}>
                {buyerName}({buyerId})
              </span>
            </HoverTooltip>{' '}
            . A new draft pretargeting will be created under the target advertiser. After confirmation, you will edit
            the new copy before saving.
          </div>
        }
        type="warning"
        style={{ marginBottom: 16, borderRadius: 6 }}
      />
      <Form form={form} layout="vertical">
        <Form.Item
          label="Target Advertiser"
          name="buyer_id"
          rules={[{ required: true, message: 'Please select advertiser' }]}
        >
          <NormalSelect options={demandOptions} placeholder="Select Advertiser" showSearch />
        </Form.Item>
      </Form>
    </NormalModal>
  );
}
