.app-right {
  background-color: #fff;
  border-radius: 6px;
  flex: 1;
  margin-left: 16px;
  padding: 16px;
  padding-top: 24px;
  margin-right: 16px;
  overflow: hidden;
  .app-right-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    .app-top-left {
      position: relative;
      padding-left: 3px;
      display: flex;
      flex: 1;
      align-items: center;
      padding-right: 20px;
      .profile-container {
        width: 50px;
        height: 50px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
        > span {
          font-size: 40px;
          font-family: 'Satoshi Variable';
          font-weight: 400;
          color: #fff;
          display: flex;
          align-items: center;
        }
        div {
          position: absolute;
          left: 0px;
          top: 0px;
          width: 14px;
          height: 14px;
          border-radius: 6px 0px;
          background-color: #fff;
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #a4c639;
          img {
            width: 14px;
            height: 14px;
          }
        }
      }
      .app-top-left-desc {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        flex: 1;
        margin-left: 16px;
        .item-desc {
          width: 50%;
          min-width: 0;
          white-space: nowrap;
          display: flex;
          align-items: center;
          span {
            line-height: 22px;
            &:first-child {
              color: #8d9799;
              width: 85px;
              margin-right: 8px;
              text-align: right;
            }
            &:last-child {
              color: var(--text-color);
              // flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            &.span-link {
              cursor: pointer;
              color: var(--primary-color);
            }
          }
        }
      }
    }
  }
  h3 {
    font-weight: 700;
    font-size: 16px;
    color: var(--text-color);
  }
  .table-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
    :global {
      .ant-input-group-wrapper {
        flex: 1;
        max-width: 400px;
      }
      .ant-input-group-addon {
        button {
          padding: 5px 8px;
        }
      }
      .ant-btn-primary[disabled] {
        border: none;
      }
    }
  }
}

.smp-con {
  display: flex;
  align-items: center;
  :global {
    .ant-typography {
      margin: 0 !important;
    }
  }
}
