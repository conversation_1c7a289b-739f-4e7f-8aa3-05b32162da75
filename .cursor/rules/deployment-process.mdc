---
description: project deployment process
globs: 
alwaysApply: false
---
# Deployment Process

## Environments
The platform supports three deployment environments:
- **Production**: Main production environment
- **Test**: Testing environment
- **Hotfix**: Environment for urgent fixes

## Deployment Script
The deployment process is automated using the [deploy.sh](mdc:deploy.sh) script:

```bash
# Deploy to production
./deploy.sh prod

# Deploy to test environment
./deploy.sh test

# Deploy to hotfix environment
./deploy.sh hotfix
```

The script performs the following steps:
1. Authenticates with Google Artifact Registry
2. Builds the client application
3. Copies built files to the server's webroot directory
4. Builds the server application
5. Starts or reloads the application using PM2

## Docker Deployment
The application can be containerized using Docker:
- [Dockerfile](mdc:Dockerfile) defines the container configuration
- Multi-stage build process:
  - First stage builds the client
  - Second stage sets up the server and copies client build artifacts

## PM2 Process Management
The application uses PM2 for process management in production:
- Configuration in [server/processes.json](mdc:server/processes.json)
- Supports automatic restarts and clustering
- Environment-specific configurations for each deployment target

## Nginx Configuration
The application uses Nginx as a reverse proxy:
- [nginx_prod.conf](mdc:nginx_prod.conf): Production configuration
- [nginx_test.conf](mdc:nginx_test.conf): Test environment configuration
- [nginx_prod_02.conf](mdc:nginx_prod_02.conf): Alternative production configuration

## CI/CD Pipeline
The project includes GitLab CI/CD configuration:
- [.gitlab-ci.yml](mdc:.gitlab-ci.yml) defines the CI/CD pipeline
- Automated building, testing, and deployment
- Environment-specific deployment stages
