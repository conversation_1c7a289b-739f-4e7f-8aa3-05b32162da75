/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 16:13:10
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-05 16:16:40
 * @Description:
 */
import { useState } from 'react';

// 保存上一页地址
export default () => {
  const [previousUrl, setPreviousUrl] = useState('');
  const [backUrl, setBackUrl] = useState('/');
  // 是否允许更新当前数据
  return {
    previousUrl,
    setPreviousUrl,
    backUrl,
    setBackUrl
  };
};
