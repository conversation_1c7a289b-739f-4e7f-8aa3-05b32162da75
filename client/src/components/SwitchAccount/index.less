.dropdown {
  width: min(300px, 100%);
  max-height: calc(100vh - 300px);

  .menuItem {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
    padding: 4px 8px;
    border-radius: 8px;
    width: 100%;
    cursor: pointer;
    transition: all 0.3s;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover,
    &.disabled {
      background: #f5f5f5;
      font-weight: bold;
    }

    &.disabled {
      cursor: not-allowed;
    }
  }

  .container {
    padding: 8px 4px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  }
}

.switchIcon {
  font-size: 14px;
  cursor: pointer;
  background-color: var(--primary-color);
  padding: 4px;
  border-radius: 50%;
  color: #ffffff !important;
}
