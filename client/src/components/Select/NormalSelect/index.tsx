/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-05 14:53:50
 * @Description:
 */
import type { SelectProps } from 'antd';
import { Select, Tooltip } from 'antd';
import classNames from 'classnames';
import styles from './index.less';

function NormalSelect(props: SelectProps): JSX.Element {
  const flag = props.mode === 'multiple' || props.mode === 'tags';

  return (
    <Select
      maxTagPlaceholder={omittedValues => (
        <Tooltip
          overlayClassName={styles['tooltip-container']}
          placement="topRight"
          title={
            <div className={styles['placeholder-container']}>
              {omittedValues.map(({ label }, index) => (
                <span key={index}>{label}</span>
              ))}
            </div>
          }
        >
          <span style={{ cursor: 'pointer' }}>+{omittedValues.length}...</span>
        </Tooltip>
      )}
      maxTagCount="responsive"
      optionFilterProp="children"
      filterOption={(input, option: any) =>
        option && option.label && option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      placeholder="Please Select"
      {...props}
      allowClear={!!flag || props.allowClear}
      className={classNames(styles['normal-select-container'], props.className, 'normal-select-container', {
        [styles['normal-select-multiple-container']]: flag
      })}
      popupClassName={classNames(styles['normal-select-dropdown'], props.popupClassName, 'normal-select-dropdown')}
    />
  );
}

NormalSelect.Option = Select.Option;

export default NormalSelect;
