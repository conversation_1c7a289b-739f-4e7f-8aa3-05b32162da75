import { useModel } from '@umijs/max';
import { useCallback, useEffect, useMemo } from 'react';

export const useOptionsData = () => {
  // 获取广告主和发布商数据
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');

  // 初始化加载数据
  useEffect(() => {
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }
  }, [demandList, supplyList, reloadDemand, reloadSupply]);

  // 处理广告主选项数据
  const demandOptions = useMemo(() => {
    if (demandList) {
      return demandList.map(({ buyer_name, buyer_id }: any) => ({
        label: `${buyer_name}(${buyer_id})`,
        value: buyer_id
      }));
    }
    return [];
  }, [demandList]);

  // 处理发布商选项数据
  const supplyOptions = useMemo(() => {
    if (supplyList) {
      return supplyList.map(({ seller_name, seller_id }: any) => ({
        label: `${seller_name}(${seller_id})`,
        value: seller_id
      }));
    }
    return [];
  }, [supplyList]);

  // 重新加载选项数据
  const reloadOptions = useCallback(() => {
    reloadDemand();
    reloadSupply();
  }, [reloadDemand, reloadSupply]);

  return {
    demandOptions,
    supplyOptions,
    reloadOptions,
    demandList,
    supplyList
  };
}; 
