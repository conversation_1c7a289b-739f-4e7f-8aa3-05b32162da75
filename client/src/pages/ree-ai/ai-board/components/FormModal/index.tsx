/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-03 22:06:12
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-20 11:31:21
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import NormalModal from '@/components/Modal/NormalModal';
import styles from './index.less';
import OriginalTable from '@/components/Table/OriginalTable';
import { RequestFormColumns } from '@/constants/ree-ai/ai-board';
import NormalPagination from '@/components/Pagination/NormalPagination';

type Props = {
  visible: boolean;
  onCancel: () => void;
  data?: BoardAPI.TrafficRequestLog[];
};
const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

const getCurrentData = <T,>(data: T[], page: number, pageSize: number): T[] => {
  if (!data) {
    return [];
  }
  const start = (page - 1) * pageSize;
  const end = page * pageSize;
  return data.slice(start, end);
};

const Modal: React.FC<Props> = ({ visible, onCancel, data }) => {
  const [pageData, setPageData] = useState<{
    currentPage: number;
    pageSize: number;
    curPageData: BoardAPI.TrafficRequestLog[];
  }>({
    currentPage: DefCurrentPage,
    pageSize: PageSize,
    curPageData: []
  });
  useEffect(() => {
    if (data?.length) {
      const tmp = getCurrentData(data, DefCurrentPage, PageSize);
      setPageData({
        ...pageData,
        curPageData: tmp
      });
    }
  }, [visible, data]);

  const handlePageChange = (page: number, pageSize: number) => {
    setPageData({
      currentPage: page,
      pageSize: pageSize,
      curPageData: getCurrentData(data || [], page, pageSize)
    });
  };
  return (
    <NormalModal
      open={visible}
      onCancel={onCancel}
      cancelText="cancel"
      okText="Request"
      title="Request Form"
      footer={null}
      width="85%"
      style={{ top: 50 }}
    >
      <div className={styles['container']}>
        <OriginalTable
          loading={false}
          rowKey={'id'}
          dataSource={pageData.curPageData}
          columns={RequestFormColumns}
          scroll={{ y: 'calc(100vh - 200px)' }}
        />
        <div className={styles['pag-container']}>
          <NormalPagination
            onChange={handlePageChange}
            total={data?.length || 0}
            pageSize={pageData.pageSize}
            current={pageData.currentPage}
            showSizeChanger
            size="small"
            responsive
            showTotal={total => `Total ${total} items`}
            pageSizeOptions={PageSizeOptions}
          />
        </div>
      </div>
    </NormalModal>
  );
};

export default Modal;
