/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-07 11:46:26
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-07 14:58:59
 * @Description:
 */
import Router from 'koa-router';
import { atcCtrl } from '@/controllers';

const router = new Router({ prefix: '/atc' });

const routers = router
  .post('/updateAtcModel', atcCtrl.updateAtc)
  .post('/getAtcModelList', atcCtrl.getAtcList);

export default routers;
