.container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-radius: 6px;
  width: 100%;
  flex-wrap: wrap;
  font-size: 16px;
  gap: 16px;
  max-width: 100%;
  .supply-container-card,
  .demand-container-card {
    flex: 1;
    max-width: calc(50% - 8px); //gap: 16px;
  }
  .supply-container,
  .demand-container {
    width: 100%;
  }
}
@media screen and (max-width: 1024px) {
  .container {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    .supply-container-card,
    .demand-container-card {
      max-width: 100%;
    }
  }
}
@media screen and (max-width: 768px) {
  .container {
    font-size: 12px;
  }
}

.tooltip-container {
  padding: 12px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.98);
  min-width: 220px;
  min-height: 88px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  .tooltip-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    gap: 16px;
    color: #8d9799;
  }
  .ad-size-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    .ad-size-item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .rev {
        color: var(--text-color);
        margin-left: auto;
      }
    }
  }
}
