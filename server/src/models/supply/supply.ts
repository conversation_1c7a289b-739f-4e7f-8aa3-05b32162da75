/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 11:21:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-26 14:33:14
 * @LastEditTime: 2023-05-09 22:51:00
 * @FilePath: /saas.rix-platform/server-ts/src/models/supply/supply.ts
 * @Description: 下游
 */
import { getLogger } from '@/config/log4js';
import { ProfitType, StatusMap, UserType } from '@/constants';
import { AuthLevel } from '@/constants/demand';
import { RoleTypeToNumber } from '@/constants/permission';
import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil, { PoolConnection, QueryParamType } from '@/db/mysql';
import { SupplyAPI } from '@/types/supply';
import { genEnCode, md5 } from '@/utils';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class SupplyModel implements SupplyAPI.SupplyModel {
  async getSupplyList(
    tnt_id: number,
    cur_time_zone: string,
    id?: number,
    isTesting?: boolean
  ): Promise<SupplyAPI.SupplyListItem[]> {
    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', id ? id : null],
      ['status != ?', isTesting ? null : StatusMap.Testing],
      ['tnt_id = ?', tnt_id]
    ]);

    const sql = `
        select
            sl.seller_id as seller_id,
            sl.seller_name as seller_name,
            sl.integration_type as integration_type,
            sl.channel_type as channel_type,
            sl.device_type as device_type,
            sl.relationship as relationship,
            sl.token as token,
            sl.pass_burl as pass_burl,
            sl.pass_nurl as pass_nurl,
            sl.pass_lurl as pass_lurl,
            sl.status as status,
            ${getTzTimeSql('sl.create_time', cur_time_zone)} as create_time,
            ${getTzTimeSql('sl.update_time', cur_time_zone)} as update_time,
            sl.profit_model as profit_model,
            sl.rev_share_ratio as rev_share_ratio,
            sl.cus_status as cus_status,
            sl.tagid_status as tagid_status,
            sl.user_id as user_id,
            sl.rev_track_type as rev_track_type,
            sl.support_omid as support_omid,
            sl.publisher_id as publisher_id,
            sl.native_strict_validation as native_strict_validation,
            sl.banner_multi_size as banner_multi_size,
            sl.adomain_filter as adomain_filter,
            sit.itg_name as integration_type_desc,
            p.id as profit_id,
            p.profit_ratio as profit_ratio,
            p.status as profit_status,
            t.host_prefix as host_prefix,
            t.pv_domain as pv_domain,
            u.account_name as seller_account_name,
            u.status as seller_account_status,
            u.api_status as api_status,
            coalesce(spt.sp_name, '') as partner_name,
            coalesce(spt.partner_id, 0) as partner_id,
            coalesce(spt.sp_id, 0) as sp_id
        from (select * from seller ${clause}) as sl
        left join seller_integration_type as sit on sl.integration_type = sit.id
        left join profit as p on sl.seller_id = p.seller_id and p.type = ? 
        left join user as u on sl.user_id = u.user_id
        left join tenant as t on sl.tnt_id = t.tnt_id
        left join seller_parent as spt on spt.sp_id=sl.sp_id
        order by sl.update_time desc
    `;

    return await dbUtil.query(sql, [...values, ProfitType.Seller]);
  }

  async addSupply(params: SupplyAPI.AddSupplyParams) {
    const {
      seller_name,
      seller_account_name,
      integration_type,
      status = 1,
      channel_type,
      device_type,
      relationship,
      tnt_id = 0,
      profit_model,
      profit_ratio = 0,
      rev_share_ratio = 100,
      op_id,
      cus_status = 2,
      tagid_status = 2,
      pass_burl = 2,
      pass_nurl = 2,
      pass_lurl = 2,
      rev_track_type = 1,
      sp_id = 0,
      publisher_id = '',
      support_omid = 2,
      adomain_filter = 1,
      native_strict_validation = 2,
      banner_multi_size = 2
    } = params;
    const token = md5(`${new Date().getTime()}QT1T-SL${genEnCode(4)}`);
    const userToken = md5(
      `${new Date().getTime()}${seller_account_name}${tnt_id}${genEnCode(6)}`
    );

    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } =
        await dbUtil.getConnectionParams();
      conn = connection;
      await beginTransaction();

      const userRow = await query({
        sql: 'insert into user (account_name,type,role,status,tnt_id,token) values ?',
        values: [
          [
            [
              `Pub_${seller_account_name}`,
              UserType.Supply,
              2,
              1,
              tnt_id,
              userToken
            ]
          ]
        ]
      });

      const userInsertId = userRow.insertId;

      const rows = await query({
        sql: 'insert into seller (seller_name, integration_type, status, channel_type, device_type, relationship, token,pass_burl ,pass_nurl, pass_lurl, tnt_id, profit_model, rev_share_ratio, cus_status,tagid_status,rev_track_type,support_omid,user_id, sp_id, publisher_id, adomain_filter, native_strict_validation, banner_multi_size) values ?',
        values: [
          [
            [
              seller_name,
              integration_type,
              status,
              channel_type,
              device_type,
              relationship,
              token,
              pass_burl,
              pass_nurl,
              pass_lurl,
              tnt_id,
              profit_model,
              rev_share_ratio,
              cus_status,
              tagid_status,
              rev_track_type,
              support_omid,
              userInsertId,
              sp_id,
              publisher_id,
              adomain_filter,
              native_strict_validation,
              banner_multi_size
            ]
          ]
        ]
      });

      const sellerInsertId = rows.insertId;

      await Promise.all([
        await query({
          sql: 'insert into user_role_rl (user_id,role_id,op_user_id,tnt_id) values ?',
          values: [
            [[userInsertId, RoleTypeToNumber['Supply User'], op_id, tnt_id]]
          ]
        }),
        await query({
          sql: 'insert into profit(type, seller_id, profit_ratio, op_id, tnt_id, status) values ?',
          values: [
            [
              [
                ProfitType.Seller,
                sellerInsertId,
                profit_ratio,
                op_id,
                tnt_id,
                1
              ]
            ]
          ]
        })
      ]);

      // if (Array.isArray(permissions) && permissions.length > 0) {
      //   const pms = permissions.map((item: any) => {
      //     return [userRow.insertId, item.type, item.rsc_id, tnt_id];
      //   });
      //   await query({ sql: sqls.addPermission, values: [pms] });
      // }
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      console.log(e);
      getLogger('error').error(`addSupply failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(e);
    }
  }

  async updateSupply(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  async isSupplyNameExists(
    seller_name: string,
    tnt_id: number,
    seller_id?: number
  ) {
    const { clause, params: values } = buildSQLWhereClause([
      ['seller_name = ?', seller_name],
      ['tnt_id = ?', tnt_id],
      ['seller_id != ?', seller_id ? seller_id : null]
    ]);

    const sql = `select seller_name from seller ${clause}`;
    return await dbUtil.query(sql, values);
  }

  // 获取授权的demand 没有过滤status
  async getSupplyAuth(seller_id: number, tnt_id: number) {
    const sql = `
      select 
        ca.buyer_id as buyer_id, 
        level, 
        pub_id,
        buyer_name,
        b.status as status,
        b.integration_type as integration_type
      from (select buyer_id, level, pub_id from config_auth where pub_id = ? and 
        level=? and tnt_id=?) as ca
      left join buyer as b on b.buyer_id = ca.buyer_id
    `;
    return await dbUtil.query(sql, [seller_id, AuthLevel.Supply, tnt_id]);
  }

  // 获取所有app
  async getSupplyApp(seller_id: number | number[], tnt_id: number) {
    const sql = `
      select 
        app_id, 
        app_name, 
        bundle, 
        platform, 
        store_url, 
        category, 
        sa.status as status, 
        seller_name, 
        sa.seller_id as seller_id, 
        sa.screen_orientation as screen_orientation,
        sa.ios_bundle as ios_bundle
      from 
        (
          select 
            * 
          from 
            seller_app 
          where 
            seller_id = ? 
            and tnt_id = ?
        ) as sa 
        left join seller as s on s.seller_id = ? 
      order by 
        sa.app_id desc
    `;
    return await dbUtil.query(sql, [seller_id, tnt_id, seller_id]);
  }

  // 获取app placement
  async getAppPlacement(app_ids: number[], tnt_id: number) {
    const sql = `
      select 
        plm_id, 
        plm_name, 
        app_id, 
        ad_format, 
        ad_width, 
        ad_height, 
        placement_type, 
        price, 
        bid_floor, 
        assets, 
        support_html, 
        support_video, 
        video_api, 
        skip, 
        companionad, 
        status, 
        mute, 
        maxduration, 
        minduration, 
        protocols, 
        banner_api, 
        pos 
      from 
        seller_placement 
      where 
        app_id in (?) 
        and tnt_id = ? 
      order by 
        plm_id desc;
      `;
    return await dbUtil.query(sql, [app_ids, tnt_id]);
  }

  // 获取授权的dsp
  async getAuthDsp(ids: number[], tnt_id: number) {
    const sql = `
      select 
        ca.buyer_id as buyer_id, 
        level, 
        pub_id,
        buyer_name,
        b.status as status,
        b.integration_type as integration_type
      from (select buyer_id, level, pub_id from config_auth where pub_id in (?) and level != ? and 
        tnt_id=?) as ca
      left join buyer as b on b.buyer_id = ca.buyer_id
    `;
    return await dbUtil.query(sql, [ids, AuthLevel.Supply, tnt_id]);
  }

  // 更改授权
  async setSupplyAuth(sqls: QueryParamType[]) {
    return !!(await dbUtil.execWaterfallTransaction(sqls));
  }

  // 获取下游endpoint
  async getSupplyEndpoint(tnt_id: number, seller_id?: number) {
    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', seller_id ? seller_id : null],
      ['t.tnt_id = ?', tnt_id]
    ]);

    const sql = `select t.host_prefix,t.pv_domain ,s.token from tenant t ,seller s ${clause}`;
    return await dbUtil.query(sql, values);
  }

  async getSupplyBySellerId(seller_id: number, tnt_id: number) {
    const sql = `select seller_name from seller where seller_id=? and tnt_id=?`;
    return await dbUtil.query(sql, [seller_id, tnt_id]);
  }

  async getPartnerSupply(tnt_id: number) {
    const sql = `
      select
        seller_name,
        seller_id,
        sp_id
      from seller where tnt_id=? and sp_id > 0
    `;
    return await dbUtil.query(sql, [tnt_id]);
  }

  // 查developer支持的下游
  async getDashboardSupplyList(params: {
    tnt_id: number;
    seller_id?: number[];
  }) {
    const { tnt_id, seller_id } = params;
    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id in (?)', seller_id?.length ? seller_id : null],
      ['sl.tnt_id = ?', tnt_id],
      ['sl.cus_status = ?', StatusMap.Active]
    ]);

    const sql = `
      select
        sl.seller_id,
        sl.seller_name,
        sl.integration_type,
        sl.channel_type,
        sl.device_type,
        sl.status,
        sl.cus_status,
        sit.itg_name as integration_type_desc
      from seller as sl
      left join seller_integration_type as sit on sl.integration_type = sit.id
      ${clause}
    `;
    return await dbUtil.query(sql, values);
  }

  // 下载的上下游 只需要名称 不需要其他
  async getDownloadSupplyList(params: {
    tnt_id: number;
    seller_id?: number[];
  }) {
    const { tnt_id, seller_id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id in (?)', seller_id?.length ? seller_id : null],
      ['tnt_id = ?', tnt_id]
    ]);

    const sql = `select seller_id, seller_name from seller ${clause}`;
    return await dbUtil.query(sql, values);
  }
}

export default new SupplyModel();
