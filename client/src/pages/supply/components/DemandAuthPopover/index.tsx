/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-15 15:57:04
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-02 14:32:46
 * @Description:
 */
import { useEffect, useRef, useState } from 'react';
import { Tag, Popover } from 'antd';
import styles from './index.less';

type DemandPopoverProps = {
  demand_list: DemandAPI.DemandListItem[]
}

const DemandPopover: React.FC<DemandPopoverProps> = ({ demand_list }) => {
  const [visible, setVisible] = useState(false);
  const [isEllipsis, setEllipsis] = useState(false);
  const domRef = useRef<HTMLDivElement | null>(null);
  const demandContent = demand_list.map((item) =>
    <Tag key={item.buyer_id} className={styles['app-list-tag']} id="demand-popover-id">{item.buyer_name}</Tag>
  );

  const handleVisible = () => {
    setVisible(false);
  };

  function findScrollElement(element: any) {
    element.onscroll = function() {
      // 返回当前滚动元素
      if (element.className.indexOf('ant-popover-inner-content') === -1) {
        setVisible(false);
      }
    };
    Array.from(element.children).forEach(findScrollElement);
  }

  const handleScroll = () => {
    findScrollElement(document.body);
  };
  // 查找对应的滚动元素
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    window.addEventListener('wheel', handleScroll);
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('wheel', handleScroll);
    };
  }, []);

  const handleResize = () => {
    isDivEllipsis(domRef.current);
    handleVisible();
  };

  const isDivEllipsis = (dom: any) => {
    const checkDom = dom.cloneNode();
    checkDom.style.width = dom.offsetWidth + 'px';
    checkDom.style.height = dom.offsetHeight + 'px';
    checkDom.style.overflow = 'auto';
    checkDom.style.position = 'absolute';
    checkDom.style.zIndex = -1;
    checkDom.style.opacity = 0;
    checkDom.style.whiteSpace = 'nowrap';
    checkDom.innerHTML = dom.innerHTML;

    const parent = document.body;
    parent.appendChild(checkDom);
    const flag = checkDom.scrollWidth > checkDom.offsetWidth;
    parent.removeChild(checkDom);
    setEllipsis(flag);
  };

  useEffect(() => {
    isDivEllipsis(domRef.current);
  }, [domRef.current, demand_list]);

  const handleVisibleChange = (visible: boolean) => {
    if (isEllipsis) {
      setVisible(visible);
    }
  };
  return (
    <Popover
      placement="top"
      title='Authorized Demands'
      style={{ width: '500px' }}
      content={<div>
        {demandContent}
      </div>}
      trigger="hover"
      open={visible}
      onOpenChange={handleVisibleChange}
      overlayClassName={styles['normal-popover-container']}
    >
      <div className={`${styles['ellipsis']} ${styles['tag-container']}`} ref={domRef}>
        {
          demandContent
        }
      </div>
    </Popover>
  );
};

export default DemandPopover;

