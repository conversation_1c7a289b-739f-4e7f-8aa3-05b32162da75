# Iframe 消息通信系统重构说明

## 概述

本次重构将原有的单一 `whiteList` 配置分离为两个独立的白名单配置：

- `sendWhiteList`: 控制消息发送的目标域名
- `listenWhiteList`: 控制消息监听的源域名

## 重构原因

原有的单一 `whiteList` 设计存在以下问题：

1. **发送消息限制**: 由于浏览器安全限制，`postMessage` 只能使用具体的域名，不支持通配符
2. **监听消息需求**: 监听消息时需要更灵活的过滤逻辑，包括正则表达式支持
3. **功能混淆**: 单一配置试图同时处理发送和监听的不同需求

## 新的配置结构

### IframeCommOptions

```typescript
interface IframeCommOptions {
  // 发送消息的域名白名单，支持完整URL
  // 例如：['https://example.com']
  sendWhiteList: string[];
  
  // 监听消息的域名白名单，支持完整URL和正则表达式
  // 例如：['https://example.com', 'regex:^https:\\/\\/.*\\.example\\.com$']
  listenWhiteList: string[];
  
  iframeRef?: React.RefObject<HTMLIFrameElement>;
  debug?: boolean;
}
```

### 支持的模式

#### sendWhiteList 支持的模式：
- 完整URL: `'https://example.com'`

#### listenWhiteList 支持的模式：
- 完整URL: `'https://example.com'`
- 正则表达式: `'regex:^https:\\/\\/.*\\.example\\.com$'`

## 最佳实践

1. **发送白名单**: 只包含具体的域名，避免使用正则表达式
2. **监听白名单**: 可以包含正则表达式，用于更灵活的源过滤
3. **安全考虑**: 监听白名单应该比发送白名单更严格，确保只接受来自可信源的消息
4. **调试模式**: 在开发环境中启用 `debug: true` 以查看详细的通信日志

## 示例ß

### Topon 配置示例

```typescript
// 发送白名单 - 只包含具体域名
export const ToponSendWhiteList: string[] = [
  'http://127.0.0.1:8001',
  'http://localhost:8001',
  'https://platform-iframe.rixfe.com',
];

// 监听白名单 - 包含正则表达式用于更灵活的匹配
export const ToponListenWhiteList: string[] = [
  'http://127.0.0.1:8001',
  'http://localhost:8001',
  'https://platform-iframe.rixfe.com',
  'regex:^https:\\/\\/topon-[\\w-]+\\.console(-t)?\\.rixengine\\.com'
];
```

## 注意事项

1. **浏览器限制**: `postMessage` 的 `targetOrigin` 参数不支持正则表达式，因此发送白名单只能使用具体域名
2. **性能考虑**: 监听白名单中的正则表达式会在每次消息接收时执行，应避免过于复杂的正则表达式
3. **安全建议**: 定期审查白名单配置，确保只与可信的域名进行通信
