/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 15:10:47
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:55:56
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getProfitList } from '@/services/api';

type ProfitListProps = {
  dataSource: StrategyAPI.ProfitListItem[],
  reload: (params?: { type: 'advertiser' | 'publisher' | 'bundle' }) => Promise<any>;
  loading: boolean
}

export default function useProfitList(): ProfitListProps {
  const { data, run, loading, error } = useCustomRequest(getProfitList, {
    cacheKey: 'profit-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
