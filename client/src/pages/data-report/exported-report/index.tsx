/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-20 11:35:54
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-05 17:05:34
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { Descriptions } from 'antd';
import { useModel } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import { ExportBreadOptions, ExportColumns, ExportSearchOptions } from '@/constants/data-report/exported-report';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';
import type { OperateRenderItem } from '@/components/OperateRender';
import { TopBarSearchItem } from '@/components/TopBar';
import NormalModal from '@/components/Modal/NormalModal';
import moment from 'moment-timezone';
import { AllMetricsOptions, DateType, DimensionsOptions } from '@/constants/data-report';
import { ExportStatusMap, OptionsType } from '@/constants';
const Page: React.FC = () => {
  const { dataSource, reload, loading } = useModel('useExportedReportList');
  const [showParameters, setShowParameters] = useState(false);
  const [timePeriod, setTimePeriod] = useState<string>('');
  const [dimensions, setDimensions] = useState<string>('');
  const [metrics, setMetrics] = useState<string>('');

  const handleParameters = (params: DashboardAPI.ExportedReportItem) => {
    setShowParameters(true);
    const queryCondition = JSON.parse(params.query_condition);
    const queryColumns = [
      ...new Set<string>(
        queryCondition.columns.map((item: string) => {
          if (item === 'date') {
            return queryCondition.splile_time === DateType.Day
              ? 'day'
              : queryCondition.splile_time === DateType.Hour
              ? 'hour'
              : '';
          }
          if (item === 'ad_width, ad_height') {
            return 'ad_size';
          }
          return item;
        })
      )
    ];

    const timePeriod = `${moment(queryCondition.start_date).format('YYYY/MM/DD')} - ${moment(
      queryCondition.end_date
    ).format('YYYY/MM/DD')}`;
    const dimensions = queryColumns
      .map((item: string) => {
        const itemLabel = DimensionsOptions.find((option: OptionsType) => option.value === item);
        return itemLabel?.label;
      })
      .join(' / ');

    const metrics =
      queryCondition.metrics
        ?.map((item: string) => {
          const itemLabel = AllMetricsOptions.find((option: OptionsType) => option.value === item);
          return itemLabel?.label;
        })
        .filter(Boolean)
        .join(' / ') || '';
    setTimePeriod(timePeriod);
    setDimensions(dimensions);
    setMetrics(metrics);
  };
  const handleDownload = (params: DashboardAPI.ExportedReportItem) => {
    const a = document.createElement('a');
    a.setAttribute('href', params.url);
    a.setAttribute('download', `${params.name}.csv`);
    a.click();
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Download',
      onClick: handleDownload,
      icon: <RixEngineFont type="download" />,
      handleDisabled: (params: DashboardAPI.ExportedReportItem) => {
        const diffTime = moment().diff(moment(params.create_time), 'minutes');
        // 30分钟后，如果还是creating状态，视为任务failed
        if (diffTime > 60 && params.status === ExportStatusMap.Creating) {
          return true;
        }
        if (params.status === ExportStatusMap.Failed || params.status === ExportStatusMap.Creating) {
          return true;
        }
        return false;
      }
    },
    {
      label: 'Parameters',
      onClick: handleParameters,
      icon: <RixEngineFont type="edit" />
    }
  ];
  const tmpColumns: ColumnProps<DashboardAPI.ExportedReportItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 150,
      fixed: 'right',
      render: (txt, params) => <OperateRender btnOptions={OperateOptions} params={params} />
    }
  ];
  const columns = [...ExportColumns, ...tmpColumns];
  const [searchOptions, setSearchOptions] = useState(ExportSearchOptions);

  useEffect(() => {
    if (dataSource) {
      reload();
    }
    handleSearchOptions();
  }, []);

  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    setSearchOptions(options);
  };

  const handleCancel = () => {
    setShowParameters(false);
  };
  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={ExportBreadOptions}>
      <FrontTable<DashboardAPI.ExportedReportItem>
        pageTitle="Export Log"
        isFold
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        scroll={{ y: 'calc(100vh - 220px)' }}
        labelWidth={120}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
      />
      <NormalModal
        footer={null}
        title={'Parameters'}
        onOk={() => {}}
        open={showParameters}
        onCancel={handleCancel}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 150 }}
        width={750}
      >
        <Descriptions layout="horizontal" column={1} bordered>
          <Descriptions.Item label={<div style={{ whiteSpace: 'nowrap' }}>Time Period</div>}>
            {timePeriod}
          </Descriptions.Item>
          <Descriptions.Item label="Dimensions">{dimensions}</Descriptions.Item>
          <Descriptions.Item label="Metrics">{metrics}</Descriptions.Item>
        </Descriptions>
      </NormalModal>
    </PageContainer>
  );
};

export default Page;
