/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-01 15:02:46
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-01 18:56:50
 * @Description:
 */
import { bigqueryStream, queryStackOverflow } from '@/db/bigquery';

// 直接查询正式数据
function joinSql(options: any) {
  const table = 'saas-373106.saas_others.human_report';
  const sql = `
    select
      ${options.normal_dimension_str ? `${options.normal_dimension_str}, ` : ''}
      ${options.metrics_str ? `${options.metrics_str}, ` : ''} 
      COUNT(*) OVER() AS total
    from ${table} as i where ${options.condition}
    ${options.dimension ? `group by ${options.dimension}` : ''}
  `;
  return sql;
}

class HumanReport {
  async getHumanReport(options: any, api_url: string) {
    const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
    return await queryStackOverflow(sql, { tag: api_url });
  }

  async downloadHumanReport(options: any, api_url: string) {
    const sql = `${joinSql(options)} ${options.order}`;
    console.log('xxsql', sql);
    return await bigqueryStream(sql, { tag: api_url });
  }
}

export const humanModel = new HumanReport();
