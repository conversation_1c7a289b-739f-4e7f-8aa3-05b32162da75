import { MappingType } from './ad-size';

export const DeviceBrand: MappingType = {
  1: 'Apple',
  2: 'Samsung',
  3: '<PERSON><PERSON><PERSON>',
  4: '<PERSON><PERSON>',
  5: '<PERSON><PERSON>',
  6: 'Vivo',
  7: '<PERSON>',
  8: 'Sony',
  9: 'OnePlus',
  10: 'Motorola',
  11: 'Nokia',
  12: '<PERSON><PERSON>',
  13: 'Honor',
  14: 'As<PERSON>',
  15: 'Z<PERSON>',
  16: 'LG',
  17: '<PERSON><PERSON>',
  18: 'TCL',
  19: 'Infinix',
  20: '<PERSON><PERSON><PERSON>',
  21: 'Itel',
  22: '<PERSON><PERSON>',
  23: '<PERSON>',
  24: 'Micromax',
  25: 'Lava',
  26: 'Karbonn',
  27: '<PERSON>',
  28: 'Fairphone',
  29: 'LeEco',
  30: '<PERSON><PERSON><PERSON>',
  31: 'Coolpad',
  32: '<PERSON>',
  33: 'Energizer',
  34: 'Ulefone',
  35: 'Doogee',
  36: 'Blackview',
  37: 'C<PERSON>ot',
  38: 'Elephone',
  39: 'Oukitel',
  40: '<PERSON><PERSON><PERSON>',
  41: 'AGM',
  42: 'Xiaomi Pocophone',
  43: 'iQO<PERSON>',
  44: '<PERSON>ubi<PERSON>',
  45: '<PERSON><PERSON>',
  46: 'Yo<PERSON><PERSON><PERSON>',
  47: '<PERSON>',
  48: 'Revvl',
  49: 'Red Magic',
  50: 'Kazam',
  51: 'Xolo'
};

export const DeviceBrandOptions = Object.entries(DeviceBrand).map(([value, label]) => ({ value, label }));
