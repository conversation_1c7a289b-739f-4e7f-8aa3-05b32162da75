.container {
  background-color: var(--background-color);
  min-height: calc(100vh - 70px);
  max-height: calc(100vh - var(--header-nav-height));
  overflow-y: auto;
  padding: 0 16px 16px;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .title {
    margin: 0 0 12px 12px;
    div {
      margin: 0;
    }
  }
  nav {
    // padding-bottom: 12px;
    padding: 16px 0 16px 0px;
    padding-left: 5px;

    li {
      height: 22px;
      display: flex;
      align-items: center;
      :global {
        .ant-breadcrumb-separator {
          color: #c1cbcc;
        }
      }
    }
  }
  .list-container {
    background-color: var(--background-color);
  }
  .content-empty {
    // border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 6px;
    border-radius: 6px;
    background-color: #fff;
    height: calc(100vh - 200px);
    .empty-svg svg {
      padding-top: 12px;
    }
    span {
      padding-bottom: 12px;
      color: #8a8a8a;
    }
  }
}
