/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-23 10:54:16
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-08 17:36:12
 * @Description:
 */
import <PERSON><PERSON> from 'joi';

const AddSchema = {
  seller_id: Joi.number().integer().required(),
  buyer_id: Joi.number().integer().required(),
  type: Joi.number().integer().required(),
  content: Joi.string()
    .required()
    .custom((value, helpers) => {
      let content;
      try {
        content = JSON.parse(value);
        if (content.length < 1) {
          // return helpers.error('any.invalid');
          return helpers.message({
            custom: 'Content must contain at least one item'
          });
        } else {
          const isRight = content.every((item: any) => {
            return (
              typeof item.ratio === 'number' &&
              item.ratio >= 0 &&
              item.ratio <= 100
            );
          });
          const totalRatio = content.reduce(
            (acc: number, item: any) => acc + item.ratio,
            0
          );

          if (!isRight || totalRatio > 100) {
            return helpers.message({
              custom:
                'Invalid format for items in content and total ratio must be between 0 and 100.'
            });
          }
        }
      } catch (error) {
        return helpers.message({ custom: 'Invalid JSON format for content' });
      }
    }),
  expire_time: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2}$/)
    .required()
};
export const AddABTestSchema = Joi.object(AddSchema);

export const UpdateABTestSchema = Joi.object({
  ...AddSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
