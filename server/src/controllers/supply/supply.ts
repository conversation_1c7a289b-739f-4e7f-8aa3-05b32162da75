/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:50:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:28:32
 * @FilePath: /saas.rix-platform/server-ts/src/controllers/supply/supply.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Context } from 'koa';
import { supplyService } from '@/services';
import { SupplyAPI } from '@/types/supply';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { validateBody } from '@/utils/validate-params';
import {
  AddPublisherScheme,
  GetSupplyAuthScheme,
  SetSupplyAuthScheme,
  UpdatePublisherScheme
} from '@/schema/supply';
import { UserType } from '@/constants';

import { syncSellerPartner2Gcp } from '../demand/utils';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class SupplyCtrl implements SupplyAPI.SupplyCtrlInterface {
  async getSupplyList(ctx: Context) {
    let result = { ...RESULT };
    const { cur_time_zone } = ctx.request.body;
    const list = await supplyService.getSupplyList(
      ctx.session!.tnt_id,
      cur_time_zone,
      undefined,
      false
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async getSupplyListWithTesting(ctx: Context) {
    let result = { ...RESULT };
    const list = await supplyService.getSupplyList(
      ctx.session!.tnt_id,
      ctx.request.body.cur_time_zone,
      undefined,
      true
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async getDashboardSupplyList(ctx: Context) {
    let result = { ...RESULT };
    const { tnt_id } = ctx.session!;
    const { curUser = {} } = ctx.state;
    let seller_id = curUser.seller_id || [];
    seller_id = Array.isArray(seller_id) ? seller_id : [seller_id];
    const supplyUser = [UserType.Supply, UserType.Partner];
    if (supplyUser.includes(curUser.type) && !seller_id.length) {
      result = getCtxResult('SUCCESS', []);
    } else {
      const list = await supplyService.getDashboardSupplyList({ seller_id, tnt_id });
      if (list) {
        result = getCtxResult('SUCCESS', list);
      }
    }
    ctx.body = result;
  }

  @validateBody(AddPublisherScheme)
  async addSupply(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const [supplyName, supplyUserName] = await Promise.all([
      supplyService.isSupplyNameExists(
        formData.seller_name,
        ctx.session!.tnt_id
      ),
      supplyService.isSupplyUserNameExists(
        `Pub_${formData.seller_account_name}`,
        ctx.session!.tnt_id
      )
    ]);
    if (supplyName?.length || supplyUserName?.length) {
      result = getCtxResult(
        supplyName?.length ? 'SELLER_NAME_EXISTS' : 'SELLER_USER_NAME_EXISTS'
      );
    } else {
      const list = await supplyService.addSupply(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `addSupply success formData=[${JSON.stringify(formData)}]`
        );
        syncSellerPartner2Gcp(ctx.session!.tnt_id);
      }
    }
    ctx.body = result;
  }

  @validateBody(UpdatePublisherScheme)
  async updateSupply(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    formData.op_id = ctx.session!.user_id;
    const [supplyName, supplyUserName] = await Promise.all([
      supplyService.isSupplyNameExists(
        formData.seller_name,
        ctx.session!.tnt_id,
        formData.seller_id
      ),
      supplyService.isSupplyUserNameExists(
        formData.seller_account_name,
        ctx.session!.tnt_id,
        formData.user_id
      )
    ]);
    console.log('supplyName', supplyName);
    console.log('supplyUserName', supplyUserName);
    if (supplyName?.length || supplyUserName?.length) {
      result = getCtxResult(
        supplyName?.length ? 'SELLER_NAME_EXISTS' : 'SELLER_USER_NAME_EXISTS'
      );
    } else {
      const list = await supplyService.updateSupply(formData);
      if (list) {
        result = getCtxResult('SUCCESS', list);
        getLogger('app').info(
          `updateSupply success formData=[${JSON.stringify(formData)}]`
        );
        syncSellerPartner2Gcp(ctx.session!.tnt_id);
      }
    }
    ctx.body = result;
  }

  @validateBody(GetSupplyAuthScheme)
  async getSupplyAuth(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await supplyService.getSupplyAuth(
      formData.seller_id,
      ctx.session!.tnt_id
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(GetSupplyAuthScheme)
  async getSupplyAppPlacement(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const flag = !!formData.developer;
    const list = await supplyService.getSupplyAppPlacement(
      formData.seller_id,
      flag,
      ctx.session!.tnt_id
    );
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  @validateBody(SetSupplyAuthScheme)
  async setSupplyAuth(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const op_id = ctx.session!.user_id;
    formData.tnt_id = ctx.session!.tnt_id;
    const list = await supplyService.setSupplyAuth(formData, op_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(
        `setSupplyAuth success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }

  async getSupplyEndpoint(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const list = await supplyService.getSupplyEndpoint(
      formData.tnt_id,
      formData.seller_id
    );

    if (list) {
      result = getCtxResult('SUCCESS', list[0]);
      getLogger('app').info(
        `getSupplyEndpoint success formData=[${JSON.stringify(formData)}]`
      );
    }
    ctx.body = result;
  }
}

export default new SupplyCtrl();
