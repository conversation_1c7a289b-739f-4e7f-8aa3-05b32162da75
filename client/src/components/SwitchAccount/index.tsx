import useCustomRequest from '@/hooks/useCustomRequest';
import { getUserLinkList, switchAccount } from '@/services/user';
import { Form, message } from 'antd';
import { useEffect, useState } from 'react';
import { useModel } from 'umi';
import NormalModal from '../Modal/NormalModal';
import NormalSelect from '../Select/NormalSelect';

interface SwitchAccountProps {
  visible: boolean;
  handleClose: () => void;
}

const SwitchAccount = ({ visible, handleClose }: SwitchAccountProps) => {
  const [form] = Form.useForm();
  const { initialState } = useModel('@@initialState');
  const [options, setOptions] = useState([]);

  const { run: switchAccountRun, loading } = useCustomRequest(switchAccount, {
    onSuccess: (data: any) => {
      if (data.user_id) {
        message.success('Switch Account Success');
        window.location.reload();
      }
    }
  });

  // 获取可切换的账号列表
  const { run: getUserLinkListRun, loading: getUserLinkListLoading } = useCustomRequest(getUserLinkList, {
    onSuccess: (data: any) => {
      const { user_id: cur_user_id } = initialState?.currentUser || {};
      const newOptions = data?.map((user: any) => {
        const { user_id, tnt_name, tnt_id, account_name } = user;
        if (user_id === cur_user_id) {
          form.setFieldValue('switch_user_id', user_id);
        }
        return {
          label: `${tnt_name}(${tnt_id})/${account_name}(${user_id})`,
          value: user_id
        };
      });
      setOptions(newOptions);
    }
  });

  useEffect(() => {
    if (visible) {
      const { special_user_id } = initialState?.currentUser || {};
      getUserLinkListRun({ special_user_id });
    }
  }, [visible]);

  const handleSubmit = async () => {
    const values = await form.validateFields();
    switchAccountRun({ switch_user_id: values.switch_user_id });
  };

  const handleCancel = () => {
    form.resetFields();
    handleClose();
  };

  return (
    <NormalModal
      title="Switch Other Account"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
    >
      <Form form={form}>
        <Form.Item
          label="Switch Account"
          name="switch_user_id"
          rules={[
            {
              required: true,
              message: 'Please select a account'
            }
          ]}
        >
          <NormalSelect options={options} loading={getUserLinkListLoading} showSearch />
        </Form.Item>
      </Form>
    </NormalModal>
  );
};

export default SwitchAccount;
