/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-11 17:47:00
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-04-27 20:56:36
 * @Description:
 */

export type Type = {
  [key: string | number]: string;
};
// 编辑类型
export const MenuOperationType = {
  Add: '1',
  Delete: '2',
  Edit: '3',
  View: '4'
};

export const OperationTitle: Type = {
  1: 'Add Sub Menu',
  2: 'Delete Menu',
  3: 'Edit Menu',
  4: 'Menu Detail'
};
export const MenuType = {
  Menu: 2,
  Button: 1
};
