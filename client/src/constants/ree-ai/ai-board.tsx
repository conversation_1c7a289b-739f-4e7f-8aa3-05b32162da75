/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-03 14:25:03
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-01 16:08:19
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { ColumnProps } from 'antd/es/table';
import StatusTag from '@/components/Tag/NewStatusTag';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';

export const BudgetAndTrafficBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Ree AI',
    icon: 'rix-board'
  },
  {
    name: 'AI Board'
  }
];

export const RequestFormColumns: ColumnProps<BoardAPI.TrafficRequestLog>[] = [
  {
    title: 'Sent',
    dataIndex: 'content',
    width: 400,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Ads.txt',
    dataIndex: 'ext_1',
    width: 70,
    render: _ => <StatusTag value={_} statusDescMap={{ 1: 'Yes', 2: 'No' }} />
  },
  {
    title: 'Traffic Type',
    dataIndex: 'ext_2',
    width: 100,
    render: _ => <>{TrafficTypeDescMap[_]}</>
  },
  {
    title: 'Created At',
    dataIndex: 'create_time',
    width: 160
  },
  {
    title: 'Operator',
    dataIndex: 'op_name',
    width: 120,
    ellipsis: { showTitle: false },
    render: _ => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    )
  }
];

export const TrafficTypeMap: { [key: string]: number } = {
  app: 1,
  site: 2
};

export const TrafficTypeDescMap: { [key: number]: string } = {
  1: 'In-app',
  2: 'Site'
};
