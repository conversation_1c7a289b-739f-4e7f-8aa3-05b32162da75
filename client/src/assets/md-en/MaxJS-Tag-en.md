# 「RixEngine」🌟MaxJS-Tag Integration Document🌟 

Before configuring Max-JSTag, Ad Tag needs to be generated on RixEngine console first. Once generated, it can be configured in MAX/Applovin backend system.

# RixEngine Platform Configuration

## Step 1: Create publisher accounts

1. Log in to **RixEngine SaaS** console, fill in the relevant information, and create publisher

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.1-1.png)



## Step 2: Creates Apps and ad placements in [Developer] module 

* **Note: Only creates one virtual app for each Publisher.**

Create a virtual App and generate app ID.

![](https://static.rixengine.com/a/platform/help/maxjs-tag-en/1-2-1.png)

![](https://static.rixengine.com/a/platform/help/maxjs-tag-en/1-2-2.png)

## Step 3: Create ad placements of different Ad Formats and Bid Floor

For example:

Naming convention

Ocrscanner_IOS_320 * 50Banner_Low/Mid/High,
Ocrscanner_And_320 * 50Banner_0.2/0.5/0.8

![](https://static.rixengine.com/a/platform/help/maxjs-tag-en/1-3-1.png)

## Step 4: Generate MAX Js-Tag for publishers. One Js-Tag needs to be generated for each ad size (Ad size does not support macro replacement).

**Js tag Entrance**：

![](https://static.rixengine.com/a/platform/help/maxjs-tag/1.4-1.png)

**Generate the JsTag code (Note that different sizes result in different Js Tags).**：

![](https://static.rixengine.com/a/platform/help/maxjs-tag-en/1-4-2.png)



# MAX Backend Configuration

## Step 1: Create a Custom Network

**Path**: MAX > Mediation > Manage > Networks, Click [**Click here to add a Custom Network**]

![](https://static.rixengine.com/a/platform/help/maxjs-tag/2.1-1.png)

After entering the page, select Network Type, and **paste the ****Js****Tag code generated by the RixEngine console**

**Note: The ****Js****Tag generated by RixEngine for different a sizes (Ad Format) must be configured to match different Ad Format in MAX backend.**

![](https://static.rixengine.com/a/platform/help/maxjs-tag-en/2-1-2.png)

# **Enable Configuraiton**

## **Step 1: Configure ad unit settings in MAX**

* Path: MAX > Mediation > Manage > Ad Units

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-1.png)

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-2.png)

Fill in the Unit ID generated in RixEngine SaaS console as Placement ID, and set CPM Price for countries according to your needs.

**Note**: Make sure your Placement ID is consistent with RixEngine SaaS console

![](https://static.rixengine.com/a/platform/help/maxjs-tag/3.1-3.png)



