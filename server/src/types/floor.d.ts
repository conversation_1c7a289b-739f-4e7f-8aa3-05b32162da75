/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-23 17:06:21
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-13 15:38:48
 * @Description:
 */
import { Context } from 'koa';
import { SupplyAPI } from './supply';
import { AppListAPI } from './app-list';

declare namespace FloorAPI {
  type ExistedFloor = {
    tnt_id: number;
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
  };
  type AddFloorParams = {
    type: number;
    buyer_id: number[];
    seller_id: number;
    plm_id: number[];
    ad_format: number;
    country: string[];
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };
  type UpdatFloorParams = {
    id: number;
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };
  type DeleteFloorParams = {
    id: number;
    tnt_id: number;
  };
  interface FloorCtrl {
    getFloorList(ctx: Context): Promise<void>;
    addFloor(ctx: Context): Promise<void>;
    updateFloor(ctx: Context): Promise<void>;
    deleteFloor(ctx: Context): Promise<void>;
    getAllSupplyPlacement(ctx: Context): Promise<void>;
  }
  interface FloorService {
    getFloorList(
      tnt_id: number,
      cur_time_zone: string,
      plm_id?: number,
      type?: number
    ): Promise<any[]>;
    addFloor(params: any): Promise<boolean>;
    isExistedFloor(params: ExistedFloor): Promise<{ count: number }>;
    updateFloor(params: UpdatFloorParams): Promise<boolean>;
    deleteFloor(params: DeleteFloorParams): Promise<boolean>;
    getAllSupplyPlacement(
      tnt_id: number
    ): Promise<AppListAPI.PlacementListItem[]>;
  }
  interface FloorModel {
    getFloorList(
      tnt_id: number,
      cur_time_zone: string,
      plm_id?: number,
      type?: number
    ): Promise<any[]>;
    addFloor(params: any): Promise<boolean>;
    isExistedFloor(params: ExistedFloor): Promise<{ count: number }>;
    updateFloor(params: UpdatFloorParams): Promise<boolean>;
    deleteFloor(params: DeleteFloorParams): Promise<boolean>;
    getAllSupplyPlacement(
      tnt_id: number
    ): Promise<AppListAPI.PlacementListItem[]>;
  }
}
