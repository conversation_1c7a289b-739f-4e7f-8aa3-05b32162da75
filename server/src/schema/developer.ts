/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-01-30 11:51:41
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-08-24 17:36:49
 * @Description:
 */

import <PERSON><PERSON> from 'joi';
import {
  PlatformType,
  Category,
  ScreenOrientation,
  minDuration,
  maxDuration,
  AdPosition,
  BannerAdSize,
  RectangleAdSize,
  InterstitialAdSize,
  RewardedAdSize,
  PlacementType,
  AdFormat
} from '@/constants/developer';

const AppScheme = {
  app_name: Joi.string().required(),
  store_url: Joi.string()
    .pattern(
      /^(?:http(s)?:\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-\._~:/?#[\]@!\$&'\*\+,;=.]+$/
    )
    .required(),
  category: Joi.string().valid(...Category)
};

const PlacementScheme = {
  isSDK: Joi.boolean().required(),
  isIOS: Joi.boolean(),
  plm_name: Joi.string().required(),
  placement_type: Joi.number()
    .integer()
    .valid(...PlacementType)
    .required(),
  ad_width: Joi.when('placement_type', {
    is: 1,
    then: Joi.number()
      .integer()
      .valid(...BannerAdSize.AdWidth)
      .required()
  })
    .when('placement_type', {
      is: 2,
      then: Joi.number()
        .integer()
        .valid(...RectangleAdSize.AdWidth)
        .required()
    })
    .when('placement_type', {
      is: 3,
      then: Joi.number()
        .integer()
        .valid(...InterstitialAdSize.AdWidth)
        .required()
    })
    .when('placement_type', {
      is: 4,
      then: Joi.number()
        .integer()
        .valid(...RewardedAdSize.AdWidth)
        .required()
    }),
  ad_height: Joi.when('placement_type', {
    is: 1,
    then: Joi.number()
      .integer()
      .valid(...BannerAdSize.AdHeight)
      .required()
  })
    .when('placement_type', {
      is: 2,
      then: Joi.number()
        .integer()
        .valid(...RectangleAdSize.AdHeight)
        .required()
    })
    .when('placement_type', {
      is: 3,
      then: Joi.number()
        .integer()
        .valid(...InterstitialAdSize.AdHeight)
        .required()
    })
    .when('placement_type', {
      is: 4,
      then: Joi.number()
        .integer()
        .valid(...RewardedAdSize.AdHeight)
        .required()
    }),
  support_html: Joi.when('placement_type', {
    is: 3,
    then: Joi.number().integer().valid(0, 1).required()
  }),
  support_video: Joi.when('placement_type', {
    is: 3,
    then: Joi.number().integer().valid(0, 1).required()
  }),
  banner_api: Joi.string(),
  video_api: Joi.string(),
  pos: Joi.number().valid(0, ...AdPosition),
  bid_floor: Joi.number().min(0).required(),
  // price: Joi.number().min(0).required(),
  companionad: Joi.boolean(),
  minduration: Joi.when('placement_type', {
    is: 3,
    then: Joi.when('support_video', {
      is: 1,
      then: Joi.when('isIOS', {
        is: false,
        then: Joi.number()
          .integer()
          .valid(0, ...minDuration)
      }).when('isIOS', {
        is: true,
        then: Joi.number()
      })
    })
  }).when('placement_type', {
    is: 4,
    then: Joi.when('isIOS', {
      is: false,
      then: Joi.number()
        .integer()
        .valid(0, ...minDuration)
        .required()
    }).when('isIOS', {
      is: true,
      then: Joi.number()
    })
    // then: Joi.number()
    //   .integer()
    //   .valid(0, ...minDuration)
    //   .required()
  }),
  maxduration: Joi.when('placement_type', {
    is: 3,
    then: Joi.when('support_video', {
      is: 1,
      then: Joi.when('isIOS', {
        is: false,
        then: Joi.number()
          .integer()
          .valid(0, ...maxDuration)
          .required()
      }).when('isIOS', {
        is: true,
        then: Joi.number()
      })
    })
  }).when('placement_type', {
    is: 4,
    then: Joi.when('isIOS', {
      is: false,
      then: Joi.number()
        .integer()
        .valid(0, ...maxDuration)
        .required()
    }).when('isIOS', {
      is: true,
      then: Joi.number()
    })
    // then: Joi.number()
    //   .integer()
    //   .valid(0, ...maxDuration)
    //   .required()
  }),
  skip: Joi.boolean(),
  mute: Joi.boolean(),
  assets: Joi.when('placement_type', {
    is: 5,
    then: Joi.string().required()
  }),
  protocols: Joi.when('placement_type', {
    is: 4,
    then: Joi.when('isSDK', {
      is: false,
      then: Joi.when('isIOS', {
        is: false,
        then: Joi.string().required()
      }).when('isIOS', {
        is: true,
        then: Joi.string()
      })
    })
  }).when('placement_type', {
    is: 3,
    then: Joi.when('support_video', {
      is: 1,
      then: Joi.when('isSDK', {
        is: false,
        then: Joi.when('isIOS', {
          is: false,
          then: Joi.string().required()
        }).when('isIOS', {
          is: true,
          then: Joi.string()
        })
      })
    })
  }),
  ad_format: Joi.number()
    .integer()
    .valid(...AdFormat)
    .required()
};

export const AddAppScheme = Joi.object({
  ...AppScheme,
  bundle: Joi.string().required(),
  platform: Joi.number()
    .integer()
    .valid(...PlatformType),

  screen_orientation: Joi.number()
    .integer()
    .valid(...ScreenOrientation)
});

export const EditAppScheme = Joi.object({
  ...AppScheme
});

export const AddPlacementScheme = Joi.object({
  ...PlacementScheme
});
