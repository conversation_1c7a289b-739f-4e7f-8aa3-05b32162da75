/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-28 18:54:42
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-23 12:46:55
 * @Description:
 */
import React from 'react';
import MdPage from '../components/MdPage';
// @ts-ignore
import GSContent from '@/assets/md/get_started.md';
// @ts-ignore
import EN_GSContent from '@/assets/md-en/get_started-en.md';
const getStartesDoc: React.FC = () => {
  return <MdPage mdPath={GSContent} enMdPath={EN_GSContent}></MdPage>;
};

export default getStartesDoc;
