import { TopBarSearchItem } from '@/components/TopBar';
import {
  ProfitAdvertiserSearchOption,
  ProfitBundleSearchOption,
  ProfitPublisherSearchOption,
  ProfitTabType
} from '@/constants/strategy/profit';
import { useEffect, useState } from 'react';

type SelectOption = {
  label: string;
  value: string;
};

/**
 * 自定义 hook 管理 searchOptions
 * @param demandOptions 需求选项
 * @param supplyOptions 供应选项
 * @param currentTab 当前 tab
 * @returns 当前 tab 的 searchOptions
 */
export const useSearchOptions = (
  demandOptions: SelectOption[],
  supplyOptions: SelectOption[],
  currentTab: ProfitTabType
) => {
  // searchOptions 初始化，然后异步设置三个不同的 searchOptions，并缓存
  const [cacheMap, setCacheMap] = useState<Record<ProfitTabType, TopBarSearchItem[]>>({
    advertiser: ProfitAdvertiserSearchOption,
    publisher: ProfitPublisherSearchOption,
    bundle: ProfitBundleSearchOption
  });

  useEffect(() => {
    setCacheMap(prev => {
      const newCacheMap = { ...prev };
      let hasChanges = false;

      // 只有当 demandOptions 有数据且与当前缓存不同时才更新 advertiser
      if (demandOptions.length > 0) {
        const newAdvertiserOptions = ProfitAdvertiserSearchOption.map(item => ({
          ...item,
          options: item.key === 'buyer_id' ? demandOptions : item.options
        }));
        
        if (JSON.stringify(newAdvertiserOptions) !== JSON.stringify(prev.advertiser)) {
          newCacheMap.advertiser = newAdvertiserOptions;
          hasChanges = true;
        }
      }

      // 只有当 supplyOptions 有数据且与当前缓存不同时才更新 publisher
      if (supplyOptions.length > 0) {
        const newPublisherOptions = ProfitPublisherSearchOption.map(item => ({
          ...item,
          options: item.key === 'seller_id' ? supplyOptions : item.options
        }));
        
        if (JSON.stringify(newPublisherOptions) !== JSON.stringify(prev.publisher)) {
          newCacheMap.publisher = newPublisherOptions;
          hasChanges = true;
        }
      }

      // 当 demandOptions 或 supplyOptions 有数据时才更新 bundle
      if (demandOptions.length > 0 || supplyOptions.length > 0) {
        const newBundleOptions = ProfitBundleSearchOption.map(item => ({
          ...item,
          options: item.key === 'buyer_id' ? demandOptions : item.key === 'seller_id' ? supplyOptions : item.options
        }));
        
        if (JSON.stringify(newBundleOptions) !== JSON.stringify(prev.bundle)) {
          newCacheMap.bundle = newBundleOptions;
          hasChanges = true;
        }
      }

      // 只有当确实有变化时才返回新的对象，避免不必要的重新渲染
      return hasChanges ? newCacheMap : prev;
    });
  }, [demandOptions, supplyOptions]);

  return cacheMap[currentTab];
};
