/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-04 16:38:06
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-12 19:46:48
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { AdFormatOptions } from '@/constants/global-mapping/ad-format';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { formatMoney } from '@/utils';
import { CountryOptions } from '@/constants/global-mapping/country';
export const DashboardSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],

    timeLimit: 31,
    dimensionTimeLimit: [
      {
        dimensionKey: 'app_bundle_id',
        limit: 3,
        searchLimit: 3
      },
      {
        dimensionKey: 'placement_id',
        limit: 3,
        searchLimit: 3
      }
    ]
  },

  {
    type: 'input',
    name: 'Unit ID',
    value: '',
    key: 'placement_id'
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    mode: 'multiple',
    value: '',
    type: 'select',
    options: AdFormatOptions
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'app_bundle_id',
    value: '',
    placeholder: 'Please Input Bundle(one per line)',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Platform',
    key: 'platform',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: Object.keys(demandCampaign.MoblieOS).map(item => {
      return {
        value: item,
        label: (demandCampaign.MoblieOS as any)[item]
      };
    })
  },
  {
    name: 'Country',
    key: 'country',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: CountryOptions,
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Date', value: 'day' },
      // { label: 'Hour', value: 'day_hour' },

      { label: 'Bundle', value: 'app_bundle_id' },
      { label: 'Unit ID', value: 'placement_id' },
      { label: 'Ad Format', value: 'ad_format' },

      { label: 'Country', value: 'country' },
      { label: 'Platform', value: 'platform' }
    ]
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Net Revenue', value: 'seller_net_revenue' },
      { label: 'Total Request', value: 'total_request' },
      { label: 'Response', value: 'response' },
      { label: 'Impression', value: 'seller_payment_impression' },
      { label: 'Win', value: 'win' },
      { label: 'Fill Rate', value: 'fill_rate' },
      // { label: 'Render Rate', value: 'impression_rate' },
      { label: 'Win Rate', value: 'win_rate' },
      { label: 'Net eCpm', value: 'seller_net_ecpm' }
    ]
  }
];

export const DashboardBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report'
  },
  {
    name: 'Publisher Reporting'
  }
];

export const DashBoardDefaultMetrics = [
  'total_request',
  'response',
  'seller_payment_impression',
  'win',
  'total_seller_bid_floor',
  'seller_net_revenue',
  'fill_rate',
  'win_rate'
  // 'impression_rate',
  // 'seller_net_ecpm',
];

export const DashboardDefaultDimension = ['day'];

export const DashboardAllColumns: ColumnProps<DashboardAPI.DashboardListItem>[] = Object.seal([
  {
    title: 'Date',
    key: 'day',
    dataIndex: 'date',
    fixed: 'left',
    width: 160,
    render: (txt, params) => <>{params.date}</>,
    sorter: true
  },

  {
    title: 'Bundle',
    dataIndex: 'app_bundle_id',
    width: 280,
    key: 'app_bundle_id'
  },
  {
    title: 'Unit ID',
    dataIndex: 'placement_id',
    width: 150,
    key: 'placement_id',
    render: (txt, params) => {
      if (!txt || txt.indexOf('""') !== -1) return <span>-</span>;
      return <>{txt}</>;
    }
  },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 150,
    key: 'ad_format',
    sorter: true
  },

  {
    title: 'Platform',
    dataIndex: 'platform',
    width: 120,
    key: 'platform'
  },
  {
    title: 'Country',
    dataIndex: 'country',
    width: 150,
    key: 'country'
  },

  {
    title: 'Net Revenue',
    key: 'seller_net_revenue',
    dataIndex: 'seller_net_revenue',
    sorter: true,
    width: 240,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },

  {
    title: 'Total Request',
    key: 'total_request',
    dataIndex: 'total_request',
    width: 220,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },

  {
    title: 'Response',
    key: 'response',
    dataIndex: 'response',
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Impression',
    key: 'seller_payment_impression',
    dataIndex: 'seller_payment_impression',
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Win',
    key: 'win',
    dataIndex: 'win',
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Fill Rate(%)',
    key: 'fill_rate',
    dataIndex: 'fill_rate',
    width: 150
  },
  // {
  //   title: 'Render Rate(%)',
  //   key: 'impression_rate',
  //   dataIndex: 'impression_rate',
  //   width: 150
  // },
  {
    title: 'Win Rate(%)',
    key: 'win_rate',
    dataIndex: 'win_rate',
    sorter: true,
    width: 150
  },
  {
    title: 'Net eCPM',
    key: 'seller_net_ecpm',
    dataIndex: 'seller_net_ecpm',
    width: 200
  }
]);

export const DashboardDefaultColumnKeys = [
  'day',
  'request',
  'response',
  'seller_payment_impression',
  'seller_net_revenue',
  'fill_rate'
  // 'impression_rate',
  // 'seller_net_ecpm',
];
