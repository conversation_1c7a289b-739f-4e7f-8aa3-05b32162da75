import BundleTableEdit from '@/components/BundleTableEdit';
import NormalInput from '@/components/Input/NormalInput';
import Select from '@/components/Select/NormalSelect';
import { ProfitTab, ProfitType } from '@/constants/strategy/profit';
import { isValidBundle } from '@/utils';
import { Form } from 'antd';
import { useCallback } from 'react';

type SelectOption = {
  label: string;
  value: number;
};

type UseProfitFormFieldsParams = {
  currentTab: string;
  demandOptions: SelectOption[];
  supplyOptions: SelectOption[];
  isEdit: boolean;
};

const renderAdvertiserField = (options: SelectOption[], disabled = false) => (
  <Form.Item
    name="buyer_id"
    label="Advertiser"
    rules={[{ required: true, message: 'Please select advertiser' }]}
    key="buyer_id"
  >
    <Select options={options} disabled={disabled} allowClear showSearch />
  </Form.Item>
);

const renderPublisherField = (options: SelectOption[], disabled = false) => (
  <Form.Item
    name="seller_id"
    label="Publisher"
    rules={[{ required: true, message: 'Please select publisher' }]}
    key="seller_id"
  >
    <Select options={options} disabled={disabled} allowClear showSearch />
  </Form.Item>
);

// 1. bundle 填入，输入框或文本框
// 2. 创建时是支持批量的，编辑时不支持
// 3. 编辑时禁用的
const renderBundleField = (isEdit: boolean) => (
  <Form.Item name="bundle" label="Bundle" rules={[{ required: true, message: 'Please select bundle' }]} key="bundle">
    {isEdit ? (
      <NormalInput placeholder="Please Input Bundle" disabled allowClear />
    ) : (
      <BundleTableEdit
        editTitle="Edit Bundle"
        editTips="Enter the bundle (one per line)"
        deleteAllTitle="Delete All Bundle"
        deleteAllTips="Are you sure to delete all bundle?"
        contentMaxHeight={'calc(20vh)'}
        editSingle={true}
        validator={(val: string) => isValidBundle(val)}
      />
    )}
  </Form.Item>
);

/**
 * 自定义 hook 管理利润表单字段渲染逻辑
 * @param params 参数对象
 * @returns 渲染表单字段的函数
 */
export const useProfitFormFields = ({
  currentTab,
  demandOptions,
  supplyOptions,
  isEdit
}: UseProfitFormFieldsParams) => {
  // 根据利润类型和编辑状态渲染相应的表单字段
  const renderFormFields = useCallback(
    (type: number) => {
      const isAdvertiserTab = currentTab === ProfitTab.advertiser;
      const isPublisherTab = currentTab === ProfitTab.publisher;
      const isBundleTab = currentTab === ProfitTab.bundle;

      if (isBundleTab) {
        // 渲染结构 [renderAdvertiserField(isEdit),renderPublisherField(isEdit), renderBundleField(isEdit)]
        // 根据 type 判断：如果 type 为 ProfitType.DemandBundle，则不需要 renderPublisherField(isEdit)
        if (type === ProfitType.DemandBundle) {
          return [renderAdvertiserField(demandOptions, isEdit), renderBundleField(isEdit)];
        }

        return [
          renderAdvertiserField(demandOptions, isEdit),
          renderPublisherField(supplyOptions, isEdit),
          renderBundleField(isEdit)
        ];
      }

      // 新增模式：根据当前tab显示对应字段
      if (!isEdit) {
        return [renderAdvertiserField(demandOptions, isAdvertiserTab), renderPublisherField(supplyOptions, isPublisherTab)].sort(() => {
          return isAdvertiserTab ? 1 : -1;
        });
      }

      // 编辑模式：根据类型显示对应字段
      if (type === ProfitType.Demand) {
        return [renderAdvertiserField(demandOptions, true)];
      }
      if (type === ProfitType.Seller) {
        return [renderPublisherField(supplyOptions, true)];
      } else if (type === ProfitType['Seller-Demand']) {
        return [
          renderAdvertiserField(demandOptions, true),
          renderPublisherField(supplyOptions, true)
        ].sort(() => {
          return isAdvertiserTab ? 1 : -1;
        });
      }

      return [];
    },
    [currentTab, demandOptions, supplyOptions, isEdit]
  );

  return { renderFormFields };
};
