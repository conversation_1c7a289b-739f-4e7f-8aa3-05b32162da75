/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-03 19:55:08
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-08 14:48:55
 * @Description:
 */

import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';
import type { SearchResultItem, TopBarSearchItem } from '@/components/TopBar';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import { DateType } from '@/constants/data-report';
import {
  DashboardAllColumns,
  DashboardBreadOptions,
  DashboardDefaultColumnKeys,
  DashboardDefaultDimension,
  DashBoardDefaultMetrics,
  DashboardSearchOption,
  IplayableTNTAllColumns,
  IplayableTNTSearchOption
} from '@/constants/data-report/advReport';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { downloadAdvList, getAdvReportList } from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { checkMetrics, dateLimit } from '@/utils/report';
import { FormInstance } from 'antd/es/form';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';
import moment, { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { useModel } from 'umi';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  // custom
  let isCustom = false;
  if (initialState?.currentUser?.tnt_id === 1059) {
    isCustom = true;
    console.log('isCustom', isCustom);
  }
  const [columns, setColumns] = useState<ColumnProps<DashboardAPI.DashboardListItem>[]>([]);
  const [searchOptions, setSearchOptions] = useState(isCustom ? IplayableTNTSearchOption : DashboardSearchOption);
  const [dataSource, setDataSource] = useState<API.BackResult<DashboardAPI.DashboardListItem>>({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  const [defaultParams, setDefaultParams] = useState<any>();
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>();
  const [searchValue, setSearchValue] = useState<any>();
  const [totalCount, setTotalCount] = useState<number>(0); // 前端缓存总数
  // 显示那些列
  const [columnKeys, setColumnKeys] = useState(DashboardDefaultColumnKeys);
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<SorterResult<DashboardAPI.DashboardListItem>>({
    columnKey: 'date',
    order: 'descend'
  });
  const [allColumns, setAllColumns] = useState<ColumnProps<DashboardAPI.DashboardListItem>[]>(
    isCustom ? IplayableTNTAllColumns : DashboardAllColumns
  );

  useEffect(() => {
    handleFillColumns();
    handleSearchOptions();
  }, []);

  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<DashboardAPI.DashboardListItem>[] = DashboardAllColumns.map(v => {
        return {
          ...v,
          sortOrder: v.sorter && sortedInfo.columnKey === v.dataIndex ? sortedInfo.order : null
        };
      });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter(v => columnKeys.includes((v.key as string) || (v.dataIndex as string)));
      setColumns(tmp);
    }
  };
  // 默认值
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));

    const index = options.findIndex(item => item.key === 'date');
    if (index !== -1) {
      // 一天前
      const lastDay = moment().subtract(1, 'days').format('YYYYMMDD');
      const lastWeek = moment().subtract(3, 'days').format('YYYYMMDD');
      options[index].value = [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')];
      const params = { start: 0, end: 50, start_date: lastWeek, end_date: lastDay };
      setDefaultParams(params);
      const tmp = {
        metrics: [...DashBoardDefaultMetrics],
        date: [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        columns: [...DashboardDefaultDimension]
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
      //  getTableData(params);
    }
    setSearchOptions(options);
  };

  const handleExport = (
    columns: ColumnProps<DashboardAPI.DashboardListItem>[],
    sourceData?: API.BackResult<DashboardAPI.DashboardListItem>
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map(item => {
      return {
        label: item.title,
        value: item.dataIndex !== 'day' && item.dataIndex !== 'day_hour' ? item.dataIndex : 'date'
      };
    });
    const data = sourceData?.data || dataSource.data;
    downloadCsv(fileName, data || [], { fields });
  };
  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(item => item.key === 'columns' && Array.isArray(item.value) && item.value.length > 0);
    const metrics = val.find(item => item.key === 'metrics' && Array.isArray(item.value) && item.value.length > 0);

    const is_hour = dimension?.value?.findIndex((v: any) => v === 'day_hour') > -1;
    const is_day = dimension?.value.findIndex((v: any) => v === 'day') > -1;
    const dim: any[] = dimension?.value.filter((d: string) => !['day', 'day_hour'].includes(d)) || [];
    const mt: any[] = metrics?.value || [];

    const column_keys = [...dim, ...mt];
    if (is_day && is_hour) {
      column_keys.unshift('day_hour');
    } else if ((is_day && !is_hour) || (!is_day && is_hour)) {
      is_day && column_keys.unshift('day');
      is_hour && column_keys.unshift('day_hour');
    }
    setColumnKeys(column_keys);
  };
  const handleSearchChange = (start: number, end: number, val: SearchResultItem[]) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params });
  };

  const handleGetSearchParams = (start: number, end: number, val: SearchResultItem[]) => {
    const tmp = val.filter(
      item => (Array.isArray(item.value) && item.value.length > 0) || (!Array.isArray(item.value) && item.value)
    );
    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach(item => {
      if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map(v => v.format('YYYYMMDD'));
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else if (item.key === 'columns') {
        params.split_time = DateType.Day;
        if (Array.isArray(item.value)) {
          // 预防污染
          const tmp = JSON.parse(JSON.stringify(item.value));
          const sizeIndex = tmp.findIndex((t: string) => t === 'ad_size');
          const hourIndex = tmp.findIndex((t: string) => t === 'day_hour');
          const dayIndex = tmp.findIndex((t: string) => t === 'day');
          console.log(dayIndex, hourIndex);
          if (sizeIndex !== -1) {
            tmp[sizeIndex] = 'ad_width, ad_height';
          }
          if (dayIndex === -1 && hourIndex === -1) {
            params.split_time = DateType.Null;
          } else if (hourIndex !== -1) {
            params.split_time = DateType.Hour;
          } else if (dayIndex !== -1) {
            params.split_time = DateType.Day;
          }
          params['columns'] = tmp;
        }
      } else {
        params[item.key] = item.value;
      }
    });

    return params;
  };

  const onSuccess = (
    data: API.BackResult<DashboardAPI.DashboardListItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.DashboardListItem>[]
  ) => {
    data.total === -1 ? (data.total = totalCount) : setTotalCount(data.total);
    const tmp = data.data.map(item => {
      item.ad_format = AdFormatToLabel[item.ad_format];
      item.platform = (demandCampaign.MoblieOS as any)[item.platform];
      // let dateStr = '';
      // if (typeof item.date === 'string' && item.date) {
      //   dateStr = item.date;
      //   (item.date as string) =
      //     isDownloadAll || hour ? formatTime(dateStr, 'yyyy-MM-dd hh:mm:ss') : formatTime(dateStr, 'yyyy-MM-dd');
      // } else if (item.date && (item.date as any).value) {
      //   dateStr = (item.date as any).value;
      //   (item.date as string) =
      //     isDownloadAll || hour ? formatTime(dateStr, 'yyyy-MM-dd hh:mm:ss') : formatTime(dateStr, 'yyyy-MM-dd');
      // }
      return item;
    });
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key
    };
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.DashboardListItem>[]
  ) => {
    const { order_key, order } = getOrderAndKey();
    const lastDay = moment().format('YYYYMMDD');
    const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time: DateType.Null,
      start_date: lastWeek,
      end_date: lastDay,
      order,
      order_key,
      ...value
    };
    if (!checkMetrics(params.metrics)) {
      return;
    }
    fetchData({
      setLoading,
      request: getAdvReportList,
      params,
      onSuccess: data => onSuccess(data, isDownloadAll, columns)
    });
  };

  const handleDownloadAll = (data: any, columns: ColumnProps<DashboardAPI.DashboardListItem>[]) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmp = {
      ...params,
      order_key,
      order,
      buyer_id: initialState?.currentUser?.buyer_id,
      type: initialState?.currentUser?.type
    };
    fetchData({
      setLoading,
      request: downloadAdvList,
      params: tmp,
      onSuccess: data => onSuccess({ data, total: 0 }, true, columns)
    });
  };

  const handleSortChange = (page: number, size: number, search: SearchResultItem[], sorter: any) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });
    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['day'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    params.isPaing = !!totalCount;
    getTableData(params);
  };
  const handleDisableDate = (currentDate: Moment) => {
    const defaultRange =
      currentDate > moment().endOf('day') || currentDate < moment().subtract(3, 'months').startOf('month');
    return defaultRange;
  };

  const handleDateLimit = (
    key: string,
    date: any[],
    timeLimit: number,
    form: FormInstance<any>,
    originCallback: () => void
  ) => {
    dateLimit(key, date, timeLimit, form, originCallback);
  };

  return (
    <PageContainer flexDirection="column" options={DashboardBreadOptions}>
      <BackTable<DashboardAPI.DashboardListItem>
        pageTitle="Advertiser Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        getTableData={getTableData}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        handleDateLimit={handleDateLimit}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        // buttonAuth={buttonAuth}
        // defaultFormItemWidth={424}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        dateRangeKeys={['Today', 'Yesterday', '3 Days', 'Last Month', 'This Month']}
        maxRange={3}
      />
    </PageContainer>
  );
};

export default Page;
