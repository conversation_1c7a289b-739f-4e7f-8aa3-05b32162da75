import { Form, Radio, message } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';

import { addSupply, isAccountNameExists, updateSupply } from '@/services/api';
import { fetchData, isValidName } from '@/utils';

import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import Input from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';

import NormalInput from '@/components/Input/NormalInput';
import NormalSelect from '@/components/Select/NormalSelect';
import { useTitleComponentMap } from '@/components/Title';
import TocAnchor from '@/components/TocAnchor';
import {
  DemandAndSupplyStatusMap,
  DemandAndSupplyStatusOptions,
  getProfitMinValue,
  ProfitMaxValue,
  ProfitModelOptions,
  ProfitModelType,
  StatusMap,
  StatusOptions
} from '@/constants';
import { IntegrationType } from '@/constants/app-list';
import { RevShareRatioRule } from '@/constants/strategy';
import {
  ChannelOptions,
  DeviceOptions,
  PassUrlStatus,
  PassUrlStatusOptions,
  RelationshipOptions,
  RevTrackType,
  RevTrackTypeOptions
} from '@/constants/supply';
import { isBannerMultiSizeLimitTnTs } from '@/pages/demand/components/AddDemandDrawer';
import { handleFormError } from '@/utils/form';
import { isCustomProfitModel } from '@/utils/permission';
import { useRequest } from 'ahooks';

type AddSupplyModelProps = {
  seller_id?: number;
  supply?: SupplyAPI.SupplyListItem;
  visible: boolean;
  isEdit: boolean;
  partnerList: PartnerAPI.PartnerListItem[];
  handleClose: () => void;
  reloadSupply: () => void;
};

const DefaultFormData = {
  seller_name: '',
  integration_type: undefined,
  channel_type: 1,
  device_type: 1,
  relationship: 2,
  profit_model: ProfitModelType.Net,
  profit_ratio: 30,
  rev_share_ratio: 100,
  profit_status: StatusMap.Active,
  cus_status: StatusMap.Paused,
  pass_burl: PassUrlStatus.Paused,
  pass_nurl: PassUrlStatus.Paused,
  pass_lurl: PassUrlStatus.Paused,
  rev_track_type: RevTrackType.ADM,
  status: DemandAndSupplyStatusMap.Active,
  publisher_id: '',
  support_omid: StatusMap.Paused,
  native_strict_validation: StatusMap.Paused,
  adomain_filter: StatusMap.Active,
  banner_multi_size: StatusMap.Paused
};

const tocOptions = [
  {
    href: '#basic-info',
    title: 'Basic Info'
  },
  {
    href: '#profit-settings',
    title: 'Profit Settings'
  },
  {
    href: '#tracking-settings',
    title: 'Tracking Settings'
  },
  {
    href: '#filter-and-compatibility-strategy',
    title: 'Filter and Compatibility Strategy'
  }
];

const AddSupplyModel: React.FC<AddSupplyModelProps> = ({
  supply,
  handleClose,
  visible,
  isEdit,
  reloadSupply,
  partnerList
}) => {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profitModel, setProfitModel] = useState(ProfitModelType.Net);
  const [type, setType] = useState(0);
  const [cusDisabled, setCusDisabled] = useState(false);
  const [showTagID, setShowTagID] = useState(false);
  const [siteDisabled, setSiteDisabled] = useState(false);
  const [profitRatioChange, setProfitRatioChange] = useState(false);
  const { dataSource: integrationTypeList, reload } = useModel('useSellerIntegrationTypeList');
  const [itgTypeOptions, setItgTypeOptions] = useState<any[]>([]);
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);

  const profitModelOptions = useMemo(() => {
    const isCustom = isCustomProfitModel(initialState?.currentUser?.tnt_id ?? 0, 'supply');
    return isCustom ? ProfitModelOptions : ProfitModelOptions.filter(v => v.value === 1);
  }, [initialState]);

  const { runAsync: checkedName } = useRequest(isAccountNameExists, {
    debounceWait: 300,
    manual: true
  });

  useEffect(() => {
    if (integrationTypeList && integrationTypeList.length) {
      const isMaxJSTag = supply?.integration_type === IntegrationType['Max-JSTag'];

      setItgTypeOptions(
        integrationTypeList
          .filter(item => {
            const isNotPerbid = item.id !== IntegrationType['Perbid'];
            const isNotMaxJSTag = item.id !== IntegrationType['Max-JSTag'];

            return isNotPerbid && (isMaxJSTag ? true : isNotMaxJSTag);
          })
          .map((item, index) => {
            return {
              label: item.itg_name,
              value: item.id,
              disabled: isEdit
            };
          })
      );
    }
    if (!integrationTypeList && visible) {
      reload();
    }
  }, [integrationTypeList, visible, supply]);

  useEffect(() => {
    if (visible && !isEdit) {
      handleDefaultType();
    }
  }, [visible, isEdit]);

  useEffect(() => {
    if (supply && isEdit && visible) {
      form.setFieldsValue({ ...supply, sp_id: supply.sp_id > 0 ? supply.sp_id : undefined });
      setType(supply.integration_type);
      setProfitModel(supply.profit_model);
      setShowTagID(supply.cus_status === 2);
    } else if (!isEdit && visible) {
      handleDefaultType();
    } else {
      form.resetFields();
      setProfitModel(ProfitModelType.Net);
    }
  }, [supply, isEdit, visible]);

  useEffect(() => {
    const item = integrationTypeList && integrationTypeList.find(v => v.id === type);
    if (item) {
      if (['iOS SDK', 'Android SDK', 'Max-JSTag', 'MW-JSTag', 'VastTag'].includes(item.itg_name)) {
        if (item.itg_name === 'MW-JSTag') {
          form.setFieldsValue({ channel_type: 2, cus_status: 1 });
        } else {
          form.setFieldsValue({ channel_type: 1, cus_status: 1 });
        }
        setSiteDisabled(true);
        setCusDisabled(true);
      } else {
        if (item.itg_name === 'RTB') {
          setCusDisabled(true);
        }
        form.setFieldValue('cus_status', supply?.cus_status || 2);
        setShowTagID(form.getFieldValue('cus_status') === 2);
        setSiteDisabled(false);
      }
    }
  }, [type]);

  const handleDefaultType = () => {
    if (integrationTypeList && integrationTypeList.length) {
      form.setFieldValue('integration_type', integrationTypeList[0].id);
      form.setFieldValue('tagid_status', 2);
      setShowTagID(true);
      setType(integrationTypeList[0].id);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };
  const handleFinish = (values: any) => {
    if (isEdit) {
      handleEditSupply({
        ...values,
        seller_name: values?.seller_name?.trim(),
        seller_id: supply?.seller_id,
        profit_id: supply?.profit_id,
        user_id: supply?.user_id,
        profitRatioChange: profitRatioChange,
        seller_account_name: values.seller_account_name?.trim(),
        ori_data: supply || {}
      });
    } else {
      handleAddSupply({
        ...values,
        seller_name: values.seller_name?.trim(),
        seller_account_name: values.seller_account_name?.trim()
      });
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadSupply();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };
  const handleAddSupply = (params: any) => {
    console.log(params);
    // 新增数据
    fetchData({ setLoading, request: addSupply, params, onSuccess });
  };

  const handleEditSupply = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateSupply, params });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.profit_model) {
      setProfitModel(changeValue.profit_model);
    }
    if (changeValue.integration_type) {
      setType(changeValue.integration_type);
    }
    if (changeValue.cus_status) {
      setShowTagID(changeValue.cus_status === 2);
    }
    if (changeValue.profit_ratio) {
      setProfitRatioChange(true);
    }
    if (changeValue.seller_name && !isEdit) {
      form.setFieldsValue({ seller_account_name: changeValue.seller_name });
      form.validateFields(['seller_account_name']);
    }
  };

  // 处理 finish 失败的逻辑
  const handleFinishFailed = useMemo(() => handleFormError(form), [form]);

  const titleComponentMap = useTitleComponentMap(tocOptions);

  return (
    <NormalDrawer
      zIndex={1001}
      blackName={isEdit ? `Edit Publisher` : `Create Publisher`}
      grayName={isEdit ? `${supply?.seller_name}` : ''}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={false}
      titleMaxWidth={325}
      contentWrapperStyle={{
        width: 'max-content',
        maxWidth: '100%'
      }}
    >
      <div style={{ display: 'flex', gap: 16, alignItems: 'flex-start' }}>
        <Form
          initialValues={DefaultFormData}
          onFinish={handleFinish}
          onFinishFailed={handleFinishFailed}
          autoComplete="off"
          form={form}
          layout="vertical"
          onValuesChange={handleValueChange}
        >
          {titleComponentMap['basic-info']}
          <Form.Item
            name="seller_name"
            label="Publisher Name:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Enter Publisher Name'
              },
              {
                validator: (rule: any, value: any) => {
                  if (!value || isValidName(value)) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error('Only letters/numbers/Chinese characters/spaces/underscores/center lines are supported')
                  );
                }
              }
            ]}
          >
            <Input placeholder="Please Enter Publisher Name" />
          </Form.Item>
          <Form.Item
            name="seller_account_name"
            label="Publisher Account Name:"
            rules={[
              {
                required: true,
                message: 'Please Enter Publisher Account Name'
              },
              {
                validator: async (_, value) => {
                  if (!value) {
                    return Promise.resolve();
                  }
                  const res = await checkedName({ account_name: `Pub_${value?.trim()}` });
                  if (res.code === 0) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error(`${res.message}.Please Edit!`));
                }
              }
            ]}
          >
            <Input
              placeholder="Please Enter Publisher Account Name"
              addonBefore={isEdit ? undefined : 'Pub_'}
              disabled={isEdit}
              allowClear
            />
          </Form.Item>
          <Form.Item
            name="publisher_id"
            label="Publisher ID:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                message: 'Please Input Publisher ID'
              }
            ]}
          >
            <NormalInput allowClear />
          </Form.Item>
          <Form.Item
            name="sp_id"
            label="Partner:"
            rules={[
              {
                required: false,
                message: 'Please Select Partner'
              }
            ]}
          >
            <NormalSelect
              // style={{ width: 360 }}
              options={partnerList.map(v => ({ label: `${v.partner_name}(${v.partner_id})`, value: v.sp_id }))}
              allowClear
              showSearch
            />
          </Form.Item>
          <Form.Item
            name="relationship"
            label="Relationship:"
            rules={[
              {
                required: true,
                message: 'Please Select Relationship'
              }
            ]}
          >
            <NormalRadio>
              {RelationshipOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="channel_type"
            label="Channel Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Channel Type'
              }
            ]}
          >
            <NormalRadio disabled={isEdit}>
              {ChannelOptions.map((item, index) => (
                <Radio key={index} value={item.value} disabled={siteDisabled || type === IntegrationType['MW-JSTag']}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="device_type"
            label="Device Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Device Type'
              }
            ]}
          >
            <NormalRadio disabled={isEdit}>
              {DeviceOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="integration_type"
            label="Integration Type:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Integration Type'
              }
            ]}
          >
            <NormalRadio
              options={itgTypeOptions}
              optionType="default"
              style={{
                backgroundColor: 'transparent'
              }}
            />
          </Form.Item>
          <Form.Item
            name="cus_status"
            label="Custom Placement:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Custom Placement'
              }
            ]}
            tooltip="Whether to support custom ad configuration"
          >
            <NormalRadio disabled={cusDisabled}>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>

          {!cusDisabled && showTagID && (
            <Form.Item
              name="tagid_status"
              label="Publisher TagID:"
              rules={[
                {
                  required: true,
                  message: 'Please Select Publisher TagID'
                }
              ]}
              tooltip="Whether to generate the publisher tagid in the unit ID field in the report"
            >
              <NormalRadio>
                {StatusOptions.map((item, index) => (
                  <Radio key={index} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          )}
          <Form.Item
            name="status"
            label="Status:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio>
              {DemandAndSupplyStatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          {titleComponentMap['profit-settings']}
          {/* 不能编辑 */}
          <Form.Item
            name="profit_status"
            label="Profit:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Profit Status'
              }
            ]}
          >
            <NormalRadio disabled>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          {/* 必填 */}
          <Form.Item
            name="profit_ratio"
            label="Profit Ratio:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Profit Ratio'
              }
            ]}
          >
            <InputNumber addonAfter="%" style={{ width: 120 }} min={profitMinValue} max={ProfitMaxValue} />
          </Form.Item>
          <Form.Item
            name="profit_model"
            label="Profit Model:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Profit Model'
              }
            ]}
          >
            <NormalRadio>
              {profitModelOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          {profitModel === ProfitModelType['Rev Share'] && (
            <Form.Item
              name="rev_share_ratio"
              label="Rev Share Ratio:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Input Rev Share Ratio'
                },
                RevShareRatioRule
              ]}
            >
              <InputNumber addonAfter="%" style={{ width: 120 }} min={1} max={100} />
            </Form.Item>
          )}
          {titleComponentMap['tracking-settings']}
          <Form.Item
            name="rev_track_type"
            label="Revenue Tracking Type:"
            rules={[
              {
                required: true,
                message: 'Please Select Revenue Tracking Type'
              }
            ]}
          >
            <NormalRadio>
              {RevTrackTypeOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="pass_nurl"
            label="NURL:"
            rules={[
              {
                required: true,
                message: 'Please Select NURL Status'
              }
            ]}
          >
            <NormalRadio disabled={!isEdit}>
              {PassUrlStatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="pass_burl"
            label="BURL:"
            rules={[
              {
                required: true,
                message: 'Please Select BURL Status'
              }
            ]}
          >
            <NormalRadio disabled={!isEdit}>
              {PassUrlStatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="pass_lurl"
            label="LURL:"
            rules={[
              {
                required: true,
                message: 'Please Select LURL Status'
              }
            ]}
          >
            <NormalRadio disabled={!isEdit}>
              {PassUrlStatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="support_omid"
            label="Support OMID:"
            rules={[
              {
                required: true,
                message: 'Please Select Support OMID'
              }
            ]}
            tooltip="ad request parameters api:7"
          >
            <NormalRadio options={StatusOptions}></NormalRadio>
          </Form.Item>
          {titleComponentMap['filter-and-compatibility-strategy']}
          {isBannerMultiSizeLimitTnTs(initialState?.currentUser?.tnt_id ?? 0) && (
            <Form.Item
              name="banner_multi_size"
              label="Banner Multi Size:"
              rules={[
                {
                  required: true,
                  message: 'Please Select Banner Multi Size'
                }
              ]}
              tooltip="Add ad size 300*250 for requests with a 320*50 banner"
            >
              <NormalRadio options={StatusOptions}></NormalRadio>
            </Form.Item>
          )}
          <Form.Item
            name="native_strict_validation"
            label="Native Size Filter:"
            rules={[
              {
                required: true,
                message: 'Please Select Native Size Filter'
              }
            ]}
            tooltip="Filter ads that do not match the traffic size and length"
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
          <Form.Item
            name="adomain_filter"
            label="Adomain Filter:"
            rules={[
              {
                required: true,
                message: 'Please Select Adomain Filter'
              }
            ]}
            tooltip="Filter ads with empty adomain"
          >
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        </Form>
        <TocAnchor
          affix={false}
          targetOffset={20}
          showInkInFixed
          tocOptions={tocOptions}
          style={{
            position: 'sticky',
            top: 20,
            display: initialState?.isCollapsed ? 'none' : 'block'
          }}
          getContainer={() => document.getElementById('drawer-content')!}
        />
      </div>
    </NormalDrawer>
  );
};

export default AddSupplyModel;
