/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:40:31
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 16:52:42
 * @Description:
 */
import { MenuType, RoleTypeToNumber } from '@/constants/permission';
import dbUtils, { QueryParamType } from '@/db/mysql';
import { PermissionAPI } from '@/types/permission';
import { buildSQLWhereClause } from '@rixfe/rix-tools';
import { commonModel } from '..';

class RoleModel implements PermissionAPI.RoleModel {
  async getRoleIdByName(role_name: string, tnt_id: number) {
    const res = await dbUtils.query(
      'select id from role where role_name = ? and tnt_id = ?',
      [role_name, tnt_id]
    );

    return res[0]?.id || 0;
  }

  async addRole(options: PermissionAPI.AddRoleParams) {
    const { role_name, tnt_id, role_type = 3 } = options;
    const res = await dbUtils.query(
      'insert into role (role_name,tnt_id,type) values ?',
      [[[role_name, tnt_id, role_type]]]
    );
    return res.insertId;
  }

  async updateRole(options: PermissionAPI.UpdateRoleNameParams) {
    const { role_name, tnt_id, id } = options;
    const sql = `update role set role_name = ? where id = ? and tnt_id = ?`;
    return !!(await dbUtils.query(sql, [role_name, id, tnt_id]));
  }

  // 删除角色
  async deleteRole(id: number, tnt_id: number) {
    const user = await this.getAllUserByRole(id, tnt_id);
    if (Array.isArray(user) && user.length) return user;
    const rSql = `delete from role where id = ? and tnt_id = ?`;
    const rlSql = `delete from role_permission_rl where role_id = ? and tnt_id = ?`;
    const sqls = [
      {
        sql: rSql,
        values: [id, tnt_id]
      },
      {
        sql: rlSql,
        values: [id, tnt_id]
      }
    ];
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  async editRolePms(sqls: QueryParamType[]) {
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  async getAllRole(tnt_id: number) {
    const sql = `
    select 
      r.id,
      r.role_name,
      r.tnt_id,
      group_concat(coalesce(rpl.rsc_id, '')) as permissions 
    from role as r
    left join role_permission_rl as rpl on rpl.role_id = r.id
    where r.type != 1 
      and r.id not in (?) 
      and (
        (r.tnt_id = 0 and r.role_name != 'ToponUser') 
        or r.tnt_id = ?
      )
    group by r.id
    order by r.id asc`;
    return dbUtils.query(sql, [
      [
        RoleTypeToNumber['Rix Admin'],
        RoleTypeToNumber['Rix Data Analyst'],
        RoleTypeToNumber['Supply User'],
        RoleTypeToNumber['Demand User'],
        RoleTypeToNumber['Demand Partner'],
        RoleTypeToNumber['Supply Partner']
      ],
      tnt_id
    ]);
  }

  async isRoleExist(role_name: string, type: number, tnt_id: number) {
    const sql = `select count(*) as count from role where role_name = ? and type=? and tnt_id=?`;
    return dbUtils.query(sql, [role_name, type, tnt_id]);
  }

  async getAllUserByRole(role_id: number, tnt_id: number) {
    const sql = `select user_id from user_role_rl where role_id=? and tnt_id=?`;
    return await dbUtils.query(sql, [role_id, tnt_id]);
  }

  async getAllUserByRoleList(roles: string[]) {
    const sql = `select user_id from user_role_rl where role_id in (?)`;
    return await dbUtils.permissionQuery(sql, [roles]);
  }

  // 需要获取active的
  async getAllRolePermission(role_ids: number[], tnt_id: number) {
    const isDefaultRole = [
      RoleTypeToNumber.Administrator,
      RoleTypeToNumber['Demand User'],
      RoleTypeToNumber['Supply User'],
      RoleTypeToNumber['Rix Admin'],
      RoleTypeToNumber['Rix Data Analyst'],
      RoleTypeToNumber['Super Administrator']
    ].includes(role_ids[0]);

    const { clause, params } = buildSQLWhereClause([
      ['tnt_id = ?', isDefaultRole ? 0 : tnt_id],
      ['role_id in (?)', role_ids ? role_ids : null]
    ]);

    const sql = `select 
        rpl.rsc_id as rsc_id, 
        rpl.type  as type,
        rpl.role_id as role_id,
        mu.title as title,
        mu.status as status,
        mu.path as path,
        mu.icon as icon,
        mu.is_hide as is_hide,
        mu.menu_render as menu_render,
        mu.access as access,
        mu.type as type
      from (select rsc_id, role_id, type from role_permission_rl ${clause}) as rpl
      join menu as mu on mu.id=rpl.rsc_id;
    `;
    return await dbUtils.query(sql, params);
  }

  // 获取当前角色的权限
  async getRolePmsById(id: number) {
    const menu_data: any[] = await commonModel.getMenuByRole(id);
    const menus = [...new Set(menu_data.map(v => v.menu_id))];
    const menu_access = menu_data
      .filter(v => v.type === MenuType.Menu)
      .map(v => v.access)
      .filter(v => v && v.trim());
    const btn_access = menu_data
      .filter(v => v.type === MenuType.Operation)
      .map(v => v.access)
      .filter(v => v && v.trim());
    let api_list = [];
    if (menus.length) {
      // 获取菜单权限
      const apis: any[] = await commonModel.getInterfaceByMenu(menus);
      api_list = apis
        .filter(v => v.path && v.path.trim())
        .map(v => v.path.trim());
    }
    return {
      btn_access: [...new Set(btn_access)],
      menu_access: [...new Set(menu_access)],
      api_list: [...new Set(api_list)]
    };
  }
}

export default new RoleModel();
