/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:27:36
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-11 11:38:56
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import { IvtConfigBreadOptions, IvtConfigColumnOptions, IvtConfigSearchOption } from '@/constants/strategy/ivt';
import RixEngineFont from '@/components/RixEngineFont';
import { useModel } from '@umijs/max';
import AddIvtConfig from '../components/AddIvtConfig';
import OperateRender from '@/components/OperateRender';
import { StatusMap } from '@/constants';

const Page: React.FC = () => {
  const { dataSource, loading, reload } = useModel('useIvtConfigListList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<StrategyAPI.IvtConfigItem>();
  const [columns, setColumns] = useState(IvtConfigColumnOptions);
  const [searchOptions, setSearchOptions] = useState(IvtConfigSearchOption);

  const onOperateRender = (_: number, row: StrategyAPI.IvtConfigItem) => {
    return (
      <OperateRender
        params={row}
        btnOptions={[
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            accessCode: 'updateIvtAuth',
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            onClick: () => handleEdit(row)
          }
        ]}
      />
    );
  };

  useEffect(() => {
    reload();
    !demandList && reloadDemand();
    !supplyList && reloadSupply();
    const columns = IvtConfigColumnOptions.map(v => ({ ...v }));
    const index = columns.findIndex(v => v.dataIndex === 'operate');
    if (index !== -1) {
      columns[index].render = onOperateRender;
    }
    setColumns(columns);
  }, []);

  useEffect(() => {
    const options = IvtConfigSearchOption.map(v => ({ ...v }));

    const sIndex = options.findIndex(v => v.key === 'seller_id');
    const bIndex = options.findIndex(v => v.key === 'buyer_id');

    if (sIndex !== -1) {
      options[sIndex].options =
        supplyList?.map((v: SupplyAPI.SupplyListItem, index: number) => {
          return {
            label: `${v.seller_name}(${v.seller_id})`,
            value: v.seller_id,
            key: `${index}`
          };
        }) || [];
    }
    if (bIndex !== -1) {
      options[bIndex].options =
        demandList?.map((v: DemandAPI.DemandListItem, index: number) => {
          return {
            label: `${v.buyer_name}(${v.buyer_id})`,
            value: v.buyer_id,
            key: `${index}`
          };
        }) || [];
    }

    setSearchOptions(options);
  }, [dataSource, demandList, supplyList]);

  const handleEdit = (row: StrategyAPI.IvtConfigItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };

  return (
    <PageContainer options={IvtConfigBreadOptions}>
      <FrontTable<StrategyAPI.IvtConfigItem>
        pageTitle="IVT"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Create IVT Rule',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            accessCode: 'addIvtAuth',
            icon: <RixEngineFont type="add" />
          }
        ]}
        labelWidth={120}
        scroll={{ y: 'calc(100vh - 220px)' }}
        isFold
        initialValues={{ status: [StatusMap.Active] }}
        // handleSearchValueChange={handleSearchValueChange}
      />
      <AddIvtConfig
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        demandList={demandList || []}
        supplyList={supplyList || []}
      />
    </PageContainer>
  );
};

export default Page;
