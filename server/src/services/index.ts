/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-22 17:12:20
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-05 10:31:28
 * @FilePath: /saas.rix-platform/server-ts/src/services/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEPP
 */
import supply from './supply/supply';
import demand from './demand/demand';
import app from './developer/app-list';
import blAndWl from './strategy/bl-wl';
import cap from './strategy/cap';
import qps from './strategy/qps';
import floor from './strategy/floor';
import geoPolicy from './strategy/geo-policy';
import dashboard from './data-report/dashboard';
import advReport from './data-report/advertiser-report';
import pubReport from './data-report/publisher-report';
import profit from './strategy/profit';
import common from './common/common';
import role from './permission/role';
import menu from './permission/menu';

export const supplyService = supply;
export const demandService = demand;
export const appService = app;
export const blAndWlService = blAndWl;
export const capService = cap;
export const qpsService = qps;
export const dashboardService = dashboard;
export const profitService = profit;
export const floorService = floor;
export const geoPolicyService = geoPolicy;
export * from './strategy/creative';
export * from './strategy/ivt';
export * from './strategy/atc';
export * from './strategy/ab-test';
export const commonService = common;
export const advReportService = advReport;
export const pubReportService = pubReport;
export const roleService = role;
export const menuService = menu;

export * from './data-report/advertiser-billing-report';
export * from './data-report/publisher-billing-report';
export * from './data-report/pixalate-report';
export * from './data-report/human-report';
export * from './data-report/export-report';
export * from './data-report/abtest-report';

export * from './troubleshooting/sample-trace';

export * from './app-ads/app-info';
export * from './ai-board/main-board';

export * from './partner/partner';

export * from './transparency/stg-chain-v2';

export * from './backend-api/full-report';
export * from './backend-api/open-service';

export * from './common/user';

export * from './permission/user';

export * from './strategy/pmp';
