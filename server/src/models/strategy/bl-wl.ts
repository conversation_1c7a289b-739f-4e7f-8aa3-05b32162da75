/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:16:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-20 16:44:51
 * @Description:
 */

import { getTzTimeSql } from '@/constants/time-zone';
import dbUtil from '@/db/mysql';
import { BlAndWlAPI } from '@/types/bl-wl';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class BlAndWlModel implements BlAndWlAPI.BlAndWl {
  // eslint-disable-next-line max-len
  async isBlAndWlExists(params: BlAndWlAPI.IsExistParams): Promise<any[]> {
    const { seller_id, buyer_id, id, type, tnt_id } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id = ?', seller_id],
      ['buyer_id = ?', buyer_id],
      ['tnt_id = ?', tnt_id],
      ['type = ?', type],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select id from white_black_list ${clause} limit 1`;
    return await dbUtil.query(sql, values);
  }

  async getBlAndWlList(
    tnt_id: number,
    cur_time_zone: string
  ): Promise<BlAndWlAPI.BlAndWlListItem[]> {
    const sql = `
      select
        wbl.seller_id as seller_id,
        wbl.buyer_id as buyer_id,
        wbl.id as id,
        wbl.type as type,
        wbl.content as content,
        wbl.op_id as op_id,
        ${getTzTimeSql('wbl.update_time', cur_time_zone)} as update_time,
        s.seller_name as seller_name,
        b.buyer_name as buyer_name,
        a.account_name as account_name,
        a.status as account_status,
        a.type as account_type,
        wbl.status as status
      from (select * from white_black_list where tnt_id=?) as wbl 
      left join (select seller_name, seller_id,status from seller) as s on s.seller_id=wbl.seller_id and s.status!=3
      left join (select buyer_name, buyer_id,status from buyer) as b on b.buyer_id=wbl.buyer_id and b.status!=3
      left join user as a on a.user_id=wbl.op_id order by wbl.update_time desc;
    `;

    return await dbUtil.query(sql, [tnt_id]);
  }

  async addBlAndWl(params: BlAndWlAPI.AddBlAndWlParams): Promise<boolean> {
    const { seller_id, buyer_id, content, type, op_id, tnt_id } = params;
    const sql = `insert into white_black_list (seller_id, buyer_id, type, content, op_id, tnt_id) values ?`;
    return !!(await dbUtil.query(sql, [
      [[seller_id, buyer_id, type, content, op_id, tnt_id]]
    ]));
  }

  async updateBlAndWl(
    params: BlAndWlAPI.updateBlAndWlParams
  ): Promise<boolean> {
    const { id, content, op_id, status, tnt_id } = params;
    const sql = `update white_black_list set content = ?, op_id=?, status=? where id=? and tnt_id=?`;
    return !!(await dbUtil.query(sql, [content, op_id, status, id, tnt_id]));
  }
}

export default new BlAndWlModel();
