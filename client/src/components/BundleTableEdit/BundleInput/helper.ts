import { isValidBundle } from "@/utils";

// 合并和去重逻辑
export const mergeAndUnique = (list1: string[], list2: string[], reverse = false) =>
  reverse ? [...list2, ...list1] : [...list1, ...list2];

// 去重并过滤空值
export const uniqueAndFilterEmpty = (list: string[]) => [...new Set(list)].filter(v => v && v.trim());
// 检查重复项
export const checkDuplicates = (list1: string[], list2: string[]) => list1.filter(item => list2.includes(item));

// 分类有效和无效的bundle
export const classifyBundles = (
  bundles: string[],
  validator?: (val: string) => boolean
) => {
  const valid: string[] = [],
    invalid: string[] = [];
  bundles.forEach(bundle => {
    validator?.(bundle) || isValidBundle(bundle) ? valid.push(bundle) : invalid.push(bundle);
  });
  return { valid, invalid };
};
