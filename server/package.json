{"name": "server-ts", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=dev nodemon --config nodemon.json src/index.ts", "start": "node ./dist/index.js", "build": "rm -rf dist && tsc && npm run remove-logs", "remove-logs": "babel ./dist --out-dir ./dist --ignore node_modules,logs --copy-files", "prod": "npm run build && npm run start", "pm2": "pm2 reload processes.json --only saas.rix-platform --update-env", "pm2-test": "pm2 reload processes.json --only test.saas.rix-platform --update-env", "pm2-hotfix": "pm2 reload processes.json --only hotfix.saas.rix-platform --update-env", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@dsnp/parquetjs": "^1.7.0", "@google-cloud/bigquery": "^6.2.0", "@google-cloud/storage": "^7.7.0", "@rixfe/rix-tools": "^1.9.0", "aws-sdk": "^2.1259.0", "axios": "^1.5.0", "cls-hooked": "^4.2.2", "crypto-js": "^4.2.0", "csv-stringify": "^6.4.4", "event-stream": "^4.0.1", "ioredis": "^5.3.1", "joi": "^17.7.0", "JSONStream": "^1.3.5", "jsonwebtoken": "^9.0.2", "koa": "^2.13.4", "koa-body": "^6.0.1", "koa-compress": "^5.1.0", "koa-convert": "^2.0.0", "koa-router": "^12.0.0", "koa-session": "^6.2.0", "koa-static": "^5.0.0", "koa-static-cache": "^5.1.4", "koa2-connect-history-api-fallback": "^0.1.3", "log4js": "^6.7.0", "module-alias": "^2.2.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mysql": "^2.18.1", "node-sql-parser": "^4.18.0", "nodemailer": "^6.8.0", "query-string": "^9.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@types/cls-hooked": "^4.3.9", "@types/crypto-js": "^4.2.2", "@types/event-stream": "^4.0.1", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/koa": "^2.13.5", "@types/koa-compress": "^4.0.3", "@types/koa-convert": "^1.2.4", "@types/koa-log4": "^2.3.3", "@types/koa-router": "^7.4.4", "@types/koa-session": "^5.10.6", "@types/koa-static": "^4.0.2", "@types/koa-static-cache": "^5.1.1", "@types/module-alias": "^2.0.1", "@types/mysql": "^2.15.21", "@types/node": "^18.11.9", "@types/nodemailer": "^6.4.6", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "babel-plugin-transform-remove-console": "^6.9.4", "cross-env": "^7.0.3", "eslint": "^8.28.0", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.26.0", "jest": "^29.7.0", "nodemon": "^2.0.20", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.8.3"}}