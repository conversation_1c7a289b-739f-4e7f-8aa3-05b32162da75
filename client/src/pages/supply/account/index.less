.account-container {
  box-sizing: border-box;
  margin-right: 16px;
  height: 100%;
  background-color: #fff;
  border-radius: 6px;
  padding: 28px 32px;
  h3 {
    font-weight: 700;
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 0px;
  }
  :global {
    .ant-table-cell {
      .copy-container {
        border-radius: 6px;
        height: 32px;
        display: flex;
        align-items: center;
        width: 100%;
        > span.info {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        :global {
          .ant-typography {
            margin-bottom: 0px;
            height: 32px;
            line-height: 32px;
            border-radius: 0px 6px 6px 0px;
            font-size: 22px;
          }
        }
      }
    }
  }
}
.self-email {
  display: flex;
  justify-content: flex-end;
}
.page-container-account {
  :global {
    .ant-spin-nested-loading {
      flex: 1;
      height: 100%;
      .ant-spin-container {
        height: 100%;
      }
    }
  }
}
