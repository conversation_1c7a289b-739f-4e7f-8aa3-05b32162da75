/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-19 16:15:59
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:56:03
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getQpsList } from '@/services/api';

export default function useQpsList() {
  const { data, run, loading, error } = useCustomRequest(getQpsList, {
    cacheKey: 'qps-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
