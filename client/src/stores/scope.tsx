/**
 * scope 状态管理，采用 valtio 实现
 *
 * 1. 高阶函数包装原组件，传递 proxy 对象
 * 2. 封装 useSnapshot 消费 proxy
 * 3. 通过泛型提供完整的类型推断和类型安全
 *
 * @example 参考 ./example.tsx 的使用方式
 */

import { createContext, useContext } from 'react';
import { proxy, useSnapshot, type Snapshot } from 'valtio';

const ScopeContext = createContext<unknown>(null);

export function ScopeProvider<T extends object>({ children, state }: { children: React.ReactNode; state: T }) {
  return <ScopeContext.Provider value={state}>{children}</ScopeContext.Provider>;
}

/**
 * 获取 store 的 hook
 */
export function useScopeStore<T extends object>(): Snapshot<T> {
  const state = useContext(ScopeContext);
  return useSnapshot(state as T);
}

/**
 * 获取原始 state 的 hook（用于修改状态）
 */
export function useScopeState<T extends object>(): T {
  const state = useContext(ScopeContext);
  if (state === null) {
    throw new Error('useScopeStore must be used within a ScopeProvider');
  }
  return state as T;
}

// 强类型的高阶函数
export function withScopeState<T extends object, H extends object>(Component: React.ComponentType<T>, initialState: H) {
  // 创建 proxy 对象
  const state = proxy(initialState);

  const WrappedComponent = (props: T) => {
    return (
      <ScopeProvider state={state}>
        <Component {...props} />
      </ScopeProvider>
    );
  };

  WrappedComponent.displayName = `withScopeState(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

export function createScopeHooks<T extends object>() {
  const useScopeStoreTyped = (): Snapshot<T> => {
    const state = useContext(ScopeContext);
    return useSnapshot(state as T);
  };

  const useScopeStateTyped = (): T => {
    const state = useContext(ScopeContext);
    if (state === null) {
      throw new Error('useScopeStore must be used within a ScopeProvider');
    }
    return state as T;
  };

  return {
    useScopeState: useScopeStateTyped,
    useScopeStore: useScopeStoreTyped
  };
}

// 类型工具
export type ScopeState<T> = Snapshot<T>;
export type ScopeStore<T> = T;
