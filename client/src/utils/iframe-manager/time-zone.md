# 时区管理工具使用指南

## 概述

`TimeZoneManager` 是一个用于管理应用时区设置的独立工具类，提供了完整的时区设置、存储、验证和页面刷新功能。

## 主要功能

1. **设置时区** - 支持设置任意有效时区
2. **本地存储** - 自动保存时区设置到 localStorage
3. **全局生效** - 设置 moment.js 默认时区和全局变量
4. **页面刷新** - 可选择是否在设置后刷新页面
5. **错误处理** - 完整的错误处理和验证机制

## 基本使用

### 导入工具类

```typescript
import { TimeZoneManager, setTimeZoneAndReload, setTimeZoneOnly } from '@/utils/iframe-manager/time-zone';
```

### 设置时区并刷新页面

```typescript
// 基本使用
const result = TimeZoneManager.setTimeZone('Asia/Shanghai', {
  userId: 'user123',
  autoReload: true,
  debug: true
});

if (result.success) {
  console.log('时区设置成功:', result.timeZone);
} else {
  console.error('设置失败:', result.error);
}
```

### 仅设置时区（不刷新页面）

```typescript
const result = TimeZoneManager.setTimeZone('America/New_York', {
  userId: 'user123',
  autoReload: false,
  debug: true
});
```

### 使用便捷函数

```typescript
// 设置时区并刷新页面
const result1 = setTimeZoneAndReload('Europe/London', {
  userId: 'user123'
});

// 仅设置时区
const result2 = setTimeZoneOnly('Asia/Tokyo', {
  userId: 'user123'
});
```

## 高级功能

### 获取当前时区

```typescript
const currentTimeZone = TimeZoneManager.getCurrentTimeZone('user123');
console.log('当前时区:', currentTimeZone);
```

### 初始化时区设置

```typescript
// 在应用启动时调用
const result = TimeZoneManager.initializeTimeZone('user123', true);
if (result.success) {
  console.log('时区初始化完成:', result.timeZone);
}
```

### 验证时区有效性

```typescript
const isValid = TimeZoneManager.isValidTimeZone('Asia/Shanghai');
console.log('时区是否有效:', isValid);
```

### 获取所有可用时区

```typescript
const timeZones = TimeZoneManager.getAvailableTimeZones();
console.log('可用时区数量:', timeZones.length);
```

### 手动刷新页面

```typescript
const reloadResult = TimeZoneManager.reloadPage(false);
if (reloadResult.success) {
  console.log('页面刷新成功');
} else {
  console.error('页面刷新失败:', reloadResult.error);
}
```

## 在 React 组件中使用

### 在组件中集成

```typescript
import React, { useEffect } from 'react';
import { TimeZoneManager } from '@/utils/iframe-manager/time-zone';

const MyComponent: React.FC = () => {
  useEffect(() => {
    // 组件挂载时初始化时区
    const result = TimeZoneManager.initializeTimeZone('user123');
    if (!result.success) {
      console.error('时区初始化失败:', result.error);
    }
  }, []);

  const handleTimeZoneChange = (newTimeZone: string) => {
    const result = TimeZoneManager.setTimeZone(newTimeZone, {
      userId: 'user123',
      autoReload: true
    });

    if (result.success) {
      console.log('时区已更新');
    } else {
      console.error('时区更新失败:', result.error);
    }
  };

  return (
    <div>
      <button onClick={() => handleTimeZoneChange('Asia/Shanghai')}>
        设置为上海时区
      </button>
    </div>
  );
};
```

## 配置选项

### TimeZoneOptions 接口

```typescript
interface TimeZoneOptions {
  /** 是否在设置后自动刷新页面 */
  autoReload?: boolean;
  /** 是否保留页面状态 */
  preserveState?: boolean;
  /** 用户ID，用于localStorage存储键名 */
  userId?: string;
  /** 是否启用调试模式 */
  debug?: boolean;
}
```

### TimeZoneResult 接口

```typescript
interface TimeZoneResult {
  /** 设置是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
  /** 设置的时区 */
  timeZone: string;
  /** 是否已刷新页面 */
  reloaded?: boolean;
}
```

## 注意事项

1. **时区验证**: 工具类会自动验证时区的有效性
2. **错误处理**: 所有操作都有完整的错误处理机制
3. **调试模式**: 启用 debug 模式可以查看详细的操作日志
4. **存储机制**: 时区设置会保存到 localStorage，键名格式为 `time-{userId}`
5. **全局变量**: 设置时区后会自动更新 `window.cur_time_zone` 变量
6. **Moment.js 集成**: 会自动设置 moment.js 的默认时区

## 迁移指南

### 从原有代码迁移

**原有代码:**
```typescript
localStorage.setItem(`time-${user_id}`, timeZone);
window.cur_time_zone = timeZone;
moment.tz.setDefault(timeZone);
window.location.reload();
```

**新代码:**
```typescript
const result = TimeZoneManager.setTimeZone(timeZone, {
  userId: user_id,
  autoReload: true
});
```

## 错误处理

工具类提供了完整的错误处理机制：

```typescript
const result = TimeZoneManager.setTimeZone('Invalid/Timezone');
if (!result.success) {
  console.error('错误类型:', result.error);
  // 可能的错误:
  // - "无效的时区: Invalid/Timezone"
  // - "window对象不可用"
  // - "刷新页面失败"
}
``` 
