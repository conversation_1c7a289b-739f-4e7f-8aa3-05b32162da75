# VastTag API 

# Http Request Requirement

* Request Method: **GET**
* Endpoint: Contact with the Operating Person.



# Request Parameters

|**Parameter** |**Type** |**Required** |**Description** |**Example** |
|---|---|---|---|---|
|sid |int |Yes |Supply ID | |
|token |string |Yes |Supply Token | |
|app_id |string |Yes |APP ID. | |
|adslot_id |int |Yes |Adslot id(Placement Id). | |
|app.bundle |string |Yes |A platform-specific application identifier intended to be unique to the app |com.bestai.scannerlite (for Android) 382201985 (for iOS)12 (Roku) |
|app.name |string |Yes |App Name, From the iTunes or Play store. |Text Scanner OCR |
|url |string |Yes |App Store URL. Please make sure URL encode it once. |https%3a%2f%2fplay.google.com%2fstore%2fapps%2fdetails%3fid%3dcom.bestai.scannerlite |
|ifa |string |Yes |Advertising ID. Apple IDFA or Google Advertising ID. |a098ce09-76f8-4aec-9775-4e4fd293e441 |
|vwd |int |Yes |Video ad width. |720 |
|vht |int |Yes |Video ad height. |1280 |
|dev.type |int |Yes |Device type categories. Refer to list <a href="#Device Type">Device Type</a> |1 |
|dev.os |int |Yes |Device operating system. Refer to list <a href="#OS">OS</a> |2 |
|dev.osv |string |No |Device operating system version. |10.0.0 |
|dev.brand |string |No |The brand of device. |samsung |
|dev.model |string |No |The model of device. |SM-A310F |
|ip |string |No |Ip address of user client. It is mandatory when request is sent from backend server.  To adapt IPV6 ip address, Please make sure URL encodes it once. |************** |
|ua |string |No |User Agent.Please make sure URL encodes it once. |Mozilla%2f5.0+(Linux%3b+Android****%3b+SM-A310F+Build%2fNRD90M%3b+wv)+AppleWebKit%2f537.36+(KHTML%2c+like+Gecko)+Version%2f4.0+Chrome%2f80.0.3987.132+Mobile+Safari%2f537.36 |
|cat |string |No |String of IAB content categories that describe the current page or view of the app. Refer to list <a href="#IAB Category Type">IAB Category Type</a> |IAB19 |
|gdpr |int |No |Flag indicating if this request is subject to the GDPR regulations established by the EU, where 0 = No, 1 = yes. |0 |
|gdpr_consent |string |No |If gdpr = 1, this string gives consent information of various vendors. | |
|coppa |int |No |Flag indicating if this request is subject to the COPPA regulations established by the USA FTC, where 0 = No, 1 = yes. |0 |
|us_privacy |string |No |Applicable to requests subject to CCPA regulations. Must follow the U.S. Privacy string format per IAB spec. This attribute is always passed when applicable to affected regions. | |
|dnt |int |No |Standard "Do Not Track" flag as set in the header by the browser, where 0 = tracking is unrestricted, 1 = do not track. |0 |
|cb |string |Yes |Cache buster. A random string to avoid browser caching |24687 |
|protocols |string |No |Supported video protocols, multiple value are separated by commas. Refer to list <a href="#Protocols">Protocols</a> |2,3,5,6 |
|minduration |int |No |Minimum video ad duration in seconds. |15 |
|maxduration |int |No |Maximum video ad duration in seconds |30 |
|bidfloor |float |No |Minimum bid for this impression expressed in CPM. If this parameter is not passed, the eCPM set in the SSP platform will be used by default. If this parameter is passed, the eCPM will be MAX(bidfloor, SSP_bidFloor). |0.5 |
# Request Sample

```HTTP
https://endpoint/rtb?sid={SID}&token={TOKEN}&app_id={APP_ID}&adslot_id={AD_SLOT_ID}&app.bundle={APP_BUNDLE}&app.name={APP_NAME}&ifa={IFA}&vwd={WIDTH}&vht={HEIGHT}&dev.type={DEVICE_TYPE}&dev.os={OS}&ua={USER_AGENT}&dev.osv={DEVICE_OS_VERSION}&dev.brand={DEVICE_BRAND}&dev.model={DEVICE_MODEL}&ip={IP}&cat={CATEGORY}&dnt=0&cb={CACHE_BUSTER}&url={APP_STORE_URL}
```

Sample request URL with filled parameters

```HTTP
https://endpoint/rtb?sid=60037&token=2c9bd02e2b4712cf6a90f922b7856db6gG&app_id=1a360ad418f40cd019fa12a31e334669R6&adslot_id=171066&app.bundle=com.bestai.scannerlite&app.name=Text+Scanner+OCR&ifa=a098ce09-76f8-4aec-9775-4e4fd293e441&vwd=720&vht=1280&dev.type=1&dev.os=2&ua=Mozilla%2f5.0+(Linux%3b+Android****%3b+SM-A310F+Build%2fNRD90M%3b+wv)+AppleWebKit%2f537.36+(KHTML%2c+like+Gecko)+Version%2f4.0+Chrome%2f80.0.3987.132+Mobile+Safari%2f537.36&dev.osv=10.0&dev.brand=samsung&dev.model=SM-A310F&ip=**************&cat=IAB19&dnt=0&cb=24687&url=https%3a%2f%2fplay.google.com%2fstore%2fapps%2fdetails%3fid%3dcom.bestai.scannerlite
```

# VAST response

## Response Content

### HTTP Body Specification

The content type of response body is **application/xml** and the content is base on **VAST** Specification.

### HTTP Code

|Response Http Code |Description |
|---|---|
|204 |No Bid |
|400 |Bad Request, please check the parameters you passed |
## Vast Content specifications

The bid response adheres to the following specifications:

* Minimum bitrate of 250 kbps
* mp4 media file
* Supported VAST protocols: VAST 2.0、VAST 3.0、VAST 2.0 Wrapper、VAST 3.0 Wrapper



# Appendix

## Device Type

The following table lists the type of device from which the impression originated. OpenRTB version 2.2 of the specification added distinct values for Mobile and Tablet. It is recommended that any bidder adding support for 2.2 treat a value of 1 as an acceptable alias of 4 & 5. This OpenRTB table has values derived from the Inventory Quality Guidelines (IQG). Practitioners should keep in sync with updates to the IQG values.

|Value |Description |Notes |
|---|---|---|
|1 |Mobile/Tablet |Version 2.0 |
|2 |Personal Computer |Version 2.0 |
|3 |Connected TV |Version 2.0 |
|4 |Phone |New for Version 2.2 |
|5 |Tablet |New for Version 2.2 |
|6 |Connected Device |New for Version 2.2 |
|7 |Set Top Box |New for Version 2.2 |
## OS

|value |Description |
|---|---|
|0 |Unknown |
|1 |iOS |
|2 |Android |
|3 |Other(DEPRECATED) |
|4 |Linux |
|5 |MacOS |
|6 |Windows |
|11 |tvOS |
|12 |Roku |
|13 |Amazon |
|14 |Microsoft |
|15 |Samsung Smart TV |
|16 |LG Smart TV |
|17 |Sony Playstation |
|18 |Vizio |
|19 |Philips Smart TV |
|50 |Tizen |
|51 |KaiOS |
## Protocols

The following table lists the options for the various bid response protocols that could be supported by an exchange.

|Value |Description |
|---|---|
|1 |VAST 1.0 |
|2 |VAST 2.0 |
|3 |VAST 3.0 |
|4 |VAST 1.0 Wrapper |
|5 |VAST 2.0 Wrapper |
|6 |VAST 3.0 Wrapper |
|7 |VAST 4.0 |
|8 |VAST 4.0 Wrapper |
|9 |DAAST 1.0 |
|10 |DAAST 1.0 Wrapper |
## IAB Category Type

|value |Description |
|---|---|
|IAB1 |Arts & Entertainment |
|IAB2 |Automotive |
|IAB3 |Business |
|IAB4 |Careers |
|IAB5 |Education |
|IAB6 |Family & Parenting |
|IAB7 |Health & Fitness |
|IAB8 |Food & Drink |
|IAB9 |Hobbies & Interests |
|IAB10 |Home & Garden |
|IAB11 |Law, Gov’t & Politics |
|IAB12 |News |
|IAB13 |Personal Finance |
|IAB14 |Society |
|IAB15 |Science |
|IAB16 |Pets |
|IAB17 |Sports |
|IAB18 |Style & Fashion |
|IAB19 |Technology & Computing |
|IAB20 |Travel |
|IAB21 |Real Estate |
|IAB22 |Shopping |
|IAB23 |Religion & Spirituality |
|IAB24 |Uncategorized |
|IAB25 |Non-Standard Content |
|IAB26 |Illegal Content |

