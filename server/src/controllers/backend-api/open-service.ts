import { RESULT } from '@/constants';
import {
  BeOpenServiceGetSupplyScheme,
  BeOpenServiceScheme
} from '@/schema/backend-api/open-service';
import { openService } from '@/services/backend-api/open-service';
import { getConfig, md5 } from '@/utils';
import { generateToken } from '@/utils/jwt';
import { getCtxResult } from '@/utils/response';
import { validateBeApiBody } from '@/utils/validate-params';
import { Context } from 'koa';

const { encryptionStr } = getConfig();

class OpenServiceCtrl {
  @validateBeApiBody(BeOpenServiceScheme, false)
  async login(ctx: Context) {
    // 执行登录操作，获取 token，url，拼接并返回
    let result = { ...RESULT };
    const formData = ctx.request.body;

    const [user] = await openService.getOneUser({
      ...formData,
      password: md5(encryptionStr + formData.password)
    });

    if (user) {
      // 确保必要字段存在
      if (!user.user_id || !user.tnt_id) {
        result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
        ctx.body = result;
        return;
      }

      // 返回 token
      const token = generateToken({
        account_name: formData.account_name,
        password: formData.password,
        cs_domain: formData.cs_domain
      });
      result = getCtxResult('SUCCESS', { token });
    } else {
      result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
    }

    ctx.body = result;
  }

  @validateBeApiBody(BeOpenServiceGetSupplyScheme, false)
  async getSupply(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;

    const [user] = await openService.getOneUser({
      ...formData,
      password: md5(encryptionStr + formData.password)
    });

    if (user) {
      // 确保必要字段存在
      if (!user.user_id || !user.tnt_id) {
        result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
        ctx.body = result;
        return;
      }

      // 下游白盒接口提供（提供的数据已经确定，只给默认的下游账号数据：publisher name, publisher id, endpoint）
      const supply = await openService.getWhiteBoxSupply(formData.cs_domain);
      result = getCtxResult('SUCCESS', supply || {});
    } else {
      result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
    }

    ctx.body = result;
  }
}

export const openServiceCtrl = new OpenServiceCtrl();
