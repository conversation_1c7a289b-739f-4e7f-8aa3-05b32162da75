/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-09 16:32:20
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 16:51:12
 * @Description:
 */
import { Outlet, Navigate } from '@umijs/max';
import { Spin } from 'antd';
import React from 'react';

// 处理菜单组装使用
type MenuItem = {
  id: string;
  name?: string;
  layout?: boolean;
  path: string;
  isLayout?: boolean;
  access?: string;
  icon?: string;
  parentId?: string; // default ant-design-pro-layout(使用自带layout的会用上这个)
  element: JSX.Element;
  hideInMenu?: boolean; // 是否不在左侧菜单显示
  menuRender?: boolean; // 是否渲染左侧菜单
  target?: string; // 以后得扩展字段
  children?: MenuItem[];
  routes?: MenuItem[];
}

// 三种菜单类型:节点,link,跟节点+link
export const NodeType = {
  'Node': 1,
  'Link': 2,
  'Node & Link': 3
};

// 类型为link的路由没有子路由，需要去除
const filterLinkRoute = (routesRaw: PermissionAPI.MenuItem[]) => {
  const links = routesRaw.filter(v => v.node_type === NodeType.Link).map(v => v.id);
  const data = routesRaw.filter(v => !links.includes(v.pid));
  return data;
};

// 查找节点类型为node的第一个子节点
const findFirstChild = (dataSource: PermissionAPI.MenuItem[], pid: number) => {
  if (dataSource.length) {
    const arr = dataSource.filter(v => v.pid === pid).sort((a, b) => a.sort - b.sort);
    if (arr.length) {
      return arr[0].path;
    }
  }
  return undefined;
};

const getElement = (item: PermissionAPI.MenuItem, restArr: PermissionAPI.MenuItem[], isChild?: boolean) => {
  let element: any = <Outlet />;
  if (item.component && (isChild || item.node_type === NodeType.Link)) {
    const Component = React.lazy(() => import(`@/${item.component}`));
    element = <React.Suspense fallback={<Spin spinning={true}>
      <div style={{ width: '100%', height: 'calc(100vh - 100px)' }} />
    </Spin>}>
      <Component />
    </React.Suspense>;
  }
  // node 节点的子节点新增重定向
  if (item.node_type === NodeType.Node && isChild) {
    const to = findFirstChild(restArr, item.id);
    element = to ? <Navigate to={to} replace/> : <Outlet />;
  }
  return element;
};

//  对于是节点是node的需要新增跳转路由； 对于是node & link 需要新增 子节点

const getRouteItem = (item: PermissionAPI.MenuItem, restArr: PermissionAPI.MenuItem[]) => {
  const element = getElement(item, restArr);
  const obj: MenuItem = {
    name: item.title,
    id: `back_${item.id}`, // 区分于默认的路由
    path: item.path,
    access: item.access || '',
    icon: item.icon || '',
    parentId: !item.pid ? 'ant-design-pro-layout' : `back_${item.pid}`,
    element,
    hideInMenu: item.is_hide === 1, // 是否不在左侧菜单显示
    menuRender: item.menu_render === 1, // 是否渲染左侧菜单
    children: [],
    routes: []
  };
  if (item.pid) {
    delete obj.isLayout;
    delete obj.layout;
  }
  if (!obj.access) {
    delete obj.access;
  }
  obj.menuRender && delete obj.menuRender;
  (!obj.hideInMenu) && delete obj.hideInMenu;
  // 对于是节点是node的需要新增跳转路由
  if (item.node_type === NodeType.Node) {
    const ele = getElement(item, restArr, true);
    const cObj: any = {
      id: `${item.id}_node_child`, // node 节点 redirect的
      path: item.path,
      access: item.access || '',
      parentId: `back_${item.id}`,
      element: ele
    };
    if (!cObj.access) {
      delete cObj.access;
    }
    obj.children?.push(cObj);
  }
  if (item.node_type === NodeType['Node & Link']) {
    const ele = getElement(item, restArr, true);
    const cObj: any = {
      id: `${item.id}_node_link_child`, // node&link 节点
      path: item.path,
      access: item.access || '',
      parentId: `back_${item.id}`,
      element: ele
    };
    if (!cObj.access) {
      delete cObj.access;
    }
    obj.children?.push(cObj);
  }
  return obj;
};

//  第二个参数表示是前台菜单还是后台菜单 pid是父菜单的pid  重定向的 需要自己加 重写 len是菜单总长度
function loopMenu(list: PermissionAPI.MenuItem[], pid: number): MenuItem[] {
  const filterArr = list.filter(item => item.pid === pid).sort((a, b) => a.sort - b.sort);
  const restArr = list.filter(item => item.pid !== pid);
  // 前台的使用自定义layout
  return filterArr.map(item => {
    // 需要加上重定向
    const obj = getRouteItem(item, restArr);
    // 遍历子child
    if (restArr.length) {
      const tmp = loopMenu(restArr, item.id);
      const children = obj.children ? [...obj.children, ...tmp] : tmp;
      obj.children = children;
      obj.routes = children;
    }
    return obj;
  });
}

// 组装路由
export function generateRoute(list: PermissionAPI.MenuItem[]) {
  // 获取正确的路由， 过滤link路由的子节点
  const dataSource = filterLinkRoute(list);
  const routes = loopMenu(dataSource, 0);
  return routes;
}
