/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-21 15:47:02
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-10 11:10:24
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { useAccess, useModel, history } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import { TopBarSearchItem } from '@/components/TopBar';
import {
  TaskBreadOptions,
  TaskDemandSearchOption,
  TaskColumnKeysMap
} from '@/constants/troubleshooting/sampletrace-task';
import { TaskAllColumns } from '@/constants/troubleshooting';
import { SampleTabOptions, SampleTab } from '@/constants/troubleshooting';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import AddTraceTaskDrawer from '../components/AddTraceTaskDrawer';
import { Switch, Tooltip } from 'antd';
import { StatusMap } from '@/constants';
import { updateSampleTraceTask } from '@/services/troubleshooting';
import { fetchData } from '@/utils';
import moment from 'moment';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { judgeIsRixSystemUser } from '@/utils/permission';

const Page: React.FC = () => {
  const access = useAccess();
  const { dataSource: tasklist, reload, loading } = useModel('useSampleTraceTaskList');
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandListWithTesting');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const [loadingStatusId, setLoadingStatusId] = useState(0);
  const [searchOptions, setSearchOptions] = useState<TopBarSearchItem[]>([]);
  const [currentTab, setCurrentTab] = useState(SampleTab.SupplyRequest);
  const [visible, setVisible] = useState(false);
  const [dataSource, setDataSource] = useState<TroubleShootingAPI.TaskListItem[]>([]);
  const [columns, setColumns] = useState<ColumnProps<TroubleShootingAPI.TaskListItem>[]>([]);

  const handleCreateTask = () => {
    setVisible(true);
  };

  // 初始化
  // 1. 获取tasklist
  // 2. 获取demandList（搜索选项）
  // 3. 获取supplyList（搜索选项）
  useEffect(() => {
    reload();
    if (!demandList) {
      reloadDemand();
    }
    if (!supplyList) {
      reloadSupply();
    }

    handleSearchOptions();
  }, []);

  // 当tasklist/demandList/supplyList/currentTab变化时
  // 1. 更新dataSource
  // 2. 设置columns
  useEffect(() => {
    if (!tasklist || !currentTab) return;
    // TODO 这种过滤逻辑应该放到 后端 sql 处理
    const tmpList = tasklist.filter(item => item.type === currentTab);

    setDataSource(tmpList);
    handleSearchOptions();
    const columns = TaskAllColumns.filter(item => TaskColumnKeysMap[currentTab].includes(item.dataIndex as string));
    columns.forEach(item => {
      if (item.dataIndex === 'tag_id') {
        item.render = (_, row) => (
          <HoverToolTip title={_}>
            <a
              onClick={e => {
                e.preventDefault();
                history.push(`/troubleshooting/sample-trace/trace?tag_id=${row.tag_id}&type=${row.type}`);
              }}
            >
              {_}
            </a>
          </HoverToolTip>
        );
      }
      if (item.dataIndex === 'status') {
        item.render = (_, row) => {
          const isExpired = moment().isAfter(moment(row.create_time).add(1, 'days'));
          const disabled =
            // 内部用户 不能操作 外部用户的任务
            (access.isRixSystemUser && !judgeIsRixSystemUser(row.account_type)) ||
            access.DisabledButton('updateSampleTraceTaskAuth') ||
            isExpired;

          return (
            <Switch
              checked={_ === StatusMap.Active && !isExpired}
              onChange={e => !isExpired && handleSwitch(e, row)}
              loading={loadingStatusId === row.id}
              checkedChildren="Active"
              unCheckedChildren="Paused"
              disabled={disabled}
            />
          );
        };
      }
      if (item.dataIndex === 'create_time') {
        item.render = (_, row) => {
          const isExpired = moment().isAfter(moment(_).add(1, 'days'));
          return (
            <>
              <span>{_}</span>
              {isExpired && (
                <Tooltip title="Expired">
                  <ExclamationCircleOutlined
                    style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }}
                  />
                </Tooltip>
              )}
            </>
          );
        };
      }
    });
    setColumns(columns);
  }, [currentTab, tasklist, loadingStatusId]);
  // 处理状态按钮 状态切换
  const handleSwitch = (e: boolean, row: TroubleShootingAPI.TaskListItem) => {
    setLoadingStatusId(row.id);
    const params = {
      id: row?.id,
      status: e ? StatusMap.Active : StatusMap.Paused,
      ori_data: { status: row?.status, id: row?.id }
    };

    fetchData({
      request: updateSampleTraceTask,
      params,
      onSuccess: () => {
        setLoadingStatusId(0);
        reload();
      },
      onError: () => {
        setLoadingStatusId(0);
      }
    });
  };

  // 设置搜索项和搜索项的选项
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(TaskDemandSearchOption));
    if (Array.isArray(supplyList)) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });
      const sIndex = options.findIndex(item => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    if ([SampleTab.DemandResponse, SampleTab.DemandRequest].includes(currentTab)) {
      if (Array.isArray(demandList)) {
        const dOptions = demandList.map((item: DemandAPI.DemandListItem) => {
          return {
            label: `${item.buyer_name}(${item.buyer_id})`,
            value: item.buyer_id
          };
        });
        const dIndex = options.findIndex(item => item.key === 'buyer_id');
        if (dIndex !== -1) {
          options[dIndex].options = dOptions;
        }
      }

      setSearchOptions(options);
    } else {
      const supplyOptions = options.filter(item => item.key !== 'buyer_id');
      setSearchOptions(supplyOptions);
    }
  };

  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as number);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const normalEmptyRender = () => <span> </span>;
  return (
    <PageContainer flexDirection="column" options={TaskBreadOptions}>
      <FrontTable<TroubleShootingAPI.TaskListItem>
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        labelWidth={120}
        tabOptions={SampleTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange}
        btnOptions={[
          {
            label: 'Create Sample Trace Task',
            type: 'primary',
            size: 'small',
            onClick: () => handleCreateTask(),
            icon: <PlusOutlined />,
            accessCode: 'createSampleTraceTaskAuth',
            tooltip: 'Only show data for the past one month'
          }
        ]}
        scroll={{ y: 'calc(100vh - 220px)' }} // 非动态， 需要自己指定
        emptyRender={tasklist && tasklist.length ? normalEmptyRender : undefined}
        isFold
      />

      <AddTraceTaskDrawer
        type={currentTab}
        visible={visible}
        handleClose={handleClose}
        reload={reload}
        demandList={demandList || []}
        supplyList={supplyList || []}
      />
    </PageContainer>
  );
};

export default Page;
