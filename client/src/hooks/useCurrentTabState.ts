import useUrlState, { type Options as UseUrlStateOptionsType } from '@ahooksjs/use-url-state';
import { useCallback } from 'react';

export type TabOption<T extends string> = {
  label: string;
  value: T;
};

const UrlStateOptions: UseUrlStateOptionsType = {
  navigateMode: 'replace'
};

/**
 * 自定义 hook 管理 tab 的 url 状态
 * @param currentTab 当前 tab
 * @param tabOptions tab 选项
 * @returns [当前 tab, 设置当前 tab]
 */
export const useCurrentTabState = <T extends string>(
  currentTab: T,
  tabOptions: TabOption<T>[]
): [T, (tab: T) => void] => {
  const [urlState, setUrlState] = useUrlState({ tab: currentTab }, UrlStateOptions);

  // 校验是否是否合法，不合法返回默认 key
  const tab: T = tabOptions.map(option => option.value).includes(urlState.tab) ? urlState.tab : tabOptions[0].value;

  const setCurrentTab = useCallback(
    (tab: T) => {
      setUrlState({ tab });
    },
    [setUrlState]
  );

  return [tab, setCurrentTab];
};
