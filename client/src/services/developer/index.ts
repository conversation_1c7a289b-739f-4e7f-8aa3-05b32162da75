/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:42:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-08 11:57:50
 * @Description:
 */
import { request } from '@umijs/max';

export async function addPlacement(body: AppAPI.AddPlacementParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/addPlacement', {
    data: body || {},
    ...(options || {})
  });
}
export async function addApp(body: AppAPI.AddAppParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/addApp', {
    data: body || {},
    ...(options || {})
  });
}
export async function updateApp(body: AppAPI.UpdateAppParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/updateApp', {
    data: body || {},
    ...(options || {})
  });
}
export async function updatePlacement(body: AppAPI.UpdatePlacementParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/updatePlacement', {
    data: body || {},
    ...(options || {})
  });
}

export async function getAppList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/getAppList', {
    data: body || {},
    ...(options || {})
  });
}

export async function getAllPlacementList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/getAllPlacementList', {
    data: body || {},
    ...(options || {})
  });
}
