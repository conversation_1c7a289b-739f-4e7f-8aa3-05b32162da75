.status-select-container {
  width: 100%;
  :global {
    .ant-select-selector {
      border-radius: 6px !important;
    }
  }
}

.status-select-multiple-container {
  :global {
    .ant-select-selection-item {
      padding-right: 20px !important;
    }
  }
}

.tooltip-container {
  max-width: 400px;

  .placeholder-container {
    display: flex;
    flex-direction: column;

    span {
      line-height: 22px;
    }
  }
}

.status-select-dropdown {
  :global {
    .ant-select-item-option-content {
      display: flex;
      align-items: center;
    }
  }
}

.option-item {
  display: flex;
  align-items: center;
  width: 100%;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-active {
  background-color: #52c41a; // 绿色，表示启用状态
}

.status-inactive {
  background-color: #fa5a42; // 红色，表示禁用状态
}
