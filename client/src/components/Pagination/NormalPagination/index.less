.pagination-container {
  height: 40px;
  display: flex;
  align-items: center;
  li {
    height: 32px !important;
    line-height: 32px !important;
  }
  :global {
    .ant-pagination-options > div .ant-select-selector{
      height: 32px !important;
      border-radius: 6px;
      display: flex;
      align-items: center;
      color: var(--text-color);
    }
    .ant-pagination-item {
      a{
        color: #5E6466;
        font-weight: 700;
        &:hover {
          background: #F3F5F5;
          border-radius: 6px;
        }
      }
    }
    .ant-pagination-item-active {
      border-radius: 6px;
      background: var(--primary-3);
      border: none;
      a {
        color: var(--primary-color);
      }
    }
    .ant-pagination-total-text {
      color: var(--text-color);
    }
    .ant-pagination-prev, .ant-pagination-next, .ant-pagination-item{
      min-width: 36px !important;
      margin-right: 6px !important;
    }
    .ant-pagination-next, .ant-pagination-prev {
      .ant-pagination-item-link {
        color: #5E6466;
        &:hover {
          background: #F3F5F5;
          border-radius: 6px;
          color: #5E6466;
        }
      }
    }
    .ant-pagination-disabled .ant-pagination-item-link, .ant-pagination-disabled:hover .ant-pagination-item-link {
      color: #D7DADB;
      &:hover {
        background: none;
      }
    }
  }
}
