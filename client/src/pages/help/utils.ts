/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-14 16:03:55
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-14 16:03:57
 * @Description:
 */

export interface LinkItem {
  anchor: string;
  level: number;
  text: string;
  children?: LinkItem[];
}

export function getLinkItem(text: string, level: number, anchors: string[], linkItems: LinkItem[]) {
  const count = anchors.filter(anchor => anchor === text).length;
  const anchor = count ? `${text}${count}` : text;
  anchors.push(anchor);
  const item = { anchor, level, text };
  const items = linkItems;

  if (items.length === 0) {
    // 第一个 item 直接 push
    items.push(item);
  } else {
    let lastItem = items[items.length - 1]; // 最后一个 item
    if (item.level > lastItem.level) {
      // item 是 lastItem 的 children
      for (let i = lastItem.level + 1; i <= 6; i++) {
        const { children } = lastItem;
        if (!children) {
          // 如果 children 不存在
          lastItem.children = [item];
          break;
        }
        lastItem = children[children.length - 1]; // 重置 lastItem 为 children 的最后一个 item
        if (item.level <= lastItem.level) {
          // item level 小于或等于 lastItem level 都视为与 children 同级
          children.push(item);
          break;
        }
      }
    } else {
      // 置于最顶级
      items.push(item);
    }
  }
  return anchor;
}
