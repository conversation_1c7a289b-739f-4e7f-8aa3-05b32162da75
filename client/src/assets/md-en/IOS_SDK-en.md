# 「RixEngine」🌟 iOS SDK Integration Document 🌟 

## **Step 1: Before Integration**

Before integrating the SDK, please make sure that your account has been created in RixEngine SaaS console, and your apps and placements have been configured. 

Then you need to import the SDK in your Xcode project. 


## **Step 2: Getting Started**

1. Xcode version 14 or above.
1. Target iOS 9.0 or above.

Through the SDK download page, you can obtain the compressed package of library files and integration guide that the integration requires. Please follow the steps below to integrate:

1. Import the decompressed Framework files and resource files to your Xcode project.
1. Add dependencies to your Xcode project target according to the popup window.

![](https://static.rixengine.com/a/platform/help/sdk/ios-1.png)

 

* Please make sure that you decompress our IOS SDK zip file under ****Mac OS****, Otherwise, the framework file may not be usable. 




### 2.1 SDK Initialization

To initiate the SDK, you need to call the following initialization code in **AppDelegate** to initiate prebidSDK.

```Swift
    Prebid.initializeSDK(host: AdConfig.YOUR_HOST, sid: AdConfig.YOUR_SID, token: AdConfig.YOUR_TOKEN, appid: AdConfig.YOUR_APP_ID) { status, error in
    if let error = error {
        print("Initialization Error: \(error.localizedDescription)")
                return
            
        }
    }
```



### 2.2 **Banner Ads**

Banner ads occupy a space within an app's UI, either at the top or bottom of the device screen. Banner ads stay on the screen while users are interacting with the app. Banner ads can refresh automatically after a certain period of time. 

#### Main API Methods

Banner ads lifecycle event callback methods: 

You can implement some or all callback methods of `BannerViewDelegate`, in order to track when the current app goes in the background due to user click, or when an error is encountered while requesting ad.

```Swift
@objc public protocol BannerViewDelegate : NSObjectProtocol {
 
    @objc func bannerViewPresentationController() -> UIViewController?
 
    @objc optional func bannerView(_ bannerView: PrebidMobile.BannerView, didReceiveAdWithAdSize adSize: CGSize)
 
    @objc optional func bannerView(_ bannerView: PrebidMobile.BannerView, didFailToReceiveAdWith error: Error)
 
    @objc optional func bannerViewWillLeaveApplication(_ bannerView: PrebidMobile.BannerView)
 
    @objc optional func bannerViewWillPresentModal(_ bannerView: PrebidMobile.BannerView)
 
    @objc optional func bannerViewDidDismissModal(_ bannerView: PrebidMobile.BannerView)
}
```

#### Integration Code

Load and show banner ads:

1. In your View Controller file, import the header file and define the BannerView object as shown below:

```Swift
import PrebidMobile
var bannerView:BannerView!
```

2. Initialize the BannerView object and load an ad using the code below:

```Swift
// 1. Create an Ad View
let banner = BannerView(frame: CGRect(origin: .zero, size: adSize),
                        configID: AdConfig.ALX_BANNER_AD_ID,
                        adSize: adSize)
banner.delegate = self 
// 2. Load an Ad
banner.loadAd()
```

3. Once the ad request is triggered, track the lifecycle event such as successful clicks and closed banners etc.

```Swift
 extension PBBannerVC:BannerViewDelegate{
    func bannerViewPresentationController() -> UIViewController? {
        self
    }
 
    func bannerView(_ bannerView: PrebidMobile.BannerView, didReceiveAdWithAdSize adSize: CGSize){
        NSLog("Banner: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
 
    func bannerView(_ bannerView: BannerView, didFailToReceiveAdWith error: Error) {
        let error1=error as NSError
        let msg = "\(error1.code):\(error1.localizedDescription)"
        
        NSLog("Banner: ad load failed: \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func bannerViewWillPresentModal(_ bannerView: PrebidMobile.BannerView){
        NSLog("Banner: ad impress")
    }
    
    func bannerViewDidDismissModal(_ bannerView: PrebidMobile.BannerView){
        NSLog("Banner: ad close")
    }
}
```

### 2.3 **Rewarded Video Ads**

Rewarded video ads are video creatives embedded in apps or games. Users who watch rewarded video ads can get certain in-app/in-game rewards in return. 

#### Main API Methods

Rewarded video ads lifecycle event callback methods: 

You can implement some or all callback methods of `RewardedAdUnitDelegate`, in order to track when the user clicks the ad, or when an error is encountered while loading an ad

```Swift
@objc public protocol RewardedAdUnitDelegate : NSObjectProtocol {
 
    /// Called when an ad is loaded and ready for display
    @objc optional func rewardedAdDidReceiveAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the user is able to receive a reward from the app
    @objc optional func rewardedAdUserDidEarnReward(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the load process fails to produce a viable ad
    @objc optional func rewardedAd(_ rewardedAd: PrebidMobile.RewardedAdUnit, didFailToReceiveAdWithError error: (Error)?)
 
    /// Called when the interstitial view will be launched,  as a result of the show() method.
    @objc optional func rewardedAdWillPresentAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the interstial is dismissed by the user
    @objc optional func rewardedAdDidDismissAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when an ad causes the sdk to leave the app
    @objc optional func rewardedAdWillLeaveApplication(_ rewardedAd: PrebidMobile.RewardedAdUnit)
 
    /// Called when the user clicked the ad
    @objc optional func rewardedAdDidClickAd(_ rewardedAd: PrebidMobile.RewardedAdUnit)
}
```

Make sure to set the delegate like below before implementing the above event callback methods:

```Swift
rewardedAdUnit.delegate = self
```

#### **Integration Code**

Load and show rewarded video ads:

1. In your View Controller file, import the header file and define the `RewardedAdUnit` object as shown below:

```Swift
import PrebidMobile
 
var adUnit:RewardedAdUnit!
```

2. In your View Controller file, initialize the rewarded ad view and load an ad using the code below: 

```Swift
adUnit=RewardedAdUnit(configID: AdConfig.ALX_REWARD_VIDEO_AD_ID)
 
rewardedAdUnit.delegate = self
```

3. Use `isReady` method to check if the ad is valid. Then show the rewarded video ad:

```Swift
guard adUnit.isReady else{
    return
}
adUnit.show(from: self)
```

4. Implement `RewardedAdUnitDelegate` to track ad lifecycle events such as successful clicks, failed rewarded video ad requests, etc.

```Swift
 extension PBRewardVideoVC: RewardedAdUnitDelegate {
 
    func rewardedAdDidReceiveAd(_ rewardedAd: RewardedAdUnit) {
        NSLog("Reward: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
 
    func rewardedAd(_ rewardedAd: RewardedAdUnit, didFailToReceiveAdWithError error: Error?) {
        var msg:String=""
        if let error = error {
            let error1=error as NSError
            msg = "\(error1.code):\(error1.localizedDescription)"
        }else{
            msg=error?.localizedDescription ?? ""
        }
        
        NSLog("Reward: ad load failed : \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func rewardedAdWillPresentAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad impression")
    }
    
    func rewardedAdUserDidEarnReward(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: user earned reward")
    }
    
    func rewardedAdDidClickAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad click")
    }
    
    func rewardedAdDidDismissAd(_ rewardedAd: PrebidMobile.RewardedAdUnit){
        NSLog("Reward: ad close")
    }
 
}
```



### 2.4 **Interstitial Ads**

Interstitial ads are full-screen ads that cover the whole interface of the app. Users can choose to click on the ad to open the landing page or close the ad to return to the app.  Interstitial ads can be either display or video as shown below.

![](https://static.rixengine.com/a/platform/help/sdk/ios-2.4-1.png)

**Interstitial Display**

![](https://static.rixengine.com/a/platform/help/sdk/ios-2.4-2.png)

 **Interstitial Video**

#### Main API Methods

Callback lifecycle event: 

You can implement some or all callback methods of `InterstitialAdUnitDelegate`, in order to track when the user clicks the ad, or when an error is encountered while loading an ad.

```Swift
@objc public protocol InterstitialAdUnitDelegate : NSObjectProtocol {
 
    /// Called when an ad is loaded and ready for display
    @objc optional func interstitialDidReceiveAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when the load process fails to produce a viable ad
    @objc optional func interstitial(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit, didFailToReceiveAdWithError error: (Error)?)
 
    /// Called when the interstitial view will be launched,  as a result of the show() method.
    @objc optional func interstitialWillPresentAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when the interstitial is dismissed by the user
    @objc optional func interstitialDidDismissAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when an ad causes the sdk to leave the app
    @objc optional func interstitialWillLeaveApplication(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
 
    /// Called when the user clicked the ad
    @objc optional func interstitialDidClickAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit)
}
```

#### **Integration Code**

Load and show interstitial ads:

1. In your View Controller file, import the header file and define the
`InterstitialRenderingAdUnit` object as shown below:

```Swift
import PrebidMobile
 
 var adUnit:InterstitialRenderingAdUnit!
```

2. In your View Controller file, initialize the interstitial ad view and load an ad using the code below: 

```Swift
 adUnit=InterstitialRenderingAdUnit(configID: AdConfig.ALX_INTERSTITIAL_AD_ID)
adUnit.adFormats = [.video,.banner]
adUnit.delegate = self
adUnit.loadAD()
```

3. Use `isReady` method to check if the ad is valid. Then show the interstitial ad:

```Swift
guard adUnit.isReady else{
    return
}
adUnit.show(from: self)
```

4. Implement `InterstitialAdUnitDelegate` to track ad lifecycle events such as clicks, failed ad requests, etc.

```Swift
extension PBInterstitialVC: InterstitialAdUnitDelegate {
    func interstitialDidReceiveAd(_ interstitial: InterstitialRenderingAdUnit) {
        NSLog("Interstitial: ad load success")
        self.isLoading=false
        self.label.text=NSLocalizedString("load_success", comment: "")
    }
    
    func interstitial(_ interstitial: InterstitialRenderingAdUnit, didFailToReceiveAdWithError error: Error?) {
        var msg:String=""
        if let error = error {
            let error1=error as NSError
            msg = "\(error1.code):\(error1.localizedDescription)"
        }else{
            msg=error?.localizedDescription ?? ""
        }
        
        NSLog("Interstitial: ad load failed : \(msg)")
        self.isLoading=false
        self.label.text=String(format: NSLocalizedString("load_failed", comment: ""), msg)
    }
    
    func interstitialWillPresentAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad impression")
    }
    
    func interstitialDidClickAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad click")
    }
 
    func interstitialDidDismissAd(_ interstitial: PrebidMobile.InterstitialRenderingAdUnit){
        NSLog("Interstitial: ad close")
    }
 
    
}
```

 

### 2.5 **Native Ads**

Native in-feed ads including images, text, or video creative assets can be less disruptive and fit seamlessly into the surrounding content to match your app content layout. The layout can be customized for native ads as you want.

#### Main API Methods

Native ads lifecycle event callback methods:

You can implement some or all callback methods of `NativeAdEventDelegate`, in order to track when user clicks the ad, or when an error is encountered while loading an ad.

Event callback methods mainly have two parts, the first one is related to ad-loading events:

```Swift
@objc public protocol NativeAdDelegate {
 
    /**
     * A successful Prebid Native ad is returned
     *
     * @param ad use this instance for displaying
     */
    @objc func nativeAdLoaded(ad: PrebidMobile.NativeAd)
 
    /**
     * Prebid Native was not found in the server-returned response,
     * Please display the ad in regular ways
     */
@objc func nativeAdNotFound()
 
 
    /**
     * Prebid Native ad was returned, however, the bid is not valid for displaying
     * Should be treated as on-ad load failed
     */
    @objc func nativeAdNotValid()
}
```

The second callback methods that are related to post-rendering events:

```Swift
@objc public protocol NativeAdEventDelegate {
 
    /**
     * Sent when the native ad is expired.
     */
    @objc optional func adDidExpire(ad: PrebidMobile.NativeAd)
 
    /**
     * Sent when the native view is clicked by the user.
     */
    @objc optional func adWasClicked(ad: PrebidMobile.NativeAd)
 
    /**
     * Sent when  an impression is recorded for a native ad
     */
    @objc optional func adDidLogImpression(ad: PrebidMobile.NativeAd)
}
```

#### **Integration Code**

Load and show native ads:

1. In your View Controller file, import the header file and define the `NativeRequest` object:

```Swift
import PrebidMobile
var nativeUnit: NativeRequest!
var nativeAd: NativeAd?
```

2. In your View Controller file, initialize the native request and load an ad  using the code below:

```Swift
{
        // 1. Create a NativeRequest
        nativeUnit = NativeRequest(configId: AdConfig.ALX_NATIVE_AD_ID)
        
        // 2. Configure the NativeRequest
        nativeUnit.context = ContextType.Social
        nativeUnit.placementType = PlacementType.FeedContent
        nativeUnit.contextSubType = ContextSubType.Social
        nativeUnit.eventtrackers = eventTrackers
        
        // 3. Make a bid request to the Prebid Server
        nativeUnit.fetchDemand { [weak self] result, kvResultDict in
            NSLog("native fetch result \(result.name())")
            
            guard let self = self else {
                return
            }
            
            // 4. Find cached native ad
            guard let kvResultDict = kvResultDict, let cacheId = kvResultDict[PrebidLocalCacheIdKey] else {
                return
            }
            
            // 5. Create a NativeAd
            guard let nativeAd = NativeAd.create(cacheId: cacheId) else {
                return
            }
            
            self.nativeAd = nativeAd
            
            // 6. Render the native ad
            self.titleLabel.text = nativeAd.title
            self.bodyLabel.text = nativeAd.text
            
            if let iconString = nativeAd.iconUrl {
                ImageHelper.downloadImageAsync(iconString) { result in
                    if case let .success(icon) = result {
                        DispatchQueue.main.async {
                            self.iconView.image = icon
                        }
                    }
                }
            }
            
            if let imageString = nativeAd.imageUrl {
                ImageHelper.downloadImageAsync(imageString) { result in
                    if case let .success(image) = result {
                        DispatchQueue.main.async {
                            self.mainImageView.image = image
                        }
                    }
                }
            }
            
            self.callToActionButton.setTitle(nativeAd.callToAction, for: .normal)
            self.sponsoredLabel.text = nativeAd.sponsoredBy
            
            self.nativeAd?.delegate = self
            self.nativeAd?.registerView(view: self.view, clickableViews: [self.callToActionButton])
        }
    }
```

3. Implement `NativeAdEventDelegate` to  track ad lifecycle events such as successful clicks, impressions etc.

```Swift
    extension PBNativeVC:NativeAdEventDelegate{
    func adDidExpire(ad:NativeAd){
        NSLog("native: ad expire")
    }
 
    func adWasClicked(ad:NativeAd){
        NSLog("native: ad click")
    }
 
    func adDidLogImpression(ad:NativeAd){
        NSLog("native: ad impression")
    }
    
}
```

## SDK Returned Error Code Description

| Error Code | Description                                                |
| ------ | --------------------------------------------------- |
| 0      | Prebid demand fetch successful                      |
| 1      | Prebid server not specified                         |
| 2      | Prebid server does not recognize account id         |
| 3      | Prebid server does not recognize config id          |
| 4      | Prebid server does not recognize the size requested |
| 5      | Network Error                                       |
| 6      | Prebid server error                                 |
| 7      | Prebid Server did not return bids                   |
| 8      | Prebid demand timedout                              |
| 9      | Prebid server url is invalid                        |
| 10     | Prebid unknown error occurred                       |
| 1000   | Response structure is invalid                       |
| 7000   | Internal SDK error                                  |
| 7001   | Wrong arguments                                     |
| 7002   | No VAST tag in media data                           |
| 8000   | SDK misuse                                          |

 
