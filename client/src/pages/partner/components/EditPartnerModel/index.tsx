/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 10:09:39
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 16:46:07
 * @Description:
 */

import { useEffect, useState } from 'react';
import { Form, message, Input } from 'antd';
import { fetchData } from '@/utils';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { PartnerType, TypeOptions } from '@/constants/partner';

type AddPartnerModelProps = {
  editItem?: PartnerAPI.PartnerListItem;
  visible: boolean;
  isEdit: boolean;
  onClose: () => void;
  onSave: () => void;
  request: any;
};

const AddPartnerModel: React.FC<AddPartnerModelProps> = ({ editItem, onClose, visible, isEdit, onSave, request }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (editItem && isEdit && visible) {
      form.setFieldsValue(editItem);
    } else {
      form.resetFields();
    }
  }, [editItem, isEdit, visible]);

  const handleConfirm = () => {
    form.submit();
  };
  const handleFinish = (values: any) => {
    const params = {
      ...values,
      partner_id: editItem?.partner_id,
      ori_data: editItem || {}
    };
    fetchData({ setLoading, request: request, params, onSuccess });
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    onSave();
  };

  const onCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Partner` : `Add Partner`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      grayName={`${editItem?.partner_id || ''}`}
      width={450}
    >
      <Form
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        validateTrigger={['onChange', 'onBlur']}
        initialValues={{ type: PartnerType.Advertiser }}
      >
        <Form.Item
          name="partner_name"
          label="Partner Name"
          rules={[{ required: true, message: 'Please Enter Partner Name' }]}
        >
          <Input placeholder="Please Enter Partner Name" />
        </Form.Item>
        <Form.Item
          name="type"
          label="Type"
          tooltip={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
          rules={[{ required: true, message: 'Please Select Type' }]}
        >
          <NormalRadio
            options={TypeOptions.map(v => ({
              ...v,
              disabled: isEdit && v.value !== PartnerType['Advertiser & Publisher'] && v.value !== editItem?.type
            }))}
          />
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default AddPartnerModel;
