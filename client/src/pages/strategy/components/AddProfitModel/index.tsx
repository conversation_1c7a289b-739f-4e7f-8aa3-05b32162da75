/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 15:51:26
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 11:46:55
 * @Description:
 */
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import { getProfitMinValue, ProfitMaxValue, StatusMap, StatusOptions } from '@/constants';
import { ProfitTab, ProfitType } from '@/constants/strategy/profit';
import { addProfit, updateProfit } from '@/services/api';
import { fetchData, handleFilterSelect } from '@/utils';
import { useModel } from '@umijs/max';
import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';

type AddProfitDrawerProps = {
  profit?: StrategyAPI.PublisherAdvertiserProfitListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadProfit: () => void;
  tab: string;
  demandOptions: { label: string; value: number }[];
  supplyOptions: { label: string; value: number }[];
};

const DefaultFormData = {
  buyer_id: [],
  seller_id: [],
  profit_radio: 30,
  status: StatusMap.Active
};

const AddProfitDrawer: React.FC<AddProfitDrawerProps> = ({
  profit,
  visible,
  isEdit,
  handleClose,
  reloadProfit,
  tab,
  demandOptions,
  supplyOptions
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<number>(StatusMap.Active);
  const disabledAdvertiser = (!isEdit && tab === ProfitTab.advertiser) || (isEdit && tab === ProfitTab.publisher);
  const disabledPublisher = (!isEdit && tab === ProfitTab.publisher) || (isEdit && tab === ProfitTab.advertiser);
  const currentTabName = tab === ProfitTab.advertiser ? 'Advertiser' : 'Publisher';
  const { initialState } = useModel('@@initialState');
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);

  useEffect(() => {
    if (profit && isEdit && visible) {
      form.setFieldsValue(profit);
      setStatus(profit.status);
    }
    if (visible && !isEdit) {
      setStatus(StatusMap.Active);
      if (profit?.type === ProfitType.Seller) {
        if (tab === ProfitTab.publisher) {
          form.setFieldValue('seller_id', profit.seller_id);
          form.setFieldValue('buyer_id', []);
        } else {
          form.setFieldValue('seller_id', [profit.seller_id]);
        }
      }
      if (profit?.type === ProfitType.Demand) {
        if (tab === ProfitTab.advertiser) {
          form.setFieldValue('buyer_id', profit.buyer_id);
          form.setFieldValue('seller_id', []);
        } else {
          form.setFieldValue('buyer_id', [profit.buyer_id]);
        }
      }
      // 默认值
      form.setFieldValue('profit_ratio', 30);
    }
  }, [profit, isEdit, visible]);
  const handleConfirm = () => {
    form.submit();
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      const params = {
        profit_ratio: profit?.profit_ratio,
        ...values,
        id: profit?.id,
        type: profit?.type,
        ori_data: profit || {}
      };
      handleEditProfit(params);
    } else {
      const params = { profit_ratio: profit?.profit_ratio, ...values, type: ProfitType['Seller-Demand'] };
      handleAddProfit(params);
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadProfit();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleAddProfit = (params: any) => {
    // 新增数据
    fetchData({ setLoading, request: addProfit, params, onSuccess });
  };

  const handleEditProfit = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateProfit, params });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.status) {
      setStatus(changeValue.status);
    }
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit ${currentTabName} Profit` : `Add ${currentTabName} Profit`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
      >
        {(profit?.type !== ProfitType.Seller || (!isEdit && profit?.type === ProfitType.Seller)) &&
          tab === ProfitTab.advertiser && (
            <Form.Item
              name="buyer_id"
              label="Advertiser:"
              rules={[
                {
                  required: true,
                  message: 'Please Select Advertiser'
                }
              ]}
            >
              <Select disabled={true} options={demandOptions} />
            </Form.Item>
          )}
        {(profit?.type !== ProfitType.Demand || (!isEdit && profit?.type === ProfitType.Demand)) && (
          <Form.Item
            name="seller_id"
            label="Publisher:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Select Publisher'
              }
            ]}
          >
            <Select
              disabled={isEdit || disabledPublisher}
              showSearch
              filterOption={handleFilterSelect}
              mode={tab === ProfitTab.publisher || isEdit ? undefined : 'multiple'}
              placeholder="Please Select Publisher"
              options={supplyOptions}
            />
          </Form.Item>
        )}
        {(profit?.type !== ProfitType.Seller || (!isEdit && profit?.type === ProfitType.Seller)) &&
          tab === ProfitTab.publisher && (
            <Form.Item
              name="buyer_id"
              label="Advertiser:"
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please Select Advertiser'
                }
              ]}
            >
              <Select
                disabled={isEdit || disabledAdvertiser}
                showSearch
                filterOption={handleFilterSelect}
                mode={!isEdit ? 'multiple' : undefined}
                placeholder="Please Select Advertiser"
                options={demandOptions}
              />
            </Form.Item>
          )}
        <Form.Item
          name="profit_ratio"
          label="Profit Ratio:"
          rules={[
            {
              required: true,
              message: 'Please Input Profit Ratio'
            },
          ]}
        >
          <InputNumber
            style={{ width: '150px' }}
            min={profitMinValue}
            max={ProfitMaxValue}
            controls
            type="number"
            addonAfter="%"
            disabled={status === StatusMap.Paused}
          />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[
              {
                required: true,
                message: 'Please Select Status'
              }
            ]}
          >
            <NormalRadio
              disabled={tab === ProfitTab.publisher && profit?.type === ProfitType.Seller}
              options={StatusOptions}
            />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddProfitDrawer;
