/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-08 15:21:26
 * @Description:
 */
import Pagination from '@/components/Pagination/NormalPagination';
import TopBar, {
  CheckboxUniqueKeyOptionsType,
  DateRangeKey,
  SearchResultItem,
  TopBarSearchItem
} from '@/components/TopBar';
import type { TopBarButtonAuth } from '@/constants';
import type { FormInstance, TablePaginationConfig, TableProps } from 'antd';
import { Button } from 'antd';
import type { ButtonProps } from 'antd/es/button';
import type { RangePickerProps } from 'antd/es/date-picker';
import { ColumnProps } from 'antd/es/table';
import { Moment } from 'moment';
import { ReactNode, RefObject, useEffect, useImperativeHandle, useRef, useState } from 'react';
import Table from '../OriginalTable';
import styles from './index.less';
type ButtonType = ButtonProps & {
  label: string;
  onClick: (params: any) => void;
};

type NormalTableProps<T, K> = TableProps<T> & {
  extraTools?: ReactNode[];
  loading: boolean;
  columns: ColumnProps<T>[];
  tableData: API.BackResult<T>;
  rowKey: string;
  btnOptions?: ButtonType[];
  pagination?: TablePaginationConfig;
  pageTitle?: string;
  searchFormRef?: RefObject<FormInstance<any> | undefined>;
  searchOptions: TopBarSearchItem[];
  isFold?: boolean;
  isExport?: boolean;
  handleExport?: (params: ColumnProps<T>[]) => void;
  labelWidth?: number;
  isBtnTable?: boolean;
  handleSearch: (start: number, end: number, val: SearchResultItem[], isPaing?: boolean) => void;
  getTableData: (params: any) => void;
  defaultParams: any;
  defaultSearchValue?: any;
  defaultFold?: boolean;
  searchValue?: any;
  handleDisableDate?: (currentDate: Moment) => boolean;
  handleDateLimit?: (key: string, value: any, timeLimit: number, form: FormInstance<any>, callback: () => void) => void;
  emptyRender?: () => JSX.Element;
  isExportAll?: boolean;
  exportAllTooltip?: string;
  handleDownloadAll?: (data: any, columns: ColumnProps<T>[]) => void;
  buttonAuth?: TopBarButtonAuth;
  defaultFormItemWidth?: number;
  defaultBoxItemWidth?: number; // checkbox label最长宽度
  handleSortChange?: (curPage: number, pageSize: number, searchVal: SearchResultItem[], sorter: any) => void;
  onCalendarChange?: (dates: any, dateStrings: [string, string], info: any) => void;
  maxRange?: number;
  defaultDates?: [Moment | null, Moment | null] | null;
  checkboxUniqueKeyOptions?: CheckboxUniqueKeyOptionsType[];
  handleMetricsChange?: (val: any) => void;
  dateRangeKeys?: DateRangeKey[];
  showTopBar?: boolean;
  showTable?: boolean;
  downloadUrl?: string; // 直接下载文件的url pixalate report
  downloadUrlTips?: string; // tooltip pixalate report
  tabOptions?: { label: string; value: string | number }[];
  defaultTab?: string | number;
  handleTabChange?: (tab: string | number) => void;
  ExtraRangePickerOptions?: RangePickerProps;
  handleSearchValueChange?: (val: any, allValues: any, isDefaultFold: boolean, form: FormInstance<any>) => void;
};

const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

function NormalTable<T extends object>({
  extraTools,
  columns,
  rowKey,
  btnOptions = [],
  pagination,
  pageTitle,
  labelWidth = 80,
  tableData,
  isExport,
  handleExport,
  searchFormRef,
  searchOptions,
  isFold,
  isBtnTable = true,
  handleSearch,
  defaultParams,
  getTableData,
  loading,
  defaultSearchValue,
  searchValue,
  defaultFold = true,
  handleDisableDate,
  handleDateLimit,
  emptyRender,
  isExportAll,
  handleDownloadAll,
  buttonAuth,
  defaultFormItemWidth,
  defaultBoxItemWidth,
  handleSortChange,
  onCalendarChange,
  maxRange,
  defaultDates,
  checkboxUniqueKeyOptions,
  handleMetricsChange,
  dateRangeKeys,
  showTopBar = true,
  showTable = true,
  downloadUrl,
  downloadUrlTips,
  tabOptions,
  defaultTab,
  handleTabChange,
  ExtraRangePickerOptions,
  handleSearchValueChange,
  exportAllTooltip,
  ...rest
}: NormalTableProps<T, keyof T>): JSX.Element {
  const [currentPage, setCurrentPage] = useState<number>(DefCurrentPage);
  const [pageSize, setPageSize] = useState<number>(PageSize);
  const [searchVal, setSearchVal] = useState<SearchResultItem[]>([]);
  const [normalOptions, setNormalOptions] = useState<TopBarSearchItem[]>([]);
  const [boxOptions, setBoxOptions] = useState<TopBarSearchItem[]>([]);
  const [isBottomFold, setBottomFold] = useState<any>({}); // checkbox折叠使用
  const topbarRef = useRef<any>(null);
  useEffect(() => {
    const normals = searchOptions.filter(item => item.type !== 'checkboxFold');
    const boxs = searchOptions.filter(item => item.type === 'checkboxFold');
    setNormalOptions(normals);
    setBoxOptions(boxs);
    const tmp = JSON.parse(JSON.stringify(isBottomFold));
    boxOptions.forEach(item => {
      tmp[item.key] = defaultFold;
    });
    setBottomFold(tmp);
  }, [searchOptions]);

  const handleBottomFold = (key: string) => {
    const tmp = JSON.parse(JSON.stringify(isBottomFold));
    tmp[key] = !isBottomFold[key];
    setBottomFold(tmp);
  };

  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    const start = size * (page - 1);
    const end = size * page;
    handleSearch(start, end, searchVal, true);
  };

  useEffect(() => {
    reset();
  }, []);
  // 重置
  const reset = () => {
    resetTable();
    // getTableData(defaultParams);
  };

  const resetTable = () => {
    setCurrentPage(DefCurrentPage);
    setPageSize(PageSize);
  };

  const handleSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
    resetTable();
    handleSearch(0, 50, val);
  };

  const handleDownload = () => {
    handleExport && handleExport(columns);
  };

  const handleDownLoadTmp = (data: any) => {
    handleDownloadAll && handleDownloadAll(data, columns);
  };

  // 目前只支持sorter，以后如果有其他的，需要做下兼容
  const handleTableChange: TableProps<T>['onChange'] = (pagination: any, filters: any, sorter: any) => {
    if (handleSortChange) {
      if (topbarRef.current?.vaildForm) {
        topbarRef.current?.vaildForm?.().then((res: any) => {
          handleSortChange(currentPage, pageSize, searchVal, sorter);
        });
      } else {
        handleSortChange(currentPage, pageSize, searchVal, sorter);
      }
    }
  };
  const handleDefaultSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
  };

  useImperativeHandle(searchFormRef, () => topbarRef.current?.getFormInstance(), [topbarRef.current]);

  return (
    <div className={styles['back-table-container']}>
      {showTopBar && (
        <TopBar
          ref={topbarRef}
          toptitle={pageTitle}
          handleSearchChange={handleSearchChange}
          isFront={false}
          extraTools={extraTools}
          handleExport={handleDownload}
          isExport={isExport}
          isFold={isFold}
          labelWidth={labelWidth}
          defaultValue={defaultSearchValue}
          normalOptions={normalOptions}
          boxOptions={boxOptions}
          setNormalOptions={setNormalOptions}
          setBoxOptions={setBoxOptions}
          isBottomFold={isBottomFold}
          handleBottomFold={handleBottomFold}
          defaultFold={defaultFold}
          searchOptions={searchOptions}
          searchValue={searchValue}
          handleDisableDate={handleDisableDate}
          handleDateLimit={handleDateLimit}
          loading={loading}
          isExportAll={isExportAll}
          exportAllTooltip={exportAllTooltip}
          handleDownloadAll={handleDownLoadTmp}
          buttonAuth={buttonAuth}
          disabledExport={!tableData?.total}
          onCalendarChange={onCalendarChange}
          defaultDates={defaultDates}
          checkboxUniqueKeyOptions={checkboxUniqueKeyOptions}
          defaultFormItemWidth={defaultFormItemWidth}
          defaultBoxItemWidth={defaultBoxItemWidth}
          dateRangeKeys={dateRangeKeys}
          downloadUrl={downloadUrl}
          downloadUrlTips={downloadUrlTips}
          handleDefaultSearchChange={handleDefaultSearchChange}
          tabOptions={tabOptions}
          defaultTab={defaultTab}
          onTabChange={handleTabChange}
          ExtraRangePickerOptions={ExtraRangePickerOptions}
          handleSearchValueChange={handleSearchValueChange}
        />
      )}
      {showTable && (
        <div className={styles['bottom-container']}>
          <div className={styles['bottom-top']}>
            <div className={styles['top-left']}>
              {btnOptions?.map(({ label, onClick, ...rest }: ButtonType, index) => {
                return (
                  <Button {...rest} key={index} onClick={onClick}>
                    {label}
                  </Button>
                );
              })}
            </div>
            <div className={styles['top-right']}>
              <Pagination
                onChange={handlePageChange}
                total={tableData.total}
                current={currentPage}
                pageSize={pageSize}
                showSizeChanger
                size="small"
                responsive
                showTotal={total => `Total ${total} items`}
                pageSizeOptions={PageSizeOptions}
                {...(pagination as TablePaginationConfig)}
              />
            </div>
          </div>
          <Table<T>
            {...rest}
            columns={columns}
            dataSource={tableData.data}
            rowKey={rowKey}
            loading={loading}
            isBtnTable={isBtnTable}
            scroll={{ y: 'calc(100vh - 220px)' }}
            emptyRender={emptyRender}
            onChange={handleTableChange}
          />
        </div>
      )}
    </div>
  );
}

export default NormalTable;
