import Router from 'koa-router';
import { pmpCtrl } from '@/controllers';

const router = new Router({ prefix: '/pmp' });

//  testing不支持编辑
const routers = router
  .post('/addDeal', pmpCtrl.addDeal)
  .post('/updateDeal', pmpCtrl.updateDeal)
  .post('/getPmpDealList', pmpCtrl.getPmpDealList)
  .post('/addInventory', pmpCtrl.addInventory)
  .post('/updateInventory', pmpCtrl.updateInventory)
  .post('/getPmpInventoryList', pmpCtrl.getPmpInventoryList);

export default routers;
