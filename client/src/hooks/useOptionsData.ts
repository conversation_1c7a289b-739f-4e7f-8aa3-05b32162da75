import { useModel } from '@umijs/max';
import { useCallback, useEffect, useMemo } from 'react';

/**
 * `AllOptionsData` 是一个包含应用程序中使用的所有选项键的常量数组。
 */
const AllOptionsData = ['demand', 'supply', 'supply_testing'] as const;

/**
 * `OptionsData` 表示从 `AllOptionsData` 派生的所有有效选项键的联合类型。
 */
type OptionsData = typeof AllOptionsData[number];

export const useOptionsData = (optionsData: OptionsData[]) => {
  // 获取广告主和发布商数据
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandList');
  const { dataSource: supplyList, supplyOptions, reload: reloadSupply } = useModel('useSupplyList');
  const {
    dataSource: supplyTestingList,
    supplyOptions: supplyTestingOptions,
    reload: reloadSupplyTesting
  } = useModel('useSupplyListWithTesting');

  const isDemand = optionsData.includes('demand');
  const isSupply = optionsData.includes('supply');
  const isSupplyTesting = optionsData.includes('supply_testing');

  // 初始化加载数据
  useEffect(() => {
    if (isDemand && !demandList) {
      reloadDemand();
    }
    if (isSupply && !supplyList) {
      reloadSupply();
    }
    if (isSupplyTesting && !supplyTestingList) {
      reloadSupplyTesting();
    }
  }, [
    isDemand,
    isSupply,
    isSupplyTesting,
    demandList,
    supplyList,
    supplyTestingList,
    reloadDemand,
    reloadSupply,
    reloadSupplyTesting
  ]);

  // 处理广告主选项数据
  const demandOptions = useMemo(() => {
    if (demandList) {
      return demandList.map(({ buyer_name, buyer_id }: any) => ({
        label: `${buyer_name}(${buyer_id})`,
        value: buyer_id
      }));
    }
    return [];
  }, [demandList]);

// 重新加载选项数据
const reloadOptions = useCallback(() => {
  if (isDemand) reloadDemand();
  if (isSupply) reloadSupply();
  if (isSupplyTesting) reloadSupplyTesting();
}, [isDemand, isSupply, isSupplyTesting, reloadDemand, reloadSupply, reloadSupplyTesting]);

  return {
    demandOptions,
    supplyOptions,
    supplyTestingOptions,
    reloadOptions,
    demandList,
    supplyList,
    supplyTestingList
  };
};
