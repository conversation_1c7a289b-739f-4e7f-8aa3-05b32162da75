import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import { getProfitMinValue, ProfitMaxValue, StatusOptions } from '@/constants';
import { ProfitTab, ProfitTypeOptions } from '@/constants/strategy/profit';
import { addBundleProfit, addProfit, updateBundleProfit, updateProfit } from '@/services/api';
import { fetchData } from '@/utils';
import { useModel } from '@umijs/max';
import { Form, FormInstance, message } from 'antd';
import React, { useImperativeHandle, useMemo, useState } from 'react';
import { useProfitFormFields } from './helper/useProfitFormFields';

export type ProfitFormRef = React.RefObject<FormInstance<any>>;

type AddProfitDrawerProps = {
  visible: boolean;
  isEdit: boolean;
  currentTab: string;
  handleClose: () => void;
  reloadProfit: () => void;
  demandOptions: { label: string; value: number }[];
  supplyOptions: { label: string; value: number }[];
  formRef?: ProfitFormRef;
};

const AddProfitDrawer = ({
  formRef,
  isEdit,
  currentTab,
  visible,
  handleClose,
  demandOptions,
  supplyOptions,
  reloadProfit
}: AddProfitDrawerProps) => {
  const { initialState } = useModel('@@initialState');
  const profitMinValue = getProfitMinValue(initialState?.currentUser?.tnt_id ?? 0);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  useImperativeHandle(formRef, () => form, [form]);

  // 提交成功的回调逻辑
  const onSuccess = () => {
    message.success('success');
    handleClose();
    reloadProfit();
  };

  const handleFinish = async () => {
    const values = await form.validateFields();
    const filteredValues = Object.fromEntries(
      Object.entries(values).filter(([_, value]) => value !== undefined && value !== null)
    );

    // 根据当前tab和编辑状态确定请求函数
    const getRequestFunction = () => {
      if (currentTab === ProfitTab.bundle) {
        return isEdit ? updateBundleProfit : addBundleProfit;
      }
      return isEdit ? updateProfit : addProfit;
    };

    const requestFunction = getRequestFunction();
    fetchData({
      setLoading,
      onSuccess,
      request: requestFunction,
      params: filteredValues
    });
  };

  // 取消
  const onCancel = () => {
    handleClose();
  };

  const title = useMemo(() => {
    if (currentTab === ProfitTab.bundle) {
      return `${isEdit ? 'Edit' : 'Add'} Bundle Profit`;
    }
    return `${isEdit ? 'Edit' : 'Add'} ${currentTab.charAt(0).toUpperCase() + currentTab.slice(1)} Profit`;
  }, [isEdit, currentTab]);

  // 对 profit options 做限制
  const { CurrentProfitTypeOptions, isProfitOptionsDisabled } = useMemo(() => {
    return {
      CurrentProfitTypeOptions: currentTab === ProfitTab.bundle ? ProfitTypeOptions.slice(3) : ProfitTypeOptions,
      isProfitOptionsDisabled: currentTab !== ProfitTab.bundle || isEdit
    };
  }, [currentTab, isEdit]);

  // 使用自定义hook获取表单字段渲染函数
  const { renderFormFields } = useProfitFormFields({
    currentTab,
    demandOptions,
    supplyOptions,
    isEdit
  });

  return (
    <NormalDrawer
      // 大写首字母 currentTab
      blackName={title}
      onConfirm={handleFinish}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
      destroyOnClose
      width="min(100%, 424px)"
    >
      <Form autoComplete="off" form={form} layout="vertical">
        <Form.Item name="id" label="ID" hidden>
          <InputNumber />
        </Form.Item>
        <Form.Item name="type" label="Type" rules={[{ required: true, message: 'Please select type' }]}>
          <Select options={CurrentProfitTypeOptions} disabled={isProfitOptionsDisabled} />
        </Form.Item>
        <Form.Item noStyle dependencies={['type']}>
          {/* 根据 type 生成的 form item 数据 */}
          {({ getFieldValue }) => renderFormFields(getFieldValue('type'))}
        </Form.Item>
        <Form.Item
          name="profit_ratio"
          label="Profit Ratio"
          rules={[{ required: true, message: 'Please enter profit ratio' }]}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={profitMinValue}
            max={ProfitMaxValue}
            controls
            type="number"
            addonAfter="%"
          />
        </Form.Item>
        {isEdit && (
          <Form.Item name="status" label="Status" rules={[{ required: true, message: 'Please select status' }]}>
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddProfitDrawer;
