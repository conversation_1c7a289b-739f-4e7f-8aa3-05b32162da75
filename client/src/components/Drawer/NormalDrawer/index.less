.drawer-content-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  .header {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background-color: #fff;
    z-index: 999;
    box-shadow: 8px 0px 8px rgba(0, 0, 0, 0.15);
  }
  .content {
    flex: 1;
    padding: 0px 20px;
    padding-top: 16px;
    overflow-y: auto;
    max-height: calc(100vh - 100px);
  }
  .footer {
    position: sticky;
    z-index: 999;
    bottom: 0;
    padding-bottom: 10px;
    padding-top: 10px;
    padding-right: 20px;
    background-color: #fff;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.15);
    height: 50px;
    text-align: right;
  }
}

.normal-drawer-container {
  :global {
    .ant-drawer-header {
      border-bottom: none;
      padding: 20px;
      .ant-drawer-title {
        color: var(--text-color);
        font-weight: 700;
        font-size: 16px;
      }
    }
    .ant-drawer-body {
      padding: 0px;
    }
    .ant-spin-nested-loading,
    .ant-spin-container {
      height: 100%;
    }
  }
}
