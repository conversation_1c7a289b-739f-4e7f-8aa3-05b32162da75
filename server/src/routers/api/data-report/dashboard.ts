/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:37:39
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-09 12:20:34
 * @Description:
 */
import Router from 'koa-router';
import { Context, Next } from 'koa';
import {
  pixalateController,
  dashboardController,
  advReportController,
  pubReportController
} from '@/controllers';
import { setInterfaceTimeOut } from '@/utils/timeout';
import { humanController } from '@/controllers/data-report/human-report';
import { parseDateParams, resolveCurConditionData } from '@/utils/report';
const router = new Router({ prefix: '/dashboard' });
const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(60 * 60 * 1000);
  await next();
};

const routers = router
  .post(
    '/getDashboardList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    dashboardController.getDashboardList
  )
  .post(
    '/downloadDashboardReport',
    resolveCurConditionData,
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    parseDateParams,
    dashboardController.downloadAllReport
  )
  .post(
    '/getAdvReportList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    advReportController.getDashboardList
  )
  .post(
    '/downloadAdvReport',
    resolveCurConditionData,
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    advReportController.downloadAllReport
  )
  .post(
    '/getPubReportList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    pubReportController.getDashboardList
  )
  .post(
    '/downloadPubReport',
    resolveCurConditionData,
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    pubReportController.downloadAllReport
  )
  .post(
    '/getPixalateReport', // pixalate报表不支持时区
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    pixalateController.getPixalateReport
  )
  .post(
    '/downloadPixalateReport',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    pixalateController.downloadPixalateReport
  )
  .post(
    '/getConfigQps',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    dashboardController.getConfigQps
  )
  .post(
    '/getHumanReport', // human报表不支持时区
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    humanController.getHumanReport
  )
  .post(
    '/downloadHumanReport',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    humanController.downloadHumanReport
  );

export default routers;
