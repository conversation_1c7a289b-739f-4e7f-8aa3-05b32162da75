/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-21 15:47:12
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';
import {
  AllMetricsOptions,
  DashboardAllColumns,
  DashboardBreadOptions,
  DashboardDefaultColumnKeys,
  DashboardDefaultDimension,
  DashBoardDefaultMetrics,
  DashboardSearchOption,
  DateType,
  AllDimensionOptions,
  FormatExportValueMap,
  SchainMap
} from '@/constants/data-report';
import {
  customConfig,
  customDashboard,
  customDimensionName,
  customRole,
  customTenant,
  ExtraDimensions,
  ExtraSearchOption
} from '@/constants/hardCode/full-report';
import { useAccess, useModel } from '@umijs/max';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ExportNotification from '../compoments/ExportNotification';
import styles from './index.less';

import EditButton from '@/components/Button/EditButton';
import NormalModal from '@/components/Modal/NormalModal';
import { usePresetConfirmModal } from '@/components/PresetConfirmModal';
import PresetDropdownButton from '@/components/PresetDropdownButton';
import { deserializeReportPresetValue } from '@/components/PresetDropdownButton/helper/data-report';
import { PresetDropdownItem, PresetDropdownStore } from '@/components/PresetDropdownButton/persistence';
import OriginalTable from '@/components/Table/OriginalTable';
import type { SearchResultItem, TopBarSearchItem } from '@/components/TopBar';
import type { TopBarButtonAuth } from '@/constants';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import { TimeZoneMapLabel } from '@/constants/base/time-zone';
import { ConfigQpsColumns } from '@/constants/data-report/config-qps';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { Countries } from '@/constants/global-mapping/country';
import { PartnerType } from '@/constants/partner';
import { QpsLevelType, RegionLabelMap, RegionListType } from '@/constants/strategy/qps';
import { downloadDashboardList, getDashboardList } from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { formatExportData } from '@/utils/export-file';
import { checkMetrics } from '@/utils/report';
import { FileSearchOutlined } from '@ant-design/icons';
import { FormInstance } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';
import moment, { Moment } from 'moment-timezone';

const Page: React.FC = () => {
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const { tnt_id, role_id = 0 } = initialState?.currentUser as UserAPI.UserListItem;
  // iion租户下的Data_Custom角色
  const isTenantCustomRole = customTenant.includes(tnt_id) && customRole[tnt_id]?.includes(role_id);
  const isCustom = (customTenant.includes(tnt_id) && isTenantCustomRole) || customDashboard.includes(tnt_id);
  const isCustomDashboard = customDashboard.includes(tnt_id);
  let cusSearchOption: TopBarSearchItem[] = [];
  if (isCustom) {
    const search = customConfig[tnt_id]?.SearchOption;
    const Metrics = customConfig[tnt_id]?.Metrics;
    const Dimensions = customConfig[tnt_id]?.Dimensions;
    const isExtraSearchOption = ExtraSearchOption.find(item => search.includes(item.key));
    cusSearchOption = DashboardSearchOption.filter(item => search.includes(item.key));

    if (isExtraSearchOption) {
      cusSearchOption = cusSearchOption.concat(ExtraSearchOption.filter(item => search.includes(item.key)));
    }
    const dIndex = cusSearchOption.findIndex(item => item.key === 'columns');
    const mIndex = cusSearchOption.findIndex(item => item.key === 'metrics');

    if (dIndex !== -1) {
      const dimensions = AllDimensionOptions?.filter(item => Dimensions.includes(item.value));
      cusSearchOption[dIndex].options = dimensions;
    }
    if (mIndex !== -1) {
      const metrics = AllMetricsOptions.filter(item => Metrics.includes(item.value));
      cusSearchOption[mIndex].options = metrics;
    }
  }

  const buttonAuth: TopBarButtonAuth = {
    Search: access.DisabledButton('DashboardSearchAuth'),
    Export: access.DisabledButton('DashboardExportAuth'),
    ExportAll: access.DisabledButton('DashboardExportAllAuth')
  };
  const { dataSource: demandList, reload: reloadDemand } = useModel('useDemandListWithTesting');
  const { dataSource: supplyList, reload: reloadSupply } = useModel('useSupplyListWithTesting');
  const { data: appList, reload: reloadAppList } = useModel('useAppList');
  const { data: qpsList, reload: reloadQpsList, loading: qpsLoading } = useModel('useDashboardConfigQpsList');
  const { dataSource: partnerList, reload: reloadPartner } = useModel('usePartnerList');
  const { dataSource: placemantList, reload: reloadPlacement } = useModel('useAllPlacementList');
  const { adSizeOptions, fetchAdSize } = useModel('useAdSizeOptions');
  const [columns, setColumns] = useState<ColumnProps<DashboardAPI.DashboardListItem>[]>([]);
  const [searchOptions, setSearchOptions] = useState(isCustom ? cusSearchOption : DashboardSearchOption);
  const [dataSource, setDataSource] = useState<API.BackResult<DashboardAPI.DashboardListItem>>({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  const [totalCount, setTotalCount] = useState<number>(0);
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<SorterResult<DashboardAPI.DashboardListItem>>({
    columnKey: 'date',
    order: 'descend'
  });
  const [allColumns, setAllColumns] = useState<ColumnProps<DashboardAPI.DashboardListItem>[]>(DashboardAllColumns);

  // 显示那些列
  const [columnKeys, setColumnKeys] = useState(
    isCustom ? customConfig[tnt_id].DefaultColumnKeys : DashboardDefaultColumnKeys
  );

  const [showQpsModal, setShowQpsModal] = useState(false);
  const [showConfigQps, setShowConfigQps] = useState(false);
  const [qpsModalData, setQpsModalData] = useState<StrategyAPI.QpsListItem[]>([]);

  const [openExport, setOpenExport] = useState(false);
  const [exportParams, setExportParams] = useState<any>({});
  const [notifyKey, setNotifyKey] = useState<string>('');
  const [downloadData, setDownloadData] = useState<any>({});

  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!qpsList || !qpsList.length) {
      reloadQpsList();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    if (!partnerList || !partnerList.length) {
      reloadPartner();
    }

    if (isCustomDashboard) {
      reloadAppList();
    }
    if (isTenantCustomRole) {
      reloadPlacement();
    }
    fetchAdSize();
  }, []);

  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList, appList, partnerList, adSizeOptions]);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<DashboardAPI.DashboardListItem>[] = DashboardAllColumns.map(v => {
        return {
          ...v,
          sortOrder: v.sorter && sortedInfo.columnKey === v.dataIndex ? sortedInfo.order : null
        };
      });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter(v => columnKeys.includes((v.key as string) || (v.dataIndex as string)));
      const advqpsColumnIndex = tmp.findIndex(v => v.key === 'adv_config_qps');
      const pubqpsColumnIndex = tmp.findIndex(v => v.key === 'pub_config_qps');

      const configQpsRender = (text: string, params: DashboardAPI.DashboardListItem, colKey: string) => {
        return showConfigQps ? (
          params[colKey as keyof DashboardAPI.DashboardListItem] ? (
            <div className={styles['qps-con']}>
              <span className={styles['text']}>{params[colKey as keyof DashboardAPI.DashboardListItem]}</span>
              <EditButton onClick={e => handleQpsClick(e, params, colKey)} icon={<FileSearchOutlined />}></EditButton>
            </div>
          ) : (
            '-'
          )
        ) : (
          <span>-</span>
        );
      };
      if (advqpsColumnIndex !== -1) {
        tmp[advqpsColumnIndex].render = (text: string, params: DashboardAPI.DashboardListItem) =>
          configQpsRender(text, params, 'adv_config_qps');
      }
      if (pubqpsColumnIndex !== -1) {
        tmp[pubqpsColumnIndex].render = (text: string, params: DashboardAPI.DashboardListItem) =>
          configQpsRender(text, params, 'pub_config_qps');
      }
      if (isTenantCustomRole) {
        const placementIndex = tmp.findIndex(v => v.key === 'placement_id');
        if (placementIndex !== -1) {
          tmp[placementIndex].title = 'Unit Name (ID)';
        }
      }
      setColumns(tmp);
    }
  };
  // 默认的值
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(JSON.stringify(searchOptions));
    const indexMap: { [key: string]: number } = {};
    options.forEach((item: TopBarSearchItem, index) => {
      indexMap[item.key] = index;
    });

    if (Array.isArray(demandList) && Array.isArray(supplyList)) {
      const dOptions: any[] = demandList.map((item: DemandAPI.DemandListItem) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id})`,
          value: item.buyer_id
        };
      });
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id
        };
      });

      const dIndex = indexMap['buyer_id'];
      if ((dIndex ?? -1) !== -1) {
        options[dIndex].options = dOptions;
      }
      const sIndex = indexMap['seller_id'];
      if ((sIndex ?? -1) !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    if (Array.isArray(appList)) {
      const appOptions = appList.map((item: AppAPI.AppListItem) => {
        return {
          label: item.app_name,
          value: item.bundle,
          key: `${item.app_id}`
        };
      });
      const index = indexMap['app_name'];
      if ((index ?? -1) !== -1) {
        options[index].options = appOptions;
      }
    }
    const index = indexMap['date'];
    if ((index ?? -1) !== -1) {
      const lastDay = moment().format('YYYYMMDD');
      const lastWeek = moment().subtract(1, 'days').format('YYYYMMDD');
      options[index].value = [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')];
      const params = { start: 0, end: 50, start_date: lastWeek, end_date: lastDay, columns: [] };
      setDefaultParams(params);
      const tmp = {
        metrics: isCustom ? [...customConfig[tnt_id].DefaultMetrics] : [...DashBoardDefaultMetrics],
        date: [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        columns: isCustom ? [...customConfig[tnt_id].DefaultDimension] : [...DashboardDefaultDimension]
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
      // iion 租户定制整个full-report查询时间
      if (tnt_id === 1053) {
        const timeLimit = options[index].dimensionTimeLimit?.map(item => {
          return {
            ...item,
            limit: 15
          };
        });
        options[index].dimensionTimeLimit = timeLimit;
      }
      // getTableData(params);
    }
    const hourIndex = indexMap['hour'];
    if ((hourIndex ?? -1) !== -1) {
      options[hourIndex].tooltip = TimeZoneMapLabel[initialState?.timeZone || 'Etc/UTC'];
    }

    const advPartnerIndex = indexMap['adv_partner_id'];
    const pubPartnerIndex = indexMap['pub_partner_id'];
    const partnerOptions = partnerList.map((item: PartnerAPI.PartnerListItem) => {
      return {
        label: `${item.partner_name}`,
        value: item.partner_id,
        type: item.type
      };
    });
    const advPartnerOptions = partnerOptions.filter(p => p.type !== PartnerType.Publisher);
    const pubPartnerOptions = partnerOptions.filter(p => p.type !== PartnerType.Advertiser);
    if ((advPartnerIndex ?? -1) !== -1) {
      options[advPartnerIndex].options = advPartnerOptions;
    }
    if ((pubPartnerIndex ?? -1) !== -1) {
      options[pubPartnerIndex].options = pubPartnerOptions;
    }
    if (!isCustom) {
      const cIndex = indexMap['columns'];
      if ((cIndex ?? -1) !== -1) {
        const cOptions = options[cIndex].options?.filter((item: any) => !ExtraDimensions.includes(item.value));
        options[cIndex].options = cOptions;
      }
    }
    if (customTenant.includes(tnt_id) && isTenantCustomRole) {
      const index = indexMap['metrics'];
      const dIndex = indexMap['columns'];
      if ((index ?? -1) !== -1) {
        console.log(options[index]);
        const metricsOptions = options[index];
        if (metricsOptions.labelIcon) {
          delete metricsOptions.labelIcon;
        }
      }
      if ((dIndex ?? -1) !== -1) {
        const dimOptions = options[dIndex].options!;
        const cusOptions = dimOptions.map(item => {
          if (customDimensionName[tnt_id] && customDimensionName[tnt_id][item.value]) {
            item.label = customDimensionName[tnt_id][item.value];
          }
          return item;
        });
        options[dIndex].options = cusOptions;
      }
    }

    const adSizeIndex = options.findIndex((item) => item.key === 'ad_size');
    if (adSizeIndex !== -1) {
      options[adSizeIndex].options = adSizeOptions;
    }

    setSearchOptions(options);
  };

  const handleExport = async (
    columns: ColumnProps<DashboardAPI.DashboardListItem>[],
    sourceData?: API.BackResult<DashboardAPI.DashboardListItem>
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map(item => {
      return {
        label: item.title,
        value: item.dataIndex !== 'day' && item.dataIndex !== 'day_hour' ? item.dataIndex : 'date'
      };
    });

    const data = await formatExportData(sourceData?.data || dataSource.data, FormatExportValueMap);

    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(item => item.key === 'columns' && Array.isArray(item.value) && item.value.length > 0);
    const metrics = val.find(item => item.key === 'metrics' && Array.isArray(item.value) && item.value.length > 0);

    const is_hour = dimension?.value?.findIndex((v: any) => v === 'day_hour') > -1;
    const is_day = dimension?.value.findIndex((v: any) => v === 'day') > -1;
    const dim: any[] = dimension?.value.filter((d: string) => !['day', 'day_hour'].includes(d)) || [];
    const mt: any[] = metrics?.value || [];

    const column_keys = [...dim, ...mt];
    if (is_day && is_hour) {
      column_keys.unshift('day_hour');
    } else if ((is_day && !is_hour) || (!is_day && is_hour)) {
      is_day && column_keys.unshift('day');
      is_hour && column_keys.unshift('day_hour');
    }

    const includeAdvOrPub = dimension?.value.findIndex((v: string) => v === 'seller_id' || v === 'buyer_id') > -1;

    if (includeAdvOrPub) {
      setShowConfigQps(true);
    } else {
      setShowConfigQps(false);
    }
    setColumnKeys(column_keys);
  };
  const handleSearchChange = (start: number, end: number, val: SearchResultItem[], isPaing?: boolean) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };

  const handleGetSearchParams = (start: number, end: number, val: SearchResultItem[]) => {
    const tmp = val.filter(
      item => (Array.isArray(item.value) && item.value.length > 0) || (!Array.isArray(item.value) && item.value)
    );
    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach(item => {
      if (item.key === 'app_bundle_id') {
        if (Array.isArray(item.value) && item.value.length) {
          params[item.key] = item.value.filter((v: string) => v && v.trim()).join(',');
        }
      } else if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map(v => v.format('YYYYMMDD'));
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else if (item.key === 'columns') {
        // 默认是day;
        params.split_time = DateType.Day;

        if (Array.isArray(item.value)) {
          // 预防污染
          const tmp = JSON.parse(JSON.stringify(item.value));
          const sizeIndex = tmp.findIndex((t: string) => t === 'ad_size');
          const hourIndex = tmp.findIndex((t: string) => t === 'day_hour');
          const dayIndex = tmp.findIndex((t: string) => t === 'day');

          if (sizeIndex !== -1) {
            tmp[sizeIndex] = 'ad_width, ad_height';
          }
          if (dayIndex === -1 && hourIndex === -1) {
            params.split_time = DateType.Null;
          }
          if (dayIndex !== -1) {
            params.split_time = DateType.Day;
          }
          if (hourIndex !== -1) {
            params.split_time = DateType.Hour;
          }

          params['columns'] = tmp;
        }
      } else if (['adv_partner_id', 'pub_partner_id'].includes(item.key)) {
        const partner_id: any[] = params['partner_id'] || [];
        if (Array.isArray(item.value) && item.value.length) {
          item.value.forEach((v: string) => {
            const partner = partnerList?.find((item: PartnerAPI.PartnerListItem) => item.partner_id === +v);
            const sids = partner?.sellers?.map(item => item.seller_id) || [];
            const bids = partner?.buyers?.map(item => item.buyer_id) || [];
            const tmpObj: any = { seller_partner_id: [], buyer_partner_id: [] };
            if (item.key === 'pub_partner_id') {
              tmpObj.seller_partner_id = sids;
            }
            if (item.key === 'adv_partner_id') {
              tmpObj.buyer_partner_id = bids;
            }
            partner_id.push(tmpObj);
          });
        }
        params['partner_id'] = partner_id;
      } else {
        params[item.key] = item.value;
      }
    });

    return params;
  };

  const onSuccess = (
    data: API.BackResult<DashboardAPI.DashboardListItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.DashboardListItem>[],
    hour?: boolean
  ) => {
    data.total === -1 ? (data.total = totalCount) : setTotalCount(data.total);
    const tmp = data.data.map(item => {
      if (item.buyer_id && demandList) {
        const buyer = demandList.find((v: any) => v.buyer_id === +item.buyer_id);
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
      }
      if (item.seller_id && supplyList) {
        const seller = supplyList.find((v: { seller_id: number }) => v.seller_id === +item.seller_id);
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
      }
      item.seller_schain_complete = SchainMap[item.seller_schain_complete] || '-';
      item.country = Countries[item.country] || 'Unknown';
      item.ad_format = AdFormatToLabel[item.ad_format];
      item.ad_size = `${item.ad_width} * ${item.ad_height}`;
      item.platform = (demandCampaign.MoblieOS as any)[item.platform];

      if (isCustomDashboard && appList?.length > 0) {
        const app = appList.find((v: any) => v.bundle === item.app_bundle_id);

        if (app) {
          item.app_name = `${app.app_name}`;
        } else {
          item.app_name = `-`;
        }
      }
      if (isTenantCustomRole && placemantList?.length > 0) {
        const placement = placemantList.find((v: any) => `${v.plm_id}` === `${item.placement_id}`);
        if (placement) {
          item.placement_id = `${placement.plm_name}(${item.placement_id})`;
        }
      }
      let adv_config_qps = 0;
      let pub_config_qps = 0;
      const advQps = qpsList.filter(
        (qps: StrategyAPI.QpsListItem) => qps.level === QpsLevelType.demand && +item.buyer_id === qps.buyer_id
      );
      const pubQps = qpsList.filter(
        (qps: StrategyAPI.QpsListItem) => qps.level === QpsLevelType.supply && +item.seller_id === qps.seller_id
      );
      advQps.forEach((qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps));
      pubQps.forEach((qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps));

      if (item.region === RegionLabelMap.USE) {
        adv_config_qps = pub_config_qps = 0;
        const useAdvQps = advQps.filter((item: StrategyAPI.QpsListItem) => item.region === RegionListType.USE);
        const usePubQps = pubQps.filter((item: StrategyAPI.QpsListItem) => item.region === RegionListType.USE);
        useAdvQps.forEach((qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps));
        usePubQps.forEach((qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps));
      }
      if (item.region === RegionLabelMap.APAC) {
        adv_config_qps = pub_config_qps = 0;
        const apacAdvQps = advQps.filter((item: StrategyAPI.QpsListItem) => item.region === RegionListType.APAC);
        const apacPubQps = pubQps.filter((item: StrategyAPI.QpsListItem) => item.region === RegionListType.APAC);
        apacAdvQps.forEach((qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps));
        apacPubQps.forEach((qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps));
      }
      item.adv_config_qps = adv_config_qps;
      item.pub_config_qps = pub_config_qps;
      return item;
    });

    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<DashboardAPI.DashboardListItem>[]
  ) => {
    const { order, order_key } = getOrderAndKey();
    const lastDay = moment().format('YYYYMMDD');
    const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time: value.columns ? 1 : DateType.Null,
      start_date: lastWeek,
      end_date: lastDay,
      order_key,
      order,
      ...value
    };
    if (!checkMetrics(params.metrics)) {
      return;
    }
    fetchData({
      setLoading,
      request: getDashboardList,
      params,
      onSuccess: data => onSuccess(data, isDownloadAll, columns, params.split_time === 2)
    });
  };
  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate.endOf('day') > moment().endOf('day') ||
      currentDate < moment('20230601') ||
      currentDate < moment().subtract(6, 'months')
    );
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key
    };
  };

  const handleDownloadAll = (data: any) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmp = {
      ...params,
      order_key,
      order
    };
    fetchData({
      setLoading,
      request: downloadDashboardList,
      params: tmp,
      onSuccess: data => {
        setExportParams(tmp);
        setOpenExport(true);
        setNotifyKey(new Date().getTime().toString());
        setDownloadData(data);
      }
    });
  };

  const handleSortChange = (page: number, size: number, search: SearchResultItem[], sorter: any) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });

    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    params.isPaing = !!totalCount;
    getTableData(params);
  };

  const handleQpsClick = (e: any, params: DashboardAPI.DashboardListItem, colKey: string) => {
    const { buyer_id, seller_id } = params;
    if (colKey === 'adv_config_qps') {
      const allQps = qpsList.filter((item: StrategyAPI.QpsListItem) => item.buyer_id === +buyer_id);

      setQpsModalData(allQps);
    } else if (colKey === 'pub_config_qps') {
      const allQps = qpsList.filter((item: StrategyAPI.QpsListItem) => item.seller_id === +seller_id);
      setQpsModalData(allQps);
    } else {
      setQpsModalData([]);
    }
    setShowQpsModal(true);
  };

  const handleCancel = () => {
    setShowQpsModal(false);
  };

  const handleCloseExport = () => {
    setExportParams(undefined);
    setOpenExport(false);
  };

  const searchFormRef = useRef<FormInstance<any> | undefined>(undefined);
  const { showModal, onSaveSubscribe, form: presetForm } = usePresetConfirmModal();

  const defaultPresetOptions = useMemo(() => {
    return [
      {
        label: 'default preset',
        value: JSON.stringify(defaultSearchValue),
        key: 'default',
        extra: {
          timeRange: '-1_0'
        }
      }
    ];
  }, [defaultSearchValue]);

  // 保存预设，步骤：弹窗(showModal) -> 保存(onSaveSubscribe) -> 提交查询
  const handleSavePreset = (currentItem: PresetDropdownItem | null, store: PresetDropdownStore): Promise<string> => {
    return new Promise((resolve, reject) => {
      showModal();

      // 1. 判断是否存在默认预设
      // 从 遍历 store.items，判断 是否在 defaultPresetOptions
      const selectedKey = currentItem?.key || 'default';
      const isDefaultPreset = defaultPresetOptions.some(preset => preset.key === selectedKey);
      // 2. 为 presetForm 设置默认值
      presetForm.setFieldsValue({
        isDefaultPreset,
        saveAsNewPreset: false,
        presetName: isDefaultPreset ? '' : currentItem?.label,
        timeRange: currentItem?.extra?.timeRange,
        key: selectedKey
      });

      const unSaveSubscribe = onSaveSubscribe(values => {
        const searchFormData = searchFormRef.current?.getFieldsValue();
        if (store && searchFormData) {
          // isDefaultPreset 默认预设，直接新建预设
          // 非默认预设，通过saveAsNewPreset判断是否另存为新预设
          const isNewPreset = isDefaultPreset || values.saveAsNewPreset;
          const key = isNewPreset ? `${values.presetName}-${Date.now()}` : selectedKey;
          const newItem = {
            key,
            label: values.presetName,
            value: JSON.stringify(searchFormData),
            extra: { timeRange: values.timeRange }
          };

          if (isNewPreset) {
            store.setItems([...store.items, newItem]);
          } else {
            store.setItems(
              store.items.map(item => {
                if (item.key === selectedKey) {
                  return newItem;
                }
                return item;
              })
            );
          }

          unSaveSubscribe();
          searchFormRef.current?.setFieldsValue(deserializeReportPresetValue(newItem));
          searchFormRef.current?.submit();
          resolve(key);
        }
        unSaveSubscribe();
        resolve('');
      });
    });
  };

  // 恢复预设，并提交搜索
  const handleRestorePreset = (item: PresetDropdownItem) => {
    // 重置搜索表单
    searchFormRef.current?.resetFields();
    // 设置搜索表单的值
    searchFormRef.current?.setFieldsValue(deserializeReportPresetValue(item));
    // 提交搜索
    searchFormRef.current?.submit();
  };

  const handleDeletePreset = (_: any, newItem: PresetDropdownItem | null) => {
    if (newItem) {
      handleRestorePreset(newItem);
    }
  };

  return (
    <PageContainer flexDirection="column" options={DashboardBreadOptions} id="dashboard-page">
      <BackTable<DashboardAPI.DashboardListItem>
        pageTitle="Full Reporting"
        searchFormRef={searchFormRef}
        searchOptions={searchOptions}
        extraTools={[
          <PresetDropdownButton
            key="dropdown"
            persistenceKey="full-reporting-preset-options"
            handleSavePreset={handleSavePreset}
            handleRestorePreset={handleRestorePreset}
            handleDeletePreset={handleDeletePreset}
            defaultPresetOptions={defaultPresetOptions}
          />
        ]}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        getTableData={getTableData}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        exportAllTooltip={
          initialState?.currentUser?.tnt_id === 1075
            ? 'Export all, only supports exporting up to 50,000 rows'
            : undefined
        }
        handleDownloadAll={handleDownloadAll}
        buttonAuth={buttonAuth}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        defaultDates={defaultSearchValue.date}
        dateRangeKeys={['Today', 'Yesterday', '3 Days', '7 Days']}
        ExtraRangePickerOptions={{
          onChange: (dates: any, dateStrings: any) => {
            console.log('dateStrings', dateStrings);
          }
        }}
      />
      <NormalModal
        footer={null}
        title={'Config QPS'}
        onOk={() => {}}
        open={showQpsModal}
        onCancel={handleCancel}
        width="80vw"
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 150 }}
        className={styles.addUserModal}
      >
        <OriginalTable
          columns={ConfigQpsColumns}
          dataSource={qpsModalData}
          rowKey={'id'}
          loading={qpsLoading}
          isBtnTable={true}
          scroll={{ y: 'calc(100vh - 220px)' }}
        ></OriginalTable>
      </NormalModal>
      <ExportNotification
        open={openExport}
        onClose={handleCloseExport}
        params={exportParams}
        placement="topRight"
        notifyKey={notifyKey}
        downloadData={downloadData}
      />
    </PageContainer>
  );
};

export default Page;
