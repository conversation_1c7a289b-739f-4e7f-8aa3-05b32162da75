/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-26 14:41:28
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 14:41:30
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { AppInfoStatusCode } from '@/constants/troubleshooting';
import { appInfoModel } from '@/models';
import { AppAdsAPI } from '@/types/app-ads';

class AppInfoService {
  async getAppInfo(options: AppAdsAPI.QueryAppInfoParams) {
    const { bundle = [], lines = [] } = options;
    const params: AppAdsAPI.GetAppInfoItem[] = bundle.length
      ? bundle.map(b => ({ bundle: b, lines }))
      : [{ lines }];

    const result = await appInfoModel.getAppInfo({ params });

    if (result.code !== AppInfoStatusCode.SUCCESS) {
      // 记录错误日志，并返回空数据
      getLogger('app').error(`getAppInfo error=[${JSON.stringify(result)}]`);
      return {
        ...result,
        data: []
      };
    }

    return {
      ...result,
      // 需要对数据进行排序，根据 status_code 排序
      data: result.data.sort((a, b) => a.status_code - b.status_code)
    };
  }
}

export const appInfoService = new AppInfoService();
