/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-05 16:48:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-12 16:16:14
 * @Description:
 */
import styles from './index.less';
import { createFromIconfontCN } from '@ant-design/icons';
import type { IconFontProps } from '@ant-design/icons/lib/components/IconFont';
import { IconFontUrl } from '@/constants';
import { useAccess } from '@umijs/max';
const RixEngineFont = createFromIconfontCN({
  scriptUrl: IconFontUrl[(process.env.UMI_ENV as string) || 'prod']
});
type AuthType = {
  accessCode?: string;
  handleClick?: (e?: any) => void;
};
function IconFont({ type, accessCode, handleClick, ...rest }: IconFontProps & AuthType): JSX.Element {
  const access = useAccess();

  // 兼容，对前缀做统一处理
  const finalType = type.indexOf('rix-') === -1 ? `rix-${type}` : type;
  return finalType === 'rix-' ? (
    <></>
  ) : (
    <RixEngineFont
      onClick={accessCode && access.DisabledButton(accessCode) ? undefined : handleClick}
      {...rest}
      type={finalType}
      className={
        (accessCode && access.DisabledButton(accessCode)) || rest.disabled ? styles['disabled'] : rest.className || ''
      }
    />
  );
}
export default IconFont;
