/** @type {import('jest').Config} */
module.exports = {
  // 开启 sourcemap
  preset: 'ts-jest',
  testEnvironment: 'node',
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.(js|ts)$',
  testPathIgnorePatterns: ['/node_modules/', '/dist/'],
  moduleFileExtensions: ['js', 'ts', 'json', 'node'],
  forceExit: true, // 所有测试完成后退出
  transform: {
    '^.+\\.tsx?$': [
      'ts-jest',
      {
        sourceMap: true // 开启sourcemap映射
      }
    ]
  }
};
