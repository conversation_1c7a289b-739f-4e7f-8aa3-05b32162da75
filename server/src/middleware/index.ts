/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-15 19:28:04
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 12:20:14
 * @Description:
 */

import { getLogger } from '@/config/log4js';
import {
  existRedisKey,
  getRedisSetCountByValue,
  setRedisSetCountByValue
} from '@/db/redis';
import { commonModel } from '@/models';
import { getConfig, getUserIp, md5 } from '@/utils';
import { sendLarkAlertCard } from '@/utils/alert';
import { getCtxResult } from '@/utils/response';
import child_process from 'child_process';
import { Context, Next } from 'koa';
const { redisConfig, domainConfig } = getConfig();

const { Tnt_Host_Prefix_KEY } = redisConfig;

const version_hash = child_process
  .execSync('git log -1 --format=%h')
  .toString()
  .trim();

// 捕获全局异常
export async function resolveError(ctx: Context, next: Next) {
  try {
    return await next();
  } catch (error: any) {
    console.log('捕获全局异常 resolveError', error);
    const {
      user_id = '',
      account_name = '',
      session_id = '',
      tnt_id
    } = ctx.session!;
    const {
      cur_log_id = 0,
      log_operator,
      basic_msg,
      ori_msg,
      params_msg
    } = ctx.state;
    const ip = getUserIp(ctx);
    sendLarkAlertCard({
      title: 'Saas Std Platform Catch Error',
      data: {
        'Log ID': cur_log_id,
        IP: ip,
        'API URL': ctx.request.originalUrl,
        'Page URL': ctx.request.header.referer,
        Operator: log_operator,
        'Tenant ID': tnt_id,
        'Session ID': ctx.session!.session_id,
        'User Agent': ctx.request.headers['user-agent'],
        'x-time-zone': ctx.request.headers['x-time-zone'],
        Params: ctx.request.body,
        Error: error
      }
    });
    const err_msg = `resolveError,${basic_msg},error=[${error.message}], ${ori_msg}, ${params_msg}`;
    getLogger('error').error(err_msg);
    getLogger('error').error(params_msg);
    getLogger('error').error(
      `username="${account_name}",user_id=${user_id}, session_id="${session_id}",resolveError catch error=[${error.message}]`
    );
    const result = getCtxResult('ERROR_SYS', null);
    result.message =
      process.env.NODE_ENV === 'prod' ? result.message : error.message;
    ctx.body = result;
  }
}

// 验证上下游是否为Testing状态
export async function validTesting(ctx: Context, next: Next) {
  const testingParamKey = [
    'seller_id',
    'pub_id',
    'buyer_id',
    'authIds',
    'buyer_ids'
  ];
  const { tnt_id } = ctx.session!;
  let seller_id = null;
  let buyer_id = null;
  const formData = { ...ctx.request.body };
  const keys = Object.keys(formData);
  // eslint-disable-next-line no-restricted-syntax
  for (const key of keys) {
    if (testingParamKey.includes(key)) {
      if (Array.isArray(formData[key])) {
        formData[key] = formData[key].join(',');
      }
      if (key === 'seller_id' || key === 'pub_id') {
        seller_id = formData[key];
      }
      if (key === 'buyer_id' || key === 'authIds' || key === 'buyer_ids') {
        buyer_id = formData[key];
      }
    }
  }

  const isTesting = await commonModel.vaildTesting(tnt_id, seller_id, buyer_id);
  if (isTesting) {
    ctx.body = getCtxResult('BUYER_READ_ONLY', []);
    return;
  }
  return next();
}

export async function resolveCtxDefaultData(ctx: Context, next: Next) {
  const timeZone = ctx.headers['x-time-zone'] || 'Etc/UTC';

  const { tnt_id = 0, user_id = 0 } = ctx.session!;
  const role_id = ctx.state.curUser ? ctx.state.curUser?.role_id : 0;
  const formData = {
    ...ctx.request.body,
    tnt_id,
    cur_user_id: user_id,
    cur_time_zone: timeZone,
    cur_role_id: role_id // 当前登录用户的角色id
  };
  ctx.request.body = formData;
  return await next();
}

export async function resolveRefreshPage(ctx: Context, next: Next) {
  const client_version_hash = ctx.request.headers['x-version-hash'] as string;

  if (client_version_hash && client_version_hash !== version_hash) {
    ctx.status = 205;
    ctx.body = {
      code: 205,
      message: 'Refresh Page',
      data: ''
    };
  } else {
    await next();
  }
}

// 日志拦截中间件 记录当前参数 增删改使用
export const resolveLogger = async (ctx: Context, next: Next) => {
  const formData = { ...ctx.request.body };
  // 原始数据
  const ori_data =
    typeof formData.ori_data === 'string'
      ? formData.ori_data
      : JSON.stringify(formData.ori_data || {});
  // 提交的参数
  const params = { ...formData };
  // 删除原始数据 不重复记录
  // eslint-disable-next-line no-unused-expressions
  params.ori_data && delete params.ori_data;
  const { account_name = '', user_id = 0 } = ctx.state.curUser || {};
  // 操作人
  const operator = `${account_name}(${user_id})`;

  // 调用接口的页面
  const pageUrl = ctx.request.header.referer || '';
  // 参数信息
  const basic_msg = `resolveLogger,ua=[${
    ctx.request.headers['user-agent'] || ''
  }],pageUrl=${pageUrl}`;
  const params_msg = `params=[${JSON.stringify(params)}]`;
  const ori_msg = `ori_data=[${ori_data}]`;
  getLogger('app').info(basic_msg);
  getLogger('app').info(params_msg);
  getLogger('app').info(ori_msg);
  // 统一日志信息 错误日志那可以打印 不用切文件查看
  ctx.state.log_operator = operator;
  ctx.state.basic_msg = basic_msg;
  ctx.state.params_msg = params_msg;
  ctx.state.ori_msg = ori_msg;
  await next();
};

export async function resolveCsDomain(ctx: Context, next: Next) {
  const Domain = domainConfig;
  const env = process.env.NODE_ENV || '';

  if (!['prod', 'test', 'hotfix'].includes(env)) {
    ctx.state.cs_domain = `allowed.${Domain.console}`;
  } else {
    ctx.state.cs_domain = ctx.request.host || '';
  }
  getLogger('app').info(
    `resolveCsDomain env=${env} cs_domain=${ctx.state.cs_domain}`
  );
  return await next();
}

export async function resolveHostPrefix(ctx: Context, next: Next) {
  if (['test', 'prod'].includes(process.env.NODE_ENV as string)) {
    const host_key = md5(`${redisConfig.platform_key}_${Tnt_Host_Prefix_KEY}`);
    const exists = await existRedisKey(host_key);
    if (!exists) {
      const data = await commonModel.getAllTenantHostPrefix();
      const host_prefix = data?.map((item: any) => item.host_prefix);
      await setRedisSetCountByValue(host_key, host_prefix || []);
    }
    const host_prefix = ctx.request.host
      .replace(/(http:\/\/|https:\/\/)/g, '')
      .split('.')[0];
    const cs_domain = ctx.request.host || '';
    const count = await getRedisSetCountByValue(host_key, host_prefix);
    const domain_count = await getRedisSetCountByValue(host_key, cs_domain);
    if (count !== 1 && domain_count !== 1) {
      ctx.status = 404;
      ctx.body = 'Not Found';
    } else {
      return await next();
    }
  } else {
    return await next();
  }
}
