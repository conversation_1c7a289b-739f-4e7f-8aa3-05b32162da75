class StorageUtils {
  private static readonly UNSUPPORTED_TYPES = ['function', 'symbol', 'bigint'];

  /**
   * 检查值的类型是否支持存储
   * @param value 要检查的值
   * @returns 是否支持存储
   */
  private static checkValueType(value: any): boolean {
    if (value === undefined) {
      console.warn('[StorageUtils] Warning: Storing undefined values is not recommended, use null instead');
      return false;
    }

    const valueType = typeof value;
    if (this.UNSUPPORTED_TYPES.includes(valueType)) {
      console.warn(`[StorageUtils] Warning: Cannot store value of type ${valueType}`);
      return false;
    }

    return true;
  }

  /**
   * 序列化值
   * @param value 要序列化的值
   * @returns 序列化后的字符串
   */
  private static serialize(value: any): string {
    try {
      if (typeof value === 'object') {
        // 检查是否包含循环引用
        JSON.stringify(value);
        return JSON.stringify({
          __type: 'json',
          data: value
        });
      }
      return String(value);
    } catch (error) {
      console.warn('[StorageUtils] Warning: Value cannot be properly serialized', error);
      return String(value);
    }
  }

  /**
   * 反序列化值
   * @param value 要反序列化的字符串
   * @returns 反序列化后的值
   */
  static deserialize(value: string): any {
    try {
      const parsed = JSON.parse(value);
      if (parsed && parsed.__type === 'json') {
        return parsed.data;
      }
      return parsed;
    } catch {
      return value;
    }
  }

  /**
   * 设置 localStorage
   * @param key 键
   * @param value 值
   * @returns 是否设置成功
   */
  static set(key: string, value: any): boolean {
    try {
      if (!this.checkValueType(value)) {
        return false;
      }

      const serializedValue = this.serialize(value);
      localStorage.setItem(key, serializedValue);
      return true;
    } catch (error) {
      if (error instanceof Error) {
        // 检查是否是存储空间不足错误
        if (error.name === 'QuotaExceededError' || error.name === 'NS_ERROR_DOM_QUOTA_REACHED') {
          console.warn('[StorageUtils] Warning: localStorage quota exceeded');
        } else {
          console.warn('[StorageUtils] Warning: Storage operation failed', error);
        }
      }
      return false;
    }
  }

  /**
   * 获取 localStorage
   * @param key 键
   * @param defaultValue 默认值
   */
  static get<T>(key: string, defaultValue?: T): T | null {
    try {
      const value = localStorage.getItem(key);
      if (value === null) {
        return defaultValue ?? null;
      }
      return this.deserialize(value);
    } catch (error) {
      console.warn(`[StorageUtils] Warning: Error reading value for key "${key}"`, error);
      return defaultValue ?? null;
    }
  }

  /**
   * 删除 localStorage
   * @param key 键
   */
  static remove(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn(`[StorageUtils] Warning: Error removing key "${key}"`, error);
    }
  }

  /**
   * 清空 localStorage
   */
  static clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.warn('[StorageUtils] Warning: Error clearing storage', error);
    }
  }

  /**
   * 获取 localStorage 长度
   */
  static length(): number {
    try {
      return localStorage.length;
    } catch (error) {
      console.warn('[StorageUtils] Warning: Error getting storage length', error);
      return 0;
    }
  }

  /**
   * 根据索引获取 key
   * @param index 索引
   */
  static key(index: number): string | null {
    try {
      return localStorage.key(index);
    } catch (error) {
      console.warn(`[StorageUtils] Warning: Error getting key at index ${index}`, error);
      return null;
    }
  }

  /**
   * 判断是否存在某个 key
   * @param key 键
   */
  static has(key: string): boolean {
    try {
      return localStorage.getItem(key) !== null;
    } catch (error) {
      console.warn(`[StorageUtils] Warning: Error checking existence of key "${key}"`, error);
      return false;
    }
  }

  /**
   * 获取当前 localStorage 使用的大小（以字节为单位）
   */
  static getSize(): number {
    try {
      let size = 0;
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          size += key.length + (localStorage.getItem(key)?.length || 0);
        }
      }
      return size;
    } catch (error) {
      console.warn('[StorageUtils] Warning: Error calculating storage size', error);
      return 0;
    }
  }
}

export { StorageUtils };
