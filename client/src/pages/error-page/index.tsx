/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-24 15:53:00
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-24 17:51:00
 * @Description:
 */

import React, { PureComponent } from 'react';

export default class ErrorBound extends PureComponent<any, any> {
  componentDidCatch(error: any, errorInfo: any) {
    console.log('error', error);
    console.log('info', errorInfo);
    const str = error && String(error).toLowerCase() || '';
    // 两种错误
    if (str.indexOf('loading chunk') !== -1 || str.indexOf('loading css chunk') !== -1) {
      this.handleRefresh();
    }
  }

  handleRefresh() {
    window.location.reload();
  }

  render() {
    return this.props.children;
  }
}
