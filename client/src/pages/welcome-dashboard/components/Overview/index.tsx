/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-12-12 17:20:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-21 11:55:21
 * @Description:
 */
import RixEngineFont from '@/components/RixEngineFont';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { TimeZoneMap } from '@/constants/base/time-zone';
import { OverviewColorMap, OverviewOptions } from '@/constants/ree-ai/main-board';
import { getOverview } from '@/services/ai-board';
import { formatNumberToUnit } from '@/utils';
import { Area } from '@ant-design/charts';
import { useModel, useRequest } from '@umijs/max';
import { Alert } from 'antd';
import moment from 'moment';
import React, { useMemo } from 'react';
import Card from '../Card';
import { getConfig } from './helper';
import styles from './index.less';

interface OverviewProps {
  data?: BoardAPI.OverviewItem[];
}

const Overview: React.FC<OverviewProps> = () => {
  const { initialState } = useModel('@@initialState');
  const { data, loading } = useRequest(getOverview, {
    formatResult: res => res?.data?.[0] || {}
  });

  // 当前时区限制的时间(UTC 1:30 转换到本地时区的时间)
  const { curLimitTime, isLessThanLimitTime } = useMemo(() => {
    // 如：当前时区：Etc/GMT+9 -》 UTC-9
    const curTimeZone = TimeZoneMap[localStorage.getItem(`time-${initialState?.currentUser?.user_id}`)!] || 'UTC+0';

    // 获取当前时区的偏移量（小时）
    const tzOffset = parseInt(curTimeZone.replace('UTC', '')) || 0;

    // 获取当前本地时间
    const now = moment();

    // 将UTC 1:30转换为本地时间后，与当前本地时间比较
    const localTimeLimit = moment()
      .startOf('day')
      .add(1, 'hours')
      .add(30, 'minutes')
      .add(tzOffset, 'hours');

    // 格式环UTC 1:30对应的本地时间点
    const localLimitTime = localTimeLimit.format('HH:mm');

    // 判断当前本地时间是否小于限制时间
    const isLessThan = now.isBefore(localTimeLimit);

    return {
      curLimitTime: localLimitTime,
      isLessThanLimitTime: isLessThan
    };
  }, [initialState?.currentUser?.user_id]);

  // 判断是否展示数据查询警告的提示
  const showDataOverviewWarning = useMemo(() => {
    if (data && Object.keys(data).length === 0 && !loading && isLessThanLimitTime) {
      return true;
    }
    return false;
  }, [data, loading, isLessThanLimitTime]);

  return (
    <Card
      title={`Growth Rate of Today  (Hour Level) Update at ${data?.['update_time'] || '--:--'} (${
        TimeZoneMap[localStorage.getItem(`time-${initialState?.currentUser?.user_id}`)!] || 'UTC+0'
      })`}
      loading={loading}
    >
      {showDataOverviewWarning && (
        <Alert
          message="No data available"
          description={`There is no data available at present, please query the data after ${curLimitTime}(${
            TimeZoneMap[localStorage.getItem(`time-${initialState?.currentUser?.user_id}`)!] || 'UTC+0'
          }).`}
          type="warning"
          style={{ marginBottom: 16, borderRadius: 6 }}
          showIcon
        />
      )}
      <div className={styles['container']}>
        {OverviewOptions.map((item, index) => {
          const config = getConfig(index, data);
          let curConfig = { ...config };
          if (item.number === 'ecpr') {
            curConfig.tooltip = {
              ...curConfig.tooltip,
              customContent: (title, data) => {
                const value = formatNumberToUnit(data?.[0]?.data.value || 0, 2, 2);
                return `<div >
                  <div style="margin-bottom: 8px">${title}</div>
                  <div>${value}</div>
                </div>`;
              }
            };
          }

          const formatNumber = (data?.[item.number] || '-') as string;
          const percentage = (data?.[item.percentage] || '-') as number | string;

          const percentageColor = typeof percentage === 'number' ? (percentage < 0 ? '#1CAF34' : '#F54B31') : '';

          return (
            <div
              key={index}
              className={styles['item-container']}
              style={{ backgroundColor: `${OverviewColorMap[index]}` }}
            >
              <div className={styles['left']}>
                <div className={styles['title']}>{item.title}</div>
                <div className={styles['number']}>
                  <span>{`${item.isDollar ? '$' : ''}`}</span>
                  <HoverToolTip title={formatNumber} placement="top" maxWidth={120}>
                    <span>{formatNumber}</span>
                  </HoverToolTip>
                </div>
                <div
                  className={styles['percentage']}
                  style={{
                    color: percentageColor
                  }}
                >
                  <RixEngineFont
                    type={typeof percentage === 'number' ? (percentage < 0 ? 'rix-decline' : 'rix-increase') : ''}
                    style={{
                      fontSize: 12,
                      color: percentageColor,
                      marginRight: 4
                    }}
                  ></RixEngineFont>
                  {percentageColor ? `${percentage}%` : '-'}
                </div>
              </div>
              <div className={styles['right']}>
                <Area {...curConfig} />
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

export default Overview;
