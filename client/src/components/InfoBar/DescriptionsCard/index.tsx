/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-04 14:50:36
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-18 13:00:21
 * @Description:
 */
import React, { useMemo } from 'react';
import { Descriptions, Collapse, Tooltip, Typography } from 'antd';
import { CaretRightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import styles from './index.less';
import OriginalTable from '@/components/Table/OriginalTable';
import RixEngineFont from '@/components/RixEngineFont';
import { ColumnProps } from 'antd/es/table';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { useAccess } from '@umijs/max';
const { Panel } = Collapse;
const { Item } = Descriptions;
const { Paragraph } = Typography;

export type DescriptionsCardColumnsProps<T> = ColumnProps<T> & {
  dataIndex: keyof T;
  title?: string;
  render?: (text: any, row: T, maxWidth?: number) => React.ReactNode;
  tooltip?: (text: any, row: T) => React.ReactNode;
  showCopy?: boolean; // 列名是否显示复制图标
  copyText?: (row: T) => string; // 复制的文本
  access?: string; // 权限
};

export type DescriptionsCardProps<T, K> = {
  columns: DescriptionsCardColumnsProps<T>[];
  dataSource: T;
  title?: string | React.ReactNode; // title不给时,showCopy/tooltip不生效,空字符串时, showCopy/tooltip生效
  collapsed?: boolean;
  type?: 'default' | 'collapse' | 'whole-line' | 'table' | 'collapse-table' | 'collapse-line';
  // default collapse whole-line表示独占一行 collapse-line折叠内容只为文本
  rowKey?: string;
  tableData?: any[];
  rowNum?: number;
  maxWidth?: number;
  showCopy?: boolean;
  access?: string; // 权限
  copyText?: (row: T) => string; // 复制的文本
};

function DescriptionsItem<T>({
  columns,
  dataSource,
  title,
  rowNum = 2,
  maxWidth,
  ...rest
}: DescriptionsCardProps<T, keyof T>): JSX.Element {
  const access = useAccess();
  return (
    <Descriptions title={title} size="small" layout="vertical" column={rowNum}>
      {dataSource &&
        columns &&
        columns.map((el, index) => {
          const text: any = dataSource[el.dataIndex] || '-';
          const tooltip = el.tooltip ? el.tooltip(text, dataSource) : '';
          const content = (
            <div
              style={{
                pointerEvents: el.access && !access[el.access] ? 'none' : 'auto',
                overflow: 'hidden',
                maxWidth: maxWidth,
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {el.render
                ? el.render(text, dataSource, maxWidth)
                : (
                  <HoverToolTip title={text} maxWidth={maxWidth} placement="bottom">
                    <span className={styles['ellipsis']} style={{ maxWidth }}>
                      {text}
                    </span>
                  </HoverToolTip>
                ) || '-'}
            </div>
          );
          const key = `${String(el.dataIndex)}_${index}`;
          const label = Object.hasOwn(el, 'title') ? (
            <>
              <span style={{ wordBreak: 'break-all' }}>{el.title}</span>
              {tooltip ? (
                <Tooltip title={tooltip}>
                  <QuestionCircleOutlined style={{ paddingLeft: 3, cursor: 'pointer' }} />
                </Tooltip>
              ) : null}
              {el.showCopy && (
                <Paragraph
                  copyable={{
                    tooltips: false,
                    text: el.copyText ? el.copyText(dataSource) : text,
                    icon: <RixEngineFont type="rix-copy" style={{ fontSize: 16 }} />
                  }}
                  style={{ marginBottom: 0 }}
                ></Paragraph>
              )}
            </>
          ) : undefined;

          return (
            <Item
              key={key}
              label={label}
              labelStyle={{
                color: '#8D9799'
              }}
            >
              {content}
            </Item>
          );
        })}
    </Descriptions>
  );
}

function DescriptionsLineItem<T>({ columns, dataSource, ...rest }: DescriptionsCardProps<T, keyof T>): JSX.Element {
  const access = useAccess();
  return (
    <div>
      {dataSource &&
        columns &&
        columns.map((el, index) => {
          const text: any = dataSource[el.dataIndex] || '-';
          const tooltip = el.tooltip ? el.tooltip(text, dataSource) : '';
          const content = (
            <div
              style={{
                pointerEvents: el.access && !access[el.access] ? 'none' : 'auto'
              }}
            >
              {el.render ? el.render(text, dataSource) : text || '-'}
            </div>
          );

          return (
            <div key={index} style={{ marginBottom: 12 }}>
              <p style={{ fontSize: 14, color: '#000000d9', marginBottom: 2, lineHeight: '22px' }}>
                {el.title}
                {tooltip ? (
                  <Tooltip title={tooltip}>
                    <QuestionCircleOutlined />
                  </Tooltip>
                ) : null}
                {!rest.type?.includes('collapse') && el.showCopy && (
                  <Paragraph
                    copyable={{
                      tooltips: false,
                      text: el.copyText ? el.copyText(dataSource) : text,
                      icon: <RixEngineFont type="rix-copy" style={{ fontSize: 16 }} />
                    }}
                    style={{ marginBottom: 0 }}
                  ></Paragraph>
                )}
              </p>
              <span style={{ color: '#606266' }}>{content}</span>
            </div>
          );
        })}
    </div>
  );
}

// 单行文本折叠
function DescriptionsCollapseLineItem<T>({
  columns,
  dataSource,
  ...rest
}: DescriptionsCardProps<T, keyof T>): JSX.Element {
  if (!dataSource || !columns) return <></>;
  const single = columns[0];
  const text: any = dataSource[single.dataIndex] || '-';
  const content = single.render ? single.render(text, dataSource) : text || '-';
  return (
    <div style={{ marginBottom: 12 }}>
      <span style={{ color: '#606266' }}>{content}</span>
    </div>
  );
}
export default function DescriptionsCard<T extends object>(props: DescriptionsCardProps<T, keyof T>): JSX.Element {
  const { collapsed, title, columns, rowKey, rowNum, maxWidth = 0, showCopy, copyText, tableData = [] } = props;
  const access = useAccess();
  const type = props.type || 'default';

  const finalColumns = useMemo(() => {
    return columns.filter((v: any) => {
      return (!v.access || access[v.access as keyof typeof access]);
    });
  }, [columns]);

  const DescItem =
    type !== 'whole-line' ? (
      <DescriptionsItem
        {...props}
        columns={finalColumns}
        title={type === 'collapse' ? '' : title}
        rowNum={rowNum}
        maxWidth={type === 'collapse' ? maxWidth - 10 : maxWidth}
      />
    ) : (
      <DescriptionsLineItem {...props} columns={finalColumns} title={title} maxWidth={maxWidth} />
    );
  return type === 'collapse' ? (
    <Collapse
      defaultActiveKey={collapsed ? [] : ['1']}
      ghost
      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
      className={styles['descriptions-card-collapse']}
    >
      <Panel header={<>{title}</>} key="1">
        {DescItem}
      </Panel>
    </Collapse>
  ) : type === 'table' ? (
    <OriginalTable<T>
      loading={false}
      dataSource={tableData}
      columns={finalColumns}
      rowKey={rowKey || 'index'}
      scroll={{ y: 'auto' }}
    />
  ) : type === 'collapse-table' ? (
    <Collapse
      defaultActiveKey={collapsed ? [] : ['1']}
      ghost
      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
      className={styles['descriptions-card-collapse']}
    >
      <Panel header={title} key="1">
        <OriginalTable<T>
          loading={false}
          dataSource={tableData}
          columns={finalColumns}
          rowKey={rowKey || 'index'}
          scroll={{ y: 'auto' }}
        />{' '}
      </Panel>
    </Collapse>
  ) : type === 'collapse-line' ? (
    <Collapse
      defaultActiveKey={collapsed ? [] : ['1']}
      ghost
      expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
      className={styles['descriptions-card-collapse']}
    >
      <Panel
        header={
          <div className={styles['collapse-line-header']}>
            {<span>{title}</span>}
            {showCopy && (
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: copyText ? copyText(props.dataSource) : '',
                  icon: <RixEngineFont type="rix-copy" style={{ fontSize: 16 }} />
                }}
                style={{ marginBottom: 0 }}
              ></Paragraph>
            )}
          </div>
        }
        key="1"
      >
        {DescriptionsCollapseLineItem(props)}
      </Panel>
    </Collapse>
  ) : (
    DescItem
  );
}
