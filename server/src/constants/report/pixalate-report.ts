/*
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON>@algorix.co
 * @Date: 2023-10-10 10:20:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-06 11:54:13
 * @Description:
 */

export const PixalateDimensions = [
  'day',
  'month',
  'fraud_type',
  'buyer_id',
  'seller_id',
  'app_bundle_id',
  'country',
  'publisher_id'
];

export const PixalateOrderKey = [
  'day',
  'month',
  'buyer_id',
  'seller_id',
  'gross_tracked_ads',
  'sivt_imp_rate',
  'givt_imp_rate',
  'ivt_rate'
];

export const PixalateMetricOrderKey = [
  'gross_tracked_ads',
  'sivt_imp_rate',
  'givt_imp_rate',
  'ivt_rate'
];
