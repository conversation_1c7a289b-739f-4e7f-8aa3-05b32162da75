/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-19 10:52:17
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:24:33
 * @Description:
 */
import Router from 'koa-router';
import { stgControllerV2 } from '@/controllers';

// const { addInterface, updateInterface, getAllInterface, deleteInterface } =
//   interfaceController;
const router = new Router({ prefix: '/stgv2' });

const routers = router
  .post('/addStgChain', stgControllerV2.addStgChain)
  .post('/getStgChainList', stgControllerV2.getStgChainList)
  .post('/updateStgChain', stgControllerV2.updateStgChain);

export default routers;
