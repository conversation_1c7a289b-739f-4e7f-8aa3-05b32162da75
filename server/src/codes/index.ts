/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-22 17:12:20
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-21 18:04:28
 * @FilePath: /saas.rix-platform/server-ts/src/codes/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const CodeSystem = {
  ERROR_SYS: -1,
  SUCCESS: 0,
  PARAMS_INVALID: -2,
  NOT_FOUND: -3,
  REQUEST_TIMEOUT: -4,
  PARAMS_LIMIT: -5,
  DOWNLOAD_ERROR: -6
};

const MessageSystem = {
  ERROR_SYS: 'system error',
  SUCCESS: 'success',
  PARAMS_INVALID: 'params invalid',
  NOT_FOUND: '404 not found',
  REQUEST_TIMEOUT: 'request time out',
  PARAMS_LIMIT: 'excessive parameter',
  DOWNLOAD_ERROR:
    'download faild, please try again or go to the exported report page to get the reason'
};

const CodeBeAPI = {
  API_TOKEN_INVALID: 1000,
  REQUEST_COUNT_LIMIT: 1001,
  CS_DOMAIN_NOT_ALLOWED: 1002
};

const MessageBeAPI = {
  API_TOKEN_INVALID: `'x-user-id' or 'x-authorization' is invalid.`,
  REQUEST_COUNT_LIMIT:
    'The number of requests for the day has reached the upper limit',
  CS_DOMAIN_NOT_ALLOWED: 'cs_domain is not allowed'
};

const CodeUser = {
  FAIL_USER_NAME_OR_PASSWORD_ERROR: 1101,
  FAIL_USER_NO_LOGIN: 1102,
  FAIL_USER_NO_PERMISSION: 1103,
  FAIL_USER_REST_PASSWORD_ERROR: 1104,
  CURRENT_PASSWORD_ERROR: 1105,
  ACCOUNT_NAME_EXISTS: 1106,
  USER_COUNT_LIMIT: 1107,
  FAIL_USER_NO_AUTH: 1108,
  PERMISSION_USER_CHANGE: 1109,
  SEND_EMAIL_ERROR: 1110,
  FAIL_USER_LOGIN_REPEAT: 1111,
  PWD_ERROR: 1112,
  FAIL_USER_SESSION_EXPIRED: 1113,
  USER_TOKEN_INVALID: 1114,
  // 接口开放 校验ip的错误状态
  REQUEST_IP_INVALID: 1115
};

const MessageUser = {
  FAIL_USER_NAME_OR_PASSWORD_ERROR: 'Wrong credentials. Try again.',
  FAIL_USER_NO_LOGIN: 'User is not logIn',
  FAIL_USER_REST_PASSWORD_ERROR: 'Reset password fail',
  FAIL_USER_NO_PERMISSION: 'You do not have the permissions to access it.',
  CURRENT_PASSWORD_ERROR: 'Current password invalid',
  ACCOUNT_NAME_EXISTS: 'Account Name has already exist',
  USER_COUNT_LIMIT: 'Allow adding up to 20 users',
  FAIL_USER_NO_AUTH: 'You do not have the permissions to access it.',
  PERMISSION_USER_CHANGE: 'You permission has change, please log in again',
  SEND_EMAIL_ERROR: 'Send email error',
  FAIL_USER_LOGIN_REPEAT:
    'Your account has been logged in in another place, please check first',
  PWD_ERROR: 'current password error',
  FAIL_USER_SESSION_EXPIRED: 'Your session has expired, please login again',
  USER_TOKEN_INVALID: 'Your token is invalid, please check your token',
  REQUEST_IP_INVALID: 'Your IP is not allowed to access, please check your IP'
};

const CodeSupply = {
  SELLER_NAME_EXISTS: 2101,
  SELLER_USER_NAME_EXISTS: 2102
};

const MessageSupply = {
  SELLER_NAME_EXISTS: 'Publisher Name has already exist',
  SELLER_USER_NAME_EXISTS: 'Publisher Account Name has already exist'
};

const CodeDemand = {
  BUYER_NAME_EXISTS: 2201,
  BUYER_READ_ONLY: 2202,
  BUYER_USER_NAME_EXISTS: 2203
};

const MessageDemand = {
  BUYER_NAME_EXISTS: 'Advertiser Name has already exist',
  BUYER_READ_ONLY: 'Advertiser or Publisher is in testing, cannot be modified',
  BUYER_USER_NAME_EXISTS: 'Advertiser Account Name has already exist'
};

const CodeStrategy = {
  BL_WL_EXISTS: 2301,
  CAP_EXISTS: 2302,
  QPS_EXISTS: 2303,
  QPS_DUPLICATED_WHEN_PAUSED: 2304,
  FLOOR_EXISTS: 2305,
  PIXALATE_EXISTS: 2306,
  UPDATE_PIXALATE: 2307,
  AB_TEST_EXISTS: 2308,
  AB_TEST_OTHER_EXIST: 2309,
  PMP_DEAL_EXISTS: 2310,
  PMP_DEAL_REPEAT: 2311,
  PMP_INTERNAL_EXISTS: 2312,
  POLICY_KEY_EXISTS: 2313
};

const MessageStrategy = {
  BL_WL_EXISTS:
    'The combination of publisher 、advertiser and list type you selected is already exists',
  CAP_EXISTS:
    'The combination of publisher and advertiser you selected is already exists',
  QPS_EXISTS: 'The combination you selected is already exists',
  QPS_DUPLICATED_WHEN_PAUSED: 'On duplicated key update',
  FLOOR_EXISTS: 'The floor is already exists',
  PIXALATE_EXISTS: 'The config is already Exists, please edit it',
  UPDATE_PIXALATE: 'On duplicated pixalate config update',
  AB_TEST_EXISTS: 'The a/b test group is already exists',
  AB_TEST_OTHER_EXIST:
    'There are another group of experiments that are currently undergoing interference results at the same time and are not allowed to be submitted.',
  PMP_DEAL_EXISTS: 'PMP deal name or deal id is already exists',
  PMP_DEAL_REPEAT: 'The combination of PMP Inventory and advertiser and bidfloor is already exists',
  PMP_INTERNAL_EXISTS: 'PMP Inventory name is already exists',
  POLICY_KEY_EXISTS: 'Policy key already exists'
};

const CodeDeveloper = {
  PLACEMENT_EXISTS: 2401,
  APP_NAME_EXISTS: 2402,
  APP_BUNDLE_EXISTS: 2403,
  MAX_TAG_APP_LIMIT: 2404
};

const MessageDeveloper = {
  PLACEMENT_EXISTS: 'The unit name you input is already exists in current app',
  APP_NAME_EXISTS:
    'The app name and platform you input in current publisher is already exists',
  APP_BUNDLE_EXISTS:
    'The bundle you input in current publisher is already exists',
  MAX_TAG_APP_LIMIT:
    'The number of apps you added for maxtag publisher has reached the limit'
};

const CodePermission = {
  MENU_PATH_EXISTS: 2501,
  OPERATION_CODE_EXISTS: 2502,
  ROLE_NAME_EXISTS: 2503,
  PERMISSION_NAME_EXISTS: 2504,
  ROUTE_PATH_EXISTS: 2505,
  ROLE_HAS_USER: 2506
};
const MessagePermission = {
  MENU_PATH_EXISTS: 'Path has already exists, please change another one',
  OPERATION_CODE_EXISTS:
    'Authorization Sign has already exists, please change another one',
  ROLE_NAME_EXISTS: 'Role Name has already exists, please change another one',
  PERMISSION_NAME_EXISTS:
    'Permission Name has already exists, please change another one',
  ROUTE_PATH_EXISTS: 'Route Path has already exists, please change another one',
  ROLE_HAS_USER: 'There are users under the changed role, cannot be deleted'
};

const CodeReeAi = {
  TRAFFIC_REQUESTED: 2601
};
const MessageReeAi = {
  TRAFFIC_REQUESTED: 'Traffic has been requested, please reload the page'
};

const CodePartner = {
  PARTNER_NAME_EXISTS: 2701
};

const MessagePartner = {
  PARTNER_NAME_EXISTS: 'Name has already exists, please change another one'
};

const CodeTransparency = {
  STG_CHAIN_EXISTS: 2801
};

const MessageTransparency = {
  STG_CHAIN_EXISTS:
    'The combination of  Domain, Type is already Exists, please edit it'
};

export const CodeSignature = {
  SIGNATURE_ERROR: 3001,
  SIGNATURE_MISSING: 3002
};

export const MessageSignature = {
  SIGNATURE_MISSING: 'Signature is missing, please refresh your page',
  SIGNATURE_ERROR: 'Your Signature is invalid'
};

const CodeSwitchAccount = {
  SWITCH_ACCOUNT_ERROR: 3101
};

const MessageSwitchAccount = {
  SWITCH_ACCOUNT_ERROR: 'Switch Account Error'
};

export const Code = {
  ...CodeSystem,
  ...CodeUser,
  ...CodeSupply,
  ...CodeDemand,
  ...CodeStrategy,
  ...CodeDeveloper,
  ...CodePermission,
  ...CodeReeAi,
  ...CodePartner,
  ...CodeBeAPI,
  ...CodeTransparency,
  ...CodeSignature,
  ...CodeSwitchAccount
};
export const Message = {
  ...MessageSystem,
  ...MessageUser,
  ...MessageSupply,
  ...MessageDemand,
  ...MessageStrategy,
  ...MessageDeveloper,
  ...MessagePermission,
  ...MessageReeAi,
  ...MessagePartner,
  ...MessageBeAPI,
  ...MessageTransparency,
  ...MessageSignature,
  ...MessageSwitchAccount
};
export type CodeProps = keyof typeof Code;
export default {};
