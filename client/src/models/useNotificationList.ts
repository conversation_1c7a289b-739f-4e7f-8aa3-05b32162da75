/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-19 10:25:25
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-19 10:31:21
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getNotificationList } from '@/services/api';
type NotificationProps = {
  dataSource: CommonAPI.NotificationListItem[];
  reload: () => Promise<any>;
  loading: boolean;
};
export default function useNotificationPropsList(): NotificationProps {
  const { data, run, loading, error } = useCustomRequest(getNotificationList, {
    cacheKey: 'notification-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
