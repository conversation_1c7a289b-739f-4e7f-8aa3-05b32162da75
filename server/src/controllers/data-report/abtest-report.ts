/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-03-05 10:36:15
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-21 19:25:21
 * @Description:
 */

import { Code, Message } from '@/codes';
import { AbTestReportScheme } from '@/schema/report/abtest-report';
import { abTestReportService } from '@/services/data-report/abtest-report';
import { ABTestAPI } from '@/types/ab-test';
import { getUserRole } from '@/utils/report/getBQLabel';
import { getCtxBackResult } from '@/utils/response';
import { validateBody } from '@/utils/validate-params';
import { Context } from 'koa';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class ABTestReportCtrl implements ABTestAPI.ABTestReportCtrl {
  @validateBody(AbTestReportScheme)
  async getABTestReportList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const userType = ctx.state.curUser?.type;
    const list = await abTestReportService.getABTestReportList(formData, {
      tag: api_url,
      tenantId: formData.tnt_id,
      userRole: getUserRole(userType)
    });

    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }

    ctx.body = result;
  }
}

export const abTestReportCtrl = new ABTestReportCtrl();
