/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-28 20:33:12
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-21 11:51:23
 * @Description:
 */

import { LabelGenerationParams } from '@rixfe/rix-tools';
import { Context } from 'koa';
export declare namespace BoardAPI {
  type MetricsItem = {
    date: string;
    revenue: number;
    profit: number;
    request: number;
    impression: number;
  };
  type OverviewItem = {
    revenue: string | number;
    revenue_increase: number;
    profit: string | number;
    profit_increase: number;
    request: string | number;
    request_increase: number;
    // impression: string | number;
    // impression_increase: number;
    ecpr: string | number;
    ecpr_increase: number;
    hours_data?: MetricsItem[];
    update_time: string;
  };

  type TopCountryItem = {
    country: string;
    revenue: number;
    top_tnts: {
      tnt: string;
      revenue: number;
      country?: string;
    }[];
  };
  type TopCountryAdFormatItem = {
    country: string;
    revenue: number;
    sl_revenue: number;
    ad_format: number;
    ad_size: string;
    top_ad_formats: TopAdFormatItem[];
  };
  type SupplyDemandItem = {
    seller_id: string;
    seller_name: string;
    buyer_id: string;
    buyer_name: string;
    revenue: number;
    sl_revenue: number;
    ad_format: string;
    country: string;
    top_ad_formats?: {
      ad_format: string;
      revenue: number;
    }[];
    top_countries?: {
      country: string;
      revenue: number;
    }[];
  };
  type SevenDaysCountryItem = {
    country: string;
    revenue: number;
    date: string;
  };

  type TopAdFormatItem = {
    ad_format: string;
    revenue: number;
    country: string;
    top_ad_sizes: {
      ad_size: string;
      revenue: number;
      ad_format: string;
    }[];
  };
  type CTVData = {
    date: string;
    revenue: number;
  };
  type TenantRevenueItem = {
    tnt_id: string;
    revenue: number;
  };
  type EcpmAndEcprItem = {
    date: string;
    ecpm: number;
    ecpr: number;
    revenue: number;
    ad_format: string;
  };

  type BudgetAndTrafficItem = {
    country: string;
    ecpr: number;
    ad_format: string;
    buyer_id: number;
    revenue: number;
  };

  type requestTrafficParams = {
    tnt_id: number;
    ad_format: number;
    country: string;
    ext_1: number;
    user_id: number;
    buyer_ids: number[];
    ext_2: number;
  };
  type AddNotificationParams = {
    title: string;
    content: string;
    msg: string; // msg表使用
    key: string; // msg表使用
    rule_id: number; // 规则
    tnt_id: number; // 当前租户
    tnt_ids: number[]; // 需要插入哪些租户
    user_id: number; // 操作人
    ext_1: number; // 是否发送ads.txt
    ext_2: number; // 流量类型 site or in-app
  };
  type isExistLogParams = {
    tnt_id: number;
    buyer_ids: number[];
    ad_format: number;
    country: string;
    date: string;
  };
  type requestTrafficFormItem = {
    id: number;
    content: string;
    ext_1: string;
    created_time: string;
  };
  interface BoardModel {
    getNewOverview(
      sql: { today: string; yesterday: string },
      label: LabelGenerationParams
    ): Promise<MetricsItem[][]>;
    getTodayHours(tnt_id: number, label: LabelGenerationParams): Promise<string>;
    getTopCountry(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams,
      limit?: number
    ): Promise<TopCountryAdFormatItem[]>;
    getSellerDemand(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams
    ): Promise<SupplyDemandItem[]>;
    getBudgetAndTraffic({
      cur_hour,
      cur_time_zone,
      tnt_id,
      labels
    }: {
      cur_hour: string;
      cur_time_zone: string;
      tnt_id: number;
      labels: LabelGenerationParams;
    }): Promise<BudgetAndTrafficItem[]>;
    getTopAdFormatEcpmAndEcpr(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams
    ): Promise<EcpmAndEcprItem[]>;
    trafficRequest(params: AddNotificationParams): Promise<boolean>;
  }

  interface BoardService {
    getOverview(
      formData: any,
      options: LabelGenerationParams
    ): Promise<OverviewItem[]>;
    getTopCountry(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams
    ): Promise<{ profit: number; data: TopCountryAdFormatItem[] }>;
    getSupplyDemand(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams
    ): Promise<{
      supplyData: Partial<SupplyDemandItem>[];
      demandData: Partial<SupplyDemandItem>[];
    }>;

    getTopAdFormatEcpmAndEcpr(
      cur_hour: string,
      cur_time_zone: string,
      tnt_id: number,
      label: LabelGenerationParams
    ): Promise<EcpmAndEcprItem[]>;

    getBudgetAndTraffic(
      cur_hour: string,
      tnt_id: number,
      cur_time_zone: string,
      labels: LabelGenerationParams
    ): Promise<{
      data: BudgetAndTrafficItem[];
      update_time: string;
    }>;
    trafficRequest(params: requestTrafficParams): Promise<boolean>;
  }

  interface BoardCtrl {
    getOverview(ctx: Context): void;
    getTopCountry(ctx: Context): void;
    getSupplyDemand(ctx: Context): void;
    getTopAdFormatEcpmAndEcpr(ctx: Context): void;
    getBudgetAndTraffic(ctx: Context): void;
    trafficRequest(ctx: Context): void;
  }
}
