/******************************************************************************
 * report 工具函数，将过程抽象，不同的 report 类型，通过传入不同配置来实现功能
 ******************************************************************************
 */

import { queryStackOverflow } from '@/db/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

/**
 * 抽离出通用的查询逻辑
 * @param sql 查询sql
 * @param tag 标签
 * @returns
 */
export function queryBQDataWithCache(sql: string, tag: LabelGenerationParams) {
  return queryStackOverflow(sql, {
    cacheTime: 60 * 10,
    ...tag
  });
}
