/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-28 15:08:06
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-03-01 13:12:44
 * @Description:
 */
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { PublisherBillingMetrics } from '@/constants/report/billing-report';
import {
  ExportTypeMap,
  StatusTypeMap
} from '@/constants/report/exported-report';
import {
  demandModel,
  exportLogModel,
  pubBillingModel,
  supplyModel
} from '@/models';
import { BillingAPI } from '@/types/billing';
import { array2Object, createDirectory } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import { getDownTaskInfo } from '@/utils/report';
import { queryBQDataWithCache } from '@/utils/report/report-util';
import {
  adaptGenerateBigQuerySQL,
  buildMultiTableQuery,
  concatSQLFragments,
  LabelGenerationParams,
  SQLFragments,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';
import { AdFormatToLabel, Country } from '@/constants/report/common-report';

class PubBillingService {
  async getPublisherBillingList(params: any, labels: LabelGenerationParams) {
    const sql = await this.initSQL(params, false, labels);
    const data = await queryBQDataWithCache(sql, labels);

    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        metrics: ['profit', 'profit_rate'],
        context: {
          start_date: params.start_date,
          end_date: params.end_date
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return { total, data: result };
  }

  async initSQL(params: any, isAll: boolean, labels: LabelGenerationParams) {
    let { columns = [], partner_id = [], tnt_id } = params;
    // 移除 columns 中的 month，这个是零时区的，不能参与 sql 片段
    columns = columns.filter((column: string) => column !== 'month');
    // 固定的指标，和 PublisherBillingMetrics 不同
    const FIXED_METRICS = [
      'request',
      'response',
      'impression',
      'seller_payment_impression',
      'total_request',
      'seller_net_revenue',
      'buyer_net_revenue',
      'profit',
      'profit_rate'
    ];
    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        columns,
        metrics: FIXED_METRICS
      },
      { api_url: labels.tag }
    );

    // 如果存在查询 ad_format, country, app_bundle_id 维度，则需要添加 IS NOT NULL 的判断条件
    const conditions = ['ad_format', 'country', 'app_bundle_id'];
    conditions.forEach(column => {
      if (columns.includes(column)) {
        fragments.where?.push(`${column} IS NOT NULL`);
      }
    });

    let multiTableQuery: SQLFragments = fragments;
    const partner_id_columns = columns.includes('partner_id')
      ? ['partner_id', 'partner_name']
      : [];
    const where_columns =
      partner_id.length > 0 ? [`partner_id in (${partner_id.join(',')})`] : [];
    const sellerPartnerTable = concatSQLFragments({
      select: ['seller_id', ...partner_id_columns],
      from: '`saas-373106.saas_others.seller_partner`',
      where: [...where_columns, `tnt=${tnt_id}`],
      groupBy: ['seller_id', ...partner_id_columns]
    });
    const sellerPartnerFragments: SQLFragments = {
      select: partner_id_columns,
      from: `(${sellerPartnerTable})`,
      where: [],
      groupBy: partner_id_columns
    };

    multiTableQuery = buildMultiTableQuery(
      {
        fragments,
        alias: 'billing_overview'
      },
      [
        {
          table: {
            fragments: sellerPartnerFragments,
            alias: 'seller_partner'
          },
          type: 'INNER JOIN',
          on: 'billing_overview.seller_id = seller_partner.seller_id'
        }
      ]
    );

    return concatSQLFragments({
      ...multiTableQuery,
      select: [...multiTableQuery.select, 'count(*) over() as total'],
      limit: isAll ? '' : multiTableQuery.limit
    });
  }

  async downloadAllReport(
    params: BillingAPI.PublisherParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { columns, cur_time_zone, tnt_id, cur_condition_str } = params;

      // 如果存在查询 ad_format, country, app_bundle_id 维度，需要移除固定指标 request, response, total_request
      const needRemoveMetrics = ['ad_format', 'country', 'app_bundle_id'].some((column) => columns.includes(column));
      const filterMetrics = PublisherBillingMetrics.filter((metric) => {
        if (needRemoveMetrics) {
          return !['request', 'response', 'total_request'].includes(metric);
        }
        return true;
      });

      const ori_condition = JSON.parse(cur_condition_str);
      ori_condition.metrics = filterMetrics;
      // const options = this.initParams(params);
      const dimensions = columns.map(v =>
        v === 'partner_id' ? 'pub_partner_id' : v
      );
      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } =
        getDownTaskInfo(
          cur_time_zone,
          tnt_id,
          dimensions,
          filterMetrics,
          'Publisher_Billing_Reporting'
        );

      const sql = await this.initSQL(params, true, labels);
      const [data, exportLogRow] = await Promise.all([
        pubBillingModel.newDownloadAllReport(sql, labels),
        exportLogModel.createExportLog(
          user_id,
          JSON.stringify(ori_condition),
          ExportTypeMap['Publisher Billing Reporting'],
          csvName,
          requestPath
        )
      ]);
      if (data) {
        const [supplyList, demandList] = await Promise.all([
          supplyModel.getDownloadSupplyList({ tnt_id }),
          demandModel.getDownloadDemandList({ tnt_id })
        ]);
        const sellerMap = array2Object(supplyList || [], 'seller_id');
        const buyerMap = array2Object(demandList || [], 'buyer_id');
        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
        });
        writeStream.on('error', async err => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format(
              'YYYY-MM-DD HH:mm:ss'
            )}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });
        writeStream.on('pipe', () => {
          const result = {
            code: Code.SUCCESS,
            name: csvName,
            type: ExportTypeMap['Publisher Billing Reporting']
          };
          resolve(result);
        });
        data
          .on('error', async err => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((data: any, index: number) => {
              const item = transformOneRowSQL(data, {
                metrics: ['profit', 'profit_rate'],
                context: {
                  start_date: params.start_date,
                  end_date: params.end_date,
                  today_hours: 0,
                  isDemand: false,
                  isSupplyReport: true
                }
              });

              const { seller_id, partner_id, partner_name, buyer_id, ad_format } = item;
              const seller = sellerMap[seller_id] || {};
              const buyer = buyerMap[buyer_id] || {};
              item.seller = seller
                ? `${seller?.seller_name}(${seller_id})`
                : `${seller_id}`;
              item.buyer = buyer
                ? `${buyer?.buyer_name}(${buyer_id})`
                : `${item.buyer_id}`;
              item.pub_partner = partner_id
                ? `${partner_name}(${partner_id})`
                : '-';
              item.ad_format = AdFormatToLabel[ad_format] || '-';
              item.country = Country[item.country] || 'Unknown';

              if (item.ad_width && item.ad_height) {
                item.ad_size = `${item.ad_width} * ${item.ad_height}`;
              }
              return item;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf-8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }

  /**
   * TODO: 目前下载逻辑沿用原来的下载逻辑，待优化
   */
  // initParams(params: BillingAPI.PublisherParams) {
  //   const {
  //     order_key,
  //     end = 50,
  //     start = 0,
  //     order,
  //     tz_start_date,
  //     tz_end_date,
  //     cur_time_zone,
  //     buyer_id
  //   } = params;

  //   const columns = params.columns || [];

  //   const isBuyer = columns?.includes('buyer_id') || !!buyer_id?.length;

  //   const condition = joinQueries(
  //     {
  //       numberCols: ['seller_id', 'buyer_id', 'tnt_id', 'partner_id'],
  //       arrayQueries: ['seller_id', 'buyer_id', 'partner_id'],
  //       exactQueries: ['tnt_id'],
  //       prefix: {
  //         'i.': ['seller_id', 'buyer_id', 'tnt_id'],
  //         'j.': ['partner_id']
  //       },
  //       extra: joinStr(
  //         [
  //           `i.day_hour >= '${tz_start_date}'`,
  //           `i.day_hour <= '${tz_end_date}'`,
  //           isBuyer
  //             ? `i.buyer_id > 0 and i.type = 'demand'`
  //             : `i.buyer_id = 0 and i.type = 'supply'`
  //         ],
  //         ' and '
  //       )
  //     },
  //     params
  //   );

  //   // 查询partner使用
  //   const leftJoinCond = joinQueries(
  //     {
  //       numberCols: ['seller_id', 'tnt', 'partner_id'],
  //       arrayQueries: ['seller_id', 'partner_id'],
  //       exactQueries: ['tnt']
  //     },
  //     { ...params, tnt: params.tnt_id }
  //   );

  //   const order_str = getOrderBy(order_key, order, {
  //     isEmptyStr: true,
  //     defaultOrderKey: 'seller_net_revenue'
  //   });

  //   const limit = `limit ${end - start} offset ${start}`;

  //   const dateArr = ['month', 'day'];
  //   const ori_dimensions = [...columns];

  //   // 去掉时间维度
  //   let dimensions = ori_dimensions
  //     .filter(v => !dateArr.includes(v))
  //     .map(v =>
  //       v === 'partner_id' ? 'j.partner_id,j.partner_name' : `i.${v}`
  //     );
  //   dimensions = [...new Set(dimensions)];
  //   const group_dimension = ori_dimensions.map(v =>
  //     dateArr.includes(v)
  //       ? 'date'
  //       : v === 'partner_id'
  //       ? 'j.partner_id,j.partner_name'
  //       : `i.${v}`
  //   );
  //   let date_time_str = '';
  //   if (columns?.includes('day')) {
  //     date_time_str = `FORMAT_TIMESTAMP('%Y-%m-%d', day_hour, '${cur_time_zone}') as date`;
  //   }
  //   if (columns?.includes('month')) {
  //     date_time_str = `FORMAT_TIMESTAMP('%Y-%m', day_hour, '${cur_time_zone}') as date`;
  //   }

  //   const option = {
  //     condition,
  //     dimensions: dimensions.join(','),
  //     group_dimension: group_dimension.join(','),
  //     order: order_str,
  //     limit,
  //     leftJoinCond,
  //     date_time_str,
  //     isBuyer
  //   };
  //   return option;
  // }

  // /**
  //  * TODO: 目前下载逻辑沿用原来的下载逻辑，待优化
  //  * @description: 数据处理
  //  * @param {any} data
  //  * @return {[*]}
  //  */
  // parseResult(
  //   data: BillingAPI.PublisherBillingListItem,
  //   index: number
  // ): BillingAPI.PublisherBillingListItem {
  //   const item = { ...data };
  //   item.seller_net_revenue = +`${str2num(item.seller_net_revenue).toFixed(2)}`;
  //   item.seller_payment_impression = +`${str2num(
  //     item.seller_payment_impression
  //   ).toFixed(2)}`;
  //   delete item.buyer_net_revenue;
  //   return {
  //     ...item,
  //     id: index
  //   };
  // }
}

export const pubBillingService = new PubBillingService();
