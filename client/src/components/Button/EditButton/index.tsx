/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-12 11:42:27
 * @Description:
 */
import { Button } from 'antd';
import type { ButtonProps } from 'antd';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';

function EditButton(props: ButtonProps): JSX.Element {
  return (
    <Button
      {...props}
      type="text"
      icon={props.icon ? props.icon : <RixEngineFont type="rix-edit" />}
      className={styles['edit-button']}
    />
  );
}

export default EditButton;
