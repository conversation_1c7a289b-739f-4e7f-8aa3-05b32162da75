/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-07 15:10:55
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-19 11:24:18
 * @Description:
 */
import LongContentMore from '@/components/LongContentMore';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { useState } from 'react';
import styles from './index.less';

type PretargetTableItemProps = {
  values: any;
  minWidth: number;
};

export default function TableItem({ values = [], minWidth }: PretargetTableItemProps) {
  const [showList, setShowList] = useState<string[] | number[]>([]);

  const handleShowDataChange = (val: string[] | number[]) => {
    setShowList(val);
  };
  return (
    <LongContentMore
      contentMaxHeight={200}
      defaultShowNum={200}
      onShowDataChange={handleShowDataChange}
      dataSource={values}
      style={{
        minWidth: minWidth,
        flex: minWidth,
        borderRight: '1px solid #EEF0F0',
        minHeight: 200,
        borderRadius: '0px'
      }}
      border={false}
    >
      {showList.map((item, index) => {
        return (
          <HoverToolTip title={item as string} key={index}>
            <div className={`${styles['table-item-content']} ${styles['ellipsis']}`} key={index}>
              {item}
            </div>
          </HoverToolTip>
        );
      })}
    </LongContentMore>
  );
}
