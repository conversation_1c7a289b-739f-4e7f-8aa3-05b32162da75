import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { StatusOptions } from '@/constants';
import { AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';
import { Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import EllipsisPopover from '@/components/EllipsisPopover';
import { ChannelType } from '../supply';
import { Countries } from '@/constants/global-mapping/country';
import StatusTag from '@/components/Tag/NewStatusTag';

export const PmpDealSearchOption: TopBarSearchItem[] = [
  {
    name: 'PMP Deal',
    type: 'select',
    key: 'id',
    value: [],
    options: [],
    placeholder: 'Please Select PMP Deal',
    mode: 'multiple'
  },
  {
    name: 'Deal ID',
    type: 'select',
    key: 'deal_id',
    value: [],
    options: [],
    placeholder: 'Please Select Deal ID',
    mode: 'multiple'
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    placeholder: 'Please Select Advertiser',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const PmpInternalSearchOption: TopBarSearchItem[] = [
  {
    name: 'PMP Inventory',
    type: 'select',
    key: 'id',
    value: [],
    options: [],
    placeholder: 'Please Select PMP Inventory',
    mode: 'multiple'
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    mode: 'multiple',
    value: [],
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    placeholder: 'Please Select Publisher',
    mode: 'multiple'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const PmpTabOptions = [
  { label: 'PMP Inventory', value: 1 },
  { label: 'PMP Deal', value: 2 }
];

export const PMPTab = {
  Inventory: 1,
  Deal: 2
};

export const PmpBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'PMP'
  }
];

export const PmpInternalColumns: ColumnProps<StrategyAPI.PmpDealListItem | StrategyAPI.PmpInternalListItem>[] = [
  {
    title: 'Name',
    dataIndex: 'name',
    width: 200,
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverToolTip title={txt}>
        <span>{txt}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_id',
    width: 200,
    ellipsis: { showTitle: false },
    render: (txt, params: any) => {
      const arr = Array.isArray(params.sellers)
        ? params.sellers?.map((v: any) => `${v.seller_name}(${v.seller_id})`)
        : [];
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    }
  },
  {
    title: 'Bundle',
    dataIndex: 'bundle',
    width: 200,
    ellipsis: { showTitle: false },
    render: txt => {
      const arr = Array.isArray(txt) ? txt : [];
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    }
  },
  {
    title: 'Channel Type',
    width: 150,
    dataIndex: 'inventory_type',
    ellipsis: { showTitle: false },
    render: (_: string[]) => {
      let arr = Array.isArray(_) ? _ : [];
      arr = arr.map(v => ChannelType[+v] as string);
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    }
  },
  {
    title: 'Country',
    width: 150,
    dataIndex: 'country',
    ellipsis: { showTitle: false },
    render: (_: string[]) => {
      let arr = Array.isArray(_) ? _ : [];
      arr = arr.map(v => Countries[v]);
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    }
  },
  {
    title: 'Ad Format',
    width: 150,
    dataIndex: 'ad_format',
    ellipsis: { showTitle: false },
    render: (_: string[]) => {
      let arr = Array.isArray(_) ? _ : [];
      arr = arr.map(v => AdFormatToLabel[v] as string);
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    }
  },
  {
    title: 'Ad Size',
    width: 150,
    dataIndex: 'ad_size',
    ellipsis: { showTitle: false },
  },
  {
    title: 'Publisher Deal ID',
    width: 150,
    dataIndex: 'seller_deal_id',
    ellipsis: { showTitle: false },
    render: (_: string) => {
      return (
        <HoverToolTip title={_}>
          <span>{_ || '-'}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['u_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Remark',
    width: 100,
    dataIndex: 'remark',
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverToolTip title={txt}>
        <span>{txt || '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100,
    render: _ => <StatusTag value={_} />
  },
  {
    title: 'Operation',
    dataIndex: 'operate',
    width: 100,
    fixed: 'right'
  }
];

export const PmpDealColumns: ColumnProps<StrategyAPI.PmpDealListItem | StrategyAPI.PmpInternalListItem>[] = [
  {
    title: 'Name',
    dataIndex: 'name',
    width: 200,
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverToolTip title={txt}>
        <span>{txt}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Deal ID',
    dataIndex: 'deal_id',
    width: 200,
    ellipsis: { showTitle: false },
    render: txt => (
      <HoverToolTip title={txt}>
        <span>{txt}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'PMP Inventory',
    dataIndex: 'pmp_internal_name',
    width: 160,
    ellipsis: { showTitle: false }
  },
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 160,
    ellipsis: { showTitle: false },
    render: (txt, params: any) => (
      <HoverToolTip title={txt ? `${txt}(${params.buyer_id})` : '-'}>
        <span>{txt ? `${txt}(${params.buyer_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Bidfloor($)',
    width: 100,
    dataIndex: 'bidfloor'
  },
  {
    title: 'Modified by',
    width: 150,
    dataIndex: 'op_name',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isDeleted = row['u_status'] === 3;
      return isDeleted ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <span style={{ color: isDeleted ? '#cccc' : '' }}>{'Unknown'}</span>
          <Tooltip title={'User is deleted'}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 100,
    render: _ => <StatusTag value={_} />
  },
  {
    title: 'Operation',
    dataIndex: 'operate',
    width: 100,
    fixed: 'right'
  }
];
