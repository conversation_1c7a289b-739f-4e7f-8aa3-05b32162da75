/*
 * @Author: ch<PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2023-02-28 17:29:07
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-19 10:48:35
 * @Description:
 */

import React, { useState, useEffect, useRef } from 'react';
import { Tag } from 'antd';
import styles from './index.less';
import { TooltipPlacement } from 'antd/es/tooltip';
import PopContent from './pop-content';
import NormalPopover from '../Popover';
import HoverToolTip from '../ToolTip/HoverTooltip';

const EllipsisPopover: React.FC<{
  dataSource: string[];
  contentWidth?: number; // popover宽度
  contentHeight?: number; // content的最大高度
  defaultShowNum?: number; // pop默认显示数据
  tdShowNum?: number; // 表格默认渲染数量
}> = ({ dataSource, contentWidth = 272, contentHeight = 270, defaultShowNum = 100, tdShowNum = 15 }) => {
  const viewRef = useRef<HTMLDivElement | null>(null);
  const [open, setOpen] = useState(false);
  const [hasElps, setHasElps] = useState(false);
  const [innerWidth, setInnerWidth] = useState(160);
  const [isShow, setIsShow] = useState(false); // 是否显示过
  const [placement, setPlacement] = useState<TooltipPlacement>('top');
  // 截取前面10条显示 减少不必要渲染
  const dataSourceTmp = [...dataSource].reverse();
  // 倒序显示
  const dataArr = dataSourceTmp.length <= tdShowNum ? dataSourceTmp : dataSourceTmp.slice(0, tdShowNum);

  useEffect(() => {
    if (open) {
      window.addEventListener('resize', handleResize);
    } else {
      window.removeEventListener('resize', handleResize);
    }
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [open]);

  useEffect(() => {
    checkEllipsis(viewRef.current);
    handlePlacement(viewRef.current);
  }, [viewRef.current, dataSource]);

  const handlePlacement = (dom: any) => {
    if (dom) {
      // 显示高度
      const height = document.body.offsetHeight;
      const rect = dom.getBoundingClientRect();
      const { top, left } = rect;
      const minTopBottom = contentHeight + 135 + 50;
      const minLeft = contentWidth + 35;
      const bottom = height - top - 50; // 50是行高
      if (top >= minTopBottom) {
        setPlacement('top');
      }
      if (bottom >= minTopBottom && top < minTopBottom) {
        setPlacement('bottom');
      }
      // 以上都不符合的时候
      if (top < minTopBottom && bottom < minTopBottom) {
        if (left >= minLeft) {
          setPlacement('left');
        } else {
          // 最后的选择
          setPlacement('right');
        }
      }
    }
  };

  const handleResize = () => {
    setOpen(false);
    if (viewRef.current) {
      checkEllipsis(viewRef.current);
      handlePlacement(viewRef.current);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      handlePlacement(viewRef.current);
    }
    if (hasElps) {
      setOpen(open);
      setIsShow(true);
    }
  };
  const checkEllipsis = (dom: any) => {
    setInnerWidth(dom.offsetWidth);
    const checkDom = dom.cloneNode();
    checkDom.style.width = dom.offsetWidth + 'px';
    checkDom.style.height = dom.offsetHeight + 'px';
    checkDom.style.overflow = 'auto';
    checkDom.style.position = 'absolute';
    checkDom.style.zIndex = -1;
    checkDom.style.opacity = 0;
    checkDom.style.whiteSpace = 'nowrap';
    checkDom.innerHTML = dom.innerHTML;

    const parent = document.body;
    parent.appendChild(checkDom);
    const hasElps = checkDom.scrollWidth > checkDom.offsetWidth;
    parent.removeChild(checkDom);
    setHasElps(hasElps);
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
  };

  return (
    <NormalPopover
      placement={placement}
      content={
        <React.Fragment>
          {/* 首次不渲染，点开了就可以渲染，解决dom渲染过多卡顿问题 */}
          {(hasElps && open) || isShow ? (
            <PopContent
              dataSource={dataSource}
              open={open}
              contentHeight={contentHeight}
              contentWidth={contentWidth}
              defaultShowNum={defaultShowNum}
              setOpen={setOpen}
            />
          ) : (
            <></>
          )}
        </React.Fragment>
      }
      trigger="click"
      open={open}
      onOpenChange={handleOpenChange}
      overlayClassName={styles['ellipsis-popover-overaly']}
    >
      <div className={`${styles['ellipsis']} ${styles['tag-container']}`} ref={viewRef} onClick={handleClick}>
        {dataArr.map((el, idx) => (
          <Tag key={`tag-${el}-${idx}`} className={`${styles['list-tag']} `}>
            {/* 30 需要预留...的空间, 20是去掉padding */}
            <HoverToolTip title={el}>
              <span className={styles['ellipsis']} style={{ display: 'inline-block', maxWidth: innerWidth - 30 - 20 }}>
                {el}
              </span>
            </HoverToolTip>
            {/* <span
              className={styles['ellipsis']}
              style={{ display: 'inline-block', maxWidth: innerWidth - 30 - 20 }}
              title={el}
            >
              {el}
            </span> */}
          </Tag>
        ))}
      </div>
    </NormalPopover>
  );
};

export default EllipsisPopover;
