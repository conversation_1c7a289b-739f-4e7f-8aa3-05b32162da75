/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-19 14:16:43
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-10 10:59:07
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import { useModel } from 'umi';
import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import EditButton from '@/components/Button/EditButton';
import RixEngineFont from '@/components/RixEngineFont';
import { ColumnProps } from 'antd/lib/table';

import { AlertSearchOption, AlertColumns, AlertTabOptions, AlertTab } from '@/constants/alert';

import { fetchData } from '@/utils';
import { updateNotificationStatus } from '@/services/common';
import { UnReadType } from '@/constants/base';
import { TimeZoneMapLabel } from '@/constants/base/time-zone';
import moment from 'moment';
import Paragraph from 'antd/lib/typography/Paragraph';

const Page: React.FC = () => {
  console.log('page');
  const { dataSource: dataList, reload, loading } = useModel('useNotificationList');
  const { initialState } = useModel('@@initialState');
  const [dataSource, setDataSource] = useState(dataList || []);
  const [currentTab, setCurrentTab] = useState(AlertTab.Revenue);
  const [searchOptions, setSearchOptions] = useState(AlertSearchOption);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [viewed, setViewed] = useState(false);
  const [ellipsisExpandKeys, setEllipsisExpandKeys] = useState<number[]>([]);
  const OperateColumns: ColumnProps<CommonAPI.NotificationListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      fixed: 'right',
      width: 50,
      render: (txt, params) => (
        <EditButton
          type="link"
          onClick={e => handleMarkRead(e, params.id)}
          disabled={params.unread === UnReadType.read}
          icon={<RixEngineFont type="rix-markread" />}
        >
          Viewed
        </EditButton>
      )
    }
  ];
  let columns = [...AlertColumns, ...OperateColumns];
  const handleClickExpand = (e: any, id: number) => {
    e.preventDefault();
    if (ellipsisExpandKeys.includes(id)) {
      setEllipsisExpandKeys(ellipsisExpandKeys.filter(item => item !== id));
    } else {
      setEllipsisExpandKeys([...ellipsisExpandKeys, id]);
    }
  };
  columns = columns.map(item => {
    if (item.dataIndex === 'create_time') {
      item.title = () => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span>Create At</span>
            <Tooltip title={`${TimeZoneMapLabel[initialState?.timeZone || 'Etc/UTC']}`}>
              <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
            </Tooltip>
          </div>
        );
      };
    }
    if (item.dataIndex === 'content') {
      item.render = (txt, row) => {
        return (
          <div>
            <Paragraph ellipsis={{ rows: ellipsisExpandKeys.includes(row.id) ? 100 : 2 }} style={{ margin: 0 }}>
              {(txt && `${txt}`) || '-'}
            </Paragraph>
            <div style={{ display: 'flex' }}>
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: txt || '',
                  icon: <RixEngineFont type="rix-copy" style={{ fontSize: 16 }} />
                }}
                style={{ marginBottom: 0, marginLeft: 'auto', marginRight: '16px' }}
              ></Paragraph>
              <a onClick={e => handleClickExpand(e, row.id)}>
                {ellipsisExpandKeys.includes(row.id) ? 'Collapse' : 'Expand'}
              </a>
            </div>
          </div>
        );
      };
    }
    return item;
  });

  const onSuccess = () => {
    setSelectedIds([]);
    reload();
  };
  const handleMarkRead = (e: any, msg_id: number | number[]) => {
    e.preventDefault();
    if (!msg_id || (Array.isArray(msg_id) && !msg_id.length)) return;
    fetchData({ setLoading: setViewed, request: updateNotificationStatus, params: { msg_id: msg_id }, onSuccess });
  };

  useEffect(() => {
    reload();
  }, []);

  useEffect(() => {
    if (Array.isArray(dataList)) {
      let tmpList: CommonAPI.NotificationListItem[] = dataList || [];
      tmpList = tmpList.filter((item: CommonAPI.NotificationListItem) => item.rule_id === +currentTab);
      setDataSource(tmpList);
    }
  }, [dataList, currentTab]);

  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as number);
  };
  const normalEmptyRender = () => <span> </span>;

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: CommonAPI.NotificationListItem[]) => {
      setSelectedIds(selectedRowKeys as number[]);
    },
    getCheckboxProps: (record: CommonAPI.NotificationListItem) => ({
      disabled: record.unread === UnReadType.read
    }),
    selectedRowKeys: selectedIds
  };

  return (
    <PageContainer flexDirection="column">
      <FrontTable<CommonAPI.NotificationListItem>
        searchOptions={searchOptions}
        loading={(loading && dataSource && !dataSource.length) || viewed}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        scroll={{ y: 'calc(100vh - 220px)' }} // 非动态， 需要自己指定
        labelWidth={110}
        emptyRender={dataSource && dataSource.length ? normalEmptyRender : undefined}
        tabOptions={AlertTabOptions}
        defaultTab={currentTab}
        onTabChange={handleTabChange}
        isFold={true}
        btnOptions={[
          {
            label: 'Viewed',
            type: 'primary',
            size: 'small',
            onClick: e => handleMarkRead(e, selectedIds),
            icon: <RixEngineFont type="rix-markread" />,
            loading: viewed
          }
        ]}
        rowClassName="alert-table-row"
        rowSelection={{
          type: 'checkbox',
          ...rowSelection
        }}
        ExtraRangePickerOptions={{
          showTime: { format: 'HH:mm:ss' },
          format: value => moment(value).format('YYYY/MM/DD HH'),
          allowClear: true
        }}
        initialValues={{ unread: [UnReadType.unread] }}
      />
    </PageContainer>
  );
};

export default Page;
