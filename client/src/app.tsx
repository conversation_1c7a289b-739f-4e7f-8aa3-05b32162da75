import RightContent from '@/components/RightContent';
import RixEngineFont from '@/components/RixEngineFont';
import { IconFontUrl, IgnoreLoginPath, LoginPath, SpecialSuccessCode, UIConfig } from '@/constants';
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons';
import type { MenuDataItem } from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { Button, message, Result } from 'antd';
import type { RequestConfig } from 'umi';
import { history, useAccess } from 'umi';
import defaultSettings from '../config/defaultSettings';
import CusLoadingIcon from './components/LoadingIcon';
import { AllTimeZone, TimeZoneMapReverse } from './constants/base/time-zone';
import ErrorBound from './pages/error-page';
import { authLogin, getCurrentUser, getMenuList } from './services/api';

import { HeaderViewProps } from '@ant-design/pro-layout/lib/Header';
import LogoHeader from './components/LogoHeader';
import MenuContent from './components/MenuContent';
import { PresetConfirmModal, PresetConfirmModalProvider } from './components/PresetConfirmModal';
import { IframeMessage } from './hooks/useIframe/type';
import { iframeManager } from './utils/iframe-manager';
import { RouteManager } from './utils/iframe-manager/route-manager';
import { setTimeZoneAndReload } from './utils/iframe-manager/time-zone';
import { generateRoute } from './utils/menu';
import { showRefreshModal } from './utils/refresh';
import { generateExtraHeaderOptions } from './utils/signature';

const { SiderWidth } = UIConfig;

// const ProdEnv = ['prod', 'production', 'test'];
// 当前登录用户的id
let cur_user_id = '0';
// 保证只调用一次 auoLogin
let isAutoLoginCalled = false;
// 从 父应用收到的时区
let receivedTimeZone = 'Etc/UTC';

let extraRoutes: PermissionAPI.MenuItem[] = [];

// 接口报错处理 比如超时
const errorHandler = function (error: any) {
  const { message: errMsg } = error;
  console.log('xx报错', error);
  errMsg && message.error(errMsg);
  throw error;
};

export const request: RequestConfig = {
  timeout: 9000000, // 15分钟超时
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  errorConfig: {
    errorHandler: errorHandler,
    errorThrower() {}
  },
  requestInterceptors: [
    (url, options) => {
      // 探测前端页面状态，携带时间戳
      // @ts-ignore
      options.headers['x-version-hash'] = process.env.VERSION_HASH;

      const cur_local_user_id = localStorage.getItem('user-ID');
      if (cur_local_user_id && +cur_user_id && cur_local_user_id !== `${cur_user_id}`) {
        window.location.reload();
      }
      const zone = localStorage.getItem(`time-${cur_user_id || ''}`) || 'Etc/UTC';
      const timeZone = AllTimeZone.includes(zone) ? zone : 'Etc/UTC';
      // @ts-ignore
      options.headers['x-time-zone'] = timeZone;
      // @ts-ignore
      const extraHeaderOptions = generateExtraHeaderOptions(options.data);
      // @ts-ignore
      Object.assign(options.headers, extraHeaderOptions);
      // @ts-ignore
      if (window.cur_time_zone && window.cur_time_zone !== timeZone) {
        message.warning('Time zone has changed', 5000);
        window.location.reload();
      }
      return { url, options };
    }
  ],
  responseInterceptors: [
    (response: any) => {
      const { data } = response;

      if (response.status === 205) {
        showRefreshModal();
        return response;
      }

      const isIgnoreLoginPath = IgnoreLoginPath.includes(location.pathname);

      // iframe 环境特殊处理
      if (iframeManager.isInIframe() && !isIgnoreLoginPath && data) {
        const url = response.config.url;
        if (url === '/api/user/authLogin') {
          // 有三种自动登录异常
          // 1101(登录凭据错误)，1113(token过期)，1114(token无效)
          const { code } = data;
          if (code !== 0) {
            message.error(data.message);
            if (code === 1113) {
              iframeManager.sendMessage(IframeMessage.AUTH_EXPIRED, {
                code: data.code,
                message: data.message || 'Authentication expired'
              });
            }
          }

          return response;
        }

        // 处理 cookie 过期的问题（1113 和 1102）
        if (data.code === 1113 || data.code === 1102) {
          message.error('Your session has expired');

          iframeManager.sendMessage(IframeMessage.AUTH_EXPIRED, {
            code: data.code,
            message: data.message || 'Authentication expired'
          });

          return response;
        }
      }

      const isSpecialCode = Object.values(SpecialSuccessCode).includes(data.code);
      if (data && (data.code === 0 || isSpecialCode)) {
        return response;
      }

      // 用户名或密码错误/密码错误/没有登录/密码错误
      const IgnoreCodes = [1101, 1105, 1102, 1112];
      if (!data || (data && data.code && !IgnoreCodes.includes(data.code))) {
        message.error(data?.message || 'system error', 4);
      }
      // 没登录/token过期/token非法
      if (data && [1102, 1113, 1114].includes(data.code)) {
        if (!isIgnoreLoginPath) {
          // 跳转到登录页面
          history.push(LoginPath);
        }
      }
      // 没有权限
      if (data && data.code === 1108) {
        if (!isIgnoreLoginPath) {
          history.push('/403');
        }
      }

      return response;
    }
  ]
};

const fetchUserInfo = async () => {
  try {
    const msg = await getCurrentUser();
    return msg.data;
  } catch (error) {
    history.push(LoginPath);
  }
  return undefined;
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<API.InitialStateType> {
  if (iframeManager.isInIframe()) {
    iframeManager.initMessageCommunicator(true);
    // 监听 父应用 发送的消息：内容格式 ({"type": "set_tz", "data": {"tz": "UTC-5"}}) ，用来设置时区
    iframeManager.onMessage('set_tz', data => {
      const { tz } = data;
      if (tz) {
        // 时区格式需要转换，比如 UTC-5 转换为 Etc/UTC-5
        receivedTimeZone = TimeZoneMapReverse[tz] || 'Etc/UTC';
        // cur_user_id 存在，说明是非初始化阶段，这里需要判断并刷新页面
        if (cur_user_id !== '0') {
          iframeManager.sendMessage('tz_success', {});
          setTimeZoneAndReload(receivedTimeZone, { userId: cur_user_id });
        }
      }
    });
  }

  const allMenuAccess = extraRoutes.map(v => v.access || '').filter(v => v && v.trim());
  const allMenuIcon = extraRoutes
    .filter(v => v.path && v.path.trim())
    .map(v => ({ path: v.path, icon: v.icon, title: v.title }));
  // 路径跟access的映射关系
  const allMenuAccessMap: { [key: string]: string } = {};
  extraRoutes
    .filter(v => v.path && v.access)
    .forEach(v => {
      allMenuAccessMap[v.path] = v.access || '';
    });

  // 如果是登录页面，不执行
  if (history.location.pathname !== LoginPath) {
    const currentUser = await fetchUserInfo();

    const { user_id } = currentUser || {};

    cur_user_id = user_id;
    localStorage.setItem('user-ID', user_id || '');
    const zone = localStorage.getItem(`time-${user_id || ''}`) || 'Etc/UTC';

    // 如果不一致，则设置时区
    if (iframeManager.isInIframe() && zone !== receivedTimeZone) {
      iframeManager.sendMessage('tz_success', {});
      setTimeZoneAndReload(receivedTimeZone, { userId: cur_user_id });
    }

    return {
      currentUser,
      isCollapsed: false,
      timeZone: zone, // 默认时区
      allMenuAccess,
      allMenuIcon,
      allMenuAccessMap
    };
  }
  return {
    currentUser: undefined,
    isCollapsed: false,
    timeZone: 'Etc/UTC', // 默认时区
    allMenuAccess,
    allMenuIcon,
    allMenuAccessMap
  };
}

export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  const setting: any = defaultSettings;
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const access = useAccess();

  const isToponTenant = iframeManager.isToponTenant(initialState?.currentUser?.tnt_type);

  return {
    iconfontUrl: IconFontUrl[(process.env.UMI_ENV as string) || 'prod'],
    siderWidth: SiderWidth,
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    waterMarkProps: {
      content: initialState?.currentUser?.account_name
    },
    onPageChange: (location: Location) => {
      // 页面改变
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && !IgnoreLoginPath.includes(location.pathname)) {
        history.push(LoginPath);
      }
      // 手动权限校验
      const access = initialState?.allMenuAccessMap?.[location.pathname] || '';
      if (access && !initialState?.currentUser?.menu_access?.includes(access)) {
        history.push('/403');
      }
      setInitialState({
        ...initialState,
        isCollapsed: false
      });
    },
    onCollapse: (collapsed: boolean) => {
      setInitialState({
        ...initialState,
        isCollapsed: collapsed
      });
    },
    menuDataRender: (menuData: MenuDataItem[]) => {
      // 控制菜单展示
      return menuData.map(item => {
        return {
          ...item,
          hideInMenu: item.hideInMenu || (item.access && !access[item.access]),
          icon: item.icon && <RixEngineFont type={`${item.icon}`} style={{ fontSize: 20 }} />
        };
      });
    },
    menuContentRender: (props, defaultDom) => {
      return <MenuContent {...props} defaultDom={defaultDom} isToponTenant={isToponTenant} />;
    },
    defaultCollapsed: false,
    collapsed: initialState?.isCollapsed,
    collapsedButtonRender: collapsed => {
      return !collapsed ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />;
    },
    breakpoint: false,
    // 增加一个 loading 的状态
    childrenRender: children => {
      if (initialState?.loading) return <PageLoading indicator={<CusLoadingIcon />} />;
      return (
        <ErrorBound>
          <div style={{ background: '#fff', width: '100%', height: '100%' }}>
            <PresetConfirmModalProvider>
              {children}
              <PresetConfirmModal />
            </PresetConfirmModalProvider>
          </div>
        </ErrorBound>
      );
    },
    contentStyle: {
      margin: 0
    },
    ...setting,
    logo: <img src={'/api/common/file/logo'} alt="Logo" className="custom-logo" />,
    headerRender: (props: HeaderViewProps, defaultDom: React.ReactNode) => {
      if (isToponTenant) {
        return false; // 隐藏 header
      }
      return <LogoHeader dom={defaultDom} {...props} />;
    },
    headerHeight: isToponTenant ? 0 : 64,
    noAccessible: (
      <Result
        status="403"
        title="403"
        subTitle="Sorry, you have no permission to access this page."
        extra={
          <Button type="primary" onClick={() => history.push('/')}>
            Back Home
          </Button>
        }
      />
    ),
    unAccessible: (
      <Result
        status="403"
        title="403"
        subTitle="Sorry, you have no permission to access this page."
        extra={
          <Button type="primary" onClick={() => history.push('/')}>
            Back Home
          </Button>
        }
      />
    )
  };
};

export function onRouteChange(props: any) {
  if (iframeManager.isInIframe()) {
    // 使用RouteManager处理路由变化
    const routeChangeInfo = RouteManager.handleRouteChange(props);
    if (routeChangeInfo) {
      iframeManager.sendMessage(IframeMessage.ROUTE_CHANGE, {
        next: routeChangeInfo.next,
        cur: routeChangeInfo.cur || null,
        timestamp: new Date().getTime()
      });
    }
  }
}

export function render(oldRender: () => void) {
  iframeManager.setToponStyle();
  if (IgnoreLoginPath.includes(location.pathname)) {
    oldRender();
    return;
  }

  if (!iframeManager.isInIframe()) {
    getMenuList({})
      .then(res => {
        extraRoutes = res.data || [];
        oldRender();
      })
      .catch(e => {
        message.error('系统错误');
      });
    return;
  }

  // 获取 URL 参数中的 token
  const token = new URLSearchParams(window.location.search).get('token') || '';

  if (!isAutoLoginCalled) {
    isAutoLoginCalled = true;
    // 执行自动登录
    authLogin({ token }).then(response => {
      isAutoLoginCalled = false;
      // 自动登录成功后，继续加载菜单
      if (response.code === 0) {
        getMenuList({})
          .then(res => {
            extraRoutes = res.data || [];
            oldRender();
          })
          .catch(e => {
            message.error('system error');
            isAutoLoginCalled = false;
          });
      }
    });
    return; // 提前返回，等待异步操作完成
  }
}

export function patchClientRoutes({ routes }: any) {
  if (extraRoutes.length) {
    const index = routes.findIndex((v: any) => v.path === '/' && v.isLayout);
    if (index !== -1) {
      const menus = generateRoute(extraRoutes);
      routes[index].routes = [...menus, ...routes[index].routes];
      routes[index].children = [...menus, ...routes[index].children];
    }
  }
}
