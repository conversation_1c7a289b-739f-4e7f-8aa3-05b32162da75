/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-22 11:16:19
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-22 11:17:47
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getSampleTraceTaskList } from '@/services/api';

type Props = {
  dataSource: TroubleShootingAPI.TaskListItem[];
  reload: () => Promise<any>;
  loading: boolean;
};

export default function useSampleTraceTaskList(): Props {
  const { data, run, loading, error } = useCustomRequest(getSampleTraceTaskList, {
    cacheKey: 'troubleshooting-sample-trace-task-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
