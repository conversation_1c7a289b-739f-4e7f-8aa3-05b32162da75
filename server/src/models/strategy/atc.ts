/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-07 11:42:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-07 14:58:06
 * @Description:
 */

import dbUtil from '@/db/mysql';
import { AtcAPI } from '@/types/atc';

class AtcModel implements AtcAPI.Atc {
  async isAtcExists(params: AtcAPI.IsExistParams): Promise<any[]> {
    // const sql = `select id from white_black_list where seller_id=${seller_id} and buyer_id=${buyer_id} and tnt_id=${tnt_id}
    // and type=${type} ${id ? `and id !=${id}` : ''} limit 1`;
    // return await dbUtil.query(sql);
    return [];
  }

  async getAtcList(tnt_id: number): Promise<AtcAPI.AtcListItem[]> {
    const sql = `
      select model from stg_atc_config where tnt_id=? order by update_time desc limit 1;
    `;
    return await dbUtil.query(sql, [tnt_id]);
  }

  async updateAtc(params: AtcAPI.updateAtcParams): Promise<boolean> {
    const { op_id, tnt_id, model = 3 } = params;
    const sql = `update stg_atc_config set model=?,op_id=? where tnt_id=?`;
    return !!(await dbUtil.query(sql, [model, op_id, tnt_id]));
  }
}

export const atcModel = new AtcModel();
