import { type SignOptions } from 'jsonwebtoken';

interface JwtConfig extends SignOptions {
  cookieName: string;
  secret: string;
}

// 只有 development 和 prod 才设置 iframeConfig
const needIframeConfig =
  process.env.NODE_ENV === 'development' ||
  process.env.NODE_ENV === 'prod' ||
  process.env.NODE_ENV === 'test';

const iframeConfig = {
  sameSite: 'none' as 'none',
  secure: true
};

export const jwtConfig: JwtConfig = {
  cookieName: 'auth_token',
  secret: 'c7d06befb6954a07b4333c472b3e7807', // 使用原有的 app.keys
  expiresIn: '10m', // token 有效期改为10分钟
  issuer: 'rix-platform',
  audience: 'rix-users',
};
