/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 19:48:58
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-11-24 14:33:59
 * @Description:
 */
import React, { useEffect, useMemo, useState } from 'react';
import { useAccess } from '@umijs/max';
import PageContainer from '@/components/RightPageContainer';
import styles from './index.less';
import { Button, Form, Radio, Space, Typography } from 'antd';
import NormalInput from '@/components/Input/NormalInput';
import { useModel, history } from '@umijs/max';
import { AccountTabOptions, AccountTab } from '@/constants/base/my-account';
import NormalRadio from '@/components/Radio/NormalRadio';
import { resetPassword, vaildPassword } from '@/services/api';
import { fetchData } from '@/utils';
import NormalModal from '@/components/Modal/NormalModal';
import { logOut } from '@/services/api';
import { DailyCSVReportingTenants, LoginPath, UserType } from '@/constants';
import { AccountBreadOptions } from '@/constants/base/my-account';
import { useRequest } from 'ahooks';
import { Link } from '@umijs/max';

const { Text } = Typography;

const Page: React.FC = () => {
  const access = useAccess();
  const { initialState } = useModel('@@initialState');
  const { previousUrl } = useModel('usePreviousUrl');
  const [form] = Form.useForm();
  const [currentTab, setCurrentTab] = useState(AccountTab.Base);
  const [loading, setLoading] = useState(false);
  const { runAsync: checkedPwd } = useRequest(vaildPassword, {
    debounceWait: 300,
    manual: true
  });
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue(initialState.currentUser);
    }
  }, [initialState?.currentUser]);

  const handleGoBack = () => {
    if (previousUrl) {
      history.go(-1);
    } else {
      history.push('/demand/advertiser');
    }
  };

  const handleTabChange = (e: any) => {
    setCurrentTab(e.target.value);
  };

  const handleSubmit = () => {
    form.submit();
  };

  const onSuccess = async () => {
    logOut();
    NormalModal.success({
      title: 'Reset Password Success',
      content: 'Your password has been reset successfully, please login again',
      onOk: () => {
        history.push(LoginPath);
      }
    });
  };
  const onError = (data: any) => {
    form.setFields([{ name: 'old_password', errors: [data] }]);
  };
  const handleFinish = (values: any) => {
    const params = {
      user_id: initialState?.currentUser?.user_id,
      new_password: values.new_password,
      old_password: values.old_password
    };
    fetchData({ setLoading, request: resetPassword, params, onSuccess, onError });
  };

  const { isDailyCSVReportingUserType, isDailyCSVReportingTenant, userToken } = useMemo(() => {
    const { type = 0, tnt_id = 0, user_token = '' } = initialState?.currentUser || {};
    return {
      isDailyCSVReportingUserType: [UserType.Tenant, UserType.Rix_Admin].includes(type),
      isDailyCSVReportingTenant: DailyCSVReportingTenants.includes(tnt_id),
      userToken: user_token
    };
  }, [initialState?.currentUser]);

  return (
    <PageContainer isBack handleGoBack={handleGoBack} options={AccountBreadOptions} hideMenu={true}>
      <div className={styles['container']}>
        <div className={styles['top']}>
          <NormalRadio value={currentTab} onChange={handleTabChange}>
            {AccountTabOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </div>
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 180 } }}
          wrapperCol={{ span: 24 }}
          onFinish={handleFinish}
        >
          {currentTab === AccountTab.Base && (
            <>
              <Form.Item
                name="account_name"
                label="User Name:"
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                  {
                    required: true
                  }
                ]}
              >
                <NormalInput style={{ maxWidth: 418 }} disabled />
              </Form.Item>
              <Form.Item
                name="tnt_name"
                label="Company Name:"
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                  {
                    required: true
                  }
                ]}
              >
                <NormalInput style={{ maxWidth: 418 }} disabled />
              </Form.Item>
            </>
          )}
          {currentTab === AccountTab.Password && (
            <>
              <Form.Item
                name="old_password"
                label="Current Password:"
                validateTrigger={['onBlur']}
                rules={[
                  {
                    required: true,
                    message: 'Please Input Current Password'
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters'
                  },
                  {
                    validateTrigger: ['onBlur'],
                    validator: (_, value, callback) => {
                      value &&
                        checkedPwd({ password: value }).then((res: any) => {
                          if (res.code === 0) {
                            callback();
                          } else {
                            callback('current password is incorrect');
                          }
                        });
                    }
                  }
                ]}
                hasFeedback
              >
                <NormalInput.Password
                  style={{ maxWidth: 418 }}
                  allowClear
                  disabled={access.DisabledButton('resetPasswordAuth')}
                />
              </Form.Item>
              <Form.Item
                name="new_password"
                label="New Password:"
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                  {
                    required: true,
                    message: 'Please Input New Password!'
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters'
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      const chineseReg = /[\u4e00-\u9fa5]/;
                      if (
                        !value ||
                        (reg.test(value) && getFieldValue('old_password') !== value && !chineseReg.test(value))
                      ) {
                        return Promise.resolve();
                      } else if (getFieldValue('old_password') === value) {
                        return Promise.reject(new Error('New password cannot be the same as the old password!'));
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Password must contain number and capitals'));
                      }
                      return Promise.reject(new Error('Password cannot contain Chinese'));
                    }
                  })
                ]}
                hasFeedback
              >
                <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
              </Form.Item>
              <Form.Item
                name="confirm_password"
                label="Repeat New Password:"
                validateTrigger={['onChange', 'onBlur']}
                dependencies={['new_password']}
                rules={[
                  {
                    required: true,
                    message: 'Please Input Repeat New Password!'
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      if (!value || (getFieldValue('new_password') === value && reg.test(value))) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(new Error('Password must contain number and capitals'));
                      }
                      return Promise.reject(new Error('Password do not match!'));
                    }
                  })
                ]}
                hasFeedback
              >
                <NormalInput.Password style={{ maxWidth: 418 }} allowClear />
              </Form.Item>
            </>
          )}
          {/* 只有租户和管理员可以查看token，这个 token 用于请求 report file 的请求头 x-authorization 认证 */}
          {isDailyCSVReportingUserType && isDailyCSVReportingTenant && (
            <Form.Item
              label="Token"
              tooltip="This token is used for the request header 'x-authorization' of the daily csv file request api"
            >
              <Space align="start" size={24} wrap>
                {/* 如果不存在 userToken，展示 重新登录 的提示 */}
                <Text copyable={!!userToken} style={{ color: '#a5a5a5' }}>
                  {userToken || 'Please re-login to display the token'}
                </Text>
                <Link to="/help/daily-csv-reporting-api" target="_blank">
                  Daily CSV Reporting API Doc
                </Link>
              </Space>
            </Form.Item>
          )}
          <Form.Item>
            <div className={styles['footer']}>
              <Button onClick={handleGoBack} disabled={loading}>
                Cancel
              </Button>
              {currentTab === AccountTab.Password && (
                <Button
                  type="primary"
                  onClick={handleSubmit}
                  loading={loading}
                  disabled={access.DisabledButton('resetPasswordAuth')}
                >
                  Submit
                </Button>
              )}
            </div>
          </Form.Item>
        </Form>
      </div>
    </PageContainer>
  );
};

export default Page;
