// radio button的统一样式

.radio-button-container,
.more-radio-button-container {
  background: var(--background-color);
  border-radius: 6px;
  padding: 3px;
  height: 100%;
  min-height: 32px;
  color: #5e6466;
  label {
    margin-right: 3px;
    &:last-child {
      margin-right: 0px;
    }
  }
  :global {
    .ant-radio-button-wrapper {
      height: 26px;
      border: none;
      background: var(--background-color);
      line-height: 26px;
      color: #5e6466;
      &.ant-radio-button-wrapper-checked:focus-within {
        box-shadow: none;
      }
      &:hover {
        background: #ffffff;
        border-radius: 4px;
        font-weight: 700;
        color: var(--primary-color);
        &:not([class*=' ant-radio-button-wrapper-disabled']).ant-radio-button-wrapper:first-child {
          border: none;
        }
      }
      &.ant-radio-button-wrapper-disabled {
        color: #d7dadb;
        background: var(--background-color);
      }
      &.ant-radio-button-wrapper-checked {
        background: #ffffff;
        border-radius: 4px;
        font-weight: 700;
        color: var(--primary-color);
      }
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      display: none;
    }
  }
}

.more-radio-button-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3px;
}
