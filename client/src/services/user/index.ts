/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-10 11:28:18
 * @Description:
 */

import { request } from '@umijs/max';

export async function getCurrentUser(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/getCurrentUser', {
    data: body || {},
    ...(options || {})
  });
}

export async function logOut(options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/logOut', {
    method: 'POST',
    ...(options || {})
  });
}

export async function login(body: UserAPI.LoginParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/logIn', {
    data: body,
    ...(options || {})
  });
}

export async function resetPassword(body: UserAPI.ResetPasswordParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/resetPassword', {
    data: body,
    ...(options || {})
  });
}

export async function sendEmail(body: UserAPI.EmialParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/sendEmail', {
    data: body,
    ...(options || {})
  });
}

export async function forceLogOut(body: UserAPI.ForceLogOutParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/forceLogOut', {
    data: body,
    ...(options || {})
  });
}

export async function isAccountNameExists(body?: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/isAccountNameExists', {
    data: body,
    ...(options || {})
  });
}

// 上下游用户接口
export async function editDashboardUser(body: UserAPI.EditUserParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/editDashboardUser', {
    data: body,
    ...(options || {})
  });
}

export async function vaildPassword(body: UserAPI.vaildPasswordParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/vaildPassword', {
    data: body,
    ...(options || {})
  });
}

// 重置用户账号
export async function resetAccountName(body: UserAPI.vaildPasswordParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/resetAccountName', {
    data: body,
    ...(options || {})
  });
}

// 重置用户密码
export async function restUserPwd(body: UserAPI.vaildPasswordParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/restUserPwd', {
    data: body,
    ...(options || {})
  });
}

export async function switchAccount(body: UserAPI.SwitchAccountParams, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/switchAccount', {
    data: body,
    ...(options || {})
  });
}

export async function getUserLinkList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/getUserLinkList', {
    data: body,
    ...(options || {})
  });
}
