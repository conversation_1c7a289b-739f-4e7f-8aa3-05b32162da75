/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-03-05 10:30:08
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-09 12:20:46
 * @Description:
 */
import { dashboardModel } from '@/models';
import { ABTestAPI } from '@/types/ab-test';
import { transformLimitStr, updateBQConfigAdapter } from '@/utils/report/slim';
import { queryBQDataWithCache } from '@/utils/report/report-util';
import {
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  includeToday,
  LabelGenerationParams,
  transformSQLResult
} from '@rixfe/rix-tools';

const DefaultMetrics = ['total_request'];

class ABTestReportService implements ABTestAPI.ABTestReportService {
  async getABTestReportList(
    params: any,
    labels: LabelGenerationParams
  ): Promise<{
    total: number;
    data: Array<ABTestAPI.ABTestReportListItem>;
  }> {
    await updateBQConfigAdapter();
    if (params.seller_id?.length === 1 && params.seller_id[0] === 0) {
      delete params.seller_id;
    }
    const { fragments, info } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        metrics: [...DefaultMetrics, ...params.metrics]
      },
      {
        api_url: labels.tag
      }
    );

    // 对select中的 test_tag_a，test_tag_b 进行替换
    const ReplaceRules: Record<string, string> = {
      test_tag_a: 'COALESCE(test_tag_a, "") AS test_tag_a',
      test_tag_b: 'COALESCE(test_tag_b, "") AS test_tag_b'
    };

    const select = fragments.select.map(
      (item: string) => ReplaceRules[item] || item
    );

    // 构建查询SQL
    const sql = concatSQLFragments({
      ...fragments,
      select: [...select, 'count(*) over() as total'],
      limit: transformLimitStr(params.tnt_id, fragments.limit, false)
    });
    // 判断表类型
    const isDailyTable = info.isDaily;
    const isDemandTable = info.isDemand;

    const { tnt_id, cur_time_zone, start_date, end_date } = params;
    const [data, today_hours] = await Promise.all([
      // 获取数据
      queryBQDataWithCache(sql, labels),
      // 对于非daily表单独查询今日小时数
      !isDailyTable
        ? dashboardModel.getNewHoursToday(
            tnt_id,
            fragments.from,
            cur_time_zone,
            labels
          )
        : 0
    ]);

    // 处理查询结果
    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        metrics: params.metrics || [],
        context: {
          start_date,
          end_date,
          today_hours,
          isDemand: isDemandTable,
          hour: params.hour,
          isToday: includeToday(start_date, end_date),
          split_time: params.split_time,
          timezone: cur_time_zone
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return { total, data: result };
  }
}
export const abTestReportService = new ABTestReportService();
