/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-08 14:47:32
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 14:49:59
 * @Description:
 */

import moment from 'moment-timezone';
import { Context, Next } from 'koa';
import path from 'path';
import { DashboardAPI } from '@/types/dashboard';
import { DateType } from '@/constants/report/dashboard';
import { HeaderOptions, TimeZoneMap } from '@/constants/report/common-report';
import { genEnCode } from '.';
import { iionCusTomCsvHeader } from '@/constants/report/custom-hardcode';

// serivce层里面的计算结果
export const getFullReportCalculationFormula = (
  metric: string,
  item: DashboardAPI.DashboardListItem,
  isDemand: boolean,
  split_time?: number,
  start_date?: string,
  end_date?: string,
  today_hours?: number, // 今天已经存在的小时数
  isToday?: boolean, // 是否包含今天
  isDemandReport?: boolean,
  isSupplyReport?: boolean,
  hour?: number[] // time slot 时段
) => {
  const {
    buyer_net_revenue,
    seller_net_revenue,
    request,
    block_request,
    response,
    out_request,
    click,
    impression,
    win,
    buyer_gross_revenue,
    total_seller_bid_floor,
    total_res_price,
    total_request
  } = item;
  const fill_rate_request =
    isDemandReport || isSupplyReport ? total_request : request;
  const formula: any = {
    profit: +(+buyer_net_revenue - +seller_net_revenue).toFixed(2),
    profit_rate: +buyer_net_revenue
      ? +(
          ((+buyer_net_revenue - +seller_net_revenue) * 100) /
          +buyer_net_revenue
        ).toFixed(2)
      : 0.0,
    total_request: isDemand
      ? +request
        ? +request
        : 0
      : +request + +block_request,
    fill_rate: +response
      ? +((+response * 100) / +fill_rate_request).toFixed(2)
      : 0.0,
    // ecpr: +request
    //   ? +((+buyer_net_revenue * 1000000) / +request).toFixed(2)
    //   : 0.0,
    // adv_ecpr: +out_request
    //   ? +((+buyer_net_revenue * 1000000) / +out_request).toFixed(2)
    //   : 0.0,
    click_rate: +impression ? +((+click * 100) / +impression).toFixed(2) : 0.0,
    win_rate: +response ? ((+win * 100) / +response).toFixed(2) : 0.0,
    impression_rate: +response
      ? +((+impression * 100) / +response).toFixed(2)
      : 0.0,
    buyer_gross_ecpm: +impression
      ? +((+buyer_gross_revenue * 1000) / +impression).toFixed(2)
      : 0.0,
    buyer_net_ecpm: +impression
      ? +((+buyer_net_revenue * 1000) / +impression).toFixed(2)
      : 0.0,
    seller_net_ecpm: +impression
      ? +((+seller_net_revenue * 1000) / +impression).toFixed(2)
      : 0.0,
    total_seller_bid_floor: +request
      ? +(+total_seller_bid_floor / +request).toFixed(4)
      : 0.0,
    bid_price: +response ? +(+total_res_price / +response).toFixed(4) : 0.0
  };

  const days = moment(end_date).diff(moment(start_date), 'days') + 1;
  const todayAecs = (today_hours || 0) * 60 * 60;
  // 用户选择了时段筛选
  const timeSlotNums = hour?.length || 24;
  // 过滤出今天已经存在的小时数
  const todayEffectHours =
    hour?.filter((h: number) => +h <= today_hours! - 1) || [];
  let real_qps = 0;
  if (split_time === DateType.Hour) {
    real_qps = +(+formula.total_request / (60 * 60)).toFixed(0);
  } else if (split_time === DateType.Day) {
    const itemIsToday =
      item.date === moment().endOf('day').format('YYYY-MM-DD');
    // 时段数（小时数）
    let seconds = 60 * 60 * timeSlotNums;
    if (itemIsToday) {
      seconds = 60 * 60 * (todayEffectHours.length || today_hours!);
    }
    real_qps = +(+formula.total_request / seconds).toFixed(0);
  } else {
    let seconds = 60 * 60 * timeSlotNums * days;
    if (isToday) {
      seconds =
        60 *
        60 *
        (timeSlotNums * (days - 1) + (todayEffectHours.length || today_hours!));
    }
    real_qps = +(+formula.total_request / seconds).toFixed(0);
  }

  formula.real_qps = real_qps;
  return formula[metric as keyof typeof formula];
};

// model层里面的计算sql
export const getFullReportCalculationSQL = (
  metric: string,
  isDemand: boolean
) => {
  const sql = {
    total_request: isDemand
      ? 'sum(request) as total_request'
      : '(case when sum(block_request) is null then sum(request) else (sum(request) + sum(block_request)) end) as total_request',
    ecpr: '(case when sum(request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 / sum(request) as numeric), 2) end) as ecpr',
    adv_ecpr: isDemand
      ? '(case when sum(request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 /sum(request) as numeric), 2) end) as adv_ecpr'
      : '(case when sum(out_request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 /sum(out_request) as numeric), 2) end) as adv_ecpr',
    win_rate:
      '(case when sum(response) = 0 then 0 else round(sum(win) *100 *1.0/ sum(response), 2) end) as win_rate',
    avg_dsp_cost_time:
      '(CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(SUM(total_cost_time) / SUM(request),2)END) AS avg_dsp_cost_time',
    avg_response_cost_time:
      '(CASE WHEN SUM(response) = 0 THEN 0 ELSE ROUND(SUM(total_response_cost_time) / SUM(response), 2) END) AS avg_response_cost_time'
  };

  return sql[metric as keyof typeof sql];
};

// 判断搜索日期是否是包含今天
export const includeToday = (start_date: string, end_date: string) => {
  const today = moment().format('YYYYMMDD');
  console.log('today', today, start_date, end_date);
  // 当天0-1点之间的数据还没有完全统计出来，所以不包含在今天
  const exsitedData = moment().startOf('day').add(1, 'hours') < moment();
  const isToday = today === start_date || today === end_date;
  return exsitedData && isToday;
};

// 设置接口超时
export const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(5 * 60 * 1000);
  await next();
};

// 将时段对应的小时数转换为时区对应的小时数（bq默认hour是UTC0的hour）
function convertHourToTimezone(hour: number, timezone: string): number {
  // 获取 UTC 与目标时区的偏移量（以分钟为单位）
  const offset = moment.tz(timezone).utcOffset();

  // 将偏移量转换为小时
  const offsetHours = offset / 60;

  // 调整小时数
  let adjustedHour = hour - offsetHours;

  // 如果结果小于 0，将其转换为 24 小时制
  if (adjustedHour < 0) {
    adjustedHour += 24;
  }

  // 如果结果大于等于 24，将其转换为 24 小时制
  if (adjustedHour >= 24) {
    adjustedHour -= 24;
  }

  return adjustedHour;
}

// 根据时区转化日期
const getDateFromTZ = (formData: any, timeZone: string) => {
  const { start_date, end_date } = formData;
  const options = { ...formData };
  moment.tz.setDefault(timeZone);

  if (Object.hasOwn(options, 'start_date')) {
    const start = moment(start_date).startOf('day').tz('Etc/UTC');
    options.tz_start_date = start.format('YYYY-MM-DD HH:00:00');
  }
  if (Object.hasOwn(options, 'end_date')) {
    const end = moment(end_date).endOf('day').tz('Etc/UTC');
    options.tz_end_date = end.format('YYYY-MM-DD HH:00:00');
  }
  if (Object.hasOwn(options, 'hour')) {
    options.hour = options.hour.map((item: number) => {
      const hour = convertHourToTimezone(item, timeZone);
      return hour;
    });
  }
  if (Object.hasOwn(options, 'dates')) {
    options.dates = options.dates.map((item: string) => {
      // const date = moment(item).startOf('day').tz('Etc/UTC').format('YYYYMMDD');
      const curStartDate = moment(item)
        .startOf('day')
        .tz('Etc/UTC')
        .format('YYYY-MM-DD HH:00:00');
      const curEndDate = moment(item)
        .endOf('day')
        .tz('Etc/UTC')
        .format('YYYY-MM-DD HH:00:00');
      return {
        start: curStartDate,
        end: curEndDate
      };
      // return date;
    });
  }
  return options;
};

// 解析时区参数中间件
export const parseDateParams = async (ctx: Context, next: Next) => {
  const formData = { ...ctx.request.body };
  const params = getDateFromTZ(formData, formData.cur_time_zone);
  params.cur_time_zone = formData.cur_time_zone;
  ctx.request.body = params;
  return await next();
};

// 获取下载csv的文件头部
export const getCsvHeaders = (
  columns: string[],
  metrics: string[],
  tnt_id?: number,
  role_id?: number
) => {
  let headerOptions: {
    header: string;
    key: string;
  }[] = JSON.parse(JSON.stringify(HeaderOptions));
  // iion租户下 Data_Custom角色自定义列名
  if (tnt_id && role_id && tnt_id === 1053 && [34].includes(role_id)) {
    const iionCustomKey = Object.keys(iionCusTomCsvHeader);
    headerOptions = headerOptions.map(item => {
      if (iionCustomKey.includes(item.key)) {
        item.header = iionCusTomCsvHeader[item.key];
      }
      return item;
    });
  }

  const headerCloumns = [...headerOptions].filter(item => {
    if (item.key === 'date') {
      return (
        columns?.includes('day') ||
        columns?.includes('day_hour') ||
        columns?.includes('month') ||
        columns?.includes('date')
      );
    }
    if (
      item.key === 'seller' ||
      item.key === 'buyer' ||
      item.key === 'adv_partner' ||
      item.key === 'pub_partner'
    ) {
      return columns?.includes(`${item.key}_id`);
    }
    if (item.key === 'ad_size') {
      return columns?.includes('ad_width, ad_height');
    }
    return columns?.includes(item.key) || metrics?.includes(item.key);
  });
  return headerCloumns;
};

export const getDownTaskInfo = (
  cur_time_zone: string,
  tnt_id: number,
  columns: string[] = [],
  metrics: string[] = [],
  type_name?: string,
  role_id?: number
) => {
  const time = moment().format('YYYYMMDD_HH_mm_ss');
  const day = moment().format('YYYYMMDD');
  const csvName = `${type_name || 'Report'}_${time}_${genEnCode(2, false)}_(${
    TimeZoneMap[cur_time_zone]
  }).csv`;
  const csvPath = `/files/report/${day}/tnt-${tnt_id}/`;
  const outputCsvPath = path.join(__dirname, `../../${csvPath}/${csvName}`);
  const requestPath = `/api/exported-report/download/${csvName}`;
  const headerCloumns = getCsvHeaders(
    columns || [],
    metrics || [],
    tnt_id,
    role_id
  );

  return {
    csvName,
    csvPath,
    outputCsvPath,
    requestPath,
    headerCloumns
  };
};

/**
 * 保存当前条件，用于记录下载报表的条件
 */
export const resolveCurConditionData = async (ctx: Context, next: Next) => {
  // 原始条件
  const original = { ...ctx.request.body };
  const formData = {
    ...ctx.request.body,
    cur_condition_str: JSON.stringify(original)
  };
  ctx.request.body = formData;
  return await next();
};

/**
 * @deprecated
 * Validates the user agent in the request headers and allows only Mozilla and Chrome browsers.
 * @return {Promise<void>} the result of the next function in the middleware chain
 */
export const vaildUserAgent = async (ctx: Context, next: Next) => {
  const userAgent = ctx.request.headers['user-agent'] || '';
  if (!userAgent.includes('Mozilla') && !userAgent.includes('Chrome')) {
    ctx.status = 403;
    ctx.body = 'Forbidden';
  } else {
    return await next();
  }
};

interface ExtraInfo {
  /**
   * 兜底排序字段，默认为 buyer_net_revenue
   */
  defaultOrderKey?: string;
  /**
   * 兜底字段排序方式，默认为 desc
   */
  defaultOrder?: string;
  /**
   * 是否启用与兜底排序比较，默认为 true，即启用
   */
  isDefault?: boolean;
  /**
   * 校验orderKeys失败时是否返回空字符串，默认为 false，返回兜底排序字段
   */
  isEmptyStr?: boolean;
  /**
   * 额外的 order keys，拼接在最后面
   */
  extraOrderKeys?: string[];
}
/**
 * 携带兜底排序字段的sql排序
 *
 * @param orderKeys string[]
 * @param order string
 * @param extra ExtraInfo
 */
export function getOrderBy(
  orderKeys: string[] = [],
  order: string,
  extra?: ExtraInfo
) {
  const {
    defaultOrderKey = 'buyer_net_revenue',
    defaultOrder = 'desc',
    isDefault = true,
    isEmptyStr = false,
    extraOrderKeys = []
  } = extra || {};
  let prefix = 'order by';

  if (!orderKeys || (!orderKeys.length && !extraOrderKeys.length)) {
    return isEmptyStr
      ? ''
      : `${prefix} ${defaultOrderKey} ${defaultOrder}`;
  }

  // isDefault 判断是否添加 defaultOrder
  const orderKeysArr = Array.from(
    new Set([
      ...orderKeys,
      ...(isDefault ? [defaultOrderKey] : []),
      ...extraOrderKeys
    ])
  ).filter(Boolean);

  const basicOrderArr = orderKeysArr
    .map((key, index) => {
      // 取得 orderKeys 第一个自定义排序字段，如果为空就全按照默认的排序
      if (orderKeys.length === 0) {
        return `${key} ${defaultOrder}`;
      }
      return `${key} ${index === 0 ? order || 'desc' : defaultOrder}`;
    })
    .join(', ');

  return `${prefix} ${basicOrderArr}`;
}
