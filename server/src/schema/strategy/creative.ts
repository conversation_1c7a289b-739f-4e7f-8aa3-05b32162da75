/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-03-22 19:28:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-22 19:37:59
 * @Description:
 */
import <PERSON><PERSON> from 'joi';

const addCreativeSchema = {
  buyer_id: Joi.number().integer().required(),
  seller_id: Joi.alternatives().try(
    Joi.number().integer(),
    Joi.array().items(Joi.number().integer())
  ),
  remark: Joi.string().allow(''),
  op_id: Joi.number().integer(),
  // 1 for crid BL, 2 for adomain BL, 3 for bundle BL
  type: Joi.number().integer().valid(1, 2, 3),
  content: Joi.array().items(Joi.string())
};

export const AddCreativeSchema = Joi.object({ ...addCreativeSchema });

export const UpdateCreativeSchema = Joi.object({
  ...addCreativeSchema,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});
