.card-container {
  border-radius: 6px;
  margin-right: 16px;
  border: none;
  :global {
    .ant-card-head {
      border-bottom: none;
      font-weight: 700;
      font-size: 16px;
      color: var(--text-color);
    }
    .ant-card-body {
      padding-top: 0px;
    }
  }
}

.supply-descriptions-card {
  margin-bottom: 10px;
  border-top: 1px solid #e9e9e9;
  :global {
    .ant-descriptions-header {
      margin-bottom: 8px;
    }
    .ant-descriptions-row {
      .ant-descriptions-item {
        .ant-descriptions-item-label {
          color: #8d9799;
        }
        .ant-descriptions-item-content {
          color: var(--text-color);
        }
      }
    }
    .ant-descriptions-row {
      .ant-descriptions-item:last-child {
        border-right: 0;
      }
    }
    .ant-descriptions-row:nth-child(odd) {
      .ant-descriptions-item {
        padding-bottom: 2px;
      }
    }

    .ant-descriptions-row:nth-child(even) {
      .ant-descriptions-item {
        padding-bottom: 16px;
      }
    }

    .ant-descriptions-row:last-child {
      .ant-descriptions-item {
        padding-bottom: 2px;
      }
    }
  }
}
