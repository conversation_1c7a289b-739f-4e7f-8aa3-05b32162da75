/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 10:35:08
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:56:10
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getSellerIntegrationType } from '@/services/api';

type IntegrationTypeProps = {
  dataSource: CommonAPI.IntegrationTypeItem[],
  reload: () => Promise<any>;
  loading: boolean
}

export default function useSellerIntegrationTypeList(): IntegrationTypeProps {
  const { data, run, loading, error } = useCustomRequest(getSellerIntegrationType, {
    cacheKey: 'seller-integration-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading
  };
}
