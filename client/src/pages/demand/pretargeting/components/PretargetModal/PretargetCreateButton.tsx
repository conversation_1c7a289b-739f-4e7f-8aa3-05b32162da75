import { DemandAndSupplyStatusMap } from '@/constants';
import { usePretargetingState, usePretargetingStore } from '@/pages/demand/pretargeting/store';
import { PlusCircleOutlined } from '@ant-design/icons';
import { useAccess } from '@umijs/max';
import { Button } from 'antd';

export default function PretargetCreateButton() {
  const access = useAccess();
  const state = usePretargetingState();
  const { currentDemand } = usePretargetingStore();

  const handleCreatePretargeting = () => {
    state.pretargetModalOp = 1;
    state.pretargetModalVisible = true;
    state.onePretargetingData = null;
  };

  return (
    <Button
      type="primary"
      icon={<PlusCircleOutlined />}
      onClick={handleCreatePretargeting}
      disabled={
        currentDemand?.status === DemandAndSupplyStatusMap.Testing || access.DisabledButton('addPretargetingAuth')
      }
    >
      Create Pretargeting
    </Button>
  );
}
