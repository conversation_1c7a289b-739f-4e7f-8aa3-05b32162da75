import NormalModal from '@/components/Modal/NormalModal';
import NormalSelect from '@/components/Select/NormalSelect';
import { Form, Input, Switch } from 'antd';
import React from 'react';
import NormalRadio from '../Radio/NormalRadio';
import { usePresetConfirmModal } from './PresetConfirmModalContext';

const PresetConfirmModal: React.FC = () => {
  const { visible, handleSave, handleCancel, form } = usePresetConfirmModal();

  const onOk = async () => {
    try {
      const values = await form.validateFields();
      handleSave(values);
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const onCancel = () => {
    form.resetFields();
    handleCancel();
  };

  return (
    <NormalModal
      title="Save Report Preset"
      open={visible}
      onOk={onOk}
      onCancel={onCancel}
      maskClosable={false}
      okText="Save"
    >
      <Form form={form} layout="vertical">
        {/* 除默认预设外，其他预设可以保存或者另存为新预设，默认预设只能存为新预设 */}
        {/* switch 开关，默认预设不展示 */}
        <Form.Item name="isDefaultPreset" hidden>
          <Switch />
        </Form.Item>
        <Form.Item noStyle dependencies={['isDefaultPreset']}>
          {({ getFieldValue }) => {
            const isDefaultPreset = getFieldValue('isDefaultPreset');
            return isDefaultPreset !== false ? null : (
              <Form.Item
                label="Action"
                name="saveAsNewPreset"
                rules={[{ required: true, message: 'Please select an action' }]}
              >
                <NormalRadio
                  options={[
                    { label: 'Save current preset', value: false },
                    { label: 'Save as new preset', value: true }
                  ]}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        {/* key */}
        <Form.Item name="key" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          name="presetName"
          label="Preset Name"
          rules={[
            { required: true, message: 'Please enter a preset name' },
            { max: 20, message: 'Name cannot exceed 20 characters' }
          ]}
        >
          <Input placeholder="Enter a name for this report preset" maxLength={20} showCount />
        </Form.Item>
        <Form.Item
          name="timeRange"
          label="Dynamic Data Time Period"
          tooltip="Select a dynamic time period that will be applied when loading this preset"
        >
          <NormalSelect
            placeholder="Select time period"
            allowClear
            options={[
              // value grammar: [start_time, end_time], eg: 0 means today, -1 means yesterday, -1_0 means yesterday including today
              { label: 'Today', value: '0' },
              { label: 'Yesterday', value: '-1' },
              { label: 'Last 2 Days(Including Today)', value: '-1_0' },
              { label: 'Last 3 Days(Including Today)', value: '-2_0' },
              { label: 'Last 3 Days', value: '-3_-1' },
              { label: 'Last 7 Days', value: '-7_-1' }
            ]}
          />
        </Form.Item>
      </Form>
    </NormalModal>
  );
};

export * from './PresetConfirmModalContext';
export { PresetConfirmModal };

