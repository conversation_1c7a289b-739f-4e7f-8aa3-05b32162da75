/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-08-30 10:36:45
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 12:06:19
 * @Description:
 */
import { ColumnProps } from 'antd/lib/table';

import { TopBarSearchItem } from '@/components/TopBar';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { formatMoney } from '@/utils';
import moment from 'moment-timezone';

export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Dashboard',
    icon: 'rix-dashboard'
  }
];

export const WelcomeDashboardColumns: ColumnProps<DashboardAPI.DashboardListItem>[] = Object.seal([
  {
    title: 'Date',
    key: 'day',
    fixed: 'left',
    width: 160,
    dataIndex: 'date',
    render: (txt, params) => moment(params.date).format('YYYY-MM-DD'),
    sorter: true
  },
  {
    title: 'Hour',
    dataIndex: 'date',
    width: 160,
    key: 'day_hour',
    render: (txt, params) => params.date,
    sorter: true
  },

  {
    title: 'Advertiser Net Revenue',
    key: 'buyer_net_revenue',
    dataIndex: 'buyer_net_revenue',
    width: 200,
    sorter: true,
    render: (txt: string) => <>{(+txt).toFixed(2)}</>
  },
  {
    title: 'Publisher Net Revenue',
    key: 'seller_net_revenue',
    dataIndex: 'seller_net_revenue',
    width: 200,
    // sorter: true,
    render: (txt: string) => <>{(+txt).toFixed(2)}</>
  },
  {
    title: 'Impression',
    key: 'impression',
    dataIndex: 'impression',
    width: 150,
    // sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Request',
    key: 'request',
    dataIndex: 'request',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Total Request',
    key: 'total_request',
    dataIndex: 'total_request',
    sorter: true,
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  },
  {
    title: 'Profit',
    key: 'profit',
    dataIndex: 'profit',
    width: 150,
    // sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>
  }
  // {
  //   title: 'Click',
  //   key: 'click',
  //   dataIndex: 'click',
  //   width: 150,
  //   // sorter: true,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>
  // },
  // {
  //   title: 'Bid Floor',
  //   key: 'total_seller_bid_floor',
  //   dataIndex: 'total_seller_bid_floor',
  //   width: 150,
  //   // sorter: true,
  //   render: (txt: string) => <>{txt}</>
  // },
  // {
  //   title: 'Fill Rate(%)',
  //   key: 'fill_rate',
  //   dataIndex: 'fill_rate',
  //   // sorter: true,
  //   width: 150
  // }
  // {
  //   title: 'Advertiser Gross Revenue',
  //   key: 'buyer_gross_revenue',
  //   dataIndex: 'buyer_gross_revenue',
  //   width: 220,
  //   // sorter: true,
  //   render: (txt: string) => <>{txt}</>
  // },
  // {
  //   title: 'Publisher Gross Revenue',
  //   key: 'seller_gross_revenue',
  //   dataIndex: 'seller_gross_revenue',
  //   width: 220,
  //   // sorter: true,
  //   render: (txt: string) => <>{txt}</>
  // },
  // {
  //   title: 'Click Rate(%)',
  //   key: 'click_rate',
  //   dataIndex: 'click_rate',
  //   // sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'eCPR',
  //   key: 'ecpr',
  //   dataIndex: 'ecpr',
  //   sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'Advertiser eCPR',
  //   key: 'adv_ecpr',
  //   dataIndex: 'adv_ecpr',
  //   sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'Profit Rate(%)',
  //   key: 'profit_rate',
  //   dataIndex: 'profit_rate',
  //   // sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'Win Rate(%)',
  //   key: 'win_rate',
  //   dataIndex: 'win_rate',
  //   sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'Render Rate(%)',
  //   key: 'impression_rate',
  //   dataIndex: 'impression_rate',
  //   // sorter: true,
  //   width: 150
  // },
  // {
  //   title: 'Advertiser Gross eCPM',
  //   key: 'buyer_gross_ecpm',
  //   dataIndex: 'buyer_gross_ecpm',
  //   // sorter: true,
  //   width: 220
  // },
  // {
  //   title: 'Advertiser Net eCPM',
  //   key: 'buyer_net_ecpm',
  //   dataIndex: 'buyer_net_ecpm',
  //   // sorter: true,
  //   width: 200
  // }
]);

export const WelcomeDashboardSearchOption: TopBarSearchItem[] = [
  {
    name: 'Dates',
    type: 'mutli-dates',
    key: 'dates',
    value: [],
    tooltip: 'Choose two days to compare. Default today and yesterday',
    dates: -7
    // timeLimit: 7,
    // maxDates: 2,
    // mode: 'multiple'
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple'
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple'
  }
];

export const Dimensions = ['day', 'day_hour'];
export const Metrics = [
  'request',
  'total_request',
  'response',
  'impression',
  'click',
  'profit',
  'buyer_net_revenue',
  'seller_net_revenue',
  'fill_rate',
  'buyer_net_ecpm',
  'ecpr',
  'adv_ecpr',
  'win',
  'win_rate'
];
export const MetricsOptions = [
  { label: 'Advertiser Net Revenue', value: 'buyer_net_revenue' },
  { label: 'Publisher Net Revenue', value: 'seller_net_revenue' },
  {
    label: 'Profit',
    value: 'profit',
    tooltip: { showTitle: true, title: 'sum(Advertiser Net Revenue) - sum(Publisher Net Revenue)' }
  },
  { label: 'Request', value: 'request' },
  { label: 'Impression', value: 'impression' },
  { label: 'Total Request', value: 'total_request' }
];
