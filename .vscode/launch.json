{
  "version": "0.2.0",
  "configurations": [
    // server的debug配置，项目中可打断点，方便开发调试
    {
      "name": "server:dev",
      "request": "launch",
      "runtimeArgs": [
        "run",
        "dev"
      ],
      "cwd": "${workspaceFolder}/server",
      "runtimeExecutable": "npm",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "console": "integratedTerminal"
    },
    // client的debug配置 (适用于 Mac 和 Linux)
    {
      "name": "client:dev (Mac/Linux)",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:8000",
      // 涉及到 chrome 多实例的问题，需要指定 userDataDir
      "userDataDir": "${env:HOME}/Desktop/debug_data/chrome",
      "webRoot": "${workspaceFolder}/client",
      "preLaunchTask": "npm: dev - client (Mac/Linux)"
    },
    // client的debug配置 (适用于 Windows)
    {
      "name": "client:dev (Windows)",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:8000",
      "userDataDir": "${env:USERPROFILE}\\Desktop\\debug_data\\chrome",
      "webRoot": "${workspaceFolder}/client",
      "preLaunchTask": "npm: dev - client (Windows)"
    }
  ]
}
