/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-18 14:12:50
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-04-18 16:10:17
 * @Description: Session配置
 */

// 只有 development 和 test 和 prod 和 才设置 iframeConfig
export const iframeEnvs = ['development', 'test', 'prod'];

const needIframeConfig = iframeEnvs.includes(process.env.NODE_ENV || 'development');

const iframeConfig = {
  sameSite: 'none' as 'none',
  secure: true
};

const timeOut = 6 * 60 * 60 * 1000; // 6小时

export const sessionConfig = {
  key: '6qVDl2XN', // cookie key (default is koa:sess)
  autoCommit: true, // (boolean) automatically commit headers (default true)
  maxAge: timeOut, // cookie的过期时间 maxAge in ms (default is 1 days)
  overwrite: true, // 是否可以overwrite    (默认default true)
  httpOnly: true, // cookie是否只有服务器端可以访问 httpOnly or not (default true)
  signed: false, // 签名默认true
  rolling: true, // 在每次请求时强行设置cookie，这将重置cookie过期时间（默认：false）
  renew: true, // (boolean) renew session when session is nearly expired,
  ...(needIframeConfig ? iframeConfig : {})
};

export default {};
