/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-19 20:14:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-13 15:41:40
 * @Description:
 */
import { Tooltip } from 'antd';
import { ColumnProps } from 'antd/lib/table';

import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/ToolTip/HoverTooltip';

import { Countries, CountryOptions } from '@/constants/global-mapping/country';
import { StatusDesc, StatusOptions } from '@/constants';
import { OptionsType } from '..';
import { AdFormatOptions, AdFormatToLabel } from '@/constants/global-mapping/ad-format';
import { ExclamationCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';

export const IsAllMap = {
  YES: 1,
  NO: 0,
  DEFAULT: -1
};

export const FloorTypeOptions: OptionsType[] = [
  {
    label: 'Ad Format + Country',
    value: 1
  },
  {
    label: 'Pub + Ad Format + Country',
    value: 2
  },
  {
    label: 'Pub + Ad Unit + Country',
    value: 3
  },
  {
    label: 'Pub + Adv + Ad Format + Country',
    value: 4
  },
  {
    label: 'Pub + Adv + Ad Unit + Country',
    value: 5
  }
];
export const FloorTypeMap = {
  1: 'Ad Format + Country',
  2: 'Pub + Ad Format + Country',
  3: 'Pub + Ad Unit + Country',
  4: 'Pub + Adv + Ad Format + Country',
  5: 'Pub + Adv + Ad Unit + Country'
};
export const FloorType = {
  'Ad Format + Country': 1,
  'Pub + Ad Format + Country': 2,
  'Pub + Ad Unit + Country': 3,
  'Pub + Adv + Ad Format + Country': 4,
  'Pub + Adv + Ad Unit + Country': 5
};
export const SSPFloorType = {
  '(SSP) Pub + Ad Unit + Country': 99
};

export const SSPFloorTypeMap = {
  99: '(SSP) Pub + Ad Unit + Country'
};
export const SSPFloorTypeOptions: OptionsType[] = [
  {
    label: '(SSP) Pub + Ad Unit + Country',
    value: 99
  }
];
export const FloorSearchOption: TopBarSearchItem[] = [
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Publisher'
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Advertiser'
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: [],
    options: [
      ...FloorTypeOptions.filter(item => item.value !== FloorType['Ad Format + Country']),
      ...SSPFloorTypeOptions
    ] as any,
    placeholder: 'Please Select Type',
    mode: 'multiple'
  },
  {
    name: 'Ad Unit',
    type: 'select',
    key: 'plm_id',
    value: [],
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Ad Unit'
  },
  {
    name: 'Ad Format',
    type: 'select',
    key: 'ad_format',
    value: [],
    options: AdFormatOptions.map(item => {
      return {
        value: +item.value,
        label: item.label
      };
    }),
    mode: 'multiple',
    placeholder: 'Please Select Ad Format'
  },
  {
    name: 'Country',
    type: 'select',
    key: 'country',
    mode: 'multiple',
    value: [],
    options: CountryOptions,
    placeholder: 'Please Select Country'
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: StatusOptions,
    placeholder: 'Please Select Status'
  }
];

export const FloorBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Strategy Config',
    icon: 'rix-strategy'
  },
  {
    name: 'Bid Floor'
  }
];

export const FloorColumns: ColumnProps<StrategyAPI.FloorListItem>[] = [
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 120,
    render: txt => <>{AdFormatToLabel[txt] || '-'}</>
  },
  {
    title: 'Country',
    dataIndex: 'country',
    width: 180,
    ellipsis: { showTitle: false },
    render: (txt, row) => {
      return (
        <HoverToolTip title={Countries[txt] || '-'}>
          <span>
            <>{Countries[txt] || '-'}</>
          </span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Bid Floor',
    dataIndex: 'bid_floor',
    width: 100,
    render: (txt, row) => {
      return !row.isParent ? txt : '-';
    }
  },
  {
    title: 'Modified by',
    width: 135,
    dataIndex: 'account_name',
    render: (_, row) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ color: row['account_status'] === 3 ? '#cccc' : '' }}>
          {row['account_status'] === 3 ? 'Unknown' : row['account_status'] === 0 ? '-' : _}
        </span>
        {row['account_status'] === 3 && (
          <Tooltip title={row['account_status'] === 3 ? 'User is deleted' : ''}>
            <ExclamationCircleOutlined style={{ color: 'rgb(250, 174, 20)', paddingLeft: '3px', cursor: 'pointer' }} />
          </Tooltip>
        )}
      </div>
    )
  },
  {
    title: 'Updated on',
    dataIndex: 'update_time',
    width: 160
  },
  {
    title: 'Status',
    dataIndex: 'status',
    width: 85,
    render: (_, row) => (
      <StatusTag type={(row['status'] && StatusDesc[row['status']] && StatusDesc[row['status']].toLowerCase()) || ''}>
        {row['status'] && StatusDesc[row['status']]}
      </StatusTag>
    )
  }
];

export const FloorColumnsForSupply: ColumnProps<StrategyAPI.FloorListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'name',
    width: 220,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={txt}>
        <span>{txt ? `${txt}(${params.tmp_id})` : '-'}</span>
      </HoverToolTip>
    )
  },
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 220,
    ellipsis: { showTitle: false },
    render: (txt, row) => <>{txt || '-'}</>
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined style={{ paddingLeft: '5px', cursor: 'pointer' }} />
        </Tooltip>
      </div>
    ),
    ellipsis: { showTitle: false },
    dataIndex: 'type',
    width: 180,
    render: (txt, row) => {
      const type = { ...FloorTypeMap, ...SSPFloorTypeMap };
      return (
        <HoverToolTip title={txt === 0 ? '-' : (type as { [key: number]: string })[txt]}>
          <span>
            <>{txt === 0 ? '-' : (type as { [key: number]: string })[txt]}</>
          </span>
        </HoverToolTip>
      );
    }
  },
  {
    title: 'Ad Unit',
    dataIndex: 'plm_id',
    width: 150,
    ellipsis: { showTitle: false },
    render: (txt, row) => (
      <HoverToolTip title={row.isParent ? '-' : txt ? `${txt}(${row.plm_name})` : '-'}>
        <span>{row.isParent ? '-' : txt ? `${txt}(${row.plm_name ? row.plm_name : '-'})` : '-'}</span>
      </HoverToolTip>
    )
  },

  ...FloorColumns
];

export const FloorColumnsForOverall: ColumnProps<StrategyAPI.FloorListItem>[] = [...FloorColumns];

export const FloorAdFormatToLabel: API.StringToStringType = {
  1: 'Banner',
  2: 'Video',
  3: 'Native'
};

export const FloorTabOptions = [
  { label: 'Overall Bidfloor', value: '1' },
  { label: 'Supply Bidfloor', value: '2' }
];

export const FloorTab = {
  'Overall Bidfloor': '1',
  'Supply Bidfloor': '2'
};

export const FloorParentItem: StrategyAPI.FloorListItem = {
  id: -2,
  type: -2,
  buyer_id: -2,
  seller_id: -2,
  plm_id: -2,
  ad_format: -2,
  country: '',
  bid_floor: -2,
  op_id: -2,
  status: -2,
  tnt_id: -2,
  account_status: -2,
  account_name: '',
  seller_name: '',
  buyer_name: '',
  name: '',
  tmp_id: -2,
  key: -2,
  children: [],
  update_time: '',
  plm_name: ''
};
