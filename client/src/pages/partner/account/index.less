.page-container-account {
  .account-container {
    margin-right: 16px;
    height: 100%;
    background-color: #fff;
    border-radius: 6px;
    padding: 28px 32px;
  }
  .account-edit {
    color: #c1cbcc;
    &:hover {
      color: var(--primary-1);
    }
  }
  :global {
    .ant-spin-nested-loading {
      flex: 1;
      height: 100%;
      .ant-spin-container {
        height: 100%;
      }
    }
    .ant-table-cell {
      .copy-container {
        border-radius: 6px;
        height: 32px;
        display: flex;
        align-items: center;
        width: 100%;
        > span.info {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        :global {
          .ant-typography {
            margin-bottom: 0px;
            height: 32px;
            line-height: 32px;
            border-radius: 0px 6px 6px 0px;
            font-size: 22px;
          }
        }
      }
    }
  }
}
