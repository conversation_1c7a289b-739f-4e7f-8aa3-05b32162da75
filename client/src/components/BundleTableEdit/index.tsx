/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 11:15:33
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-13 17:41:03
 * @Description:
 */

import styles from './index.less';
import { useEffect, useRef, useState } from 'react';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Spin } from 'antd';

import NormalModal from '@/components/Modal/NormalModal';
import BundleInput from './BundleInput';
import LongContentMore from '../LongContentMore';
import useSizeHook from '@/hooks/useSizeHook';
import HoverTooltip from '@/components/ToolTip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';

type BundleTableEditProps = {
  editTitle: React.ReactNode;
  editTips: string;
  deleteAllTitle: string;
  deleteAllTips: string;
  disabled?: boolean;
  defaultShowNum?: number; // 默认显示数据
  open?: boolean; // 是否显示
  contentMaxHeight?: number | string; // 内容最大高度
  editSingle?: boolean; // 是否是单个编辑
  loading?: boolean;
  onChange?: (val: string[]) => void;
  value?: string[];
  validator?: (val: string) => boolean; // 对里面的内容校验使用
  limitCreateNum?: number; // 限制创建的数量
};

export default function BundleTableEdit({
  editTitle,
  editTips,
  deleteAllTitle,
  deleteAllTips,
  disabled = false,
  defaultShowNum = 100,
  open = true,
  contentMaxHeight = 'calc(100vh - 500px)',
  editSingle = false,
  loading = false,
  value,
  onChange,
  validator,
  limitCreateNum
}: BundleTableEditProps): JSX.Element {
  const { size } = useSizeHook({ width: 0, height: 0 });
  const divRef = useRef<HTMLDivElement | null>(null);
  const [contentList, setContentList] = useState<string[]>([]);
  const [bundleVisible, setBundleVisible] = useState(false);
  // 显示数据
  const [showList, setShowList] = useState<string[]>([]);
  const [maxWidth, setMaxWidth] = useState(376);
  const [maxHeight, setMaxHeight] = useState<string | number>(contentMaxHeight);
  const [isEdit, setIsEdit] = useState(false);
  const [currentEditBundle, setCurrentEditBundle] = useState<string>('');

  useEffect(() => {
    const offsetWidth = divRef.current?.offsetWidth || 376;
    setMaxWidth(offsetWidth);
  }, [divRef.current]);

  useEffect(() => {
    const minHeight = 700; // 至少显示200px的高度
    if (size.height < minHeight) {
      setMaxHeight(200);
    } else {
      setMaxHeight(contentMaxHeight);
    }
    const offsetWidth = divRef.current?.offsetWidth || 376;
    setMaxWidth(offsetWidth);
  }, [size]);

  useEffect(() => {
    if (value?.length) {
      // 需要倒序显示
      const data = [...value].reverse();
      setContentList(data);
    } else {
      setContentList([]);
    }
  }, [value]);

  const handleSaveBundle = (val: string[] | any) => {
    setContentList(val);
    // 需要翻转一遍
    const data = [...val].reverse();
    onChange?.(data);
    handleCloseBundle();
  };

  const handleCloseBundle = () => {
    setCurrentEditBundle('');
    setBundleVisible(false);
    setIsEdit(false);
  };

  const handleOpenBundle = () => {
    if (!disabled) {
      setBundleVisible(true);
      setIsEdit(false);
    }
  };

  const handleClearBundle = () => {
    if (contentList.length && !disabled) {
      NormalModal.confirm({
        title: deleteAllTitle,
        content: deleteAllTips,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          // 清空数据
          setContentList([]);
          onChange?.([]);
        },
        okButtonProps: {
          danger: true
        }
      });
    }
  };
  const handleDeleteBundle = (item: string) => {
    if (!disabled) {
      NormalModal.confirm({
        title: 'Tips',
        content: `Are you sure to delete  ”${item}“ ?`,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          const arr = contentList.filter(val => val !== item);
          // 删除数据
          setContentList(arr);
          const data = [...arr].reverse();
          onChange?.(data);
        },
        okButtonProps: {
          danger: true
        }
      });
    }
  };

  const handleEditBundle = (item: string) => {
    if (!disabled) {
      setIsEdit(true);
      setCurrentEditBundle(item);
      setBundleVisible(true);
    }
  };

  const handleShowDataChange = (val: string[] | number[]) => {
    setShowList(val as string[]);
  };

  return (
    <div className={styles['content-container']} ref={divRef}>
      <Spin spinning={loading}>
        <div className={styles['content-btn']}>
          <RixEngineFont
            type="rix-edit"
            onClick={() => handleOpenBundle()}
            style={{ fontSize: 18 }}
            className={disabled ? styles['svg-disabled'] : styles['svg-default']}
          />
          <RixEngineFont
            type="rix-trash"
            onClick={() => handleClearBundle()}
            style={
              contentList.length > 0 ? { cursor: 'pointer', fontSize: 20 } : { cursor: 'not-allowed', fontSize: 22 }
            }
            className={!disabled && contentList.length > 0 ? styles['svg-trash'] : styles['svg-disabled']}
          />
        </div>
        {contentList.length > 0 ? (
          <LongContentMore
            contentMaxHeight={maxHeight}
            defaultShowNum={defaultShowNum}
            open={open}
            onShowDataChange={handleShowDataChange}
            dataSource={contentList}
            // style={{ maxWidth: maxWidth - 60 - 45 }}
          >
            {showList.map((item, index) => (
              <div className={styles['content-body']} key={index}>
                <div className={styles['left']} style={{ width: 60 }}>
                  {/* 倒序显示 */}
                  <span>{index + 1}</span>
                </div>
                <div className={styles['center']} style={{ width: maxWidth - 60 - 45 }}>
                  <HoverTooltip title={item} maxWidth={maxWidth - 60 - 45} placement="top">
                    <span style={{ maxWidth: maxWidth - 60 - 45 }} className={styles['ellipsis']}>
                      {item}
                    </span>
                  </HoverTooltip>
                </div>

                {editSingle && (
                  <RixEngineFont
                    type="rix-edit"
                    onClick={() => handleEditBundle(item)}
                    style={{ cursor: 'pointer', width: 45, fontSize: 20 }}
                    className={!disabled ? styles['svg-default'] : styles['svg-disabled']}
                    disabled={disabled}
                  />
                )}
                <RixEngineFont
                  type="rix-trash"
                  onClick={() => handleDeleteBundle(item)}
                  style={{ cursor: 'pointer', width: 45, fontSize: 20 }}
                  className={!disabled ? styles['svg-trash'] : styles['svg-disabled']}
                  disabled={disabled}
                />
              </div>
            ))}
          </LongContentMore>
        ) : (
          <div className={styles['content-empty']}>
            <RixEngineFont type="rix-empty" style={{ fontSize: 48, paddingTop: 12 }} />
            <span>No Data</span>
          </div>
        )}
      </Spin>

      <BundleInput
        visible={bundleVisible}
        handleCancel={handleCloseBundle}
        handleOk={handleSaveBundle}
        defaultList={contentList}
        title={editTitle}
        tips={editTips}
        validator={validator || (val => !!val)}
        isReverse={true}
        isEdit={isEdit}
        currentEditBundle={currentEditBundle}
        limitCreateNum={limitCreateNum}
      />
    </div>
  );
}
